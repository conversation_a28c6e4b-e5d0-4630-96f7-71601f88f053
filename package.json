{"name": "cyber-learning-platform", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "vite --port 3000 --host 0.0.0.0 --strictPort", "dev": "vite --port 3000 --host 0.0.0.0 --strictPort", "dev:analyze": "vite --mode analyze --port 3000 --host 0.0.0.0 --strictPort", "build": "vite build", "build:analyze": "vite build --mode analyze", "preview": "vite preview", "server": "node server.js", "train": "node src/lib/trainModel.js", "migrate-db": "node scripts/migrate-database.js", "setup-db": "node scripts/setup-supabase.js", "type-check": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore --fix", "format": "prettier --write .", "check-format": "prettier --check .", "test": "NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install", "pre-commit": "lint-staged"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@react-three/drei": "^9.80.0", "@react-three/fiber": "^8.13.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.3", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@types/react-query": "^1.2.8", "autoprefixer": "^10.4.17", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "d3": "^7.8.5", "d3-geo": "^3.1.1", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "framer-motion": "^10.18.0", "helmet": "^8.1.0", "howler": "^2.2.4", "joi": "^17.13.3", "lucide-react": "^0.511.0", "matter-js": "^0.19.0", "postcss": "^8.4.33", "react": "^18.2.0", "react-countup": "^6.5.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-helmet": "^6.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "react-intersection-observer": "^9.16.0", "react-particles": "^2.12.2", "react-query": "^3.39.3", "react-router-dom": "^6.21.3", "react-simple-maps": "^3.0.0", "recharts": "^2.12.1", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.1", "three": "^0.160.1", "three-globe": "^2.42.3", "tone": "^14.7.77", "topojson-client": "^3.1.0", "tsparticles": "^2.12.0", "typed.js": "^2.1.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-unicode11": "^0.6.0", "xterm-addon-web-links": "^0.9.0", "zod": "^3.25.13", "zustand": "^4.5.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml,css,scss}": ["prettier --write"]}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@swc/core": "^1.11.24", "@swc/jest": "^0.2.38", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.6.1", "@types/d3": "^7.4.3", "@types/howler": "^2.2.12", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.176.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.9.0", "babel-jest": "^29.7.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^5.0.8", "vite-plugin-compression2": "^1.3.3"}}