"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./Billboard.cjs.js"),r=require("./ScreenSpace.cjs.js"),s=require("./QuadraticBezierLine.cjs.js"),t=require("./CubicBezierLine.cjs.js"),o=require("./CatmullRomLine.cjs.js"),i=require("./Line.cjs.js"),a=require("./PositionalAudio.cjs.js"),u=require("./Text.cjs.js"),c=require("./Text3D.cjs.js"),n=require("./Effects.cjs.js"),j=require("./GradientTexture.cjs.js"),p=require("./Image.cjs.js"),x=require("./Edges.cjs.js"),l=require("./Trail.cjs.js"),q=require("./Sampler.cjs.js"),m=require("./ComputedAttribute.cjs.js"),d=require("./Clone.cjs.js"),h=require("./MarchingCubes.cjs.js"),C=require("./Decal.cjs.js"),M=require("./Svg.cjs.js"),S=require("./Gltf.cjs.js"),f=require("./AsciiRenderer.cjs.js"),T=require("./OrthographicCamera.cjs.js"),P=require("./PerspectiveCamera.cjs.js"),b=require("./CubeCamera.cjs.js"),B=require("./DeviceOrientationControls.cjs.js"),g=require("./FlyControls.cjs.js"),v=require("./MapControls.cjs.js"),F=require("./OrbitControls.cjs.js"),L=require("./TrackballControls.cjs.js"),A=require("./ArcballControls.cjs.js"),k=require("./TransformControls.cjs.js"),D=require("./PointerLockControls.cjs.js"),E=require("./FirstPersonControls.cjs.js"),G=require("./CameraControls.cjs.js"),R=require("./FaceControls.cjs.js"),w=require("./GizmoHelper.cjs.js"),z=require("./GizmoViewcube.cjs.js"),O=require("./GizmoViewport.cjs.js"),y=require("./Grid.cjs.js"),I=require("./useCubeTexture.cjs.js"),H=require("./useFBX.cjs.js"),V=require("./useGLTF.cjs.js"),W=require("./useKTX2.cjs.js"),Q=require("./useProgress.cjs.js"),X=require("./useTexture.cjs.js"),K=require("./useVideoTexture.cjs.js"),N=require("./useFont.cjs.js"),U=require("./Stats.cjs.js"),_=require("./StatsGl.cjs.js"),J=require("./useDepthBuffer.cjs.js"),Y=require("./useAspect.cjs.js"),Z=require("./useCamera.cjs.js"),$=require("./useDetectGPU.cjs.js"),ee=require("./useHelper.cjs.js"),re=require("./useBVH.cjs.js"),se=require("./useContextBridge.cjs.js"),te=require("./useAnimations.cjs.js"),oe=require("./useFBO.cjs.js"),ie=require("./useIntersect.cjs.js"),ae=require("./useBoxProjectedEnv.cjs.js"),ue=require("./BBAnchor.cjs.js"),ce=require("./useTrailTexture.cjs.js"),ne=require("./useCubeCamera.cjs.js"),je=require("./Example.cjs.js"),pe=require("./SpriteAnimator.cjs.js"),xe=require("./FaceLandmarker.cjs.js"),le=require("./CurveModifier.cjs.js"),qe=require("./MeshDistortMaterial.cjs.js"),me=require("./MeshWobbleMaterial.cjs.js"),de=require("./MeshReflectorMaterial.cjs.js"),he=require("./MeshRefractionMaterial.cjs.js"),Ce=require("./MeshTransmissionMaterial.cjs.js"),Me=require("./MeshDiscardMaterial.cjs.js"),Se=require("./PointMaterial.cjs.js"),fe=require("./shaderMaterial.cjs.js"),Te=require("./softShadows.cjs.js"),Pe=require("./shapes.cjs.js"),be=require("./Facemesh.cjs.js"),Be=require("./RoundedBox.cjs.js"),ge=require("./ScreenQuad.cjs.js"),ve=require("./Center.cjs.js"),Fe=require("./Resize.cjs.js"),Le=require("./Bounds.cjs.js"),Ae=require("./CameraShake.cjs.js"),ke=require("./Float.cjs.js"),De=require("./Stage.cjs.js"),Ee=require("./Backdrop.cjs.js"),Ge=require("./Shadow.cjs.js"),Re=require("./Caustics.cjs.js"),we=require("./ContactShadows.cjs.js"),ze=require("./AccumulativeShadows.cjs.js"),Oe=require("./Reflector.cjs.js"),ye=require("./SpotLight.cjs.js"),Ie=require("./Environment.cjs.js"),He=require("./Lightformer.cjs.js"),Ve=require("./Sky.cjs.js"),We=require("./Stars.cjs.js"),Qe=require("./Cloud.cjs.js"),Xe=require("./Sparkles.cjs.js"),Ke=require("./useEnvironment.cjs.js"),Ne=require("./useMatcapTexture.cjs.js"),Ue=require("./useNormalTexture.cjs.js"),_e=require("./Wireframe.cjs.js"),Je=require("./Points.cjs.js"),Ye=require("./Instances.cjs.js"),Ze=require("./Segments.cjs.js"),$e=require("./Detailed.cjs.js"),er=require("./Preload.cjs.js"),rr=require("./BakeShadows.cjs.js"),sr=require("./meshBounds.cjs.js"),tr=require("./AdaptiveDpr.cjs.js"),or=require("./AdaptiveEvents.cjs.js"),ir=require("./PerformanceMonitor.cjs.js"),ar=require("./RenderTexture.cjs.js"),ur=require("./Mask.cjs.js"),cr=require("./Hud.cjs.js"),nr=require("./MeshPortalMaterial.cjs.js");require("@babel/runtime/helpers/extends"),require("react"),require("@react-three/fiber"),require("react-merge-refs"),require("three"),require("three-stdlib"),require("troika-three-text"),require("suspend-react"),require("meshline"),require("lodash.pick"),require("lodash.omit"),require("camera-controls"),require("maath"),require("@mediapipe/tasks-vision"),require("zustand"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("stats-gl"),require("detect-gpu"),require("three-mesh-bvh"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js"),require("react-composer"),exports.Billboard=e.Billboard,exports.ScreenSpace=r.ScreenSpace,exports.QuadraticBezierLine=s.QuadraticBezierLine,exports.CubicBezierLine=t.CubicBezierLine,exports.CatmullRomLine=o.CatmullRomLine,exports.Line=i.Line,exports.PositionalAudio=a.PositionalAudio,exports.Text=u.Text,exports.Text3D=c.Text3D,exports.Effects=n.Effects,exports.isWebGL2Available=n.isWebGL2Available,exports.GradientTexture=j.GradientTexture,exports.GradientType=j.GradientType,exports.Image=p.Image,exports.Edges=x.Edges,exports.Trail=l.Trail,exports.useTrail=l.useTrail,exports.Sampler=q.Sampler,exports.useSurfaceSampler=q.useSurfaceSampler,exports.ComputedAttribute=m.ComputedAttribute,exports.Clone=d.Clone,exports.MarchingCube=h.MarchingCube,exports.MarchingCubes=h.MarchingCubes,exports.MarchingPlane=h.MarchingPlane,exports.Decal=C.Decal,exports.Svg=M.Svg,exports.Gltf=S.Gltf,exports.AsciiRenderer=f.AsciiRenderer,exports.OrthographicCamera=T.OrthographicCamera,exports.PerspectiveCamera=P.PerspectiveCamera,exports.CubeCamera=b.CubeCamera,exports.DeviceOrientationControls=B.DeviceOrientationControls,exports.FlyControls=g.FlyControls,exports.MapControls=v.MapControls,exports.OrbitControls=F.OrbitControls,exports.TrackballControls=L.TrackballControls,exports.ArcballControls=A.ArcballControls,exports.TransformControls=k.TransformControls,exports.PointerLockControls=D.PointerLockControls,exports.FirstPersonControls=E.FirstPersonControls,exports.CameraControls=G.CameraControls,exports.FaceControls=R.FaceControls,exports.useFaceControls=R.useFaceControls,exports.GizmoHelper=w.GizmoHelper,exports.useGizmoContext=w.useGizmoContext,exports.GizmoViewcube=z.GizmoViewcube,exports.GizmoViewport=O.GizmoViewport,exports.Grid=y.Grid,exports.useCubeTexture=I.useCubeTexture,exports.useFBX=H.useFBX,exports.useGLTF=V.useGLTF,exports.useKTX2=W.useKTX2,exports.useProgress=Q.useProgress,exports.IsObject=X.IsObject,exports.useTexture=X.useTexture,exports.useVideoTexture=K.useVideoTexture,exports.useFont=N.useFont,exports.Stats=U.Stats,exports.StatsGl=_.StatsGl,exports.useDepthBuffer=J.useDepthBuffer,exports.useAspect=Y.useAspect,exports.useCamera=Z.useCamera,exports.useDetectGPU=$.useDetectGPU,exports.useHelper=ee.useHelper,exports.Bvh=re.Bvh,exports.useBVH=re.useBVH,exports.useContextBridge=se.useContextBridge,exports.useAnimations=te.useAnimations,exports.useFBO=oe.useFBO,exports.useIntersect=ie.useIntersect,exports.useBoxProjectedEnv=ae.useBoxProjectedEnv,exports.BBAnchor=ue.BBAnchor,exports.useTrailTexture=ce.useTrailTexture,exports.useCubeCamera=ne.useCubeCamera,exports.Example=je.Example,exports.SpriteAnimator=pe.SpriteAnimator,exports.FaceLandmarker=xe.FaceLandmarker,exports.FaceLandmarkerDefaults=xe.FaceLandmarkerDefaults,exports.useFaceLandmarker=xe.useFaceLandmarker,exports.CurveModifier=le.CurveModifier,exports.MeshDistortMaterial=qe.MeshDistortMaterial,exports.MeshWobbleMaterial=me.MeshWobbleMaterial,exports.MeshReflectorMaterial=de.MeshReflectorMaterial,exports.MeshRefractionMaterial=he.MeshRefractionMaterial,exports.MeshTransmissionMaterial=Ce.MeshTransmissionMaterial,exports.MeshDiscardMaterial=Me.MeshDiscardMaterial,exports.PointMaterial=Se.PointMaterial,exports.PointMaterialImpl=Se.PointMaterialImpl,exports.shaderMaterial=fe.shaderMaterial,exports.SoftShadows=Te.SoftShadows,exports.Box=Pe.Box,exports.Capsule=Pe.Capsule,exports.Circle=Pe.Circle,exports.Cone=Pe.Cone,exports.Cylinder=Pe.Cylinder,exports.Dodecahedron=Pe.Dodecahedron,exports.Extrude=Pe.Extrude,exports.Icosahedron=Pe.Icosahedron,exports.Lathe=Pe.Lathe,exports.Octahedron=Pe.Octahedron,exports.Plane=Pe.Plane,exports.Polyhedron=Pe.Polyhedron,exports.Ring=Pe.Ring,exports.Shape=Pe.Shape,exports.Sphere=Pe.Sphere,exports.Tetrahedron=Pe.Tetrahedron,exports.Torus=Pe.Torus,exports.TorusKnot=Pe.TorusKnot,exports.Tube=Pe.Tube,exports.Facemesh=be.Facemesh,exports.FacemeshDatas=be.FacemeshDatas,exports.FacemeshEye=be.FacemeshEye,exports.FacemeshEyeDefaults=be.FacemeshEyeDefaults,exports.RoundedBox=Be.RoundedBox,exports.ScreenQuad=ge.ScreenQuad,exports.Center=ve.Center,exports.Resize=Fe.Resize,exports.Bounds=Le.Bounds,exports.useBounds=Le.useBounds,exports.CameraShake=Ae.CameraShake,exports.Float=ke.Float,exports.Stage=De.Stage,exports.Backdrop=Ee.Backdrop,exports.Shadow=Ge.Shadow,exports.Caustics=Re.Caustics,exports.ContactShadows=we.ContactShadows,exports.AccumulativeShadows=ze.AccumulativeShadows,exports.RandomizedLight=ze.RandomizedLight,exports.accumulativeContext=ze.accumulativeContext,exports.Reflector=Oe.Reflector,exports.SpotLight=ye.SpotLight,exports.SpotLightShadow=ye.SpotLightShadow,exports.Environment=Ie.Environment,exports.EnvironmentCube=Ie.EnvironmentCube,exports.EnvironmentMap=Ie.EnvironmentMap,exports.EnvironmentPortal=Ie.EnvironmentPortal,exports.Lightformer=He.Lightformer,exports.Sky=Ve.Sky,exports.calcPosFromAngles=Ve.calcPosFromAngles,exports.Stars=We.Stars,exports.Cloud=Qe.Cloud,exports.Sparkles=Xe.Sparkles,exports.useEnvironment=Ke.useEnvironment,exports.useMatcapTexture=Ne.useMatcapTexture,exports.useNormalTexture=Ue.useNormalTexture,exports.Wireframe=_e.Wireframe,exports.Point=Je.Point,exports.Points=Je.Points,exports.PointsBuffer=Je.PointsBuffer,exports.PositionPoint=Je.PositionPoint,exports.Instance=Ye.Instance,exports.Instances=Ye.Instances,exports.Merged=Ye.Merged,exports.Segment=Ze.Segment,exports.SegmentObject=Ze.SegmentObject,exports.Segments=Ze.Segments,exports.Detailed=$e.Detailed,exports.Preload=er.Preload,exports.BakeShadows=rr.BakeShadows,exports.meshBounds=sr.meshBounds,exports.AdaptiveDpr=tr.AdaptiveDpr,exports.AdaptiveEvents=or.AdaptiveEvents,exports.PerformanceMonitor=ir.PerformanceMonitor,exports.usePerformanceMonitor=ir.usePerformanceMonitor,exports.RenderTexture=ar.RenderTexture,exports.Mask=ur.Mask,exports.useMask=ur.useMask,exports.Hud=cr.Hud,exports.MeshPortalMaterial=nr.MeshPortalMaterial;
