"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("lodash.omit"),n=require("lodash.pick"),u=require("react"),o=require("three"),c=require("three-stdlib");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var f=s(e),i=s(r),l=s(n),d=a(u),h=a(o);const m=d.forwardRef((({children:e,domElement:r,onChange:n,onMouseDown:u,onMouseUp:o,onObjectChange:s,object:a,makeDefault:m,...v},E)=>{const b=["enabled","axis","mode","translationSnap","rotationSnap","scaleSnap","space","size","showX","showY","showZ"],{camera:g,...p}=v,L=l.default(p,b),j=i.default(p,b),y=t.useThree((e=>e.controls)),O=t.useThree((e=>e.gl)),w=t.useThree((e=>e.events)),T=t.useThree((e=>e.camera)),q=t.useThree((e=>e.invalidate)),D=t.useThree((e=>e.get)),C=t.useThree((e=>e.set)),R=g||T,M=r||w.connected||O.domElement,x=d.useMemo((()=>new c.TransformControls(R,M)),[R,M]),_=d.useRef();d.useLayoutEffect((()=>(a?x.attach(a instanceof h.Object3D?a:a.current):_.current instanceof h.Object3D&&x.attach(_.current),()=>{x.detach()})),[a,e,x]),d.useEffect((()=>{if(y){const e=e=>y.enabled=!e.value;return x.addEventListener("dragging-changed",e),()=>x.removeEventListener("dragging-changed",e)}}),[x,y]);const k=d.useRef(),P=d.useRef(),S=d.useRef(),U=d.useRef();return d.useLayoutEffect((()=>{k.current=n}),[n]),d.useLayoutEffect((()=>{P.current=u}),[u]),d.useLayoutEffect((()=>{S.current=o}),[o]),d.useLayoutEffect((()=>{U.current=s}),[s]),d.useEffect((()=>{const e=e=>{q(),null==k.current||k.current(e)},t=e=>null==P.current?void 0:P.current(e),r=e=>null==S.current?void 0:S.current(e),n=e=>null==U.current?void 0:U.current(e);return x.addEventListener("change",e),x.addEventListener("mouseDown",t),x.addEventListener("mouseUp",r),x.addEventListener("objectChange",n),()=>{x.removeEventListener("change",e),x.removeEventListener("mouseDown",t),x.removeEventListener("mouseUp",r),x.removeEventListener("objectChange",n)}}),[q,x]),d.useEffect((()=>{if(m){const e=D().controls;return C({controls:x}),()=>C({controls:e})}}),[m,x]),x?d.createElement(d.Fragment,null,d.createElement("primitive",f.default({ref:E,object:x},L)),d.createElement("group",f.default({ref:_},j),e)):null}));exports.TransformControls=m;
