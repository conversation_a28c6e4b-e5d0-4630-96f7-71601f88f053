"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three-stdlib"),r=require("@react-three/fiber");let o=null;function t(r,t,d){return s=>{d&&d(s),r&&(o||(o=new e.DRACOLoader),o.setDecoderPath("string"==typeof r?r:"https://www.gstatic.com/draco/versioned/decoders/1.5.5/"),s.setDRACOLoader(o)),t&&s.setMeshoptDecoder("function"==typeof e.MeshoptDecoder?e.MeshoptDecoder():e.MeshoptDecoder)}}function d(o,d=!0,s=!0,a){return r.useLoader(e.GLTFLoader,o,t(d,s,a))}d.preload=(o,d=!0,s=!0,a)=>r.useLoader.preload(e.G<PERSON>FLoader,o,t(d,s,a)),d.clear=o=>r.useLoader.clear(e.G<PERSON>FLoader,o),exports.useGLTF=d;
