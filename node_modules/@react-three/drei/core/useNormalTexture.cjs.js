"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("./useTexture.cjs.js"),t=require("three"),s=require("suspend-react");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("@react-three/fiber");var a=n(e);exports.useNormalTexture=function(e=0,n={},o){const{repeat:u=[1,1],anisotropy:c=1,offset:i=[0,0]}=n,f=s.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/normals/normals.json").then((e=>e.json()))),["normalsList"]),p=a.useMemo((()=>Object.keys(f).length),[]),d=f[0],l=`https://rawcdn.githack.com/pmndrs/drei-assets/7a3104997e1576f83472829815b00880d88b32fb/normals/${f[e]||d}`,b=r.useTexture(l,o);return a.useLayoutEffect((()=>{b&&(b.wrapS=b.wrapT=t.RepeatWrapping,b.repeat=new t.Vector2(u[0],u[1]),b.offset=new t.Vector2(i[0],i[1]),b.anisotropy=c)}),[b,c,u,i]),[b,l,p]};
