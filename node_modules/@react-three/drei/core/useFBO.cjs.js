"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=u(e),i=u(r);exports.useFBO=function(e,r,u){const s=t.useThree((e=>e.size)),o=t.useThree((e=>e.viewport)),a="number"==typeof e?e:s.width*o.dpr,p="number"==typeof r?r:s.height*o.dpr,f=("number"==typeof e?u:e)||{},{samples:c=0,depth:l,...d}=f,b=n.useMemo((()=>{const e=new i.WebGLRenderTarget(a,p,{minFilter:i.LinearFilter,magFilter:i.LinearFilter,type:i.HalfFloatType,...d});return l&&(e.depthTexture=new i.DepthTexture(a,p,i.FloatType)),e.samples=c,e}),[]);return n.useLayoutEffect((()=>{b.setSize(a,p),c&&(b.samples=c)}),[c,b,a,p]),n.useEffect((()=>()=>b.dispose()),[]),b};
