"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("@react-three/fiber"),t=require("react"),s=require("three"),o=require("three-mesh-bvh");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var a=u(e),c=n(t);const i=e=>e.isMesh;const d=c.forwardRef((({enabled:e=!0,firstHitOnly:t=!1,children:u,strategy:n=o.SAH,verbose:d=!1,setBoundingBox:f=!0,maxDepth:p=40,maxLeafTris:y=10,...l},m)=>{const B=c.useRef(null),b=r.useThree((e=>e.raycaster));return c.useImperativeHandle(m,(()=>B.current),[]),c.useEffect((()=>{if(e){const e={strategy:n,verbose:d,setBoundingBox:f,maxDepth:p,maxLeafTris:y},r=B.current;return b.firstHitOnly=t,r.traverse((r=>{i(r)&&!r.geometry.boundsTree&&r.raycast===s.Mesh.prototype.raycast&&(r.raycast=o.acceleratedRaycast,r.geometry.computeBoundsTree=o.computeBoundsTree,r.geometry.disposeBoundsTree=o.disposeBoundsTree,r.geometry.computeBoundsTree(e))})),()=>{delete b.firstHitOnly,r.traverse((e=>{i(e)&&e.geometry.boundsTree&&(e.geometry.disposeBoundsTree(),e.raycast=s.Mesh.prototype.raycast)}))}}})),c.createElement("group",a.default({ref:B},l),u)}));exports.Bvh=d,exports.useBVH=function(e,r){r={strategy:o.SAH,verbose:!1,setBoundingBox:!0,maxDepth:40,maxLeafTris:10,...r},c.useEffect((()=>{if(e.current){e.current.raycast=o.acceleratedRaycast;const t=e.current.geometry;return t.computeBoundsTree=o.computeBoundsTree,t.disposeBoundsTree=o.disposeBoundsTree,t.computeBoundsTree(r),()=>{t.boundsTree&&t.disposeBoundsTree()}}}),[e,JSON.stringify(r)])};
