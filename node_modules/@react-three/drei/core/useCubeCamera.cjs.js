"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("react"),t=require("@react-three/fiber");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=u(e),a=u(r);exports.useCubeCamera=function({resolution:u=256,near:o=.1,far:c=1e3,envMap:s,fog:f}={}){const b=t.useThree((({gl:e})=>e)),i=t.useThree((({scene:e})=>e)),l=r.useMemo((()=>{const r=new e.WebGLCubeRenderTarget(u);return r.texture.type=e.HalfFloatType,r}),[u]);r.useEffect((()=>()=>{l.dispose()}),[l]);const d=r.useMemo((()=>new n.CubeCamera(o,c,l)),[o,c,l]);let g,p;const O=a.useCallback((()=>{g=i.fog,p=i.background,i.background=s||p,i.fog=f||g,d.update(b,i),i.fog=g,i.background=p}),[b,i,d]);return{fbo:l,camera:d,update:O}};
