import { useLoader } from '@react-three/fiber';
import { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';
import { RGBELoader, EXRLoader } from 'three-stdlib';
import { presetsObj } from '../helpers/environment-assets.js';

const CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';

const isArray = arr => Array.isArray(arr);

function useEnvironment({
  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],
  path = '',
  preset = undefined,
  encoding = undefined,
  extensions
} = {}) {
  var _files$split$pop, _files$split$pop$spli, _files$split$pop$spli2;

  let loader = null;
  let isCubeMap = false;
  let extension;

  if (preset) {
    if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));
    files = presetsObj[preset];
    path = CUBEMAP_ROOT;
  } // Everything else


  isCubeMap = isArray(files);
  extension = isArray(files) ? 'cube' : files.startsWith('data:application/exr') ? 'exr' : files.startsWith('data:application/hdr') ? 'hdr' : (_files$split$pop = files.split('.').pop()) == null ? void 0 : (_files$split$pop$spli = _files$split$pop.split('?')) == null ? void 0 : (_files$split$pop$spli2 = _files$split$pop$spli.shift()) == null ? void 0 : _files$split$pop$spli2.toLowerCase();
  loader = isCubeMap ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : null;
  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);
  const loaderResult = useLoader( // @ts-expect-error
  loader, isCubeMap ? [files] : files, loader => {
    loader.setPath == null ? void 0 : loader.setPath(path);
    if (extensions) extensions(loader);
  });
  const texture = isCubeMap ? // @ts-ignore
  loaderResult[0] : loaderResult;
  texture.mapping = isCubeMap ? CubeReflectionMapping : EquirectangularReflectionMapping;
  const sRGBEncoding = 3001;
  const LinearEncoding = 3000;
  if ('colorSpace' in texture) texture.colorSpace = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? 'srgb' : 'srgb-linear';else texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;
  return texture;
}

export { useEnvironment };
