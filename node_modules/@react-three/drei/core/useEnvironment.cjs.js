"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),r=require("three"),n=require("three-stdlib"),t=require("../helpers/environment-assets.cjs.js");const s=e=>Array.isArray(e);exports.useEnvironment=function({files:i=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"],path:o="",preset:a,encoding:p,extensions:l}={}){var u,c,d;let b,h=null,g=!1;if(a){if(!(a in t.presetsObj))throw new Error("Preset must be one of: "+Object.keys(t.presetsObj).join(", "));i=t.presetsObj[a],o="https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/"}if(g=s(i),b=s(i)?"cube":i.startsWith("data:application/exr")?"exr":i.startsWith("data:application/hdr")?"hdr":null==(u=i.split(".").pop())||null==(c=u.split("?"))||null==(d=c.shift())?void 0:d.toLowerCase(),h=g?r.CubeTextureLoader:"hdr"===b?n.RGBELoader:"exr"===b?n.EXRLoader:null,!h)throw new Error("useEnvironment: Unrecognized file extension: "+i);const f=e.useLoader(h,g?[i]:i,(e=>{null==e.setPath||e.setPath(o),l&&l(e)})),x=g?f[0]:f;return x.mapping=g?r.CubeReflectionMapping:r.EquirectangularReflectionMapping,"colorSpace"in x?x.colorSpace=(null!=p?p:g)?"srgb":"srgb-linear":x.encoding=(null!=p?p:g)?3001:3e3,x};
