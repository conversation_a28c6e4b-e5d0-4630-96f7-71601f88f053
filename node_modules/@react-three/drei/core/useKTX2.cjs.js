"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),r=require("react"),s=require("three-stdlib"),t=require("./useTexture.cjs.js");require("three");const a="https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master";function c(c,u=`${a}/basis/`){const o=e.useThree((e=>e.gl)),i=e.useLoader(s.KTX2Loader,t.IsObject(c)?Object.values(c):c,(e=>{e.detectSupport(o),e.setTranscoderPath(u)}));if(r.useEffect((()=>{(Array.isArray(i)?i:[i]).forEach(o.initTexture)}),[o,i]),t.IsObject(c)){const e=Object.keys(c),r={};return e.forEach((s=>Object.assign(r,{[s]:i[e.indexOf(s)]}))),r}return i}c.preload=(r,t=`${a}/basis/`)=>e.useLoader.preload(s.KTX2Loader,r,(e=>{e.setTranscoderPath(t)})),c.clear=r=>e.useLoader.clear(s.KTX2Loader,r),exports.useKTX2=c;
