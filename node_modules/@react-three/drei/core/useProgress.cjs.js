"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("zustand");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let a=0;const o=t(r).default((r=>(e.DefaultLoadingManager.onStart=(e,t,o)=>{r({active:!0,item:e,loaded:t,total:o,progress:(t-a)/(o-a)*100})},e.DefaultLoadingManager.onLoad=()=>{r({active:!1})},e.DefaultLoadingManager.onError=e=>r((r=>({errors:[...r.errors,e]}))),e.DefaultLoadingManager.onProgress=(e,t,o)=>{t===o&&(a=o),r({active:!0,item:e,loaded:t,total:o,progress:(t-a)/(o-a)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0})));exports.useProgress=o;
