"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("./useTexture.cjs.js"),r=require("suspend-react");function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("three"),require("@react-three/fiber");var u=s(e);exports.useMatcapTexture=function(e=0,s=1024,n){const c=r.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/matcaps.json").then((e=>e.json()))),["matcapList"]),a=c[0],i=u.useMemo((()=>Object.keys(c).length),[]),p=`${u.useMemo((()=>"string"==typeof e?e:"number"==typeof e?c[e]:null),[e])||a}${function(e){switch(e){case 64:return"-64px";case 128:return"-128px";case 256:return"-256px";case 512:return"-512px";default:return""}}(s)}.png`,o=`https://rawcdn.githack.com/emmelleppi/matcaps/9b36ccaaf0a24881a39062d05566c9e92be4aa0d/${s}/${p}`;return[t.useTexture(o,n),o,i]};
