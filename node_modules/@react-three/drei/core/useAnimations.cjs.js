"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=n(e);exports.useAnimations=function(e,n){const u=c.useRef(),[a]=c.useState((()=>n?n instanceof t.Object3D?{current:n}:n:u)),[o]=c.useState((()=>new t.AnimationMixer(void 0)));c.useLayoutEffect((()=>{o._root=a.current}),[o,n]);const i=c.useRef({}),[s]=c.useState((()=>{const t={};return e.forEach((e=>Object.defineProperty(t,e.name,{enumerable:!0,get(){if(a.current)return i.current[e.name]||(i.current[e.name]=o.clipAction(e,a.current))},configurable:!0}))),{ref:a,clips:e,actions:t,names:e.map((e=>e.name)),mixer:o}}));return r.useFrame(((e,t)=>o.update(t))),c.useEffect((()=>{const e=a.current;return()=>{i.current={},Object.values(s.actions).forEach((t=>{e&&o.uncacheAction(t,e)}))}}),[e]),c.useEffect((()=>()=>{o.stopAllAction()}),[o]),s};
