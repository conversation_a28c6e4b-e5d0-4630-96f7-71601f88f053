"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber");function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var u=t(e);exports.useHelper=function(e,t,...n){const c=u.useRef(),a=r.useThree((e=>e.scene));return u.useLayoutEffect((()=>{let r;if(e&&null!=e&&e.current&&t&&(c.current=r=new t(e.current,...n)),r)return r.traverse((e=>e.raycast=()=>null)),a.add(r),()=>{c.current=void 0,a.remove(r),null==r.dispose||r.dispose()}}),[a,t,e,...n]),r.useFrame((()=>{var e;null==(e=c.current)||null==e.update||e.update()})),c};
