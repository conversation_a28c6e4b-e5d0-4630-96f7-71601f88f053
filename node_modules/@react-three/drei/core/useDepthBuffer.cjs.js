"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("react"),t=require("@react-three/fiber"),u=require("./useFBO.cjs.js");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var s=n(r);exports.useDepthBuffer=function({size:r=256,frames:n=1/0}={}){const c=t.useThree((e=>e.viewport.dpr)),{width:i,height:o}=t.useThree((e=>e.size)),a=r||i*c,f=r||o*c,p=s.useMemo((()=>{const r=new e.DepthTexture(a,f);return r.format=e.DepthFormat,r.type=e.UnsignedShortType,{depthTexture:r}}),[a,f]);let d=0;const h=u.useFBO(a,f,p);return t.useFrame((e=>{(n===1/0||d<n)&&(e.gl.setRenderTarget(h),e.gl.render(e.scene,e.camera),e.gl.setRenderTarget(null),d++)})),h.depthTexture};
