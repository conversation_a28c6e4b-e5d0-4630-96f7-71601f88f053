/** Returns true if the `target` has ever animated. */
export declare const hasAnimated: (target: any) => boolean;
/** Returns true if the `target` is animating (even if paused). */
export declare const isAnimating: (target: any) => boolean;
/** Returns true if the `target` is paused (even if idle). */
export declare const isPaused: (target: any) => boolean;
/** Set the active bit of the `target` phase. */
export declare const setActiveBit: (target: any, active: boolean) => number;
export declare const setPausedBit: (target: any, paused: boolean) => number;
