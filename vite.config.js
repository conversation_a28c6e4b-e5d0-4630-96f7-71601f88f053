import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  define: {
    'process.env.PORT': '3000'
  },
  optimizeDeps: {
    include: [
      'xterm',
      'xterm-addon-fit',
      'xterm-addon-web-links',
      'xterm-addon-search',
      'xterm-addon-unicode11',
      'three',
    ]
  },
  build: {
    commonjsOptions: {
      transformMixedEsModules: true
    },
    chunkSizeWarningLimit: 1000,
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: false,
    strictPort: true,
    allowedHosts: [
      'dev.cyberforce.om',
      '*************',
      'localhost',
      '127.0.0.1',
      '************'
    ]
  }
})
