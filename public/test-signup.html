<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup - CyberForce</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #2DD4BF;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #333;
            background-color: #2a2a2a;
            color: #fff;
            border-radius: 5px;
        }
        button {
            background-color: #2DD4BF;
            color: #000;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #26b8a5;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        .success {
            border-left: 4px solid #10b981;
        }
        .error {
            border-left: 4px solid #ef4444;
        }
        .debug {
            background-color: #1e293b;
            border: 1px solid #334155;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 CyberForce Signup Test</h1>
    <p>This is a standalone test page to debug signup issues.</p>

    <form id="signupForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="TestPassword123!" required>
        </div>
        
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" value="testuser" required>
        </div>
        
        <div class="form-group">
            <label for="fullName">Full Name:</label>
            <input type="text" id="fullName" name="fullName" value="Test User" required>
        </div>
        
        <button type="submit">Test Signup</button>
    </form>

    <div id="result"></div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Supabase configuration
        const supabaseUrl = "https://yselfyopwwtslluyreoo.supabase.co";
        const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzZWxmeW9wd3d0c2xsdXlyZW9vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxNTU4ODYsImV4cCI6MjA1NTczMTg4Nn0.uw3o_iNlD3Ytm5WI8pO-AyEEfPAwLIwbG1_15oTDpTc";

        const supabase = createClient(supabaseUrl, supabaseAnonKey);

        function showResult(message, type = 'info', debugInfo = null) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="result ${type}">
                    <strong>${type.toUpperCase()}:</strong> ${message}
                    ${debugInfo ? `<div class="debug">${debugInfo}</div>` : ''}
                </div>
            `;
        }

        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');
            const username = formData.get('username');
            const fullName = formData.get('fullName');

            showResult('Testing signup...', 'info');

            try {
                console.log('🔍 Starting signup test...');
                console.log('Email:', email);
                console.log('Username:', username);

                const { data, error } = await supabase.auth.signUp({
                    email,
                    password,
                    options: {
                        data: {
                            username,
                            full_name: fullName
                        },
                        emailRedirectTo: `${window.location.origin}/auth/callback`
                    }
                });

                console.log('Signup response:', { data, error });

                if (error) {
                    showResult(`Signup failed: ${error.message}`, 'error', JSON.stringify(error, null, 2));
                    return;
                }

                if (data.user && !data.session) {
                    showResult(
                        'Signup successful! Email verification required. Check your email for verification link.',
                        'success',
                        `User ID: ${data.user.id}\nEmail: ${data.user.email}\nConfirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`
                    );
                } else if (data.session) {
                    showResult(
                        'Signup successful! User was automatically signed in (this should not happen in production).',
                        'success',
                        `User ID: ${data.user.id}\nEmail: ${data.user.email}\nSession: ${data.session.access_token.substring(0, 20)}...`
                    );
                } else {
                    showResult('Signup completed but response is unclear', 'info', JSON.stringify(data, null, 2));
                }

            } catch (error) {
                console.error('Signup error:', error);
                showResult(`Unexpected error: ${error.message}`, 'error', error.stack);
            }
        });

        // Test Supabase connection on page load
        window.addEventListener('load', async () => {
            try {
                const { data, error } = await supabase.auth.getSession();
                console.log('Initial session check:', { data, error });
                
                if (error) {
                    showResult(`Supabase connection issue: ${error.message}`, 'error');
                } else {
                    showResult('Supabase connection successful. Ready to test signup.', 'success');
                }
            } catch (error) {
                showResult(`Failed to connect to Supabase: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
