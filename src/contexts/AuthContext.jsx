import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import ProgressService from '../services/progressService';

// Create context
const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    const initAuth = async () => {
      try {
        setLoading(true);

        // Production-ready authentication - check for valid session only
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session error:', sessionError);
          setError(sessionError.message);
          setLoading(false);
          return;
        }

        if (session?.user) {
          setUser(session.user);

          // Fetch user profile from database
          try {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', session.user.id)
              .single();

            if (profileData && !profileError) {
              // We have real profile data from the database
              setProfile(profileData);
            } else {
              // Try to get from user_profiles table instead
              const { data: userProfileData, error: userProfileError } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('user_id', session.user.id)
                .single();

              if (userProfileData && !userProfileError) {
                setProfile(userProfileData);
              } else {
                // Create a basic profile based on user data
                const basicProfile = {
                  id: session.user.id,
                  user_id: session.user.id,
                  username: session.user.user_metadata?.username || session.user.email?.split('@')[0] || 'user',
                  full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
                  email: session.user.email || '<EMAIL>',
                  avatar_url: session.user.user_metadata?.avatar_url || null,
                  subscription_tier: 'free', // Default to free for production
                  total_coins: 0,
                  total_points: 0,
                  modules_completed: 0,
                  challenges_completed: 0,
                  current_streak: 0,
                  longest_streak: 0,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                };
                setProfile(basicProfile);

                // Initialize progress in database
                try {
                  await ProgressService.initializeUserProgress(session.user.id);
                } catch (initError) {
                  console.warn('Could not initialize user progress:', initError);
                }
              }
            }
          } catch (err) {
            console.error('Error fetching profile:', err);
            // Fallback to basic profile based on user data
            const basicProfile = {
              id: session.user.id,
              user_id: session.user.id,
              username: session.user.user_metadata?.username || session.user.email?.split('@')[0] || 'user',
              full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
              email: session.user.email || '<EMAIL>',
              avatar_url: session.user.user_metadata?.avatar_url || null,
              subscription_tier: 'free',
              total_coins: 0,
              total_points: 0,
              modules_completed: 0,
              challenges_completed: 0,
              current_streak: 0,
              longest_streak: 0,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setProfile(basicProfile);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    initAuth();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user);

          // Fetch user profile
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (!profileError) {
            setProfile(profileData);
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setProfile(null);
        }
      }
    );

    // Cleanup subscription
    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  // Sign up with email and password
  const signUp = async (email, password, userData = {}) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.fullName || '',
            username: userData.username || email.split('@')[0],
          }
        }
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error signing up:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign in with email and password
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error signing in:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.signOut();

      if (error) throw error;
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('You must be logged in to update your profile');

      const { data, error } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      setProfile(data);
      return data;
    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update subscription tier
  const updateSubscription = async (tier) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('You must be logged in to update your subscription');

      const { data, error } = await supabase
        .from('profiles')
        .update({ subscription_tier: tier })
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      // Record subscription change in history
      await supabase
        .from('subscription_history')
        .insert([{
          user_id: user.id,
          tier,
          changed_at: new Date()
        }]);

      setProfile(data);
      return data;
    } catch (error) {
      console.error('Error updating subscription:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Check if user has access to premium content
  const hasPremiumAccess = () => {
    return profile?.subscription_tier === 'premium' || profile?.subscription_tier === 'business';
  };

  // Check if user has access to business content
  const hasBusinessAccess = () => {
    return profile?.subscription_tier === 'business';
  };

  // Context value
  const value = {
    user,
    profile,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    updateProfile,
    updateSubscription,
    hasPremiumAccess,
    hasBusinessAccess
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
