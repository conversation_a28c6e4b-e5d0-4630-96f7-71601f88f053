import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { signIn, signUp } from '../lib/auth';
import { FaLock, FaEnvelope, <PERSON>a<PERSON>ser, FaUserCircle } from 'react-icons/fa';
import Navbar from '../components/Navbar';
import { AUTH_CONFIG } from '../config/auth';
import { supabase } from '../lib/supabase';

function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    fullName: ''
  });

  // Debug Supabase connection on component mount
  useEffect(() => {
    console.log("🔧 Login component mounted");
    console.log("Supabase URL:", import.meta.env.VITE_SUPABASE_URL);
    console.log("Supabase client exists:", !!supabase);
    console.log("Environment:", import.meta.env.MODE);

    // Test Supabase connection
    supabase.auth.getSession().then(({ data, error }) => {
      console.log("Initial session check:", { data, error });
    }).catch(err => {
      console.error("Supabase connection error:", err);
    });
  }, []);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setError(null);
  };

  const validateForm = () => {
    if (!formData.email || !formData.password) {
      setError('Please fill in all required fields');
      return false;
    }

    if (!isLogin) {
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        return false;
      }
      if (!formData.username) {
        setError('Username is required');
        return false;
      }
      if (formData.password.length < 6) {
        setError('Password must be at least 6 characters');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log("🚀 Form submitted!", { isLogin, formData });

    if (!validateForm()) {
      console.log("❌ Form validation failed");
      return;
    }

    console.log("✅ Form validation passed, proceeding...");
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (isLogin) {
        console.log("Attempting login with:", formData.email);
        const { session } = await signIn(formData.email, formData.password);

        if (session) {
          console.log("Login successful, redirecting...");

          // Force a delay to ensure session is properly set
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Redirect to the page the user was trying to access, or to the dashboard
          const redirectTo = location.state?.from || '/enhanced-dashboard';
          navigate(redirectTo, { replace: true });
        }
      } else {
        console.log("Attempting registration with:", formData.email, formData.username);

        // Direct Supabase signup with detailed logging
        console.log("🔍 Starting signup process...");

        // Get the correct redirect URL for production environment
        const redirectUrl = import.meta.env.VITE_APP_URL || window.location.origin;
        const emailRedirectTo = `${redirectUrl}/auth/callback`;

        console.log("📧 Email redirect URL:", emailRedirectTo);

        const { data, error } = await supabase.auth.signUp({
          email: formData.email,
          password: formData.password,
          options: {
            data: {
              username: formData.username,
              full_name: formData.fullName
            },
            emailRedirectTo: emailRedirectTo
          }
        });

        console.log("Signup response:", { data, error });

        if (error) {
          console.error("❌ Signup failed:", error);
          throw new Error(error.message);
        }

        if (data.user && !data.session) {
          console.log("✅ User created, email verification required");
          setSuccess(`🎉 Account created successfully!

📧 We've sent a verification email to ${formData.email}

Please check your email and click the verification link to activate your account.

⚠️ You won't be able to log in until your email is verified.`);
          // Switch to login mode after successful signup
          setIsLogin(true);
          setFormData({
            ...formData,
            password: '',
            confirmPassword: ''
          });
        } else if (data.session) {
          console.log("⚠️ User created and automatically signed in - this should not happen in production");
          setSuccess("Registration successful! However, email verification is still recommended for security.");
          setIsLogin(true);
          setFormData({
            ...formData,
            password: '',
            confirmPassword: ''
          });
        } else {
          console.log("✅ Signup completed");
          setSuccess(`🎉 Registration successful!

📧 Please check your email (${formData.email}) for a verification link to activate your account.`);
          setIsLogin(true);
        }
      }
    } catch (err) {
      console.error('Auth error:', err);
      setError(err.message || 'An error occurred during authentication');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md">
          {/* Removed CyberForceLogo from the login form as requested */}

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="w-full max-w-md bg-black rounded-lg border border-gray-800 shadow-xl overflow-hidden relative z-10"
          >
            <div className="p-6 sm:p-8">
              <div className="text-center mb-6">
                <div className="inline-block mx-auto">
                  <span className="font-bold text-3xl">
                    <span className="text-[#2DD4BF]">Cyber</span>
                    <span className="text-amber-500">XCerberus</span>
                  </span>
                </div>
              </div>

              <h2 className="text-2xl sm:text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#2DD4BF] to-amber-500 text-transparent bg-clip-text">
                {isLogin ? 'Welcome Back' : 'Join XCerberus'}
              </h2>

              <div className="flex justify-center space-x-4 mb-8">
                <button
                  onClick={() => {
                    setIsLogin(true);
                    setError(null);
                    setSuccess(null);
                  }}
                  className={`px-4 py-2 rounded-full transition-all duration-300 ${
                    isLogin
                      ? 'bg-[#2DD4BF] text-black font-bold'
                      : 'text-gray-500 hover:text-gray-300'
                  }`}
                >
                  Login
                </button>
                <button
                  onClick={() => {
                    setIsLogin(false);
                    setError(null);
                    setSuccess(null);
                  }}
                  className={`px-4 py-2 rounded-full transition-all duration-300 ${
                    !isLogin
                      ? 'bg-[#2DD4BF] text-black font-bold'
                      : 'text-gray-500 hover:text-gray-300'
                  }`}
                >
                  Register
                </button>
              </div>

              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-6 p-3 rounded-lg bg-red-900/50 border border-red-500/50 text-red-400 text-sm text-center"
                >
                  {error}
                </motion.div>
              )}

              {success && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-6 p-3 rounded-lg bg-green-900/50 border border-green-500/50 text-green-400 text-sm text-center"
                >
                  {success}
                </motion.div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="email">
                    <FaEnvelope className="mr-2 text-[#2DD4BF]" />
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>

                {!isLogin && (
                  <>
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="username">
                        <FaUser className="mr-2 text-[#2DD4BF]" />
                        Username
                      </label>
                      <input
                        type="text"
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                        placeholder="Choose a username"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="fullName">
                        <FaUserCircle className="mr-2 text-[#2DD4BF]" />
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                        placeholder="Your full name"
                      />
                    </div>
                  </>
                )}

                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="password">
                    <FaLock className="mr-2 text-[#2DD4BF]" />
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                    placeholder="••••••••"
                  />
                </div>

                {!isLogin && (
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2 flex items-center" htmlFor="confirmPassword">
                      <FaLock className="mr-2 text-[#2DD4BF]" />
                      Confirm Password
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-lg bg-gray-900 border border-gray-700 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-[#2DD4BF] focus:ring-1 focus:ring-[#2DD4BF] transition-colors"
                      placeholder="••••••••"
                    />
                  </div>
                )}

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-[#2DD4BF] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#2DD4BF]/80 transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
                >
                  <span className="relative z-10">
                    {loading ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                    ) : (
                      isLogin ? 'Sign In' : 'Create Account'
                    )}
                  </span>
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500" style={{transform: 'translateX(-100%)'}}></span>
                </button>
              </form>

              {/* Test accounts removed for production security */}
              {import.meta.env.DEV && isLogin && (
                <div className="mt-6">
                  <p className="text-center text-sm text-gray-500 mb-4">Development Mode Only</p>
                  <p className="text-center text-xs text-red-400 mb-2">Test accounts disabled in production</p>
                </div>
              )}
            </div>

            <div className="px-6 py-4 bg-gray-900 text-center text-sm border-t border-gray-800">
              <p className="text-gray-400">
                {isLogin ? "Don't have an account? " : "Already have an account? "}
                <button
                  onClick={() => {
                    setIsLogin(!isLogin);
                    setError(null);
                  }}
                  className="text-[#2DD4BF] hover:text-[#2DD4BF]/80 font-medium transition-colors"
                >
                  {isLogin ? 'Sign up' : 'Sign in'}
                </button>
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </>
  );
}

export default Login;