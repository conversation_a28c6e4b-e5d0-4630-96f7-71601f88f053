// Production Environment Validation
export const validateProductionEnvironment = () => {
  const errors = [];
  const warnings = [];

  // Check required environment variables
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];

  requiredVars.forEach(varName => {
    if (!import.meta.env[varName]) {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  });

  // Check production security settings
  if (import.meta.env.NODE_ENV === 'production') {
    // Ensure mock auth is disabled in production
    if (import.meta.env.VITE_ALLOW_MOCK_AUTH === 'true') {
      errors.push('Mock authentication must be disabled in production (VITE_ALLOW_MOCK_AUTH=false)');
    }

    // Ensure email verification is enabled
    if (import.meta.env.VITE_REQUIRE_EMAIL_VERIFICATION !== 'true') {
      warnings.push('Email verification should be enabled in production (VITE_REQUIRE_EMAIL_VERIFICATION=true)');
    }

    // Check for development-only features
    if (import.meta.env.VITE_ENABLE_DEBUG === 'true') {
      warnings.push('Debug mode should be disabled in production');
    }
  }

  // Validate Supabase URL format
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  if (supabaseUrl && !supabaseUrl.startsWith('https://')) {
    errors.push('Supabase URL must use HTTPS in production');
  }

  // Check for hardcoded credentials (basic check)
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  if (supabaseKey && supabaseKey.includes('localhost')) {
    warnings.push('Supabase key appears to be for local development');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    environment: import.meta.env.NODE_ENV || 'development'
  };
};

// Security headers validation
export const validateSecurityHeaders = () => {
  const recommendations = [];

  // Check if running in secure context
  if (typeof window !== 'undefined') {
    if (!window.isSecureContext && window.location.protocol !== 'https:') {
      recommendations.push('Application should be served over HTTPS in production');
    }

    // Check for security headers (these would be set by the server)
    // This is a client-side check for educational purposes
    recommendations.push('Ensure Content-Security-Policy header is set');
    recommendations.push('Ensure X-Frame-Options header is set');
    recommendations.push('Ensure X-Content-Type-Options header is set');
    recommendations.push('Ensure Strict-Transport-Security header is set');
  }

  return recommendations;
};

// Authentication configuration validation
export const validateAuthConfig = () => {
  const issues = [];

  try {
    // Check if auth configuration is properly loaded
    const { AUTH_CONFIG } = await import('../config/auth.js');

    if (!AUTH_CONFIG.SUPABASE.URL) {
      issues.push('Supabase URL not configured in auth config');
    }

    if (!AUTH_CONFIG.SUPABASE.ANON_KEY) {
      issues.push('Supabase anonymous key not configured in auth config');
    }

    if (AUTH_CONFIG.SECURITY.ALLOW_MOCK_AUTH && import.meta.env.NODE_ENV === 'production') {
      issues.push('Mock authentication is enabled in production environment');
    }

    if (!AUTH_CONFIG.AUTHENTICATION.REQUIRE_EMAIL_VERIFICATION) {
      issues.push('Email verification is not required - this is a security risk');
    }

  } catch (error) {
    issues.push('Failed to load authentication configuration');
  }

  return issues;
};

// Run all validations
export const runProductionChecks = async () => {
  console.log('🔍 Running production environment checks...');

  const envValidation = validateProductionEnvironment();
  const securityHeaders = validateSecurityHeaders();
  const authConfigIssues = await validateAuthConfig();

  const report = {
    environment: envValidation.environment,
    isProductionReady: envValidation.isValid && authConfigIssues.length === 0,
    errors: [...envValidation.errors, ...authConfigIssues],
    warnings: [...envValidation.warnings],
    securityRecommendations: securityHeaders,
    timestamp: new Date().toISOString()
  };

  // Log results
  if (report.errors.length > 0) {
    console.error('❌ Production readiness check failed:');
    report.errors.forEach(error => console.error(`  • ${error}`));
  }

  if (report.warnings.length > 0) {
    console.warn('⚠️ Production warnings:');
    report.warnings.forEach(warning => console.warn(`  • ${warning}`));
  }

  if (report.securityRecommendations.length > 0) {
    console.info('🔒 Security recommendations:');
    report.securityRecommendations.forEach(rec => console.info(`  • ${rec}`));
  }

  if (report.isProductionReady) {
    console.log('✅ Application is production ready!');
  } else {
    console.error('❌ Application is NOT production ready. Please fix the errors above.');
  }

  return report;
};

// Initialize validation on app start
if (import.meta.env.NODE_ENV === 'production' || import.meta.env.VITE_RUN_PRODUCTION_CHECKS === 'true') {
  // Run checks after a short delay to allow app to initialize
  setTimeout(() => {
    runProductionChecks().catch(error => {
      console.error('Failed to run production checks:', error);
    });
  }, 1000);
}

export default {
  validateProductionEnvironment,
  validateSecurityHeaders,
  validateAuthConfig,
  runProductionChecks
};
