// Debug Authentication Issues
import { supabase } from '../lib/supabase';

export const debugSignup = async (email, password, username, fullName) => {
  console.log('🔍 Debug Signup Process Started');
  console.log('Email:', email);
  console.log('Username:', username);
  console.log('Full Name:', fullName);
  console.log('Password length:', password.length);

  try {
    // Test Supabase connection
    console.log('📡 Testing Supabase connection...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('Current session:', session);
    if (sessionError) {
      console.error('Session error:', sessionError);
    }

    // Test signup
    console.log('📝 Attempting signup...');
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          username,
          full_name: fullName
        },
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });

    console.log('Signup response data:', data);
    console.log('Signup response error:', error);

    if (error) {
      console.error('❌ Signup failed:', error.message);
      return { success: false, error: error.message };
    }

    if (data.user && !data.session) {
      console.log('✅ User created, email verification required');
      return { 
        success: true, 
        requiresVerification: true,
        message: 'Please check your email to verify your account'
      };
    }

    if (data.session) {
      console.log('⚠️ User created and automatically signed in (this should not happen in production)');
      return { 
        success: true, 
        requiresVerification: false,
        message: 'Account created successfully'
      };
    }

    console.log('✅ Signup completed');
    return { success: true };

  } catch (error) {
    console.error('💥 Debug signup error:', error);
    return { success: false, error: error.message };
  }
};

export const debugSupabaseConfig = () => {
  console.log('🔧 Supabase Configuration Debug');
  console.log('VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
  console.log('VITE_SUPABASE_ANON_KEY exists:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);
  console.log('Environment:', import.meta.env.MODE);
  console.log('Development mode:', import.meta.env.DEV);
  
  // Test if supabase client is working
  try {
    console.log('Supabase client exists:', !!supabase);
    console.log('Supabase auth exists:', !!supabase.auth);
    console.log('Supabase auth methods:', Object.keys(supabase.auth));
  } catch (error) {
    console.error('Supabase client error:', error);
  }
};

// Auto-run config debug
if (typeof window !== 'undefined') {
  debugSupabaseConfig();
}

export default {
  debugSignup,
  debugSupabaseConfig
};
