import { supabase } from './supabase';

// Retry mechanism for auth operations
const retryAuth = async (operation, maxRetries = 3) => {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await operation();
      return result;
    } catch (error) {
      console.error(`Auth operation failed (attempt ${i + 1}/${maxRetries}):`, error);
      lastError = error;
      
      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }
  
  throw lastError;
};

// Sign in with retry mechanism
export const signIn = async (email, password) => {
  return retryAuth(async () => {
    try {
      // Sign in user
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (authError) throw authError;

      // Wait for session to be established
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get user subscription using RPC function
      const { data: subscriptionData, error: subError } = await supabase
        .rpc('get_user_subscription', { p_user_id: authData.session.user.id });

      if (subError) {
        console.error('Error fetching subscription:', subError);
      }

      // Set session in localStorage
      if (authData.session) {
        localStorage.setItem('supabase.auth.token', authData.session.access_token);
        localStorage.setItem('supabase.auth.refreshToken', authData.session.refresh_token);
        localStorage.setItem('supabase.auth.user', JSON.stringify(authData.session.user));
      }

      return {
        session: authData.session,
        subscription: subscriptionData
      };
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  });
};

// Sign up with retry mechanism - Production ready with email verification
export const signUp = async (email, password, username, fullName) => {
  return retryAuth(async () => {
    try {
      // Validate password strength
      try {
        const { validatePassword } = await import('../config/auth.js');
        const passwordValidation = validatePassword(password);

        if (!passwordValidation.isValid) {
          throw new Error(passwordValidation.errors.join(', '));
        }
      } catch (importError) {
        console.warn('Could not import password validation, using basic validation:', importError);
        // Basic password validation fallback
        if (password.length < 8) {
          throw new Error('Password must be at least 8 characters long');
        }
      }

      // Sign up user with email verification REQUIRED
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            full_name: fullName
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;

      // Check if email verification is required (production behavior)
      if (data.user && !data.session) {
        // User created but not confirmed - email verification required
        return {
          user: data.user,
          session: null,
          message: 'Please check your email and click the verification link to complete your registration.',
          requiresVerification: true
        };
      }

      // This should not happen in production with email verification enabled
      if (data.session) {
        console.warn('User was automatically signed in without email verification - this should not happen in production');

        // Don't automatically set session data for unverified users
        return {
          user: data.user,
          session: data.session,
          message: 'Registration successful, but email verification is recommended.'
        };
      }

      return { user: data?.user };
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  });
};

// Sign out with retry mechanism
export const signOut = async () => {
  return retryAuth(async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear all auth data from localStorage
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('supabase.auth.refreshToken');
      localStorage.removeItem('supabase.auth.user');
      localStorage.removeItem('supabase.auth.expires_at');
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  });
};

// Get current session with subscription
export const getSession = async () => {
  return retryAuth(async () => {
    try {
      // Check localStorage first
      const token = localStorage.getItem('supabase.auth.token');
      const refreshToken = localStorage.getItem('supabase.auth.refreshToken');
      
      if (!token || !refreshToken) {
        return null;
      }

      // Try to get session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        // Try to refresh session
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession({
          refresh_token: refreshToken
        });

        if (refreshError) {
          throw refreshError;
        }

        if (refreshData.session) {
          localStorage.setItem('supabase.auth.token', refreshData.session.access_token);
          localStorage.setItem('supabase.auth.refreshToken', refreshData.session.refresh_token);
          localStorage.setItem('supabase.auth.user', JSON.stringify(refreshData.session.user));
          return refreshData.session;
        }
      }

      if (session?.user) {
        // Get subscription data
        const { data: subscriptionData } = await supabase
          .rpc('get_user_subscription', { p_user_id: session.user.id });

        return {
          ...session,
          subscription: subscriptionData
        };
      }

      return session;
    } catch (error) {
      console.error('Get session error:', error);
      // Clear invalid session
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('supabase.auth.refreshToken');
      localStorage.removeItem('supabase.auth.user');
      localStorage.removeItem('supabase.auth.expires_at');
      return null;
    }
  });
};

// Get current user with subscription
export const getUser = async () => {
  return retryAuth(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;

      if (user) {
        // Get subscription data
        const { data: subscriptionData } = await supabase
          .rpc('get_user_subscription', { p_user_id: user.id });

        return {
          ...user,
          subscription: subscriptionData
        };
      }

      return user;
    } catch (error) {
      console.error('Get user error:', error);
      return null;
    }
  });
};

// Refresh session
export const refreshSession = async () => {
  return retryAuth(async () => {
    try {
      const refreshToken = localStorage.getItem('supabase.auth.refreshToken');
      if (!refreshToken) return null;

      const { data: { session }, error } = await supabase.auth.refreshSession({
        refresh_token: refreshToken
      });

      if (error) throw error;

      if (session) {
        localStorage.setItem('supabase.auth.token', session.access_token);
        localStorage.setItem('supabase.auth.refreshToken', session.refresh_token);
        localStorage.setItem('supabase.auth.user', JSON.stringify(session.user));
      }

      return session;
    } catch (error) {
      console.error('Refresh session error:', error);
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('supabase.auth.refreshToken');
      localStorage.removeItem('supabase.auth.user');
      localStorage.removeItem('supabase.auth.expires_at');
      return null;
    }
  });
};