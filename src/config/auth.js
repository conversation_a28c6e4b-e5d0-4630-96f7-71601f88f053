// Production-ready Authentication Configuration
export const AUTH_CONFIG = {
  // Supabase Configuration
  SUPABASE: {
    URL: import.meta.env.VITE_SUPABASE_URL,
    ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
    AUTH_OPTIONS: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: 'pkce'
    }
  },

  // Authentication Rules
  AUTHENTICATION: {
    // Require email verification for signup
    REQUIRE_EMAIL_VERIFICATION: true,
    
    // Session timeout (24 hours)
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000,
    
    // Password requirements
    PASSWORD_REQUIREMENTS: {
      MIN_LENGTH: 8,
      REQUIRE_UPPERCASE: true,
      REQUIRE_LOWERCASE: true,
      REQUIRE_NUMBERS: true,
      REQUIRE_SPECIAL_CHARS: false
    },
    
    // Rate limiting
    RATE_LIMITING: {
      MAX_LOGIN_ATTEMPTS: 5,
      LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
      RESET_WINDOW: 60 * 60 * 1000 // 1 hour
    }
  },

  // Security Settings
  SECURITY: {
    // Disable development bypasses in production
    ALLOW_MOCK_AUTH: import.meta.env.VITE_PRODUCTION_MODE !== 'true' && import.meta.env.VITE_ALLOW_MOCK_AUTH === 'true',

    // Clear session data on logout
    CLEAR_SESSION_ON_LOGOUT: true,

    // Validate session on route change
    VALIDATE_SESSION_ON_ROUTE_CHANGE: true,

    // Auto logout on session expiry
    AUTO_LOGOUT_ON_EXPIRY: true,

    // Production environment settings
    IS_PRODUCTION: import.meta.env.VITE_PRODUCTION_MODE === 'true',

    // Email verification settings
    REQUIRE_EMAIL_VERIFICATION: import.meta.env.VITE_REQUIRE_EMAIL_VERIFICATION === 'true'
  },

  // Routes that don't require authentication
  PUBLIC_ROUTES: [
    '/',
    '/login',
    '/signup',
    '/forgot-password',
    '/reset-password',
    '/auth/callback',
    '/about',
    '/pricing',
    '/contact',
    '/privacy-policy',
    '/terms-and-conditions'
  ],

  // Routes that require authentication
  PROTECTED_ROUTES: [
    '/dashboard',
    '/enhanced-dashboard',
    '/profile',
    '/subscription',
    '/admin',
    '/teams'
  ],

  // Default redirect after login
  DEFAULT_REDIRECT_AFTER_LOGIN: '/enhanced-dashboard',
  
  // Default redirect after logout
  DEFAULT_REDIRECT_AFTER_LOGOUT: '/login'
};

// Validate password against requirements
export const validatePassword = (password) => {
  const { PASSWORD_REQUIREMENTS } = AUTH_CONFIG.AUTHENTICATION;
  const errors = [];

  if (password.length < PASSWORD_REQUIREMENTS.MIN_LENGTH) {
    errors.push(`Password must be at least ${PASSWORD_REQUIREMENTS.MIN_LENGTH} characters long`);
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_NUMBERS && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (PASSWORD_REQUIREMENTS.REQUIRE_SPECIAL_CHARS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Check if route requires authentication
export const isProtectedRoute = (pathname) => {
  return AUTH_CONFIG.PROTECTED_ROUTES.some(route => 
    pathname.startsWith(route)
  );
};

// Check if route is public
export const isPublicRoute = (pathname) => {
  return AUTH_CONFIG.PUBLIC_ROUTES.includes(pathname) || 
         AUTH_CONFIG.PUBLIC_ROUTES.some(route => 
           pathname.startsWith(route) && route !== '/'
         );
};

// Rate limiting helper
export const checkRateLimit = (attempts, lastAttempt) => {
  const { MAX_LOGIN_ATTEMPTS, LOCKOUT_DURATION, RESET_WINDOW } = AUTH_CONFIG.AUTHENTICATION.RATE_LIMITING;
  const now = Date.now();
  
  // Reset attempts if reset window has passed
  if (lastAttempt && (now - lastAttempt) > RESET_WINDOW) {
    return { allowed: true, attemptsRemaining: MAX_LOGIN_ATTEMPTS };
  }
  
  // Check if locked out
  if (attempts >= MAX_LOGIN_ATTEMPTS) {
    const lockoutEnd = lastAttempt + LOCKOUT_DURATION;
    if (now < lockoutEnd) {
      return { 
        allowed: false, 
        lockoutEnd,
        message: `Too many failed attempts. Try again in ${Math.ceil((lockoutEnd - now) / 60000)} minutes.`
      };
    } else {
      // Lockout period has ended, reset attempts
      return { allowed: true, attemptsRemaining: MAX_LOGIN_ATTEMPTS };
    }
  }
  
  return { allowed: true, attemptsRemaining: MAX_LOGIN_ATTEMPTS - attempts };
};

export default AUTH_CONFIG;
