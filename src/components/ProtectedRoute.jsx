import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { getSession, refreshSession } from '../lib/auth';

const ProtectedRoute = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Production-ready authentication - no mock bypasses
        let session = await getSession();

        // If no session, try to refresh
        if (!session) {
          session = await refreshSession();
        }

        if (session?.user) {
          // Verify session is still valid
          const { data: { user }, error: userError } = await supabase.auth.getUser();

          if (userError || !user) {
            console.error('Session validation failed:', userError);
            setUser(null);
            setLoading(false);
            return;
          }

          // Get user subscription using RPC function
          const { data: subscriptionData, error: subError } = await supabase
            .rpc('get_user_subscription', { p_user_id: session.user.id });

          if (subError) {
            console.error('Error fetching subscription:', subError);
          }

          setUser({ ...session.user, subscription: subscriptionData });
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);

      // Check for mock user data first (for development/testing)
      const mockUserData = localStorage.getItem('supabase.auth.user');
      const mockToken = localStorage.getItem('supabase.auth.token');

      if (mockUserData && mockToken) {
        console.log('Using mock user data from localStorage');
        const mockUser = JSON.parse(mockUserData);

        // Get mock subscription data
        const mockSubscriptionData = localStorage.getItem('user_subscription');
        let subscriptionData = null;

        if (mockSubscriptionData) {
          subscriptionData = JSON.parse(mockSubscriptionData);
        }

        setUser({ ...mockUser, subscription: subscriptionData });
        setLoading(false);
        return;
      }

      if (session?.user) {
        try {
          // Get user subscription on auth change
          const { data: subscriptionData, error: subError } = await supabase
            .rpc('get_user_subscription', { p_user_id: session.user.id });

          if (subError) {
            console.error('Error fetching subscription:', subError);
          }

          setUser({ ...session.user, subscription: subscriptionData });
        } catch (error) {
          console.error('Error updating user data:', error);
          setUser(session.user);
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0B1120] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Production-ready authentication check
  // Require valid authenticated user for all protected routes
  if (!user) {
    // Clear any invalid session data
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.refreshToken');
    localStorage.removeItem('supabase.auth.user');
    localStorage.removeItem('user_subscription');

    // Save the attempted URL for redirecting after login
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  return children;
};

export default ProtectedRoute;