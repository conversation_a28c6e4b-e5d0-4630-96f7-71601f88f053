import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setUser } = useAuth();
  const [error, setError] = useState(null);
  const [status, setStatus] = useState('processing');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('🔄 Processing auth callback...');

        // Check for error in URL params
        const urlError = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (urlError) {
          console.error('Auth callback error:', urlError, errorDescription);
          setError(errorDescription || 'Authentication failed');
          setStatus('error');
          setMessage(errorDescription || 'Authentication failed');
          return;
        }

        // Handle the auth callback
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session error:', sessionError);
          setError(sessionError.message);
          setStatus('error');
          setMessage('Failed to establish session');
          return;
        }

        if (session) {
          console.log('✅ Authentication successful:', session.user);
          setUser(session.user);
          setStatus('success');
          setMessage('Authentication successful! Redirecting...');

          // Store session data
          localStorage.setItem('supabase.auth.token', session.access_token);
          localStorage.setItem('supabase.auth.refreshToken', session.refresh_token);
          localStorage.setItem('supabase.auth.user', JSON.stringify(session.user));

          setTimeout(() => {
            navigate('/dashboard', { replace: true });
          }, 2000);
        } else {
          // If no session, check if this is a password reset
          const hash = window.location.hash;
          if (hash.includes('type=recovery')) {
            navigate('/reset-password');
          } else {
            console.log('No session found, checking for email confirmation...');
            setStatus('info');
            setMessage('Please check your email and click the confirmation link to complete registration.');

            setTimeout(() => {
              navigate('/login', { replace: true });
            }, 5000);
          }
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        setError(error.message);
        setStatus('error');
        setMessage('An unexpected error occurred during authentication');
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleAuthCallback();
  }, [navigate, setUser, searchParams]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 dark:text-red-400">Error</h2>
            <p className="mt-2 text-gray-600 dark:text-gray-300">{error}</p>
            <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              Redirecting to login...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Processing...</h2>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Please wait while we complete the authentication process.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthCallback; 