import{w as L,bw as Q,r as u,v as f,j as e,br as F,m as p,Q as R,y as q,z as k}from"./index-CVvVjHWF.js";const Y=({moduleId:x,onComplete:g})=>{const{user:m}=L(),{preferences:h}=Q(),[w,a]=u.useState(!0),[d,j]=u.useState([]),[r,y]=u.useState(0),[i,N]=u.useState({}),[l,v]=u.useState(null),[_,S]=u.useState(!1);u.useEffect(()=>{A()},[x]);const A=async()=>{try{a(!0);const{data:s,error:n}=await f.from("skill_assessment_questions").select("*").eq("module_id",x).order("difficulty");if(n)throw n;let o=[...s];h.difficulty!=="adaptive"&&h.difficulty!=="standard"&&(o=s.filter(b=>h.difficulty==="easier"&&b.difficulty<3||h.difficulty==="challenging"&&b.difficulty>2)),o.length<5&&(o=s);const c=o.sort(()=>Math.random()-.5).slice(0,10);j(c),a(!1)}catch(s){console.error("Error fetching assessment questions:",s),a(!1)}},C=(s,n)=>{N({...i,[s]:n})},$=()=>{r<d.length-1?y(r+1):E()},E=async()=>{try{a(!0);let s=0;d.forEach(c=>{i[c.id]===c.correct_answer_id&&s++});const n=s/d.length,o=n>=.7;if(m){const{error:c}=await f.from("user_skill_assessments").insert({user_id:m.id,module_id:x,score:n,passed:o,completed_at:new Date});if(c)throw c;o&&await f.from("user_module_progress").upsert({user_id:m.id,module_id:x,progress_percentage:100,is_completed:!0,completed_at:new Date,updated_at:new Date},{onConflict:"user_id, module_id"})}v({score:n,passed:o,correctCount:s,total:d.length}),S(!0),a(!1)}catch(s){console.error("Error saving assessment results:",s),a(!1)}},D=async()=>{try{a(!0),m&&await f.from("user_module_progress").upsert({user_id:m.id,module_id:x,progress_percentage:100,is_completed:!0,is_skipped:!0,completed_at:new Date,updated_at:new Date},{onConflict:"user_id, module_id"}),a(!1),g&&g({skipped:!0})}catch(s){console.error("Error skipping module:",s),a(!1)}};if(w)return e.jsx(F,{text:"Loading assessment..."});if(_)return e.jsxs(p.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:`inline-block p-4 rounded-full mb-4 ${l.passed?"bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400":"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400"}`,children:l.passed?e.jsx(R,{className:"text-4xl"}):e.jsx(q,{className:"text-4xl"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-white mb-2",children:l.passed?"Congratulations! You've passed the assessment.":"You didn't pass this assessment yet."}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:l.passed?"You can skip this module and move on to the next one.":"We recommend going through this module to strengthen your knowledge."}),e.jsx("div",{className:"mt-4 flex justify-center items-center",children:e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2",children:[e.jsx("span",{className:"font-semibold",children:"Score: "}),e.jsxs("span",{className:`${l.passed?"text-green-600 dark:text-green-400":"text-yellow-600 dark:text-yellow-400"}`,children:[l.correctCount," / ",l.total," (",Math.round(l.score*100),"%)"]})]})})]}),e.jsx("div",{className:"flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4",children:l.passed?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:D,className:"bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center",children:["Skip this module ",e.jsx(k,{className:"ml-2"})]}),e.jsx("button",{onClick:()=>g&&g({skipped:!1}),className:"bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:"Take the module anyway"})]}):e.jsxs("button",{onClick:()=>g&&g({skipped:!1}),className:"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center",children:["Start learning ",e.jsx(k,{className:"ml-2"})]})})]});const t=d[r];return e.jsxs(p.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:"Skill Assessment"}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Question ",r+1," of ",d.length]})]}),e.jsx("div",{className:"h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-blue-600 rounded-full",style:{width:`${(r+1)/d.length*100}%`}})})]}),t&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-800 dark:text-white mb-4",children:t.question_text}),e.jsx("div",{className:"space-y-3",children:t.answers.map(s=>e.jsx("div",{onClick:()=>C(t.id,s.id),className:`p-4 rounded-lg border cursor-pointer transition-colors ${i[t.id]===s.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/30":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${i[t.id]===s.id?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:i[t.id]===s.id&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsx("span",{className:`${i[t.id]===s.id?"text-gray-900 dark:text-white font-medium":"text-gray-700 dark:text-gray-300"}`,children:s.text})]})},s.id))})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>r>0&&y(r-1),className:`px-4 py-2 rounded-lg ${r>0?"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700":"text-gray-400 dark:text-gray-600 cursor-not-allowed"}`,disabled:r===0,children:"Previous"}),e.jsx("button",{onClick:$,disabled:!i[t==null?void 0:t.id],className:`px-6 py-3 rounded-lg font-semibold ${i[t==null?void 0:t.id]?"bg-blue-600 hover:bg-blue-700 text-white":"bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"}`,children:r<d.length-1?"Next":"Submit"})]})]})};export{Y as default};
