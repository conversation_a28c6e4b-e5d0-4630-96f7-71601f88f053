import{w as H,bw as W,r as n,v as f,j as e,br as R,G as m,b6 as I,I as O,bB as Y,m as j,Q as z,x as K,bA as Q}from"./index-CVvVjHWF.js";const V=()=>{const{user:c}=H(),{preferences:g,generateStudySchedule:_}=W(),[M,y]=n.useState(!0),[i,C]=n.useState(null),[p,D]=n.useState([]),[x,h]=n.useState("overview"),[v,$]=n.useState(0),[b,L]=n.useState(0),[N,P]=n.useState([]);n.useEffect(()=>{if(c){G();const t=_();D(t)}},[c]);const G=async()=>{try{y(!0);const{data:t,error:r}=await f.from("user_study_sessions").select("*").eq("user_id",c.id).order("start_time",{ascending:!1}).limit(30);if(r)throw r;const{data:s,error:a}=await f.from("user_study_metrics").select("current_streak, weekly_minutes").eq("user_id",c.id).single();if(a&&a.code!=="PGRST116")throw a;const d=t||[],u=(s==null?void 0:s.current_streak)||0,S=(s==null?void 0:s.weekly_minutes)||0,T=E(),A=S/T;P(d),L(u),$(Math.min(A,1));const U=F(d);C({dailyData:U,totalMinutes:d.reduce((q,B)=>q+B.duration_minutes,0),weeklyMinutes:S,weeklyGoalMinutes:T}),y(!1)}catch(t){console.error("Error fetching study data:",t),y(!1)}},E=()=>{const{studyTimePreference:t}=g;return t.weekday*5+t.weekend*2},F=t=>{const r=[],s=new Date;for(let a=6;a>=0;a--){const d=new Date(s);d.setDate(d.getDate()-a),d.setHours(0,0,0,0),r.push({date:d,day:d.toLocaleDateString("en-US",{weekday:"short"}),totalMinutes:0,sessions:[]})}return t.forEach(a=>{const d=new Date(a.start_time);d.setHours(0,0,0,0);for(const u of r)if(u.date.getTime()===d.getTime()){u.totalMinutes+=a.duration_minutes,u.sessions.push(a);break}}),r},k=async()=>{if(c)try{const t=new Date,{data:r,error:s}=await f.from("user_study_sessions").insert({user_id:c.id,start_time:t.toISOString(),is_active:!0}).select().single();if(s)throw s;window.location.href=`/learning/study-session/${r.id}`}catch(t){console.error("Error starting study session:",t),alert("Failed to start study session. Please try again.")}},l=t=>{const r=Math.floor(t/60),s=t%60;return r===0?`${s} min`:s===0?`${r} hr`:`${r} hr ${s} min`};if(M)return e.jsx(R,{text:"Loading study data..."});const w=new Date().toLocaleDateString("en-US",{weekday:"long"}),o=p.find(t=>t.day===w);return e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden",children:[e.jsxs("div",{className:"border-b border-gray-200 dark:border-gray-700 px-6 py-4",children:[e.jsxs("h2",{className:"text-xl font-bold text-gray-800 dark:text-white flex items-center",children:[e.jsx(m,{className:"mr-2 text-blue-500"}),"Study Time Tracking"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"Track your study sessions and optimize your learning schedule"})]}),e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsxs("nav",{className:"flex overflow-x-auto",children:[e.jsxs("button",{onClick:()=>h("overview"),className:`py-3 px-6 text-sm font-medium flex items-center whitespace-nowrap ${x==="overview"?"border-b-2 border-blue-500 text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"}`,children:[e.jsx(I,{className:"mr-2"}),"Overview"]}),e.jsxs("button",{onClick:()=>h("schedule"),className:`py-3 px-6 text-sm font-medium flex items-center whitespace-nowrap ${x==="schedule"?"border-b-2 border-blue-500 text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"}`,children:[e.jsx(O,{className:"mr-2"}),"Study Schedule"]}),e.jsxs("button",{onClick:()=>h("history"),className:`py-3 px-6 text-sm font-medium flex items-center whitespace-nowrap ${x==="history"?"border-b-2 border-blue-500 text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"}`,children:[e.jsx(Y,{className:"mr-2"}),"Session History"]})]})}),e.jsxs("div",{className:"p-6",children:[x==="overview"&&e.jsxs(j.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-800 rounded-full p-3 mr-4",children:e.jsx(m,{className:"text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Weekly Study Time"}),e.jsx("p",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:l((i==null?void 0:i.weeklyMinutes)||0)}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Goal: ",l((i==null?void 0:i.weeklyGoalMinutes)||0)]})]})]}),e.jsx("div",{className:"mt-3",children:e.jsxs("div",{className:"relative pt-1",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("div",{children:e.jsxs("span",{className:"text-xs font-semibold inline-block text-blue-600 dark:text-blue-400",children:[Math.round(v*100),"%"]})})}),e.jsx("div",{className:"overflow-hidden h-2 mt-2 text-xs flex rounded bg-blue-200 dark:bg-blue-800",children:e.jsx("div",{style:{width:`${Math.round(v*100)}%`},className:"shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"})})]})})]}),e.jsx("div",{className:"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-100 dark:border-purple-800",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-purple-100 dark:bg-purple-800 rounded-full p-3 mr-4",children:e.jsx(z,{className:"text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Study Streak"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:[b," ",b===1?"day":"days"]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:b>0?"Keep up the good work!":"Start your streak today!"})]})]})}),e.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-100 dark:border-green-800",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"bg-green-100 dark:bg-green-800 rounded-full p-3 mr-4",children:e.jsx(K,{className:"text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Today's Plan"}),e.jsx("p",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:l((o==null?void 0:o.recommendedMinutes)||0)}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:(o==null?void 0:o.sessions.length)>1?`${o.sessions.length} sessions recommended`:"1 session recommended"})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("button",{onClick:k,className:"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center",children:[e.jsx(m,{className:"mr-2"}),"Start Study Session"]})})]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-4",children:"Last 7 Days Activity"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg",children:e.jsx("div",{className:"grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700",children:i==null?void 0:i.dailyData.map((t,r)=>{const s=t.totalMinutes/60;let a="bg-green-100 dark:bg-green-900/10";return s>0&&(s<.5?a="bg-green-200 dark:bg-green-900/20":s<1?a="bg-green-300 dark:bg-green-900/30":s<2?a="bg-green-400 dark:bg-green-900/40":a="bg-green-500 dark:bg-green-900/50"),e.jsxs("div",{className:`p-4 h-24 ${a}`,children:[e.jsx("div",{className:"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:t.day}),e.jsx("div",{className:"font-medium text-gray-800 dark:text-white",children:t.totalMinutes>0?l(t.totalMinutes):"No activity"})]},r)})})})]}),e.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-100 dark:border-yellow-800",children:[e.jsxs("h4",{className:"flex items-center text-yellow-800 dark:text-yellow-300 font-medium",children:[e.jsx(Q,{className:"mr-2"}),"Study Tips"]}),e.jsx("p",{className:"text-sm text-yellow-700 dark:text-yellow-400 mt-2",children:"Based on your study patterns, you might benefit from shorter, more frequent study sessions. Try the Pomodoro Technique: 25 minutes of focused study followed by a 5-minute break."})]})]}),x==="schedule"&&e.jsxs(j.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-4",children:"Weekly Study Schedule"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:e.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:p.map(t=>{const r=t.day===w;return e.jsxs("div",{className:`p-4 ${r?"bg-blue-50 dark:bg-blue-900/20":""}`,children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"font-medium text-gray-800 dark:text-white flex items-center",children:[r&&e.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),t.day,r&&e.jsx("span",{className:"ml-2 text-xs font-medium text-blue-600 dark:text-blue-400",children:"Today"})]}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:l(t.recommendedMinutes)})]}),e.jsx("div",{className:"mt-2",children:t.sessions.length>1?e.jsx("div",{className:"flex flex-wrap gap-2",children:t.sessions.map((s,a)=>e.jsxs("div",{className:"text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300",children:["Session ",a+1,": ",l(s.duration)]},a))}):e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:["Single session of ",l(t.sessions[0].duration)]})}),r&&e.jsx("div",{className:"mt-3",children:e.jsxs("button",{onClick:k,className:"text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center",children:[e.jsx(m,{className:"mr-1"}),"Start today's session"]})})]},t.day)})})}),e.jsxs("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("h4",{className:"font-medium text-gray-800 dark:text-white mb-2",children:"About Your Schedule"}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:["This schedule is based on your weekly time commitment of "," ",l((i==null?void 0:i.weeklyGoalMinutes)||0),", with "," ",l(g.studyTimePreference.weekday)," on weekdays and "," ",l(g.studyTimePreference.weekend)," on weekends."]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-2",children:"You can adjust these preferences in your Learning Preferences settings."})]})]}),x==="history"&&e.jsxs(j.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-4",children:"Recent Study Sessions"}),N.length>0?e.jsx("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:e.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:N.map(t=>{const r=new Date(t.start_time),s=t.end_time?new Date(t.end_time):null;return e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-800 dark:text-white",children:r.toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"})}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:[r.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit"}),s&&` - ${s.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit"})}`]}),t.focus_areas&&e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:t.focus_areas.map((a,d)=>e.jsx("div",{className:"text-xs px-2 py-1 rounded bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300",children:a},d))})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-medium text-gray-800 dark:text-white",children:l(t.duration_minutes)}),t.is_active&&e.jsx("span",{className:"text-xs font-medium text-green-600 dark:text-green-400 mt-1 inline-block",children:"Active"})]})]}),t.notes&&e.jsx("div",{className:"mt-3 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded",children:t.notes})]},t.id)})})}):e.jsxs("div",{className:"text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx(m,{className:"mx-auto text-gray-400 text-3xl mb-2"}),e.jsx("h4",{className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:"No study sessions yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:"Start your first study session to track your progress"}),e.jsxs("button",{onClick:k,className:"mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center mx-auto",children:[e.jsx(m,{className:"mr-2"}),"Start Study Session"]})]})]})]})]})};export{V as default};
