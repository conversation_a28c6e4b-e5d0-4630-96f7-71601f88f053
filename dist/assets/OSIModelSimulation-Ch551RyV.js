import{r as i,j as e,P as O,O as R,ai as W,J,a4 as G,t as K}from"./index-CVvVjHWF.js";const V=({onComplete:h})=>{var C,T;const o=[{id:7,name:"Application",color:"#0D9488",unit:"Data",protocol:"HTTP, FTP, SMTP"},{id:6,name:"Presentation",color:"#0891B2",unit:"Data",protocol:"SSL, TLS, JPEG"},{id:5,name:"Session",color:"#0284C7",unit:"Data",protocol:"NetBIOS, RPC"},{id:4,name:"Transport",color:"#2563EB",unit:"Segment/Datagram",protocol:"TCP, UDP"},{id:3,name:"Network",color:"#4F46E5",unit:"Packet",protocol:"IP, ICMP, OSPF"},{id:2,name:"Data Link",color:"#7C3AED",unit:"Frame",protocol:"Ethernet, PPP, HDLC"},{id:1,name:"Physical",color:"#9333EA",unit:"Bits",protocol:"Ethernet, USB, Bluetooth"}],[n,D]=i.useState("down"),[t,u]=i.useState(n==="down"?7:1),[p,d]=i.useState(!1),[g,b]=i.useState(!1),[x,A]=i.useState(!1),[F,m]=i.useState([]),[E,y]=i.useState(0),[I,j]=i.useState(!1),[c,w]=i.useState(null),[M,v]=i.useState(null),[l,N]=i.useState(null),[$,P]=i.useState([]),f=[{id:1,question:"Which layer is responsible for routing and logical addressing?",options:["Transport Layer","Network Layer","Data Link Layer","Session Layer"],correctAnswer:1,explanation:"The Network Layer (Layer 3) is responsible for routing and logical addressing using protocols like IP."},{id:2,question:"What is the Protocol Data Unit (PDU) at the Transport Layer?",options:["Frame","Packet","Segment","Bits"],correctAnswer:2,explanation:"The Transport Layer (Layer 4) uses segments for TCP or datagrams for UDP as its PDU."},{id:3,question:"Which layer adds the source and destination MAC addresses?",options:["Physical Layer","Data Link Layer","Network Layer","Transport Layer"],correctAnswer:1,explanation:"The Data Link Layer (Layer 2) adds MAC addresses to frames for local network delivery."},{id:4,question:"What happens during encapsulation?",options:["Data is broken down into smaller units","Headers are removed as data moves up the layers","Headers are added as data moves down the layers","Data is encrypted at each layer"],correctAnswer:2,explanation:"During encapsulation, each layer adds its own header information as data moves down the OSI model."},{id:5,question:"Which layer is responsible for end-to-end connections and reliability?",options:["Session Layer","Transport Layer","Network Layer","Application Layer"],correctAnswer:1,explanation:"The Transport Layer (Layer 4) handles end-to-end connections and reliability, particularly with TCP."}];i.useEffect(()=>{m(n==="down"?[{layer:7,color:o[0].color}]:[{layer:1,color:o[6].color}])},[n]);const B=()=>{if(!p)if(d(!0),n==="down")if(t>1){const s=t-1,a=o.findIndex(r=>r.id===s);setTimeout(()=>{m(r=>[...r,{layer:s,color:o[a].color}]),u(s),y(r=>r+1),d(!1)},1e3)}else setTimeout(()=>{b(!0),d(!1),h&&h()},1e3);else if(t<7){const s=t+1,a=o.findIndex(r=>r.id===s);setTimeout(()=>{m(r=>[...r,{layer:s,color:o[a].color}]),u(s),y(r=>r+1),d(!1)},1e3)}else setTimeout(()=>{b(!0),d(!1),h&&h()},1e3)},H=()=>{u(n==="down"?7:1),d(!1),b(!1),m(n==="down"?[{layer:7,color:o[0].color}]:[{layer:1,color:o[6].color}]),y(0)},S=s=>{D(s),u(s==="down"?7:1),d(!1),b(!1),m(s==="down"?[{layer:7,color:o[0].color}]:[{layer:1,color:o[6].color}]),y(0)},U=()=>{const s=f.filter(a=>!$.includes(a.id));s.length===0?(P([]),w(f[Math.floor(Math.random()*f.length)])):w(s[Math.floor(Math.random()*s.length)]),j(!0),v(null),N(null)},Q=s=>{v(s);const a=s===c.correctAnswer;N({correct:a,explanation:c.explanation}),a&&P(r=>[...r,c.id])},k=()=>o.find(s=>s.id===t),L=Math.round(n==="down"?(7-t)/6*100:(t-1)/6*100);return e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"OSI Model Simulation"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>S("down"),className:`px-3 py-1 rounded text-sm ${n==="down"?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:"Encapsulation"}),e.jsx("button",{onClick:()=>S("up"),className:`px-3 py-1 rounded text-sm ${n==="up"?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:"Decapsulation"}),e.jsx("button",{onClick:()=>A(!x),className:`px-3 py-1 rounded text-sm ${x?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:x?"Hide Details":"Show Details"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-400 mb-1",children:[e.jsxs("span",{children:["Step ",E," of 6"]}),e.jsxs("span",{children:[L,"% Complete"]})]}),e.jsx("div",{className:"h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-primary transition-all duration-300",style:{width:`${L}%`}})})]}),e.jsxs("div",{className:"relative bg-[#1E293B] rounded-lg p-4 mb-4 overflow-hidden border border-gray-700",children:[e.jsx("div",{className:"flex flex-col space-y-1",children:o.map(s=>e.jsxs("div",{className:`flex items-center p-2 rounded transition-all ${t===s.id?"bg-[#0F172A] border border-primary/50":""}`,style:{borderLeft:`4px solid ${s.color}`},children:[e.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3",style:{backgroundColor:s.color},children:s.id}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"font-bold",children:[s.name," Layer"]}),x&&e.jsxs("div",{className:"text-xs text-gray-400 mt-1",children:[e.jsxs("div",{children:["PDU: ",s.unit]}),e.jsxs("div",{children:["Protocols: ",s.protocol]})]})]}),e.jsx("div",{className:"w-24 text-right",children:t===s.id&&e.jsx("span",{className:"text-primary text-sm",children:"Current"})})]},s.id))}),e.jsxs("div",{className:"absolute right-8 top-0 bottom-0 w-20 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"h-full w-0.5 bg-gray-700"}),n==="down"&&e.jsx(O,{className:"absolute top-4 text-gray-400"}),n==="up"&&e.jsx(R,{className:"absolute bottom-4 text-gray-400"}),F.map((s,a)=>{const r=(s.layer-1)/6,q=n==="down"?`${r*100}%`:`${(1-r)*100}%`;return e.jsx("div",{className:"absolute flex items-center justify-center",style:{top:q,transform:"translateY(-50%)",transition:"top 1s ease"},children:e.jsx("div",{className:"w-12 h-12 rounded flex items-center justify-center text-white text-xs",style:{backgroundColor:s.color},children:o.find(z=>z.id===s.layer).unit})},a)})]})]}),e.jsxs("div",{className:"bg-[#0F172A] p-4 rounded-lg mb-4 border border-gray-700",children:[e.jsxs("h4",{className:"font-bold mb-2",children:[n==="down"?"Encapsulation":"Decapsulation"," Process"]}),e.jsx("p",{className:"text-sm text-gray-300 mb-3",children:n==="down"?`At the ${(C=k())==null?void 0:C.name} Layer, data is ${t>1?"encapsulated with header information before being passed down.":"converted to bits for transmission."}`:`At the ${(T=k())==null?void 0:T.name} Layer, ${t<7?"headers are processed and removed before passing data up.":"the application receives the original data."}`}),x&&e.jsxs("div",{className:"bg-[#1E293B] p-3 rounded text-sm",children:[e.jsxs("h5",{className:"font-bold mb-1",children:["Layer ",t," Functions:"]}),e.jsxs("ul",{className:"list-disc list-inside text-gray-300 space-y-1",children:[t===7&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Provides network services to applications"}),e.jsx("li",{children:"Implements protocols like HTTP, FTP, SMTP"}),e.jsx("li",{children:"Handles user authentication and data syntax"})]}),t===6&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Translates data between application and network formats"}),e.jsx("li",{children:"Handles encryption, compression, and format conversion"}),e.jsx("li",{children:"Ensures data is readable by the receiving system"})]}),t===5&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Establishes, manages, and terminates sessions"}),e.jsx("li",{children:"Handles session checkpointing and recovery"}),e.jsx("li",{children:"Controls dialog between devices"})]}),t===4&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Provides end-to-end data transport"}),e.jsx("li",{children:"Handles segmentation, flow control, and error correction"}),e.jsx("li",{children:"Implements TCP (reliable) and UDP (unreliable) protocols"})]}),t===3&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Routes data packets between networks"}),e.jsx("li",{children:"Handles logical addressing (IP addresses)"}),e.jsx("li",{children:"Performs fragmentation and reassembly of packets"})]}),t===2&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Provides node-to-node data transfer"}),e.jsx("li",{children:"Handles physical addressing (MAC addresses)"}),e.jsx("li",{children:"Detects and potentially corrects errors from Physical layer"})]}),t===1&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Transmits raw bit stream over physical medium"}),e.jsx("li",{children:"Defines electrical, mechanical, and timing specifications"}),e.jsx("li",{children:"Handles physical connections and topology"})]})]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("button",{onClick:H,className:"px-4 py-2 rounded bg-[#1E293B] text-white hover:bg-[#334155]",children:[e.jsx(W,{className:"inline mr-1"})," Reset"]}),e.jsxs("div",{children:[e.jsxs("button",{onClick:U,className:"px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 mr-2",children:[e.jsx(J,{className:"inline mr-1"})," Test Knowledge"]}),e.jsx("button",{onClick:B,disabled:p||g,className:`px-4 py-2 rounded ${p||g?"bg-gray-700 text-gray-400 cursor-not-allowed":"bg-primary text-black hover:bg-primary-hover"}`,children:g?"Completed":p?"Processing...":"Next Step"})]})]}),I&&c&&e.jsx("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6 max-w-2xl w-full border border-gray-700",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"OSI Model Quiz"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-bold mb-3",children:c.question}),e.jsx("div",{className:"space-y-2",children:c.options.map((s,a)=>e.jsx("div",{onClick:()=>l===null&&Q(a),className:`p-3 rounded-lg cursor-pointer border ${M===a?l!=null&&l.correct?"bg-green-900/20 border-green-500":"bg-red-900/20 border-red-500":l!==null&&a===c.correctAnswer?"bg-green-900/20 border-green-500":"bg-[#1E293B] border-gray-700 hover:border-primary"}`,children:s},a))}),l&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg ${l.correct?"bg-green-900/20 border border-green-500":"bg-red-900/20 border border-red-500"}`,children:[e.jsxs("div",{className:"flex items-center mb-2",children:[l.correct?e.jsx(G,{className:"text-green-500 mr-2"}):e.jsx(K,{className:"text-red-500 mr-2"}),e.jsx("span",{className:"font-bold",children:l.correct?"Correct!":"Incorrect"})]}),e.jsx("p",{children:l.explanation})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>j(!1),className:"px-4 py-2 rounded bg-primary text-black",children:"Close"})})]})})]})};export{V as default};
