import{r as a,j as e,aj as W,ak as L,ai as M,a2 as O,ac as R,J as V,a4 as H,t as J}from"./index-CVvVjHWF.js";const X=({simulationType:l="tcp",onComplete:f})=>{const[d,m]=a.useState(!1),[i,x]=a.useState(0),[T,g]=a.useState(!1),[u,A]=a.useState(!1),[p,D]=a.useState(!1),[F,n]=a.useState([]),[U,b]=a.useState([]),[I,y]=a.useState(!1),[h,$]=a.useState(null),[K,w]=a.useState(null),[o,k]=a.useState(null),E=[{title:"Step 1: SYN",description:"Client sends a SYN packet to initiate connection",details:"The client sends a TCP packet with the SYN flag set to 1 and an initial sequence number (ISN). This begins the connection establishment process."},{title:"Step 2: SYN-ACK",description:"Server responds with SYN-ACK packet",details:"The server acknowledges the client's SYN by sending a packet with both SYN and ACK flags set. It includes its own ISN and acknowledges the client's sequence number by adding 1."},{title:"Step 3: ACK",description:"Client acknowledges the server's SYN-ACK",details:"The client sends an ACK packet to acknowledge the server's SYN-ACK. At this point, the connection is established and both sides can begin sending data."},{title:"Step 4: Data Transfer",description:"Client sends data packets to server",details:"The client begins sending data packets to the server. Each packet contains a sequence number so the server can reassemble them in the correct order."},{title:"Step 5: ACK Data",description:"Server acknowledges received data",details:"The server sends ACK packets to confirm receipt of data. If a packet is lost, the client will retransmit it after a timeout period."},{title:"Step 6: FIN",description:"Client initiates connection termination",details:"When data transfer is complete, the client sends a FIN packet to begin the connection termination process."},{title:"Step 7: FIN-ACK",description:"Server acknowledges and begins its own termination",details:"The server acknowledges the client's FIN and sends its own FIN packet to indicate it's ready to close the connection."},{title:"Step 8: ACK",description:"Client acknowledges server's FIN",details:"The client sends a final ACK to acknowledge the server's FIN. After a timeout period, the connection is fully closed."}],q=[{title:"Step 1: No Connection Setup",description:"UDP doesn't establish a connection before sending data",details:"Unlike TCP, UDP is connectionless. It doesn't perform a handshake before sending data, which reduces latency but also eliminates connection guarantees."},{title:"Step 2: Data Transmission",description:"Client sends data packets directly",details:"The client immediately begins sending data packets (datagrams) to the server without waiting for acknowledgment or establishing a connection."},{title:"Step 3: Continued Transmission",description:"Client continues sending packets without waiting for acknowledgments",details:"UDP doesn't wait for acknowledgments before sending more data. This increases speed but means there's no guarantee of delivery or packet order."},{title:"Step 4: Packet Loss",description:"Some packets may be lost during transmission",details:"If a UDP packet is lost during transmission, it remains lost. UDP has no built-in recovery mechanism for lost packets."},{title:"Step 5: No Connection Termination",description:"UDP doesn't formally terminate connections",details:"Since UDP doesn't establish connections, there's no formal termination process. The client simply stops sending data when finished."}],j=[{question:"What is the main difference between TCP and UDP?",options:["TCP is faster than UDP","TCP is connection-oriented while UDP is connectionless","TCP uses IP addresses while UDP uses MAC addresses","TCP is used for the internet while UDP is only for local networks"],correctAnswer:1,explanation:"The main difference is that TCP is connection-oriented, establishing a connection before sending data and ensuring reliable delivery, while UDP is connectionless and doesn't guarantee delivery."},{question:"Which protocol would be better for streaming live video?",options:["TCP because it guarantees all data will arrive","UDP because it's faster with less overhead","Both are equally suitable","Neither, a different protocol should be used"],correctAnswer:1,explanation:"UDP is generally better for streaming live video because it prioritizes speed over reliability. In live video, it's usually better to skip frames (accept some packet loss) than to wait for retransmissions, which would cause buffering and delays."},{question:"What happens during the TCP three-way handshake?",options:["Data is encrypted three different ways","Three packets of data are sent simultaneously","SYN, SYN-ACK, and ACK messages are exchanged","Three different protocols are used together"],correctAnswer:2,explanation:"The TCP three-way handshake involves exchanging SYN (synchronize), SYN-ACK (synchronize-acknowledge), and ACK (acknowledge) messages to establish a connection."},{question:"What happens if a UDP packet is lost during transmission?",options:["The sender automatically retransmits it","The receiver requests retransmission","Nothing, the data is permanently lost","The router stores it and tries again later"],correctAnswer:2,explanation:"If a UDP packet is lost during transmission, nothing happens at the protocol level. UDP has no built-in mechanism for detecting or recovering lost packets. The data is permanently lost unless the application layer has implemented its own recovery mechanism."},{question:"Which of these applications would most likely use TCP instead of UDP?",options:["Video conferencing","Online gaming","Voice over IP (VoIP)","File downloading"],correctAnswer:3,explanation:"File downloading would most likely use TCP because it requires all data to be delivered accurately and completely. A single missing or corrupted packet could render the entire file unusable, so TCP's reliability mechanisms are essential."}],r=l==="tcp"?E:q;a.useEffect(()=>{C()},[l]),a.useEffect(()=>{let t;return d&&i<r.length-1?t=setTimeout(()=>{N()},2e3):i>=r.length-1&&(g(!0),m(!1),f&&f()),()=>clearTimeout(t)},[d,i,r.length]);const N=()=>{i<r.length-1?(x(i+1),l==="tcp"?v(i+1):Y(i+1)):(g(!0),m(!1))},v=t=>{switch(t){case 0:n([{id:"syn",type:"syn",direction:"right",position:0}]);break;case 1:n(s=>[...s,{id:"syn-ack",type:"syn-ack",direction:"left",position:100}]);break;case 2:n(s=>[...s,{id:"ack",type:"ack",direction:"right",position:0}]);break;case 3:n(s=>[...s,{id:"data-1",type:"data",direction:"right",position:0},{id:"data-2",type:"data",direction:"right",position:0,delay:300},{id:"data-3",type:"data",direction:"right",position:0,delay:600}]),p&&setTimeout(()=>{n(s=>s.map(c=>c.id==="data-2"?{...c,lost:!0}:c))},1e3);break;case 4:b([{id:"ack-data-1",type:"ack",direction:"left",position:100},{id:"ack-data-3",type:"ack",direction:"left",position:100,delay:300}]),p&&setTimeout(()=>{n(s=>[...s,{id:"data-2-retry",type:"data",direction:"right",position:0}]),setTimeout(()=>{b(s=>[...s,{id:"ack-data-2",type:"ack",direction:"left",position:100}])},1e3)},1500);break;case 5:n(s=>[...s,{id:"fin",type:"fin",direction:"right",position:0}]);break;case 6:n(s=>[...s,{id:"fin-ack",type:"fin-ack",direction:"left",position:100}]);break;case 7:n(s=>[...s,{id:"final-ack",type:"ack",direction:"right",position:0}]);break}},Y=t=>{switch(t){case 1:n([{id:"udp-1",type:"data",direction:"right",position:0},{id:"udp-2",type:"data",direction:"right",position:0,delay:200}]);break;case 2:n(s=>[...s,{id:"udp-3",type:"data",direction:"right",position:0},{id:"udp-4",type:"data",direction:"right",position:0,delay:200},{id:"udp-5",type:"data",direction:"right",position:0,delay:400}]);break;case 3:n(s=>[...s,{id:"udp-6",type:"data",direction:"right",position:0},{id:"udp-7",type:"data",direction:"right",position:0,delay:200,lost:!0},{id:"udp-8",type:"data",direction:"right",position:0,delay:400}]);break;case 4:n(s=>[...s,{id:"udp-9",type:"data",direction:"right",position:0},{id:"udp-10",type:"data",direction:"right",position:0,delay:200}]);break}},C=()=>{x(0),m(!1),g(!1),n([]),b([]),l==="tcp"&&v(0)},z=()=>{m(!d)},B=()=>{$(j[Math.floor(Math.random()*j.length)]),y(!0),w(null),k(null)},Q=t=>{w(t);const s=t===h.correctAnswer;k({correct:s,explanation:h.explanation})},P=Math.round(i/(r.length-1)*100),S=t=>{switch(t){case"syn":return"bg-blue-500";case"syn-ack":return"bg-green-500";case"ack":return"bg-purple-500";case"data":return"bg-yellow-500";case"fin":return"bg-red-500";case"fin-ack":return"bg-orange-500";default:return"bg-gray-500"}};return e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold",children:[l.toUpperCase()," Protocol Simulation"]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:z,className:`px-3 py-1 rounded text-sm ${d?"bg-gray-700 text-white":"bg-primary text-black"}`,disabled:T,children:d?e.jsxs(e.Fragment,{children:[e.jsx(W,{className:"inline mr-1"})," Pause"]}):e.jsxs(e.Fragment,{children:[e.jsx(L,{className:"inline mr-1"})," Play"]})}),e.jsxs("button",{onClick:C,className:"px-3 py-1 rounded text-sm bg-[#1E293B] text-white",children:[e.jsx(M,{className:"inline mr-1"})," Reset"]}),e.jsx("button",{onClick:()=>A(!u),className:`px-3 py-1 rounded text-sm ${u?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:u?"Hide Details":"Show Details"}),l==="tcp"&&e.jsx("button",{onClick:()=>D(!p),className:`px-3 py-1 rounded text-sm ${p?"bg-red-600 text-white":"bg-[#1E293B] text-white"}`,children:p?"Packet Loss: On":"Packet Loss: Off"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-400 mb-1",children:[e.jsxs("span",{children:["Step ",i+1," of ",r.length]}),e.jsxs("span",{children:[P,"% Complete"]})]}),e.jsx("div",{className:"h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-primary transition-all duration-300",style:{width:`${P}%`}})})]}),e.jsxs("div",{className:"bg-[#1E293B] p-4 rounded-lg mb-4 border border-gray-700",children:[e.jsx("h4",{className:"font-bold mb-1",children:r[i].title}),e.jsx("p",{className:"text-gray-300 mb-2",children:r[i].description}),u&&e.jsx("div",{className:"bg-[#0F172A] p-3 rounded border border-gray-700 text-sm",children:e.jsx("p",{className:"text-gray-300",children:r[i].details})})]}),e.jsxs("div",{className:"relative h-60 bg-[#0F172A] rounded-lg mb-4 overflow-hidden border border-gray-700",children:[e.jsxs("div",{className:"absolute left-8 top-1/2 transform -translate-y-1/2 w-20 h-20 bg-[#1E293B] rounded-lg flex flex-col items-center justify-center",children:[e.jsx(O,{className:"text-2xl text-blue-400 mb-2"}),e.jsx("div",{className:"text-sm font-bold",children:"Client"})]}),e.jsxs("div",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 w-20 h-20 bg-[#1E293B] rounded-lg flex flex-col items-center justify-center",children:[e.jsx(R,{className:"text-2xl text-green-400 mb-2"}),e.jsx("div",{className:"text-sm font-bold",children:"Server"})]}),e.jsx("div",{className:"absolute left-28 right-28 top-1/2 h-0.5 bg-gray-700"}),F.map((t,s)=>{const c=t.direction==="right"?28:72;return t.direction,e.jsx("div",{className:`absolute top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full flex items-center justify-center text-xs text-white ${S(t.type)} ${t.lost?"opacity-0":""}`,style:{left:`${c}%`,transition:"left 1.5s ease, opacity 0.3s ease",transitionDelay:t.delay?`${t.delay}ms`:"0ms",animation:t.lost?"none":`movePacket-${t.direction} 1.5s forwards ${t.delay||0}ms`},children:t.type.toUpperCase().substring(0,3)},t.id)}),U.map((t,s)=>{const c=t.direction==="right"?28:72;return t.direction,e.jsx("div",{className:`absolute top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full flex items-center justify-center text-xs text-white ${S(t.type)}`,style:{left:`${c}%`,transition:"left 1.5s ease",transitionDelay:t.delay?`${t.delay}ms`:"0ms",animation:`movePacket-${t.direction} 1.5s forwards ${t.delay||0}ms`},children:"ACK"},t.id)}),e.jsx("style",{jsx:!0,children:`
          @keyframes movePacket-right {
            from { left: 28%; }
            to { left: 72%; }
          }
          
          @keyframes movePacket-left {
            from { left: 72%; }
            to { left: 28%; }
          }
        `})]}),e.jsxs("div",{className:"bg-[#1E293B] p-4 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-bold mb-2",children:"TCP vs UDP Comparison"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:`p-3 rounded ${l==="tcp"?"bg-blue-900/30 border border-blue-500/50":"bg-[#0F172A]"}`,children:[e.jsx("h5",{className:"font-bold text-blue-400 mb-1",children:"TCP"}),e.jsxs("ul",{className:"list-disc list-inside text-gray-300 space-y-1",children:[e.jsx("li",{children:"Connection-oriented"}),e.jsx("li",{children:"Reliable delivery"}),e.jsx("li",{children:"Ordered packets"}),e.jsx("li",{children:"Flow control"}),e.jsx("li",{children:"Error recovery"}),e.jsx("li",{children:"Slower but reliable"})]})]}),e.jsxs("div",{className:`p-3 rounded ${l==="udp"?"bg-green-900/30 border border-green-500/50":"bg-[#0F172A]"}`,children:[e.jsx("h5",{className:"font-bold text-green-400 mb-1",children:"UDP"}),e.jsxs("ul",{className:"list-disc list-inside text-gray-300 space-y-1",children:[e.jsx("li",{children:"Connectionless"}),e.jsx("li",{children:"No delivery guarantee"}),e.jsx("li",{children:"No packet ordering"}),e.jsx("li",{children:"No flow control"}),e.jsx("li",{children:"No error recovery"}),e.jsx("li",{children:"Faster but unreliable"})]})]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("button",{onClick:()=>N(),disabled:d||i>=r.length-1,className:`px-4 py-2 rounded ${d||i>=r.length-1?"bg-gray-700 text-gray-400 cursor-not-allowed":"bg-[#1E293B] text-white hover:bg-[#334155]"}`,children:"Manual Step"}),e.jsxs("button",{onClick:B,className:"px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700",children:[e.jsx(V,{className:"inline mr-1"})," Test Knowledge"]})]}),I&&h&&e.jsx("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-6 max-w-2xl w-full border border-gray-700",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"TCP/IP Protocol Quiz"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-bold mb-3",children:h.question}),e.jsx("div",{className:"space-y-2",children:h.options.map((t,s)=>e.jsx("div",{onClick:()=>o===null&&Q(s),className:`p-3 rounded-lg cursor-pointer border ${K===s?o!=null&&o.correct?"bg-green-900/20 border-green-500":"bg-red-900/20 border-red-500":o!==null&&s===h.correctAnswer?"bg-green-900/20 border-green-500":"bg-[#1E293B] border-gray-700 hover:border-primary"}`,children:t},s))}),o&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg ${o.correct?"bg-green-900/20 border border-green-500":"bg-red-900/20 border border-red-500"}`,children:[e.jsxs("div",{className:"flex items-center mb-2",children:[o.correct?e.jsx(H,{className:"text-green-500 mr-2"}):e.jsx(J,{className:"text-red-500 mr-2"}),e.jsx("span",{className:"font-bold",children:o.correct?"Correct!":"Incorrect"})]}),e.jsx("p",{children:o.explanation})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>y(!1),className:"px-4 py-2 rounded bg-primary text-black",children:"Close"})})]})})]})};export{X as default};
