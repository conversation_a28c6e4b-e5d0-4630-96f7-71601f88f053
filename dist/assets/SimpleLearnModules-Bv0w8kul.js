import{j as e,aR as h,L as i,r as o,u,$ as p,am as f,Y as g,U as y,l as b,z as j}from"./index-CVvVjHWF.js";const v=({targetTier:s="premium",price:n=6,currency:r="OMR ",message:l="Upgrade to Premium for full access to all features"})=>e.jsx("div",{className:"bg-[#1A1F35] border border-[#88cc14]/30 rounded-lg p-4 mb-6 relative overflow-hidden",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:e.jsx(h,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-bold text-white",children:[s.charAt(0).toUpperCase()+s.slice(1)," Subscription"]}),e.jsx("p",{className:"text-gray-300 text-sm",children:l})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[s==="business"?e.jsx("span",{className:"text-white font-bold",children:"Contact Sales"}):e.jsxs("span",{className:"text-white font-bold",children:[r,n]}),e.jsx(i,{to:"/pricing",className:"bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium px-4 py-2 rounded-lg transition-colors",children:"Upgrade Now"})]})]})}),N=[{id:1,title:"Introduction to Cybersecurity",description:"Learn the fundamentals of cybersecurity and understand the importance of security in today's digital world.",difficulty:"Beginner",tags:["Fundamentals","Security Basics"],duration:"2 hours",rating:4.8,completed:!1,progress:0,free:!0},{id:2,title:"Network Security Fundamentals",description:"Understand how networks function and learn about common network vulnerabilities and how to protect against them.",difficulty:"Beginner",tags:["Network","Security"],duration:"3 hours",rating:4.7,completed:!1,progress:0,free:!0},{id:3,title:"Web Application Security",description:"Learn about common web application vulnerabilities like XSS, CSRF, and SQL injection and how to prevent them.",difficulty:"Intermediate",tags:["Web Security","OWASP"],duration:"4 hours",rating:4.9,completed:!1,progress:0,free:!0},{id:4,title:"Ethical Hacking Methodology",description:"Understand the ethical hacking process and learn about the methodologies used by security professionals.",difficulty:"Intermediate",tags:["Ethical Hacking","Methodology"],duration:"5 hours",rating:4.6,completed:!1,progress:0,free:!1},{id:5,title:"Cryptography Basics",description:"Learn about encryption, hashing, and other cryptographic concepts essential for secure communications.",difficulty:"Intermediate",tags:["Cryptography","Encryption"],duration:"3 hours",rating:4.5,completed:!1,progress:0,free:!1},{id:6,title:"Malware Analysis",description:"Learn how to analyze malware and understand its behavior to better protect systems and networks.",difficulty:"Advanced",tags:["Malware","Analysis"],duration:"6 hours",rating:4.8,completed:!1,progress:0,free:!1}],S=()=>{const[s,n]=o.useState(""),[r,l]=o.useState("all"),{darkMode:d}=u(),m=N.filter(t=>{const a=t.title.toLowerCase().includes(s.toLowerCase())||t.description.toLowerCase().includes(s.toLowerCase())||t.tags.some(c=>c.toLowerCase().includes(s.toLowerCase()));return r==="all"?a:r==="beginner"?a&&t.difficulty==="Beginner":r==="intermediate"?a&&t.difficulty==="Intermediate":r==="advanced"?a&&t.difficulty==="Advanced":a}),x=t=>{switch(t){case"Beginner":return"bg-green-500/20 text-green-500";case"Intermediate":return"bg-yellow-500/20 text-yellow-500";case"Advanced":return"bg-red-500/20 text-red-500";default:return"bg-blue-500/20 text-blue-500"}};return e.jsx("div",{className:`min-h-screen ${d?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Learning Modules"}),e.jsx("p",{className:"text-gray-400",children:"Master cybersecurity skills with our comprehensive learning modules"})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex items-center gap-2",children:[e.jsx(i,{to:"/learn/paths",className:"theme-button-secondary px-4 py-2 rounded-md transition-colors",children:"View Career Paths"}),e.jsx(i,{to:"/learn/certifications",className:"theme-button-primary px-4 py-2 rounded-md font-medium transition-colors",children:"Certifications"})]})]}),e.jsx(v,{message:"Upgrade to Premium to unlock all 50 learning modules and accelerate your cybersecurity journey"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(p,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"theme-input rounded-lg block w-full pl-10 p-2.5 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"Search modules...",value:s,onChange:t=>n(t.target.value)})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("div",{className:"theme-card rounded-lg p-2.5 flex items-center gap-2",children:[e.jsx(f,{className:"text-gray-500"}),e.jsxs("select",{className:"bg-transparent theme-text-primary focus:outline-none",value:r,onChange:t=>l(t.target.value),children:[e.jsx("option",{value:"all",className:"theme-bg-secondary theme-text-primary",children:"All Levels"}),e.jsx("option",{value:"beginner",className:"theme-bg-secondary theme-text-primary",children:"Beginner"}),e.jsx("option",{value:"intermediate",className:"theme-bg-secondary theme-text-primary",children:"Intermediate"}),e.jsx("option",{value:"advanced",className:"theme-bg-secondary theme-text-primary",children:"Advanced"})]})]})})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map(t=>e.jsxs("div",{className:"theme-card rounded-lg overflow-hidden border theme-border hover:border-[#88cc14]/50 transition-colors",children:[e.jsxs("div",{className:"p-4 border-b theme-border",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"text-lg font-bold theme-text-primary",children:t.title}),e.jsx("span",{className:`px-2 py-0.5 rounded text-xs ${x(t.difficulty)}`,children:t.difficulty})]}),e.jsx("p",{className:"text-sm theme-text-secondary mb-4 line-clamp-2",children:t.description}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:t.tags.map((a,c)=>e.jsx("span",{className:"px-2 py-0.5 theme-bg-primary theme-text-secondary rounded text-xs",children:a},c))}),e.jsxs("div",{className:"flex justify-between items-center text-sm theme-text-secondary",children:[e.jsxs("span",{children:["Duration: ",t.duration]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(g,{className:"text-yellow-500 text-xs"}),e.jsx("span",{children:t.rating})]})]})]}),e.jsx("div",{className:"p-4 theme-bg-tertiary",children:t.free?e.jsxs(i,{to:`/learn/modules/${t.id}`,className:"theme-button-primary flex items-center justify-center gap-2 font-medium py-2 rounded-lg transition-colors w-full",children:[e.jsx(y,{className:"text-xs"}),"Start Learning"]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-1 text-gray-400",children:[e.jsx(b,{className:"text-xs"}),e.jsx("span",{children:"Premium"})]}),e.jsxs(i,{to:"/pricing",className:"text-[#88cc14] hover:underline flex items-center gap-1",children:["Upgrade ",e.jsx(j,{className:"text-xs"})]})]})})]},t.id))})]})})};export{S as default};
