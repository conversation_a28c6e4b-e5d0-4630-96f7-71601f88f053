import{u as z,r as t,j as e,J as Q,E as K,a4 as B,v as I,aV as le,m as J,a3 as oe,aW as ge,S as ae,G as te,af as ie,at as se,t as Z,aX as he,aq as xe,U as be,$ as ce,ac as de,l as me,aY as pe,aZ as ye,a_ as fe,D as je,b as ve,a as Ne,ae as Se,a1 as ne,aQ as we,a$ as ke,N as _e}from"./index-CVvVjHWF.js";const $e=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z(),[_,o]=t.useState(0),[d,q]=t.useState(""),[F,C]=t.useState(""),[M,T]=t.useState(!1),[b,D]=t.useState(0),[p,n]=t.useState(null),[y,O]=t.useState(0),[v,Y]=t.useState(null),[V,E]=t.useState(null);t.useEffect(()=>{(async()=>{try{const{data:{user:x}}=await I.auth.getUser();if(x){const{data:U,error:k}=await I.from("practice_simulation_attempts").select("*").eq("user_id",x.id).eq("simulation_id",P).single();k&&k.code!=="PGSQL_ERROR"&&console.error("Error fetching user progress:",k),E(U||null)}}catch(x){console.error("Error fetching user progress:",x)}})()},[P]),t.useEffect(()=>{if(!p){n(Date.now());const $=setInterval(()=>{O(Math.floor((Date.now()-p)/1e3))},1e3);Y($)}return()=>{v&&clearInterval(v)}},[p,v]);const R=$=>{const x=Math.floor($/60),U=$%60;return`${x.toString().padStart(2,"0")}:${U.toString().padStart(2,"0")}`},w=[{title:"Introduction to SQL Injection",content:e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"SQL Injection is a code injection technique that exploits vulnerabilities in applications that interact with databases. In this simulation, you'll learn how to identify and exploit a basic SQL injection vulnerability in a login form."}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30",children:[e.jsx(Q,{className:"text-blue-500 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"This is a safe environment to practice SQL injection techniques. The skills you learn here should only be used ethically, such as in penetration testing with proper authorization."})]}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Objectives:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Understand how SQL injection works"}),e.jsx("li",{children:"Learn to identify vulnerable input fields"}),e.jsx("li",{children:"Successfully bypass a login form using SQL injection"})]}),e.jsx("button",{onClick:()=>o(1),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Start Simulation"})]})},{title:"The Login Form",content:e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"You're presented with a login form for a website. Your goal is to bypass the authentication without knowing the password."}),e.jsx("p",{className:"mb-4",children:"When a user submits this form, the application might execute an SQL query like this:"}),e.jsx("div",{className:`p-4 rounded-lg font-mono text-sm mb-4 ${s?"bg-gray-800":"bg-gray-100"}`,children:e.jsx("code",{children:"SELECT * FROM users WHERE username = 'input_username' AND password = 'input_password';"})}),e.jsx("p",{className:"mb-4",children:"If the query returns a row, the login is successful. If not, it fails."}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30",children:[e.jsx(K,{className:"text-yellow-500 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"Think about how you could manipulate this query to always return a result, regardless of the password."})]}),e.jsxs("div",{className:`p-6 rounded-lg border mb-4 ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4 text-center",children:"Login Form"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Username:"}),e.jsx("input",{type:"text",value:d,onChange:$=>q($.target.value),className:`w-full px-4 py-2 rounded-lg ${s?"bg-gray-700 border-gray-600 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,placeholder:"Enter username"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Password:"}),e.jsx("input",{type:"password",className:`w-full px-4 py-2 rounded-lg ${s?"bg-gray-700 border-gray-600 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,placeholder:"Enter password",value:"********",readOnly:!0})]}),e.jsx("button",{onClick:()=>{if(D(b+1),d.includes("'")&&(d.includes("--")||d.includes("OR")||d.includes("="))){C("Login successful! You've bypassed the authentication."),T(!0),v&&clearInterval(v);const $=Math.max(0,100-Math.floor(y/5)),x=Math.max(0,100-b*10),U=Math.floor(($+x)/2);h({success:!0,timeSpent:y,attempts:b,score:U}),A(!0)}else C("Login failed. Invalid username or password.")},className:`w-full px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Login"})]}),F&&e.jsx("div",{className:`p-4 rounded-lg mb-4 ${M?"bg-green-500/10 border border-green-500/30 text-green-500":"bg-red-500/10 border border-red-500/30 text-red-500"}`,children:e.jsx("p",{children:F})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>o(0),className:`px-4 py-2 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:"Back"}),M&&e.jsx("button",{onClick:()=>o(2),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Continue"})]})]})},{title:"Explanation",content:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"How SQL Injection Works"}),e.jsx("p",{className:"mb-4",children:"Congratulations! You've successfully exploited an SQL injection vulnerability. Let's understand what happened:"}),e.jsxs("div",{className:`p-4 rounded-lg font-mono text-sm mb-4 ${s?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("p",{children:"Original query:"}),e.jsx("code",{children:"SELECT * FROM users WHERE username = 'input_username' AND password = 'input_password';"}),e.jsx("p",{className:"mt-2",children:"With your injection:"}),e.jsxs("code",{children:["SELECT * FROM users WHERE username = '",d,"' AND password = 'anything';"]})]}),e.jsx("p",{className:"mb-4",children:"By injecting SQL code into the username field, you manipulated the query to always return true, regardless of the password."}),e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Prevention Techniques"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Use parameterized queries or prepared statements"}),e.jsx("li",{children:"Implement input validation and sanitization"}),e.jsx("li",{children:"Apply the principle of least privilege for database accounts"}),e.jsx("li",{children:"Use ORM (Object-Relational Mapping) libraries"})]}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-green-500/10 border border-green-500/30",children:[e.jsx(B,{className:"text-green-500 mr-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-bold",children:"Simulation Complete!"}),e.jsxs("p",{children:["Time: ",R(y)," | Attempts: ",b]})]})]}),e.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Restart Simulation"})]})}],A=async $=>{try{const{data:{user:x}}=await I.auth.getUser();if(x){const{error:U}=await I.from("practice_simulation_attempts").upsert({user_id:x.id,simulation_id:P,is_completed:$,completion_time:y,approach_score:Math.max(0,100-b*10),total_score:Math.max(0,100-Math.floor(y/10)-b*5),started_at:new Date(p).toISOString(),completed_at:$?new Date().toISOString():null});U&&console.error("Error saving progress:",U)}}catch(x){console.error("Error saving progress:",x)}};return e.jsxs("div",{className:`rounded-lg border ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"}`,children:[e.jsx("div",{className:`p-4 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(le,{className:"text-blue-500 mr-2"}),w[_].title]}),e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:`px-3 py-1 rounded-lg text-sm ${s?"bg-gray-800":"bg-gray-100"}`,children:["Time: ",R(y)]})})]})}),e.jsx("div",{className:"p-6",children:e.jsx(J.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:w[_].content},_)})]})},Ce=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z(),[_,o]=t.useState(0),[d,q]=t.useState(50),[F,C]=t.useState("syn"),[M,T]=t.useState("web-server"),[b,D]=t.useState(!1),[p,n]=t.useState(0),[y,O]=t.useState("online"),[v,Y]=t.useState([]),[V,E]=t.useState(!1),[R,w]=t.useState(null),[A,$]=t.useState(0),[x,U]=t.useState(null),[k,f]=t.useState(null),g=t.useRef(null);t.useEffect(()=>{(async()=>{try{const{data:{user:m}}=await I.auth.getUser();if(m){const{data:j,error:L}=await I.from("practice_simulation_attempts").select("*").eq("user_id",m.id).eq("simulation_id",P).single();L&&L.code!=="PGSQL_ERROR"&&console.error("Error fetching user progress:",L),f(j||null)}}catch(m){console.error("Error fetching user progress:",m)}})()},[P]),t.useEffect(()=>{if(!R){w(Date.now());const l=setInterval(()=>{$(Math.floor((Date.now()-R)/1e3))},1e3);U(l)}return()=>{x&&clearInterval(x)}},[R,x]),t.useEffect(()=>{g.current&&g.current.scrollIntoView({behavior:"smooth"})},[v]);const u=l=>{const m=Math.floor(l/60),j=l%60;return`${m.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")}`},r=()=>{if(b)return;D(!0),O("degraded"),Y([...v,`[${u(A)}] Starting ${F.toUpperCase()} flood attack on ${M}...`]);const l=setInterval(()=>{n(m=>{const j=d/100*(Math.random()*5+5),L=m+j;if(L>=100){clearInterval(l),D(!1),O("offline"),E(!0),Y(S=>[...S,`[${u(A)}] Attack successful! Target server is down.`]),x&&clearInterval(x);const c=Math.max(0,100-Math.floor(A/5)),W=Math.max(0,d),i=Math.floor((c+W)/2);return h({success:!0,timeSpent:A,attackType:F,targetServer:M,score:i}),N(!0),100}return Math.floor(m/10)<Math.floor(L/10)&&Y(c=>[...c,`[${u(A)}] Server load at ${Math.floor(L)}%. ${L>50?"Target showing signs of strain.":"Target still responsive."}`]),L})},1e3);return()=>clearInterval(l)},N=async l=>{try{const{data:{user:m}}=await I.auth.getUser();if(m){const{error:j}=await I.from("practice_simulation_attempts").upsert({user_id:m.id,simulation_id:P,is_completed:l,completion_time:A,approach_score:d,total_score:Math.max(0,100-Math.floor(A/10)+d/2),started_at:new Date(R).toISOString(),completed_at:l?new Date().toISOString():null});j&&console.error("Error saving progress:",j)}}catch(m){console.error("Error saving progress:",m)}},H=[{title:"Introduction to DDoS Attacks",content:e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"A Distributed Denial of Service (DDoS) attack is a malicious attempt to disrupt the normal traffic of a targeted server, service, or network by overwhelming it with a flood of internet traffic."}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30",children:[e.jsx(Q,{className:"text-blue-500 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"This is a safe environment to understand how DDoS attacks work. The skills you learn here should only be used ethically, such as in penetration testing with proper authorization or to better understand how to defend against such attacks."})]}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Objectives:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Understand how DDoS attacks work"}),e.jsx("li",{children:"Learn about different types of DDoS attacks"}),e.jsx("li",{children:"Successfully simulate a DDoS attack in a controlled environment"})]}),e.jsx("button",{onClick:()=>o(1),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Start Simulation"})]})},{title:"DDoS Attack Simulator",content:e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"In this simulation, you'll configure and launch a simulated DDoS attack against a target server. Your goal is to overwhelm the server's resources and cause it to become unresponsive."}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30",children:[e.jsx(K,{className:"text-yellow-500 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"Remember: This is a simulation. In real-world scenarios, unauthorized DDoS attacks are illegal and can result in severe penalties."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg border ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Attack Configuration"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Target Server:"}),e.jsxs("select",{value:M,onChange:l=>T(l.target.value),className:`w-full px-4 py-2 rounded-lg ${s?"bg-gray-700 border-gray-600 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,disabled:b,children:[e.jsx("option",{value:"web-server",children:"Web Server"}),e.jsx("option",{value:"database-server",children:"Database Server"}),e.jsx("option",{value:"auth-server",children:"Authentication Server"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Attack Type:"}),e.jsxs("select",{value:F,onChange:l=>C(l.target.value),className:`w-full px-4 py-2 rounded-lg ${s?"bg-gray-700 border-gray-600 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,disabled:b,children:[e.jsx("option",{value:"syn",children:"SYN Flood"}),e.jsx("option",{value:"udp",children:"UDP Flood"}),e.jsx("option",{value:"http",children:"HTTP Flood"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("label",{className:"block mb-2",children:["Attack Rate: ",d,"%"]}),e.jsx("input",{type:"range",min:"10",max:"100",value:d,onChange:l=>q(parseInt(l.target.value)),className:"w-full",disabled:b}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{children:"Low"}),e.jsx("span",{children:"Medium"}),e.jsx("span",{children:"High"})]})]}),e.jsx("button",{onClick:r,disabled:b||V,className:`w-full px-4 py-2 rounded-lg ${b||V?`${s?"bg-gray-700":"bg-gray-300"} cursor-not-allowed`:`${s?"bg-red-600 hover:bg-red-700":"bg-red-500 hover:bg-red-600"} text-white`}`,children:b?"Attack in Progress...":V?"Attack Completed":"Launch Attack"})]}),e.jsxs("div",{className:`p-4 rounded-lg border ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Target Status"}),e.jsxs("div",{className:"mb-4 flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-2 ${y==="online"?"bg-green-500":y==="degraded"?"bg-yellow-500":"bg-red-500"}`}),e.jsx("span",{className:"capitalize",children:y})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Server Load:"}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700",children:e.jsx("div",{className:`h-4 rounded-full ${p<50?"bg-green-500":p<80?"bg-yellow-500":"bg-red-500"}`,style:{width:`${p}%`}})}),e.jsxs("div",{className:"text-right mt-1",children:[Math.floor(p),"%"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Attack Logs:"}),e.jsxs("div",{className:`h-40 overflow-y-auto p-2 rounded-lg font-mono text-xs ${s?"bg-gray-900 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[v.length===0?e.jsx("p",{className:"text-gray-500",children:"No logs yet. Start the attack to see logs."}):v.map((l,m)=>e.jsx("div",{className:"mb-1",children:l},m)),e.jsx("div",{ref:g})]})]})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>o(0),className:`px-4 py-2 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,disabled:b,children:"Back"}),V&&e.jsx("button",{onClick:()=>o(2),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Continue"})]})]})},{title:"Explanation",content:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"How DDoS Attacks Work"}),e.jsx("p",{className:"mb-4",children:"Congratulations! You've successfully simulated a DDoS attack. Let's understand what happened:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:"Attack Types:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"SYN Flood:"})," Exploits TCP handshake by sending many SYN packets without completing the handshake"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"UDP Flood:"})," Sends a large number of UDP packets to random ports on a target server"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"HTTP Flood:"})," Overwhelms a web server with seemingly legitimate HTTP GET or POST requests"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:"Impact:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Exhausts server resources (CPU, memory, bandwidth)"}),e.jsx("li",{children:"Prevents legitimate users from accessing services"}),e.jsx("li",{children:"Can cause financial losses due to downtime"}),e.jsx("li",{children:"May be used as a smokescreen for other attacks"})]})]})]}),e.jsx("h3",{className:"text-lg font-bold mb-4",children:"DDoS Defense Techniques"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Traffic filtering and rate limiting"}),e.jsx("li",{children:"Using Content Delivery Networks (CDNs)"}),e.jsx("li",{children:"Implementing load balancers"}),e.jsx("li",{children:"Deploying DDoS protection services"}),e.jsx("li",{children:"Configuring firewalls and intrusion prevention systems"})]}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-green-500/10 border border-green-500/30",children:[e.jsx(B,{className:"text-green-500 mr-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-bold",children:"Simulation Complete!"}),e.jsxs("p",{children:["Time: ",u(A)," | Attack Type: ",F.toUpperCase()," | Target: ",M]})]})]}),e.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Restart Simulation"})]})}];return e.jsxs("div",{className:`rounded-lg border ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"}`,children:[e.jsx("div",{className:`p-4 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(oe,{className:"text-red-500 mr-2"}),H[_].title]}),e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:`px-3 py-1 rounded-lg text-sm ${s?"bg-gray-800":"bg-gray-100"}`,children:["Time: ",u(A)]})})]})}),e.jsx("div",{className:"p-6",children:e.jsx(J.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:H[_].content},_)})]})},Te=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z(),[_,o]=t.useState(0),[d,q]=t.useState(""),[F,C]=t.useState(""),[M,T]=t.useState(!1),[b,D]=t.useState(0),[p,n]=t.useState(null),[y,O]=t.useState(0),[v,Y]=t.useState(null),[V,E]=t.useState(null),[R,w]=t.useState(""),A=t.useRef(null);t.useEffect(()=>{(async()=>{try{const{data:{user:f}}=await I.auth.getUser();if(f){const{data:g,error:u}=await I.from("practice_simulation_attempts").select("*").eq("user_id",f.id).eq("simulation_id",P).single();u&&u.code!=="PGSQL_ERROR"&&console.error("Error fetching user progress:",u),E(g||null)}}catch(f){console.error("Error fetching user progress:",f)}})()},[P]),t.useEffect(()=>{if(!p){n(Date.now());const k=setInterval(()=>{O(Math.floor((Date.now()-p)/1e3))},1e3);Y(k)}return()=>{v&&clearInterval(v)}},[p,v]);const $=k=>{const f=Math.floor(k/60),g=k%60;return`${f.toString().padStart(2,"0")}:${g.toString().padStart(2,"0")}`};t.useEffect(()=>{var k;if(_===1){const f=`
        <div class="comment-section">
          <h3>User Comments</h3>
          <div class="comment">
            <strong>Admin:</strong> Welcome to our website!
          </div>
          <div class="comment">
            <strong>User1:</strong> Great content, thanks for sharing!
          </div>
          <div class="comment">
            <strong>You:</strong> ${d}
          </div>
        </div>
      `;if(w(f),A.current)try{const g=A.current.contentDocument||((k=A.current.contentWindow)==null?void 0:k.document);g&&(g.open(),g.write(`
              <html>
                <head>
                  <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .comment-section { border: 1px solid #ccc; padding: 15px; border-radius: 5px; }
                    .comment { margin-bottom: 10px; padding: 8px; background-color: #f5f5f5; border-radius: 3px; }
                  </style>
                </head>
                <body>
                  ${f}
                </body>
              </html>
            `),g.close())}catch(g){console.error("Error updating iframe content:",g)}}},[d,_]);const x=async k=>{try{const{data:{user:f}}=await I.auth.getUser();if(f){const{error:g}=await I.from("practice_simulation_attempts").upsert({user_id:f.id,simulation_id:P,is_completed:k,completion_time:y,approach_score:Math.max(0,100-b*10),total_score:Math.max(0,100-Math.floor(y/10)-b*5),started_at:new Date(p).toISOString(),completed_at:k?new Date().toISOString():null});g&&console.error("Error saving progress:",g)}}catch(f){console.error("Error saving progress:",f)}},U=[{title:"Introduction to Cross-Site Scripting (XSS)",content:e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"Cross-Site Scripting (XSS) is a type of security vulnerability that allows attackers to inject malicious client-side scripts into web pages viewed by other users. In this simulation, you'll learn how to identify and exploit a basic XSS vulnerability in a comment section."}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30",children:[e.jsx(Q,{className:"text-blue-500 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"This is a safe environment to practice XSS techniques. The skills you learn here should only be used ethically, such as in penetration testing with proper authorization."})]}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Objectives:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Understand how XSS vulnerabilities work"}),e.jsx("li",{children:"Learn to identify vulnerable input fields"}),e.jsx("li",{children:"Successfully exploit an XSS vulnerability"})]}),e.jsx("button",{onClick:()=>o(1),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Start Simulation"})]})},{title:"The Comment Section",content:e.jsxs("div",{children:[e.jsx("p",{className:"mb-4",children:"You're presented with a comment section on a website. Your goal is to inject JavaScript code that will execute when the page loads."}),e.jsx("p",{className:"mb-4",children:"The website doesn't properly sanitize user input, making it vulnerable to XSS attacks."}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30",children:[e.jsx(K,{className:"text-yellow-500 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm",children:"Try entering HTML tags or JavaScript code in the comment field to see if they execute."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:`p-6 rounded-lg border ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4 text-center",children:"Comment Form"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2",children:"Your Comment:"}),e.jsx("textarea",{value:d,onChange:k=>q(k.target.value),className:`w-full px-4 py-2 rounded-lg ${s?"bg-gray-700 border-gray-600 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border`,placeholder:"Enter your comment here...",rows:4})]}),e.jsx("button",{onClick:()=>{if(D(b+1),d.includes("<script>")&&d.includes("<\/script>")||d.includes("javascript:")||d.includes("onerror=")||d.includes("onclick=")||d.includes("alert(")){C("XSS vulnerability successfully exploited!"),T(!0),v&&clearInterval(v);const k=Math.max(0,100-Math.floor(y/5)),f=Math.max(0,100-b*10),g=Math.floor((k+f)/2);h({success:!0,timeSpent:y,attempts:b,score:g}),x(!0)}else C("Comment posted, but no XSS vulnerability detected.")},className:`w-full px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Post Comment"})]}),e.jsxs("div",{className:`p-6 rounded-lg border ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4 text-center",children:"Preview"}),e.jsx("div",{className:`h-48 overflow-auto rounded-lg border ${s?"bg-gray-700 border-gray-600":"bg-white border-gray-300"}`,children:e.jsx("iframe",{ref:A,title:"Comment Preview",className:"w-full h-full",sandbox:"allow-scripts",srcDoc:`
                    <html>
                      <head>
                        <style>
                          body { font-family: Arial, sans-serif; padding: 20px; }
                          .comment-section { border: 1px solid #ccc; padding: 15px; border-radius: 5px; }
                          .comment { margin-bottom: 10px; padding: 8px; background-color: #f5f5f5; border-radius: 3px; }
                        </style>
                      </head>
                      <body>
                        <div class="comment-section">
                          <h3>User Comments</h3>
                          <div class="comment">
                            <strong>Admin:</strong> Welcome to our website!
                          </div>
                          <div class="comment">
                            <strong>User1:</strong> Great content, thanks for sharing!
                          </div>
                          <div class="comment">
                            <strong>You:</strong> ${d}
                          </div>
                        </div>
                      </body>
                    </html>
                  `})})]})]}),F&&e.jsx("div",{className:`p-4 rounded-lg mb-4 ${M?"bg-green-500/10 border border-green-500/30 text-green-500":"bg-red-500/10 border border-red-500/30 text-red-500"}`,children:e.jsx("p",{children:F})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>o(0),className:`px-4 py-2 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:"Back"}),M&&e.jsx("button",{onClick:()=>o(2),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Continue"})]})]})},{title:"Explanation",content:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"How XSS Vulnerabilities Work"}),e.jsx("p",{className:"mb-4",children:"Congratulations! You've successfully exploited an XSS vulnerability. Let's understand what happened:"}),e.jsxs("div",{className:`p-4 rounded-lg font-mono text-sm mb-4 ${s?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("p",{children:"You injected JavaScript code that executed in the browser:"}),e.jsx("code",{children:d})]}),e.jsx("p",{className:"mb-4",children:"When a website doesn't properly sanitize user input, attackers can inject malicious scripts that execute when other users view the page. This can lead to cookie theft, session hijacking, or other attacks."}),e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Types of XSS Attacks"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Reflected XSS:"})," The malicious script is reflected off a web server, such as in search results or error messages."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Stored XSS:"})," The malicious script is stored on the target server, such as in a database, message forum, or comment field."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"DOM-based XSS:"})," The vulnerability exists in client-side code rather than server-side code."]})]}),e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Prevention Techniques"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Input validation and sanitization"}),e.jsx("li",{children:"Output encoding"}),e.jsx("li",{children:"Content Security Policy (CSP)"}),e.jsx("li",{children:"Use of modern frameworks that automatically escape output"}),e.jsx("li",{children:"X-XSS-Protection header"})]}),e.jsxs("div",{className:"flex items-center p-4 mb-4 rounded-lg bg-green-500/10 border border-green-500/30",children:[e.jsx(B,{className:"text-green-500 mr-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-bold",children:"Simulation Complete!"}),e.jsxs("p",{children:["Time: ",$(y)," | Attempts: ",b]})]})]}),e.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Restart Simulation"})]})}];return e.jsxs("div",{className:`rounded-lg border ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"}`,children:[e.jsx("div",{className:`p-4 border-b ${s?"border-gray-800":"border-gray-200"}`,children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(ge,{className:"text-orange-500 mr-2"}),U[_].title]}),e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:`px-3 py-1 rounded-lg text-sm ${s?"bg-gray-800":"bg-gray-100"}`,children:["Time: ",$(y)]})})]})}),e.jsx("div",{className:"p-6",children:e.jsx(J.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:U[_].content},_)})]})},Ie=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z(),{awardXP:_}=ae(),[o,d]=t.useState(0),[q,F]=t.useState(null),[C,M]=t.useState(""),[T,b]=t.useState(""),[D,p]=t.useState(!1),[n,y]=t.useState(0),[O,v]=t.useState(!1),[Y,V]=t.useState(!1),[E,R]=t.useState(0),[w,A]=t.useState(null),[$,x]=t.useState(!1),[U,k]=t.useState(!1),[f,g]=t.useState(null),u={title:"Password Cracking Challenge",description:"In this simulation, you'll learn how to identify and crack different types of password hashes using various techniques.",targetHash:"$2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy",hashType:"bcrypt",correctPassword:"password123",hints:["This is a common bcrypt hash format","Try looking for common passwords in the dictionary","The password contains both letters and numbers"],techniques:[{id:"dictionary",name:"Dictionary Attack",description:"Uses a list of common words and passwords to try to crack the hash",effectiveness:"Medium",speed:"Fast",icon:e.jsx(he,{className:"text-blue-500"})},{id:"bruteforce",name:"Brute Force Attack",description:"Tries every possible combination of characters",effectiveness:"High",speed:"Very Slow",icon:e.jsx(xe,{className:"text-red-500"})},{id:"rainbow",name:"Rainbow Table",description:"Uses precomputed tables to find passwords from hashes",effectiveness:"High for unsalted hashes",speed:"Very Fast",icon:e.jsx(be,{className:"text-green-500"})}]};t.useEffect(()=>{(async()=>{const{data:W}=await I.auth.getUser();g(W.user)})()},[]),t.useEffect(()=>{o>0&&!w&&(A(Date.now()),x(!0))},[o,w]),t.useEffect(()=>{let c;return $&&(c=setInterval(()=>{R(Math.floor((Date.now()-w)/1e3))},1e3)),()=>clearInterval(c)},[$,w]);const r=c=>{F(c),d(2)},N=c=>{b(c),c===u.hashType?(p(!0),d(1)):(p(!1),y(n+1))},H=c=>{c.preventDefault(),C.toLowerCase()===u.correctPassword.toLowerCase()?(p(!0),x(!1),V(!0),k(!0),f&&l(),typeof _=="function"&&_("simulation_completed",{simulationId:P,timeTaken:E,attempts:n}),h&&h({success:!0,timeTaken:E,attempts:n})):(p(!1),y(n+1))},l=async()=>{try{const{data:c,error:W}=await I.from("practice_simulation_attempts").upsert({user_id:f.id,simulation_id:P||"password-cracking-simulation",is_completed:!0,completion_time:E,approach_score:Math.max(100-n*5,50),total_score:Math.max(100-n*5,50),started_at:new Date(w).toISOString(),completed_at:new Date().toISOString()},{onConflict:"user_id, simulation_id"});W&&console.error("Error recording completion:",W)}catch(c){console.error("Error recording completion:",c)}},m=c=>{const W=Math.floor(c/60),i=c%60;return`${W.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`},j=()=>{switch(o){case 0:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Step 1: Identify the Hash Type"}),e.jsx("div",{className:`p-4 rounded-lg ${s?"bg-gray-800":"bg-gray-100"}`,children:e.jsx("p",{className:"font-mono text-sm break-all",children:u.targetHash})}),e.jsx("p",{children:"Examine the hash above and identify what type of hash it is:"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[e.jsx("button",{onClick:()=>N("md5"),className:`p-3 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:"MD5"}),e.jsx("button",{onClick:()=>N("sha1"),className:`p-3 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:"SHA-1"}),e.jsx("button",{onClick:()=>N("bcrypt"),className:`p-3 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:"bcrypt"}),e.jsx("button",{onClick:()=>N("sha256"),className:`p-3 rounded-lg ${s?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:"SHA-256"})]}),n>0&&!D&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500 flex items-center`,children:[e.jsx(Z,{className:"mr-2"})," Incorrect. Try again."]}),n>=2&&!D&&e.jsx("button",{onClick:()=>v(!0),className:`mt-2 text-sm ${s?"text-blue-400":"text-blue-600"}`,children:"Need a hint?"}),O&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-blue-900/30":"bg-blue-100"} text-blue-500 flex items-center`,children:[e.jsx(Q,{className:"mr-2"})," ",u.hints[0]]})]});case 1:return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-green-900/30":"bg-green-100"} text-green-500 flex items-center`,children:[e.jsx(B,{className:"mr-2"})," Correct! This is a ",u.hashType," hash."]}),e.jsx("h2",{className:"text-xl font-bold",children:"Step 2: Choose a Password Cracking Technique"}),e.jsx("p",{children:"Select a technique to attempt to crack the password:"}),e.jsx("div",{className:"grid grid-cols-1 gap-4 mt-4",children:u.techniques.map(c=>e.jsxs("button",{onClick:()=>r(c),className:`p-4 rounded-lg ${s?"bg-gray-800 hover:bg-gray-700":"bg-white hover:bg-gray-100"} border ${s?"border-gray-700":"border-gray-200"} text-left flex items-start`,children:[e.jsx("div",{className:"mr-4 mt-1",children:c.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold",children:c.name}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:c.description}),e.jsxs("div",{className:"flex mt-2 text-xs",children:[e.jsxs("span",{className:`mr-3 ${s?"text-gray-400":"text-gray-600"}`,children:["Effectiveness: ",c.effectiveness]}),e.jsxs("span",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Speed: ",c.speed]})]})]})]},c.id))})]});case 2:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Step 3: Crack the Password"}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-800":"bg-gray-100"}`,children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(se,{className:"mr-2"}),e.jsx("span",{className:"font-bold",children:"Terminal"})]}),e.jsxs("div",{className:"font-mono text-sm",children:[e.jsxs("p",{children:["$ Running ",q.name,"..."]}),e.jsxs("p",{children:["$ Target hash: ",u.targetHash]}),e.jsx("p",{children:"$ Analyzing hash structure..."}),e.jsxs("p",{children:["$ Identified as ",u.hashType," hash"]}),e.jsxs("p",{children:["$ Starting ",q.id," attack..."]}),e.jsx("p",{children:"$ ..."}),e.jsx("p",{children:"$ Enter the password you think matches this hash:"})]})]}),e.jsx("form",{onSubmit:H,className:"mt-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("input",{type:"text",value:C,onChange:c=>M(c.target.value),placeholder:"Enter password",className:`flex-grow p-2 rounded-l-lg ${s?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`}),e.jsx("button",{type:"submit",className:`px-4 py-2 rounded-r-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Submit"})]})}),n>0&&!D&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500 flex items-center`,children:[e.jsx(Z,{className:"mr-2"})," Incorrect password. Try again."]}),n>=2&&!D&&e.jsx("button",{onClick:()=>v(!0),className:`mt-2 text-sm ${s?"text-blue-400":"text-blue-600"}`,children:"Need a hint?"}),O&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-blue-900/30":"bg-blue-100"} text-blue-500 flex items-center`,children:[e.jsx(Q,{className:"mr-2"})," ",u.hints[Math.min(n-1,u.hints.length-1)]]})]});default:return null}},L=()=>e.jsxs(J.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:`p-6 rounded-lg ${s?"bg-gray-800":"bg-white"} text-center`,children:[e.jsx("div",{className:"w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(ie,{className:"text-4xl text-green-500"})}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Password Cracked Successfully!"}),e.jsx("p",{className:`mb-4 ${s?"text-gray-400":"text-gray-600"}`,children:"You've successfully identified the hash type and cracked the password."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Time Taken"}),e.jsx("p",{className:"text-xl font-bold",children:m(E)})]}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Attempts"}),e.jsx("p",{className:"text-xl font-bold",children:n+1})]})]}),e.jsxs("div",{className:`p-4 mb-6 rounded-lg text-left ${s?"bg-blue-900/30":"bg-blue-100"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"What You've Learned"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1",children:[e.jsx("li",{children:"How to identify different types of password hashes"}),e.jsx("li",{children:"Different techniques for password cracking"}),e.jsx("li",{children:"The importance of using strong, unique passwords"}),e.jsx("li",{children:"How password hashing and salting works"})]})]}),e.jsx("button",{onClick:()=>window.location.href="/dashboard/start-hack",className:`px-6 py-3 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Return to Simulations"})]});return e.jsxs("div",{className:`max-w-4xl mx-auto ${s?"text-white":"text-gray-900"}`,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:u.title}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:u.description})]}),e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold",children:o+1}),e.jsx("div",{className:`h-1 w-16 mx-2 ${o>=1?"bg-blue-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-8 h-8 rounded-full ${o>=1?"bg-blue-500 text-white":"bg-gray-300 text-gray-600"} flex items-center justify-center font-bold`,children:"2"}),e.jsx("div",{className:`h-1 w-16 mx-2 ${o>=2?"bg-blue-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-8 h-8 rounded-full ${o>=2?"bg-blue-500 text-white":"bg-gray-300 text-gray-600"} flex items-center justify-center font-bold`,children:"3"})]}),e.jsxs("div",{className:`px-3 py-1 rounded-lg ${s?"bg-gray-800":"bg-gray-100"} flex items-center`,children:[e.jsx(te,{className:"mr-2"}),e.jsx("span",{children:m(E)})]})]}),e.jsx("div",{className:`p-6 rounded-lg ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} border`,children:U?L():j()})]})},De=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z(),{awardXP:_}=ae(),[o,d]=t.useState(0),[q,F]=t.useState(null),[C,M]=t.useState(""),[T,b]=t.useState(""),[D,p]=t.useState(!1),[n,y]=t.useState(0),[O,v]=t.useState(!1),[Y,V]=t.useState(!1),[E,R]=t.useState(0),[w,A]=t.useState(null),[$,x]=t.useState(!1),[U,k]=t.useState(!1),[f,g]=t.useState([]),[u,r]=t.useState([]),[N,H]=t.useState(null),l={title:"Network Scanning Simulation",description:"In this simulation, you'll learn how to perform network reconnaissance and identify vulnerable services on a network.",targetNetwork:"***********/24",vulnerableHost:"************",vulnerablePorts:[22,80,443,3306],vulnerableServices:[{port:22,service:"SSH",version:"OpenSSH 7.2p2",vulnerability:"CVE-2016-6210: User enumeration vulnerability"},{port:80,service:"HTTP",version:"Apache 2.4.18",vulnerability:"CVE-2016-8743: HTTP request smuggling vulnerability"},{port:443,service:"HTTPS",version:"Apache 2.4.18",vulnerability:"CVE-2016-8743: HTTP request smuggling vulnerability"},{port:3306,service:"MySQL",version:"5.7.12",vulnerability:"CVE-2016-6662: Remote code execution vulnerability"}],hints:["Try scanning the entire subnet to find hosts","Look for common ports like 22 (SSH), 80 (HTTP), 443 (HTTPS), and database ports","Check for outdated service versions that might have known vulnerabilities"],scanTypes:[{id:"ping",name:"Ping Sweep",description:"Discovers active hosts on the network using ICMP echo requests",effectiveness:"Basic host discovery",speed:"Fast",icon:e.jsx(ce,{className:"text-blue-500"})},{id:"port",name:"Port Scan",description:"Scans for open ports on a specific host or network",effectiveness:"Service discovery",speed:"Medium",icon:e.jsx(de,{className:"text-green-500"})},{id:"vulnerability",name:"Vulnerability Scan",description:"Identifies potential vulnerabilities in discovered services",effectiveness:"Vulnerability detection",speed:"Slow",icon:e.jsx(me,{className:"text-red-500"})}]};t.useEffect(()=>{(async()=>{const{data:X}=await I.auth.getUser();H(X.user)})()},[]),t.useEffect(()=>{o>0&&!w&&(A(Date.now()),x(!0))},[o,w]),t.useEffect(()=>{let a;return $&&(a=setInterval(()=>{R(Math.floor((Date.now()-w)/1e3))},1e3)),()=>clearInterval(a)},[$,w]);const m=a=>{F(a),a.id==="ping"&&setTimeout(()=>{g([{host:"***********",status:"up",role:"Gateway"},{host:"***********0",status:"up",role:"Workstation"},{host:"************",status:"up",role:"Workstation"},{host:"************",status:"up",role:"Server"},{host:"*************",status:"up",role:"Workstation"},{host:"*************",status:"up",role:"Printer"}]),d(1)},1500)},j=a=>{a.preventDefault(),C===l.vulnerableHost?(p(!0),setTimeout(()=>{g([{port:22,state:"open",service:"SSH"},{port:80,state:"open",service:"HTTP"},{port:443,state:"open",service:"HTTPS"},{port:3306,state:"open",service:"MySQL"}]),d(2)},1500)):(p(!1),y(n+1))},L=a=>{a.preventDefault();const X=T.split(",").map(G=>parseInt(G.trim())).filter(G=>!isNaN(G)),re=l.vulnerablePorts.every(G=>X.includes(G)),ue=X.every(G=>l.vulnerablePorts.includes(G));re&&ue?(p(!0),setTimeout(()=>{r(l.vulnerableServices),x(!1),V(!0),k(!0),N&&c(),typeof _=="function"&&_("simulation_completed",{simulationId:P,timeTaken:E,attempts:n}),h&&h({success:!0,timeTaken:E,attempts:n})},1500)):(p(!1),y(n+1))},c=async()=>{try{const{data:a,error:X}=await I.from("practice_simulation_attempts").upsert({user_id:N.id,simulation_id:P||"network-scanning-basics",is_completed:!0,completion_time:E,approach_score:Math.max(100-n*5,50),total_score:Math.max(100-n*5,50),started_at:new Date(w).toISOString(),completed_at:new Date().toISOString()},{onConflict:"user_id, simulation_id"});X&&console.error("Error recording completion:",X)}catch(a){console.error("Error recording completion:",a)}},W=a=>{const X=Math.floor(a/60),re=a%60;return`${X.toString().padStart(2,"0")}:${re.toString().padStart(2,"0")}`},i=()=>{switch(o){case 0:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Step 1: Choose a Network Scanning Technique"}),e.jsxs("p",{children:["Select a technique to begin reconnaissance on the target network (",l.targetNetwork,"):"]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 mt-4",children:l.scanTypes.map(a=>e.jsxs("button",{onClick:()=>m(a),className:`p-4 rounded-lg ${s?"bg-gray-800 hover:bg-gray-700":"bg-white hover:bg-gray-100"} border ${s?"border-gray-700":"border-gray-200"} text-left flex items-start`,children:[e.jsx("div",{className:"mr-4 mt-1",children:a.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold",children:a.name}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:a.description}),e.jsxs("div",{className:"flex mt-2 text-xs",children:[e.jsxs("span",{className:`mr-3 ${s?"text-gray-400":"text-gray-600"}`,children:["Purpose: ",a.effectiveness]}),e.jsxs("span",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Speed: ",a.speed]})]})]})]},a.id))})]});case 1:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Step 2: Analyze Ping Sweep Results"}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-800":"bg-gray-100"}`,children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(se,{className:"mr-2"}),e.jsx("span",{className:"font-bold",children:"Terminal"})]}),e.jsxs("div",{className:"font-mono text-sm",children:[e.jsxs("p",{children:["$ Running ping sweep on ",l.targetNetwork,"..."]}),e.jsx("p",{children:"$ Discovered hosts:"}),f.map((a,X)=>e.jsxs("p",{children:["$ ",a.host," is ",a.status," (",a.role,")"]},X))]})]}),e.jsx("p",{children:"Based on the ping sweep results, which host would you like to investigate further?"}),e.jsx("form",{onSubmit:j,className:"mt-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("input",{type:"text",value:C,onChange:a=>M(a.target.value),placeholder:"Enter IP address (e.g., 192.168.1.x)",className:`flex-grow p-2 rounded-l-lg ${s?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`}),e.jsx("button",{type:"submit",className:`px-4 py-2 rounded-r-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Scan"})]})}),n>0&&!D&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500 flex items-center`,children:[e.jsx(Z,{className:"mr-2"})," Try looking for a server that might have valuable services."]}),n>=2&&!D&&e.jsx("button",{onClick:()=>v(!0),className:`mt-2 text-sm ${s?"text-blue-400":"text-blue-600"}`,children:"Need a hint?"}),O&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-blue-900/30":"bg-blue-100"} text-blue-500 flex items-center`,children:[e.jsx(Q,{className:"mr-2"})," ",l.hints[0]]})]});case 2:return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-green-900/30":"bg-green-100"} text-green-500 flex items-center`,children:[e.jsx(B,{className:"mr-2"})," Port scan completed on ",l.vulnerableHost]}),e.jsx("h2",{className:"text-xl font-bold",children:"Step 3: Identify Vulnerable Ports"}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-800":"bg-gray-100"}`,children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(se,{className:"mr-2"}),e.jsx("span",{className:"font-bold",children:"Terminal"})]}),e.jsxs("div",{className:"font-mono text-sm",children:[e.jsxs("p",{children:["$ Running port scan on ",l.vulnerableHost,"..."]}),e.jsx("p",{children:"$ Discovered open ports:"}),f.map((a,X)=>e.jsxs("p",{children:["$ Port ",a.port,"/",a.state,": ",a.service]},X))]})]}),e.jsx("p",{children:"Based on the port scan results, which ports would you investigate for vulnerabilities? (Enter comma-separated list)"}),e.jsx("form",{onSubmit:L,className:"mt-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("input",{type:"text",value:T,onChange:a=>b(a.target.value),placeholder:"Enter ports (e.g., 22,80,443)",className:`flex-grow p-2 rounded-l-lg ${s?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`}),e.jsx("button",{type:"submit",className:`px-4 py-2 rounded-r-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Scan"})]})}),n>0&&!D&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500 flex items-center`,children:[e.jsx(Z,{className:"mr-2"})," Make sure to include all potentially vulnerable ports."]}),n>=2&&!D&&e.jsx("button",{onClick:()=>v(!0),className:`mt-2 text-sm ${s?"text-blue-400":"text-blue-600"}`,children:"Need a hint?"}),O&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-blue-900/30":"bg-blue-100"} text-blue-500 flex items-center`,children:[e.jsx(Q,{className:"mr-2"})," ",l.hints[1]]})]});default:return null}},S=()=>e.jsxs(J.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:`p-6 rounded-lg ${s?"bg-gray-800":"bg-white"} text-center`,children:[e.jsx("div",{className:"w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(ie,{className:"text-4xl text-green-500"})}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Network Scan Completed Successfully!"}),e.jsx("p",{className:`mb-4 ${s?"text-gray-400":"text-gray-600"}`,children:"You've successfully identified vulnerable services on the target network."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Time Taken"}),e.jsx("p",{className:"text-xl font-bold",children:W(E)})]}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Attempts"}),e.jsx("p",{className:"text-xl font-bold",children:n+1})]})]}),e.jsxs("div",{className:`p-4 mb-6 rounded-lg text-left ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"Vulnerability Scan Results"}),e.jsx("div",{className:"space-y-3",children:u.map((a,X)=>e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500`,children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(K,{className:"mr-2"}),e.jsxs("span",{className:"font-bold",children:[a.service," (Port ",a.port,")"]})]}),e.jsxs("p",{className:"mt-1",children:["Version: ",a.version]}),e.jsxs("p",{className:"mt-1",children:["Vulnerability: ",a.vulnerability]})]},X))})]}),e.jsxs("div",{className:`p-4 mb-6 rounded-lg text-left ${s?"bg-blue-900/30":"bg-blue-100"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"What You've Learned"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1",children:[e.jsx("li",{children:"How to perform network reconnaissance using ping sweeps"}),e.jsx("li",{children:"How to identify open ports on target systems"}),e.jsx("li",{children:"How to detect potentially vulnerable services"}),e.jsx("li",{children:"The importance of keeping services updated to prevent exploitation"})]})]}),e.jsx("button",{onClick:()=>window.location.href="/dashboard/start-hack",className:`px-6 py-3 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Return to Simulations"})]});return e.jsxs("div",{className:`max-w-4xl mx-auto ${s?"text-white":"text-gray-900"}`,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:l.title}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:l.description})]}),e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold",children:o+1}),e.jsx("div",{className:`h-1 w-16 mx-2 ${o>=1?"bg-blue-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-8 h-8 rounded-full ${o>=1?"bg-blue-500 text-white":"bg-gray-300 text-gray-600"} flex items-center justify-center font-bold`,children:"2"}),e.jsx("div",{className:`h-1 w-16 mx-2 ${o>=2?"bg-blue-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-8 h-8 rounded-full ${o>=2?"bg-blue-500 text-white":"bg-gray-300 text-gray-600"} flex items-center justify-center font-bold`,children:"3"})]}),e.jsxs("div",{className:`px-3 py-1 rounded-lg ${s?"bg-gray-800":"bg-gray-100"} flex items-center`,children:[e.jsx(te,{className:"mr-2"}),e.jsx("span",{children:W(E)})]})]}),e.jsx("div",{className:`p-6 rounded-lg ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} border`,children:U?S():i()})]})},Ee=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z(),{awardXP:_}=ae(),[o,d]=t.useState(0),[q,F]=t.useState(null),[C,M]=t.useState([]),[T,b]=t.useState([]),[D,p]=t.useState(!1),[n,y]=t.useState(0),[O,v]=t.useState(!1),[Y,V]=t.useState(!1),[E,R]=t.useState(0),[w,A]=t.useState(null),[$,x]=t.useState(!1),[U,k]=t.useState(!1),[f,g]=t.useState(null),u={title:"Social Engineering Simulation",description:"In this simulation, you'll learn how attackers use social engineering techniques to manipulate users into revealing sensitive information.",targetCompany:"TechCorp Industries",targetDepartment:"Finance Department",goal:"Gain access to financial records",hints:["Focus on employees with access to sensitive information","Look for information that could help bypass security measures","Consider which technique would be most effective for the target"],techniques:[{id:"phishing",name:"Phishing",description:"Create deceptive emails that appear to be from legitimate sources to trick users into revealing information",effectiveness:"High for mass targeting",difficulty:"Medium",icon:e.jsx(pe,{className:"text-blue-500"})},{id:"pretexting",name:"Pretexting",description:"Create a fabricated scenario to engage a victim and gain their trust to extract information",effectiveness:"High for specific targets",difficulty:"High",icon:e.jsx(ye,{className:"text-green-500"})},{id:"impersonation",name:"Impersonation",description:"Pose as someone with authority or right to know the information",effectiveness:"Very high for specific targets",difficulty:"Very high",icon:e.jsx(fe,{className:"text-red-500"})}],employees:[{id:1,name:"John Smith",position:"IT Administrator",access:"System admin access",vulnerability:"Helpful to colleagues"},{id:2,name:"Sarah Johnson",position:"Finance Director",access:"Financial records",vulnerability:"Very busy, delegates tasks"},{id:3,name:"Mike Williams",position:"Receptionist",access:"Visitor logs, basic company info",vulnerability:"New employee, eager to please"},{id:4,name:"Lisa Chen",position:"HR Manager",access:"Employee records",vulnerability:"Trusting personality"}],informationTypes:[{id:1,name:"Login credentials",value:"High",difficulty:"High"},{id:2,name:"Employee ID numbers",value:"Medium",difficulty:"Medium"},{id:3,name:"Organization structure",value:"Low",difficulty:"Low"},{id:4,name:"Financial system details",value:"High",difficulty:"High"},{id:5,name:"Personal information",value:"Medium",difficulty:"Medium"}]};t.useEffect(()=>{(async()=>{const{data:S}=await I.auth.getUser();g(S.user)})()},[]),t.useEffect(()=>{o>0&&!w&&(A(Date.now()),x(!0))},[o,w]),t.useEffect(()=>{let i;return $&&(i=setInterval(()=>{R(Math.floor((Date.now()-w)/1e3))},1e3)),()=>clearInterval(i)},[$,w]);const r=i=>{F(i),d(1)},N=i=>{const S=C.some(a=>a.id===i.id);M(S?C.filter(a=>a.id!==i.id):[...C,i])},H=()=>{const i=C.some(a=>a.position==="Finance Director"),S=C.some(a=>a.position==="IT Administrator");(i||S)&&C.length<=2?(p(!0),d(2)):(p(!1),y(n+1))},l=i=>{const S=T.some(a=>a.id===i.id);b(S?T.filter(a=>a.id!==i.id):[...T,i])},m=()=>{const i=T.some(a=>a.name==="Login credentials"),S=T.some(a=>a.name==="Financial system details");i&&S&&T.length<=3?(p(!0),x(!1),V(!0),k(!0),f&&j(),typeof _=="function"&&_("simulation_completed",{simulationId:P,timeTaken:E,attempts:n}),h&&h({success:!0,timeTaken:E,attempts:n})):(p(!1),y(n+1))},j=async()=>{try{const{data:i,error:S}=await I.from("practice_simulation_attempts").upsert({user_id:f.id,simulation_id:P||"social-engineering-simulation",is_completed:!0,completion_time:E,approach_score:Math.max(100-n*5,50),total_score:Math.max(100-n*5,50),started_at:new Date(w).toISOString(),completed_at:new Date().toISOString()},{onConflict:"user_id, simulation_id"});S&&console.error("Error recording completion:",S)}catch(i){console.error("Error recording completion:",i)}},L=i=>{const S=Math.floor(i/60),a=i%60;return`${S.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},c=()=>{switch(o){case 0:return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Step 1: Choose a Social Engineering Technique"}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Scenario"}),e.jsxs("p",{children:["Target: ",u.targetCompany,", ",u.targetDepartment]}),e.jsxs("p",{children:["Goal: ",u.goal]})]}),e.jsx("p",{children:"Select a technique to begin your social engineering attack:"}),e.jsx("div",{className:"grid grid-cols-1 gap-4 mt-4",children:u.techniques.map(i=>e.jsxs("button",{onClick:()=>r(i),className:`p-4 rounded-lg ${s?"bg-gray-800 hover:bg-gray-700":"bg-white hover:bg-gray-100"} border ${s?"border-gray-700":"border-gray-200"} text-left flex items-start`,children:[e.jsx("div",{className:"mr-4 mt-1",children:i.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold",children:i.name}),e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:i.description}),e.jsxs("div",{className:"flex mt-2 text-xs",children:[e.jsxs("span",{className:`mr-3 ${s?"text-gray-400":"text-gray-600"}`,children:["Effectiveness: ",i.effectiveness]}),e.jsxs("span",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Difficulty: ",i.difficulty]})]})]})]},i.id))})]});case 1:return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-green-900/30":"bg-green-100"} text-green-500 flex items-center`,children:[e.jsx(B,{className:"mr-2"})," You've selected ",q.name," as your technique."]}),e.jsx("h2",{className:"text-xl font-bold",children:"Step 2: Select Your Targets"}),e.jsxs("p",{children:["Choose which employees to target with your ",q.name," attack (select 1-2):"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:u.employees.map(i=>{const S=C.some(a=>a.id===i.id);return e.jsxs("button",{onClick:()=>N(i),className:`p-4 rounded-lg border text-left ${S?s?"bg-blue-900/30 border-blue-700":"bg-blue-100 border-blue-300":s?"bg-gray-800 border-gray-700 hover:bg-gray-700":"bg-white border-gray-200 hover:bg-gray-100"}`,children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h3",{className:"font-bold",children:i.name}),S&&e.jsx(B,{className:"text-green-500"})]}),e.jsxs("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["Position: ",i.position]}),e.jsxs("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["Access: ",i.access]}),e.jsxs("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["Vulnerability: ",i.vulnerability]})]},i.id)})}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsx("button",{onClick:H,disabled:C.length===0,className:`px-4 py-2 rounded-lg ${C.length===0?`${s?"bg-gray-700":"bg-gray-300"} cursor-not-allowed`:`${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`}`,children:"Continue"})}),n>0&&!D&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500 flex items-center`,children:[e.jsx(Z,{className:"mr-2"})," Reconsider your targets. Think about who has access to the information you need."]}),n>=2&&!D&&e.jsx("button",{onClick:()=>v(!0),className:`mt-2 text-sm ${s?"text-blue-400":"text-blue-600"}`,children:"Need a hint?"}),O&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-blue-900/30":"bg-blue-100"} text-blue-500 flex items-center`,children:[e.jsx(Q,{className:"mr-2"})," ",u.hints[0]]})]});case 2:return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-green-900/30":"bg-green-100"} text-green-500 flex items-center`,children:[e.jsx(B,{className:"mr-2"})," You've selected appropriate targets for your attack."]}),e.jsx("h2",{className:"text-xl font-bold",children:"Step 3: Select Information to Target"}),e.jsxs("p",{children:["What information will you try to obtain through your ",q.name," attack? (select 2-3)"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:u.informationTypes.map(i=>{const S=T.some(a=>a.id===i.id);return e.jsxs("button",{onClick:()=>l(i),className:`p-4 rounded-lg border text-left ${S?s?"bg-blue-900/30 border-blue-700":"bg-blue-100 border-blue-300":s?"bg-gray-800 border-gray-700 hover:bg-gray-700":"bg-white border-gray-200 hover:bg-gray-100"}`,children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h3",{className:"font-bold",children:i.name}),S&&e.jsx(B,{className:"text-green-500"})]}),e.jsxs("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["Value: ",i.value]}),e.jsxs("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["Difficulty to obtain: ",i.difficulty]})]},i.id)})}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsx("button",{onClick:m,disabled:T.length===0,className:`px-4 py-2 rounded-lg ${T.length===0?`${s?"bg-gray-700":"bg-gray-300"} cursor-not-allowed`:`${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`}`,children:"Complete Attack"})}),n>0&&!D&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-red-900/30":"bg-red-100"} text-red-500 flex items-center`,children:[e.jsx(Z,{className:"mr-2"})," Reconsider what information you need to achieve your goal."]}),n>=2&&!D&&e.jsx("button",{onClick:()=>v(!0),className:`mt-2 text-sm ${s?"text-blue-400":"text-blue-600"}`,children:"Need a hint?"}),O&&e.jsxs("div",{className:`p-3 rounded-lg ${s?"bg-blue-900/30":"bg-blue-100"} text-blue-500 flex items-center`,children:[e.jsx(Q,{className:"mr-2"})," ",u.hints[1]]})]});default:return null}},W=()=>e.jsxs(J.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:`p-6 rounded-lg ${s?"bg-gray-800":"bg-white"} text-center`,children:[e.jsx("div",{className:"w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(ie,{className:"text-4xl text-green-500"})}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Social Engineering Attack Simulated Successfully!"}),e.jsx("p",{className:`mb-4 ${s?"text-gray-400":"text-gray-600"}`,children:"You've successfully planned a social engineering attack to gain access to financial records."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Time Taken"}),e.jsx("p",{className:"text-xl font-bold",children:L(E)})]}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-gray-700":"bg-gray-100"}`,children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Attempts"}),e.jsx("p",{className:"text-xl font-bold",children:n+1})]})]}),e.jsxs("div",{className:`p-4 mb-6 rounded-lg text-left ${s?"bg-red-900/30":"bg-red-100"} text-red-500`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"Attack Summary"}),e.jsxs("p",{children:[e.jsx("strong",{children:"Technique:"})," ",q.name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Targets:"})," ",C.map(i=>i.name).join(", ")]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Information Sought:"})," ",T.map(i=>i.name).join(", ")]}),e.jsxs("div",{className:"mt-3 p-3 bg-yellow-100 text-yellow-800 rounded-lg",children:[e.jsx(K,{className:"inline-block mr-2"}),e.jsx("span",{className:"font-bold",children:"Important Note:"})," This simulation is for educational purposes only. Actual social engineering attacks are illegal and unethical."]})]}),e.jsxs("div",{className:`p-4 mb-6 rounded-lg text-left ${s?"bg-blue-900/30":"bg-blue-100"}`,children:[e.jsx("h3",{className:"font-bold text-lg mb-2",children:"What You've Learned"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1",children:[e.jsx("li",{children:"Different social engineering techniques and their effectiveness"}),e.jsx("li",{children:"How attackers select targets based on access and vulnerabilities"}),e.jsx("li",{children:"What types of information attackers typically seek"}),e.jsx("li",{children:"How to recognize and defend against social engineering attacks"})]})]}),e.jsx("button",{onClick:()=>window.location.href="/dashboard/start-hack",className:`px-6 py-3 rounded-lg ${s?"bg-blue-600 hover:bg-blue-700":"bg-blue-500 hover:bg-blue-600"} text-white`,children:"Return to Simulations"})]});return e.jsxs("div",{className:`max-w-4xl mx-auto ${s?"text-white":"text-gray-900"}`,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:u.title}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:u.description})]}),e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold",children:o+1}),e.jsx("div",{className:`h-1 w-16 mx-2 ${o>=1?"bg-blue-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-8 h-8 rounded-full ${o>=1?"bg-blue-500 text-white":"bg-gray-300 text-gray-600"} flex items-center justify-center font-bold`,children:"2"}),e.jsx("div",{className:`h-1 w-16 mx-2 ${o>=2?"bg-blue-500":"bg-gray-300"}`}),e.jsx("div",{className:`w-8 h-8 rounded-full ${o>=2?"bg-blue-500 text-white":"bg-gray-300 text-gray-600"} flex items-center justify-center font-bold`,children:"3"})]}),e.jsxs("div",{className:`px-3 py-1 rounded-lg ${s?"bg-gray-800":"bg-gray-100"} flex items-center`,children:[e.jsx(te,{className:"mr-2"}),e.jsx("span",{children:L(E)})]})]}),e.jsx("div",{className:`p-6 rounded-lg ${s?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} border`,children:U?W():c()})]})},Pe=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z();return e.jsxs("div",{className:`p-6 rounded-lg ${s?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Process Scripting Simulator"}),e.jsx("p",{className:"mb-6",children:"This simulation is currently under development. Check back soon!"})]})},Ae=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z();return e.jsxs("div",{className:`p-6 rounded-lg ${s?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Command Injection Simulator"}),e.jsx("p",{className:"mb-6",children:"This simulation is currently under development. Check back soon!"})]})},Le=({onComplete:h,simulationId:P})=>{const{darkMode:s}=z();return e.jsxs("div",{className:`p-6 rounded-lg ${s?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Privilege Escalation Simulator"}),e.jsx("p",{className:"mb-6",children:"This simulation is currently under development. Check back soon!"})]})},He=()=>{const{darkMode:h}=z(),{subscriptionLevel:P}=je(),s=ve(),{simulationId:_}=Ne(),[o,d]=t.useState([]),[q,F]=t.useState([]),[C,M]=t.useState(!0),[T,b]=t.useState("all"),[D,p]=t.useState(""),[n,y]=t.useState(null),[O,v]=t.useState([]),[Y,V]=t.useState("all"),[E,R]=t.useState([]),[w,A]=t.useState("all"),[$,x]=t.useState(null),[U,k]=t.useState(!1);t.useEffect(()=>{(async()=>{try{const{data:{user:N}}=await I.auth.getUser();x(N)}catch(N){console.error("Error getting current user:",N)}finally{k(!0)}})()},[]),t.useEffect(()=>{(async()=>{var N;try{M(!0);const H=[{id:"web-security",name:"Web Security"},{id:"network-security",name:"Network Security"},{id:"cryptography",name:"Cryptography"},{id:"human-security",name:"Human Security"}],l=[{id:"beginner",name:"Beginner"},{id:"intermediate",name:"Intermediate"},{id:"advanced",name:"Advanced"},{id:"expert",name:"Expert"}];try{const{data:m,error:j}=await I.from("practice_simulation_categories").select("*").order("display_order");!j&&(m==null?void 0:m.length)>0?v(m):v(H);const{data:L,error:c}=await I.from("practice_simulation_difficulty_levels").select("*").order("display_order");!c&&(L==null?void 0:L.length)>0?R(L):R(l);const{data:W,error:i}=await I.from("practice_simulations").select(`
              *,
              category:category_id(id, name),
              difficulty:difficulty_id(id, name)
            `).eq("is_active",!0).order("created_at");d(ee);const{data:S}=await I.auth.getUser();if((N=S==null?void 0:S.user)!=null&&N.id){const{data:a,error:X}=await I.from("practice_simulation_attempts").select("*").eq("user_id",S.user.id);X||F(a||[])}}catch(m){console.error("Database error:",m),v(H),R(l),d(ee)}if(_){const m=ee.find(j=>j.id===_||j.slug===_);m&&y(m)}M(!1)}catch(H){console.error("Error fetching simulations:",H),d(ee),M(!1)}})()},[_]);const f=o.filter(r=>{if(!r||!r.title||!r.description)return!1;const N=r.title.toLowerCase().includes(D.toLowerCase())||r.description.toLowerCase().includes(D.toLowerCase()),H=Y==="all"||r.category&&r.category.id===Y,l=w==="all"||r.difficulty&&r.difficulty.id===w,m=T==="all"||T==="completed"&&q.some(j=>j.simulation_id===r.id&&j.is_completed)||T==="in-progress"&&q.some(j=>j.simulation_id===r.id&&!j.is_completed)||T==="not-started"&&!q.some(j=>j.simulation_id===r.id);return N&&H&&l&&m}),g=async r=>{if(console.log("Simulation completed:",r),n){const{data:N}=await I.auth.getUser();if(N&&N.id){const{data:H}=await I.from("practice_simulation_attempts").select("*").eq("user_id",N.id);F(H||[])}}},u=r=>{switch(r.simulation_type){case"sql_injection":return e.jsx($e,{onComplete:g,simulationId:r.id});case"ddos":return e.jsx(Ce,{onComplete:g,simulationId:r.id});case"xss":return e.jsx(Te,{onComplete:g,simulationId:r.id});case"password_cracking":return e.jsx(Ie,{onComplete:g,simulationId:r.id});case"network_scanning":return e.jsx(De,{onComplete:g,simulationId:r.id});case"social_engineering":return e.jsx(Ee,{onComplete:g,simulationId:r.id});case"process_scripting":return e.jsx(Pe,{onComplete:g,simulationId:r.id});case"command_injection":return e.jsx(Ae,{onComplete:g,simulationId:r.id});case"privilege_escalation":return e.jsx(Le,{onComplete:g,simulationId:r.id});default:return e.jsxs("div",{className:`p-6 rounded-lg ${h?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4",children:["Coming Soon: ",r.title]}),e.jsx("p",{className:"mb-6",children:"This simulation is currently under development and will be available soon."}),e.jsxs("div",{className:"bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6",children:[e.jsx("p",{className:"font-bold",children:"What you'll learn in this simulation:"}),e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsxs("li",{children:["Understanding ",r.title.toLowerCase()," concepts and techniques"]}),e.jsx("li",{children:"Identifying common vulnerabilities and attack vectors"}),e.jsx("li",{children:"Practical hands-on exercises with guided instructions"}),e.jsx("li",{children:"Remediation strategies and best practices"})]})]}),e.jsx("button",{onClick:()=>{y(null),s("/dashboard/start-hack")},className:`px-4 py-2 rounded-lg ${h?"bg-blue-600":"bg-blue-500"} text-white hover:bg-blue-700`,children:"Back to Simulations"})]})}};return n?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("button",{onClick:()=>{y(null),s("/dashboard/start-hack")},className:`mb-4 flex items-center ${h?"text-gray-300 hover:text-white":"text-gray-700 hover:text-gray-900"}`,children:[e.jsx(Se,{className:"mr-2"})," Back to Simulations"]}),e.jsx("h1",{className:"text-2xl font-bold mb-6",children:n.title}),u(n)]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Start Hack"}),e.jsx("p",{className:`mb-6 ${h?"text-gray-400":"text-gray-600"}`,children:"Practice cybersecurity techniques in realistic simulations"}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ce,{className:`${h?"text-gray-400":"text-gray-500"}`})}),e.jsx("input",{type:"text",placeholder:"Search simulations...",value:D,onChange:r=>p(r.target.value),className:`pl-10 pr-4 py-2 w-full rounded-lg ${h?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("select",{value:Y,onChange:r=>V(r.target.value),className:`px-4 py-2 rounded-lg ${h?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,children:[e.jsx("option",{value:"all",children:"All Categories"}),O.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]}),e.jsxs("select",{value:w,onChange:r=>A(r.target.value),className:`px-4 py-2 rounded-lg ${h?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,children:[e.jsx("option",{value:"all",children:"All Difficulties"}),E.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]}),e.jsxs("select",{value:T,onChange:r=>b(r.target.value),className:`px-4 py-2 rounded-lg ${h?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,children:[e.jsx("option",{value:"all",children:"All Simulations"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"in-progress",children:"In Progress"}),e.jsx("option",{value:"not-started",children:"Not Started"})]})]})]}),C?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):f.length===0?e.jsxs("div",{className:`text-center py-12 ${h?"text-gray-400":"text-gray-600"}`,children:[e.jsx(Q,{className:"mx-auto text-4xl mb-4"}),e.jsx("p",{className:"text-xl",children:"No simulations found matching your filters."}),e.jsx("p",{className:"mt-2",children:"Try adjusting your search criteria or filters."})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(r=>{var m,j,L,c,W;const N=q.find(i=>i.simulation_id===r.id),H=N==null?void 0:N.is_completed,l=N&&!H;return r.is_premium,r.is_business,e.jsxs(J.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:`rounded-lg overflow-hidden border ${h?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"}`,children:[e.jsxs("div",{className:`p-4 border-b ${h?"border-gray-800":"border-gray-200"}`,children:[e.jsx("div",{className:"flex justify-between items-start",children:e.jsx("h3",{className:"text-lg font-bold",children:r.title})}),e.jsxs("div",{className:"flex items-center mt-2 text-sm",children:[e.jsx("span",{className:`px-2 py-0.5 rounded ${((m=r.difficulty)==null?void 0:m.name)==="Beginner"?"bg-green-100 text-green-800":((j=r.difficulty)==null?void 0:j.name)==="Intermediate"?"bg-blue-100 text-blue-800":((L=r.difficulty)==null?void 0:L.name)==="Advanced"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:((c=r.difficulty)==null?void 0:c.name)||"Unknown"}),e.jsx("span",{className:`ml-2 px-2 py-0.5 rounded ${h?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-800"}`,children:((W=r.category)==null?void 0:W.name)||"Uncategorized"}),r.estimated_time&&e.jsxs("span",{className:`ml-2 flex items-center ${h?"text-gray-400":"text-gray-600"}`,children:[e.jsx(te,{className:"mr-1"})," ",r.estimated_time," min"]})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("p",{className:`mb-4 ${h?"text-gray-400":"text-gray-600"}`,children:r.description}),e.jsxs("div",{className:"flex justify-center mb-4",children:[r.simulation_type==="sql_injection"&&e.jsx(le,{className:"text-5xl text-blue-500"}),r.simulation_type==="ddos"&&e.jsx(oe,{className:"text-5xl text-red-500"}),r.simulation_type==="xss"&&e.jsx(ne,{className:"text-5xl text-green-500"}),r.simulation_type==="password_cracking"&&e.jsx(me,{className:"text-5xl text-yellow-500"}),r.simulation_type==="network_scanning"&&e.jsx(de,{className:"text-5xl text-purple-500"}),r.simulation_type==="social_engineering"&&e.jsx(we,{className:"text-5xl text-orange-500"}),r.simulation_type==="process_scripting"&&e.jsx(se,{className:"text-5xl text-cyan-500"}),r.simulation_type==="command_injection"&&e.jsx(ne,{className:"text-5xl text-pink-500"}),r.simulation_type==="privilege_escalation"&&e.jsx(ke,{className:"text-5xl text-indigo-500"}),!["sql_injection","ddos","xss","password_cracking","network_scanning","social_engineering","process_scripting","command_injection","privilege_escalation"].includes(r.simulation_type)&&e.jsx(_e,{className:"text-5xl text-gray-500"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[H&&e.jsxs("span",{className:"flex items-center text-green-500",children:[e.jsx(B,{className:"mr-1"})," Completed"]}),l&&e.jsxs("span",{className:"flex items-center text-blue-500",children:[e.jsx(Q,{className:"mr-1"})," In Progress"]})]}),e.jsx("button",{onClick:()=>{y(r),s(`/dashboard/start-hack/${r.slug||r.id}`)},className:`px-4 py-2 rounded-lg ${h?"bg-blue-600":"bg-blue-500"} text-white hover:bg-blue-700`,children:H?"Practice Again":l?"Continue":"Start"})]})]})]},r.id)})})]})},ee=[{id:"sql-injection-basics",slug:"sql-injection-basics",title:"SQL Injection Basics",description:"Learn the fundamentals of SQL injection by exploiting a vulnerable login form.",simulation_type:"sql_injection",category:{id:"web-security",name:"Web Security"},difficulty:{id:"beginner",name:"Beginner"},estimated_time:20,is_premium:!1,is_business:!1},{id:"ddos-attack-simulation",slug:"ddos-attack-simulation",title:"DDoS Attack Simulation",description:"Learn how DDoS attacks work by simulating an attack on a vulnerable server.",simulation_type:"ddos",category:{id:"network-security",name:"Network Security"},difficulty:{id:"intermediate",name:"Intermediate"},estimated_time:30,is_premium:!1,is_business:!1},{id:"xss-vulnerability-basics",slug:"xss-vulnerability-basics",title:"XSS Vulnerability Basics",description:"Learn how Cross-Site Scripting (XSS) vulnerabilities work by exploiting a vulnerable comment section.",simulation_type:"xss",category:{id:"web-security",name:"Web Security"},difficulty:{id:"beginner",name:"Beginner"},estimated_time:25,is_premium:!1,is_business:!1},{id:"password-cracking-simulation",slug:"password-cracking-simulation",title:"Password Cracking Simulation",description:"Learn how to crack passwords using various techniques including dictionary attacks and brute force methods.",simulation_type:"password_cracking",category:{id:"cryptography",name:"Cryptography"},difficulty:{id:"beginner",name:"Beginner"},estimated_time:30,is_premium:!1,is_business:!1},{id:"network-scanning-basics",slug:"network-scanning-basics",title:"Network Scanning Basics",description:"Learn how to perform network reconnaissance and identify vulnerable services on a network.",simulation_type:"network_scanning",category:{id:"network-security",name:"Network Security"},difficulty:{id:"intermediate",name:"Intermediate"},estimated_time:35,is_premium:!1,is_business:!1},{id:"social-engineering-simulation",slug:"social-engineering-simulation",title:"Social Engineering Simulation",description:"Learn how attackers use social engineering techniques to manipulate users into revealing sensitive information.",simulation_type:"social_engineering",category:{id:"human-security",name:"Human Security"},difficulty:{id:"beginner",name:"Beginner"},estimated_time:25,is_premium:!1,is_business:!1},{id:"advanced-sql-injection",slug:"advanced-sql-injection",title:"Advanced SQL Injection",description:"Master advanced SQL injection techniques including blind SQL injection and time-based attacks.",simulation_type:"sql_injection",category:{id:"web-security",name:"Web Security"},difficulty:{id:"advanced",name:"Advanced"},estimated_time:45,is_premium:!0,is_business:!1},{id:"enterprise-ddos-protection",slug:"enterprise-ddos-protection",title:"Enterprise DDoS Protection",description:"Learn how to implement and test enterprise-grade DDoS protection measures.",simulation_type:"ddos",category:{id:"network-security",name:"Network Security"},difficulty:{id:"expert",name:"Expert"},estimated_time:60,is_premium:!1,is_business:!0},{id:"advanced-xss-attacks",slug:"advanced-xss-attacks",title:"Advanced XSS Attacks",description:"Master advanced Cross-Site Scripting techniques including DOM-based XSS and filter evasion methods.",simulation_type:"xss",category:{id:"web-security",name:"Web Security"},difficulty:{id:"advanced",name:"Advanced"},estimated_time:40,is_premium:!0,is_business:!1},{id:"wifi-hacking-simulation",slug:"wifi-hacking-simulation",title:"WiFi Hacking Simulation",description:"Learn how to identify and exploit vulnerabilities in wireless networks using common tools and techniques.",simulation_type:"network_scanning",category:{id:"network-security",name:"Network Security"},difficulty:{id:"intermediate",name:"Intermediate"},estimated_time:45,is_premium:!1,is_business:!1},{id:"phishing-campaign-simulation",slug:"phishing-campaign-simulation",title:"Phishing Campaign Simulation",description:"Learn how to identify and create phishing campaigns, and understand the psychology behind successful attacks.",simulation_type:"social_engineering",category:{id:"human-security",name:"Human Security"},difficulty:{id:"intermediate",name:"Intermediate"},estimated_time:35,is_premium:!1,is_business:!1},{id:"hash-cracking-advanced",slug:"hash-cracking-advanced",title:"Advanced Hash Cracking",description:"Master advanced password hash cracking techniques using specialized tools and hardware acceleration.",simulation_type:"password_cracking",category:{id:"cryptography",name:"Cryptography"},difficulty:{id:"advanced",name:"Advanced"},estimated_time:50,is_premium:!1,is_business:!1},{id:"process-scripting-attack",slug:"process-scripting-attack",title:"Process Scripting Vulnerabilities",description:"Learn how to identify and exploit process scripting vulnerabilities in various platforms and systems.",simulation_type:"process_scripting",category:{id:"system-security",name:"System Security"},difficulty:{id:"intermediate",name:"Intermediate"},estimated_time:40,is_premium:!1,is_business:!1},{id:"command-injection-basics",slug:"command-injection-basics",title:"Command Injection Basics",description:"Learn how to identify and exploit command injection vulnerabilities in web applications and system interfaces.",simulation_type:"command_injection",category:{id:"web-security",name:"Web Security"},difficulty:{id:"beginner",name:"Beginner"},estimated_time:30,is_premium:!1,is_business:!1},{id:"privilege-escalation-linux",slug:"privilege-escalation-linux",title:"Linux Privilege Escalation",description:"Learn techniques to escalate privileges on Linux systems by exploiting common misconfigurations and vulnerabilities.",simulation_type:"privilege_escalation",category:{id:"system-security",name:"System Security"},difficulty:{id:"intermediate",name:"Intermediate"},estimated_time:45,is_premium:!1,is_business:!1}];export{He as S};
