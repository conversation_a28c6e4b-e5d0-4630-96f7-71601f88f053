import{bS as Ti,bO as ce,r as I,ah as j,u as rn,j as y,bT as Qm,$ as yi,bd as eg,bU as Hi,bV as tg,b as dp,m as rg,U as ol,g as Vc,x as sl,a5 as cl,be as ul,bM as ng,bW as Xc,h as Yc,L as ig,z as ag,bX as It,aD as og,c as sg,a1 as cg,aN as qo,a0 as ug,bY as Ho,bZ as lg,au as fg,a4 as hp,t as pp,bx as dg,aV as hg,aY as pg,N as vg,v as ll,aH as yg,ax as mg,aR as gg}from"./index-CVvVjHWF.js";import{P as ie}from"./index-Cc2BstNK.js";function vp(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=vp(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Q(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=vp(e))&&(n&&(n+=" "),n+=t);return n}var bg=Array.isArray,Fe=bg,xg=typeof Ti=="object"&&Ti&&Ti.Object===Object&&Ti,yp=xg,wg=yp,Og=typeof self=="object"&&self&&self.Object===Object&&self,Ag=wg||Og||Function("return this")(),yt=Ag,Sg=yt,Pg=Sg.Symbol,mi=Pg,fl=mi,mp=Object.prototype,_g=mp.hasOwnProperty,jg=mp.toString,mn=fl?fl.toStringTag:void 0;function $g(e){var t=_g.call(e,mn),r=e[mn];try{e[mn]=void 0;var n=!0}catch{}var i=jg.call(e);return n&&(t?e[mn]=r:delete e[mn]),i}var Tg=$g,Eg=Object.prototype,Ng=Eg.toString;function Cg(e){return Ng.call(e)}var Mg=Cg,dl=mi,kg=Tg,Ig=Mg,Dg="[object Null]",Lg="[object Undefined]",hl=dl?dl.toStringTag:void 0;function Rg(e){return e==null?e===void 0?Lg:Dg:hl&&hl in Object(e)?kg(e):Ig(e)}var Et=Rg;function Bg(e){return e!=null&&typeof e=="object"}var Nt=Bg,Fg=Et,Ug=Nt,Wg="[object Symbol]";function zg(e){return typeof e=="symbol"||Ug(e)&&Fg(e)==Wg}var nn=zg,qg=Fe,Hg=nn,Kg=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Gg=/^\w*$/;function Vg(e,t){if(qg(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Hg(e)?!0:Gg.test(e)||!Kg.test(e)||t!=null&&e in Object(t)}var Zc=Vg;function Xg(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Ft=Xg;const an=ce(Ft);var Yg=Et,Zg=Ft,Jg="[object AsyncFunction]",Qg="[object Function]",eb="[object GeneratorFunction]",tb="[object Proxy]";function rb(e){if(!Zg(e))return!1;var t=Yg(e);return t==Qg||t==eb||t==Jg||t==tb}var Jc=rb;const Y=ce(Jc);var nb=yt,ib=nb["__core-js_shared__"],ab=ib,Ko=ab,pl=function(){var e=/[^.]+$/.exec(Ko&&Ko.keys&&Ko.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function ob(e){return!!pl&&pl in e}var sb=ob,cb=Function.prototype,ub=cb.toString;function lb(e){if(e!=null){try{return ub.call(e)}catch{}try{return e+""}catch{}}return""}var gp=lb,fb=Jc,db=sb,hb=Ft,pb=gp,vb=/[\\^$.*+?()[\]{}|]/g,yb=/^\[object .+?Constructor\]$/,mb=Function.prototype,gb=Object.prototype,bb=mb.toString,xb=gb.hasOwnProperty,wb=RegExp("^"+bb.call(xb).replace(vb,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Ob(e){if(!hb(e)||db(e))return!1;var t=fb(e)?wb:yb;return t.test(pb(e))}var Ab=Ob;function Sb(e,t){return e==null?void 0:e[t]}var Pb=Sb,_b=Ab,jb=Pb;function $b(e,t){var r=jb(e,t);return _b(r)?r:void 0}var hr=$b,Tb=hr,Eb=Tb(Object,"create"),Xa=Eb,vl=Xa;function Nb(){this.__data__=vl?vl(null):{},this.size=0}var Cb=Nb;function Mb(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var kb=Mb,Ib=Xa,Db="__lodash_hash_undefined__",Lb=Object.prototype,Rb=Lb.hasOwnProperty;function Bb(e){var t=this.__data__;if(Ib){var r=t[e];return r===Db?void 0:r}return Rb.call(t,e)?t[e]:void 0}var Fb=Bb,Ub=Xa,Wb=Object.prototype,zb=Wb.hasOwnProperty;function qb(e){var t=this.__data__;return Ub?t[e]!==void 0:zb.call(t,e)}var Hb=qb,Kb=Xa,Gb="__lodash_hash_undefined__";function Vb(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Kb&&t===void 0?Gb:t,this}var Xb=Vb,Yb=Cb,Zb=kb,Jb=Fb,Qb=Hb,e0=Xb;function on(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}on.prototype.clear=Yb;on.prototype.delete=Zb;on.prototype.get=Jb;on.prototype.has=Qb;on.prototype.set=e0;var t0=on;function r0(){this.__data__=[],this.size=0}var n0=r0;function i0(e,t){return e===t||e!==e&&t!==t}var Qc=i0,a0=Qc;function o0(e,t){for(var r=e.length;r--;)if(a0(e[r][0],t))return r;return-1}var Ya=o0,s0=Ya,c0=Array.prototype,u0=c0.splice;function l0(e){var t=this.__data__,r=s0(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():u0.call(t,r,1),--this.size,!0}var f0=l0,d0=Ya;function h0(e){var t=this.__data__,r=d0(t,e);return r<0?void 0:t[r][1]}var p0=h0,v0=Ya;function y0(e){return v0(this.__data__,e)>-1}var m0=y0,g0=Ya;function b0(e,t){var r=this.__data__,n=g0(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var x0=b0,w0=n0,O0=f0,A0=p0,S0=m0,P0=x0;function sn(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}sn.prototype.clear=w0;sn.prototype.delete=O0;sn.prototype.get=A0;sn.prototype.has=S0;sn.prototype.set=P0;var Za=sn,_0=hr,j0=yt,$0=_0(j0,"Map"),eu=$0,yl=t0,T0=Za,E0=eu;function N0(){this.size=0,this.__data__={hash:new yl,map:new(E0||T0),string:new yl}}var C0=N0;function M0(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var k0=M0,I0=k0;function D0(e,t){var r=e.__data__;return I0(t)?r[typeof t=="string"?"string":"hash"]:r.map}var Ja=D0,L0=Ja;function R0(e){var t=L0(this,e).delete(e);return this.size-=t?1:0,t}var B0=R0,F0=Ja;function U0(e){return F0(this,e).get(e)}var W0=U0,z0=Ja;function q0(e){return z0(this,e).has(e)}var H0=q0,K0=Ja;function G0(e,t){var r=K0(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var V0=G0,X0=C0,Y0=B0,Z0=W0,J0=H0,Q0=V0;function cn(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}cn.prototype.clear=X0;cn.prototype.delete=Y0;cn.prototype.get=Z0;cn.prototype.has=J0;cn.prototype.set=Q0;var tu=cn,bp=tu,ex="Expected a function";function ru(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(ex);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(ru.Cache||bp),r}ru.Cache=bp;var xp=ru;const tx=ce(xp);var rx=xp,nx=500;function ix(e){var t=rx(e,function(n){return r.size===nx&&r.clear(),n}),r=t.cache;return t}var ax=ix,ox=ax,sx=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,cx=/\\(\\)?/g,ux=ox(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(sx,function(r,n,i,a){t.push(i?a.replace(cx,"$1"):n||r)}),t}),lx=ux;function fx(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var nu=fx,ml=mi,dx=nu,hx=Fe,px=nn,gl=ml?ml.prototype:void 0,bl=gl?gl.toString:void 0;function wp(e){if(typeof e=="string")return e;if(hx(e))return dx(e,wp)+"";if(px(e))return bl?bl.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var vx=wp,yx=vx;function mx(e){return e==null?"":yx(e)}var Op=mx,gx=Fe,bx=Zc,xx=lx,wx=Op;function Ox(e,t){return gx(e)?e:bx(e,t)?[e]:xx(wx(e))}var Ap=Ox,Ax=nn;function Sx(e){if(typeof e=="string"||Ax(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Qa=Sx,Px=Ap,_x=Qa;function jx(e,t){t=Px(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[_x(t[r++])];return r&&r==n?e:void 0}var iu=jx,$x=iu;function Tx(e,t,r){var n=e==null?void 0:$x(e,t);return n===void 0?r:n}var Sp=Tx;const Ve=ce(Sp);function Ex(e){return e==null}var Nx=Ex;const Z=ce(Nx);var Cx=Et,Mx=Fe,kx=Nt,Ix="[object String]";function Dx(e){return typeof e=="string"||!Mx(e)&&kx(e)&&Cx(e)==Ix}var Lx=Dx;const gi=ce(Lx);var Pp={exports:{}},ae={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var au=Symbol.for("react.element"),ou=Symbol.for("react.portal"),eo=Symbol.for("react.fragment"),to=Symbol.for("react.strict_mode"),ro=Symbol.for("react.profiler"),no=Symbol.for("react.provider"),io=Symbol.for("react.context"),Rx=Symbol.for("react.server_context"),ao=Symbol.for("react.forward_ref"),oo=Symbol.for("react.suspense"),so=Symbol.for("react.suspense_list"),co=Symbol.for("react.memo"),uo=Symbol.for("react.lazy"),Bx=Symbol.for("react.offscreen"),_p;_p=Symbol.for("react.module.reference");function tt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case au:switch(e=e.type,e){case eo:case ro:case to:case oo:case so:return e;default:switch(e=e&&e.$$typeof,e){case Rx:case io:case ao:case uo:case co:case no:return e;default:return t}}case ou:return t}}}ae.ContextConsumer=io;ae.ContextProvider=no;ae.Element=au;ae.ForwardRef=ao;ae.Fragment=eo;ae.Lazy=uo;ae.Memo=co;ae.Portal=ou;ae.Profiler=ro;ae.StrictMode=to;ae.Suspense=oo;ae.SuspenseList=so;ae.isAsyncMode=function(){return!1};ae.isConcurrentMode=function(){return!1};ae.isContextConsumer=function(e){return tt(e)===io};ae.isContextProvider=function(e){return tt(e)===no};ae.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===au};ae.isForwardRef=function(e){return tt(e)===ao};ae.isFragment=function(e){return tt(e)===eo};ae.isLazy=function(e){return tt(e)===uo};ae.isMemo=function(e){return tt(e)===co};ae.isPortal=function(e){return tt(e)===ou};ae.isProfiler=function(e){return tt(e)===ro};ae.isStrictMode=function(e){return tt(e)===to};ae.isSuspense=function(e){return tt(e)===oo};ae.isSuspenseList=function(e){return tt(e)===so};ae.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===eo||e===ro||e===to||e===oo||e===so||e===Bx||typeof e=="object"&&e!==null&&(e.$$typeof===uo||e.$$typeof===co||e.$$typeof===no||e.$$typeof===io||e.$$typeof===ao||e.$$typeof===_p||e.getModuleId!==void 0)};ae.typeOf=tt;Pp.exports=ae;var Fx=Pp.exports,Ux=Et,Wx=Nt,zx="[object Number]";function qx(e){return typeof e=="number"||Wx(e)&&Ux(e)==zx}var jp=qx;const Hx=ce(jp);var Kx=jp;function Gx(e){return Kx(e)&&e!=+e}var Vx=Gx;const bi=ce(Vx);var ke=function(t){return t===0?0:t>0?1:-1},er=function(t){return gi(t)&&t.indexOf("%")===t.length-1},B=function(t){return Hx(t)&&!bi(t)},Pe=function(t){return B(t)||gi(t)},Xx=0,un=function(t){var r=++Xx;return"".concat(t||"").concat(r)},Ie=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!B(t)&&!gi(t))return n;var a;if(er(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return bi(a)&&(a=n),i&&a>r&&(a=r),a},Lt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},Yx=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},Ke=function(t,r){return B(t)&&B(r)?function(n){return t+n*(r-t)}:function(){return r}};function Vi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ve(n,t))===r})}function $r(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function xs(e){"@babel/helpers - typeof";return xs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xs(e)}var Zx=["viewBox","children"],Jx=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],xl=["points","pathLength"],Go={svg:Zx,polygon:xl,polyline:xl},su=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Xi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(I.isValidElement(t)&&(n=t.props),!an(n))return null;var i={};return Object.keys(n).forEach(function(a){su.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},Qx=function(t,r,n){return function(i){return t(r,n,i),null}},cr=function(t,r,n){if(!an(t)||xs(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];su.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=Qx(o,r,n))}),i},ew=["children"],tw=["children"];function wl(e,t){if(e==null)return{};var r=rw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function rw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ws(e){"@babel/helpers - typeof";return ws=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ws(e)}var Ol={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},At=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Al=null,Vo=null,cu=function e(t){if(t===Al&&Array.isArray(Vo))return Vo;var r=[];return I.Children.forEach(t,function(n){Z(n)||(Fx.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Vo=r,Al=t,r};function Xe(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return At(i)}):n=[At(t)],cu(e).forEach(function(i){var a=Ve(i,"type.displayName")||Ve(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function qe(e,t){var r=Xe(e,t);return r&&r[0]}var Sl=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!B(n)||n<=0||!B(i)||i<=0)},nw=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],iw=function(t){return t&&t.type&&gi(t.type)&&nw.indexOf(t.type)>=0},aw=function(t){return t&&ws(t)==="object"&&"clipDot"in t},ow=function(t,r,n,i){var a,o=(a=Go==null?void 0:Go[i])!==null&&a!==void 0?a:[];return!Y(t)&&(i&&o.includes(r)||Jx.includes(r))||n&&su.includes(r)},K=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(I.isValidElement(t)&&(i=t.props),!an(i))return null;var a={};return Object.keys(i).forEach(function(o){var s;ow((s=i)===null||s===void 0?void 0:s[o],o,r,n)&&(a[o]=i[o])}),a},Os=function e(t,r){if(t===r)return!0;var n=I.Children.count(t);if(n!==I.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Pl(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Pl(a,o))return!1}return!0},Pl=function(t,r){if(Z(t)&&Z(r))return!0;if(!Z(t)&&!Z(r)){var n=t.props||{},i=n.children,a=wl(n,ew),o=r.props||{},s=o.children,c=wl(o,tw);return i&&s?$r(a,c)&&Os(i,s):!i&&!s?$r(a,c):!1}return!1},_l=function(t,r){var n=[],i={};return cu(t).forEach(function(a,o){if(iw(a))n.push(a);else if(a){var s=At(a.type),c=r[s]||{},u=c.handler,f=c.once;if(u&&(!f||!i[s])){var l=u(a,s,o);n.push(l),i[s]=!0}}}),n},sw=function(t){var r=t&&t.type;return r&&Ol[r]?Ol[r]:null},cw=function(t,r){return cu(r).indexOf(t)},uw=["children","width","height","viewBox","className","style","title","desc"];function As(){return As=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},As.apply(this,arguments)}function lw(e,t){if(e==null)return{};var r=fw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ss(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,s=e.title,c=e.desc,u=lw(e,uw),f=i||{width:r,height:n,x:0,y:0},l=Q("recharts-surface",a);return j.createElement("svg",As({},K(u,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),j.createElement("title",null,s),j.createElement("desc",null,c),t)}var dw=["children","className"];function Ps(){return Ps=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ps.apply(this,arguments)}function hw(e,t){if(e==null)return{};var r=pw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function pw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var re=j.forwardRef(function(e,t){var r=e.children,n=e.className,i=hw(e,dw),a=Q("recharts-layer",n);return j.createElement("g",Ps({className:a},K(i,!0),{ref:t}),r)}),ct=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function vw(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var yw=vw,mw=yw;function gw(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:mw(e,t,r)}var bw=gw,xw="\\ud800-\\udfff",ww="\\u0300-\\u036f",Ow="\\ufe20-\\ufe2f",Aw="\\u20d0-\\u20ff",Sw=ww+Ow+Aw,Pw="\\ufe0e\\ufe0f",_w="\\u200d",jw=RegExp("["+_w+xw+Sw+Pw+"]");function $w(e){return jw.test(e)}var $p=$w;function Tw(e){return e.split("")}var Ew=Tw,Tp="\\ud800-\\udfff",Nw="\\u0300-\\u036f",Cw="\\ufe20-\\ufe2f",Mw="\\u20d0-\\u20ff",kw=Nw+Cw+Mw,Iw="\\ufe0e\\ufe0f",Dw="["+Tp+"]",_s="["+kw+"]",js="\\ud83c[\\udffb-\\udfff]",Lw="(?:"+_s+"|"+js+")",Ep="[^"+Tp+"]",Np="(?:\\ud83c[\\udde6-\\uddff]){2}",Cp="[\\ud800-\\udbff][\\udc00-\\udfff]",Rw="\\u200d",Mp=Lw+"?",kp="["+Iw+"]?",Bw="(?:"+Rw+"(?:"+[Ep,Np,Cp].join("|")+")"+kp+Mp+")*",Fw=kp+Mp+Bw,Uw="(?:"+[Ep+_s+"?",_s,Np,Cp,Dw].join("|")+")",Ww=RegExp(js+"(?="+js+")|"+Uw+Fw,"g");function zw(e){return e.match(Ww)||[]}var qw=zw,Hw=Ew,Kw=$p,Gw=qw;function Vw(e){return Kw(e)?Gw(e):Hw(e)}var Xw=Vw,Yw=bw,Zw=$p,Jw=Xw,Qw=Op;function e1(e){return function(t){t=Qw(t);var r=Zw(t)?Jw(t):void 0,n=r?r[0]:t.charAt(0),i=r?Yw(r,1).join(""):t.slice(1);return n[e]()+i}}var t1=e1,r1=t1,n1=r1("toUpperCase"),i1=n1;const lo=ce(i1);function le(e){return function(){return e}}const Ip=Math.cos,Yi=Math.sin,ut=Math.sqrt,Zi=Math.PI,fo=2*Zi,$s=Math.PI,Ts=2*$s,Yt=1e-6,a1=Ts-Yt;function Dp(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function o1(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Dp;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class s1{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Dp:o1(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,s=this._y1,c=n-t,u=i-r,f=o-t,l=s-r,d=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(d>Yt)if(!(Math.abs(l*c-u*f)>Yt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,p=i-s,v=c*c+u*u,m=h*h+p*p,x=Math.sqrt(v),O=Math.sqrt(d),w=a*Math.tan(($s-Math.acos((v+d-m)/(2*x*O)))/2),S=w/O,g=w/x;Math.abs(S-1)>Yt&&this._append`L${t+S*f},${r+S*l}`,this._append`A${a},${a},0,0,${+(l*h>f*p)},${this._x1=t+g*c},${this._y1=r+g*u}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let s=n*Math.cos(i),c=n*Math.sin(i),u=t+s,f=r+c,l=1^o,d=o?i-a:a-i;this._x1===null?this._append`M${u},${f}`:(Math.abs(this._x1-u)>Yt||Math.abs(this._y1-f)>Yt)&&this._append`L${u},${f}`,n&&(d<0&&(d=d%Ts+Ts),d>a1?this._append`A${n},${n},0,1,${l},${t-s},${r-c}A${n},${n},0,1,${l},${this._x1=u},${this._y1=f}`:d>Yt&&this._append`A${n},${n},0,${+(d>=$s)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function uu(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new s1(t)}function lu(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Lp(e){this._context=e}Lp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ho(e){return new Lp(e)}function Rp(e){return e[0]}function Bp(e){return e[1]}function Fp(e,t){var r=le(!0),n=null,i=ho,a=null,o=uu(s);e=typeof e=="function"?e:e===void 0?Rp:le(e),t=typeof t=="function"?t:t===void 0?Bp:le(t);function s(c){var u,f=(c=lu(c)).length,l,d=!1,h;for(n==null&&(a=i(h=o())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(l,u,c),+t(l,u,c));if(h)return a=null,h+""||null}return s.x=function(c){return arguments.length?(e=typeof c=="function"?c:le(+c),s):e},s.y=function(c){return arguments.length?(t=typeof c=="function"?c:le(+c),s):t},s.defined=function(c){return arguments.length?(r=typeof c=="function"?c:le(!!c),s):r},s.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),s):i},s.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),s):n},s}function Ei(e,t,r){var n=null,i=le(!0),a=null,o=ho,s=null,c=uu(u);e=typeof e=="function"?e:e===void 0?Rp:le(+e),t=typeof t=="function"?t:le(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?Bp:le(+r);function u(l){var d,h,p,v=(l=lu(l)).length,m,x=!1,O,w=new Array(v),S=new Array(v);for(a==null&&(s=o(O=c())),d=0;d<=v;++d){if(!(d<v&&i(m=l[d],d,l))===x)if(x=!x)h=d,s.areaStart(),s.lineStart();else{for(s.lineEnd(),s.lineStart(),p=d-1;p>=h;--p)s.point(w[p],S[p]);s.lineEnd(),s.areaEnd()}x&&(w[d]=+e(m,d,l),S[d]=+t(m,d,l),s.point(n?+n(m,d,l):w[d],r?+r(m,d,l):S[d]))}if(O)return s=null,O+""||null}function f(){return Fp().defined(i).curve(o).context(a)}return u.x=function(l){return arguments.length?(e=typeof l=="function"?l:le(+l),n=null,u):e},u.x0=function(l){return arguments.length?(e=typeof l=="function"?l:le(+l),u):e},u.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:le(+l),u):n},u.y=function(l){return arguments.length?(t=typeof l=="function"?l:le(+l),r=null,u):t},u.y0=function(l){return arguments.length?(t=typeof l=="function"?l:le(+l),u):t},u.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:le(+l),u):r},u.lineX0=u.lineY0=function(){return f().x(e).y(t)},u.lineY1=function(){return f().x(e).y(r)},u.lineX1=function(){return f().x(n).y(t)},u.defined=function(l){return arguments.length?(i=typeof l=="function"?l:le(!!l),u):i},u.curve=function(l){return arguments.length?(o=l,a!=null&&(s=o(a)),u):o},u.context=function(l){return arguments.length?(l==null?a=s=null:s=o(a=l),u):a},u}class Up{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function c1(e){return new Up(e,!0)}function u1(e){return new Up(e,!1)}const fu={draw(e,t){const r=ut(t/Zi);e.moveTo(r,0),e.arc(0,0,r,0,fo)}},l1={draw(e,t){const r=ut(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Wp=ut(1/3),f1=Wp*2,d1={draw(e,t){const r=ut(t/f1),n=r*Wp;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},h1={draw(e,t){const r=ut(t),n=-r/2;e.rect(n,n,r,r)}},p1=.8908130915292852,zp=Yi(Zi/10)/Yi(7*Zi/10),v1=Yi(fo/10)*zp,y1=-Ip(fo/10)*zp,m1={draw(e,t){const r=ut(t*p1),n=v1*r,i=y1*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=fo*a/5,s=Ip(o),c=Yi(o);e.lineTo(c*r,-s*r),e.lineTo(s*n-c*i,c*n+s*i)}e.closePath()}},Xo=ut(3),g1={draw(e,t){const r=-ut(t/(Xo*3));e.moveTo(0,r*2),e.lineTo(-Xo*r,-r),e.lineTo(Xo*r,-r),e.closePath()}},Ye=-.5,Ze=ut(3)/2,Es=1/ut(12),b1=(Es/2+1)*3,x1={draw(e,t){const r=ut(t/b1),n=r/2,i=r*Es,a=n,o=r*Es+r,s=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(s,c),e.lineTo(Ye*n-Ze*i,Ze*n+Ye*i),e.lineTo(Ye*a-Ze*o,Ze*a+Ye*o),e.lineTo(Ye*s-Ze*c,Ze*s+Ye*c),e.lineTo(Ye*n+Ze*i,Ye*i-Ze*n),e.lineTo(Ye*a+Ze*o,Ye*o-Ze*a),e.lineTo(Ye*s+Ze*c,Ye*c-Ze*s),e.closePath()}};function w1(e,t){let r=null,n=uu(i);e=typeof e=="function"?e:le(e||fu),t=typeof t=="function"?t:le(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:le(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:le(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function Ji(){}function Qi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function qp(e){this._context=e}qp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Qi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Qi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function O1(e){return new qp(e)}function Hp(e){this._context=e}Hp.prototype={areaStart:Ji,areaEnd:Ji,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Qi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function A1(e){return new Hp(e)}function Kp(e){this._context=e}Kp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Qi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function S1(e){return new Kp(e)}function Gp(e){this._context=e}Gp.prototype={areaStart:Ji,areaEnd:Ji,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function P1(e){return new Gp(e)}function jl(e){return e<0?-1:1}function $l(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),s=(a*i+o*n)/(n+i);return(jl(a)+jl(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(s))||0}function Tl(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Yo(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,s=(a-n)/3;e._context.bezierCurveTo(n+s,i+s*t,a-s,o-s*r,a,o)}function ea(e){this._context=e}ea.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Yo(this,this._t0,Tl(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Yo(this,Tl(this,r=$l(this,e,t)),r);break;default:Yo(this,this._t0,r=$l(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Vp(e){this._context=new Xp(e)}(Vp.prototype=Object.create(ea.prototype)).point=function(e,t){ea.prototype.point.call(this,t,e)};function Xp(e){this._context=e}Xp.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function _1(e){return new ea(e)}function j1(e){return new Vp(e)}function Yp(e){this._context=e}Yp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=El(e),i=El(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function El(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function $1(e){return new Yp(e)}function po(e,t){this._context=e,this._t=t}po.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function T1(e){return new po(e,.5)}function E1(e){return new po(e,0)}function N1(e){return new po(e,1)}function Nr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,s=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<s;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Ns(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function C1(e,t){return e[t]}function M1(e){const t=[];return t.key=e,t}function k1(){var e=le([]),t=Ns,r=Nr,n=C1;function i(a){var o=Array.from(e.apply(this,arguments),M1),s,c=o.length,u=-1,f;for(const l of a)for(s=0,++u;s<c;++s)(o[s][u]=[0,+n(l,o[s].key,u,a)]).data=l;for(s=0,f=lu(t(o));s<c;++s)o[f[s]].index=s;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:le(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:le(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Ns:typeof a=="function"?a:le(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??Nr,i):r},i}function I1(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Nr(e,t)}}function D1(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,s=0;o<i;++o)s+=e[o][r][1]||0;n[r][1]+=n[r][0]=-s/2}Nr(e,t)}}function L1(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var s=0,c=0,u=0;s<o;++s){for(var f=e[t[s]],l=f[n][1]||0,d=f[n-1][1]||0,h=(l-d)/2,p=0;p<s;++p){var v=e[t[p]],m=v[n][1]||0,x=v[n-1][1]||0;h+=m-x}c+=l,u+=h*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=u/c)}i[n-1][1]+=i[n-1][0]=r,Nr(e,t)}}function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}var R1=["type","size","sizeType"];function Cs(){return Cs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cs.apply(this,arguments)}function Nl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Cl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nl(Object(r),!0).forEach(function(n){B1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B1(e,t,r){return t=F1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F1(e){var t=U1(e,"string");return In(t)=="symbol"?t:t+""}function U1(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function W1(e,t){if(e==null)return{};var r=z1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function z1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Zp={symbolCircle:fu,symbolCross:l1,symbolDiamond:d1,symbolSquare:h1,symbolStar:m1,symbolTriangle:g1,symbolWye:x1},q1=Math.PI/180,H1=function(t){var r="symbol".concat(lo(t));return Zp[r]||fu},K1=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*q1;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},G1=function(t,r){Zp["symbol".concat(lo(t))]=r},du=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,s=o===void 0?"area":o,c=W1(t,R1),u=Cl(Cl({},c),{},{type:n,size:a,sizeType:s}),f=function(){var m=H1(n),x=w1().type(m).size(K1(a,s,n));return x()},l=u.className,d=u.cx,h=u.cy,p=K(u,!0);return d===+d&&h===+h&&a===+a?j.createElement("path",Cs({},p,{className:Q("recharts-symbols",l),transform:"translate(".concat(d,", ").concat(h,")"),d:f()})):null};du.registerSymbol=G1;function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Ms(){return Ms=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ms.apply(this,arguments)}function Ml(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function V1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ml(Object(r),!0).forEach(function(n){Dn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ml(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function X1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Y1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qp(n.key),n)}}function Z1(e,t,r){return t&&Y1(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function J1(e,t,r){return t=ta(t),Q1(e,Jp()?Reflect.construct(t,r||[],ta(e).constructor):t.apply(e,r))}function Q1(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return eO(e)}function eO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jp(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Jp=function(){return!!e})()}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ta(e)}function tO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ks(e,t)}function ks(e,t){return ks=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ks(e,t)}function Dn(e,t,r){return t=Qp(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qp(e){var t=rO(e,"string");return Cr(t)=="symbol"?t:t+""}function rO(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var nt=32,hu=function(e){function t(){return X1(this,t),J1(this,t,arguments)}return tO(t,e),Z1(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=nt/2,o=nt/6,s=nt/3,c=n.inactive?i:n.color;if(n.type==="plainline")return j.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:nt,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return j.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(s,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*s,",").concat(a,`
            H`).concat(nt,"M").concat(2*s,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(s,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return j.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(nt/8,"h").concat(nt,"v").concat(nt*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(j.isValidElement(n.legendIcon)){var u=V1({},n);return delete u.legendIcon,j.cloneElement(n.legendIcon,u)}return j.createElement(du,{fill:c,cx:a,cy:a,size:nt,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,s=i.layout,c=i.formatter,u=i.inactiveColor,f={x:0,y:0,width:nt,height:nt},l={display:s==="horizontal"?"inline-block":"block",marginRight:10},d={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,p){var v=h.formatter||c,m=Q(Dn(Dn({"recharts-legend-item":!0},"legend-item-".concat(p),!0),"inactive",h.inactive));if(h.type==="none")return null;var x=Y(h.value)?null:h.value;ct(!Y(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var O=h.inactive?u:h.color;return j.createElement("li",Ms({className:m,style:l,key:"legend-item-".concat(p)},cr(n.props,h,p)),j.createElement(Ss,{width:o,height:o,viewBox:f,style:d},n.renderIcon(h)),j.createElement("span",{className:"recharts-legend-item-text",style:{color:O}},v?v(x,h,p):x))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var s={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return j.createElement("ul",{className:"recharts-default-legend",style:s},this.renderItems())}}])}(I.PureComponent);Dn(hu,"displayName","Legend");Dn(hu,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var nO=Za;function iO(){this.__data__=new nO,this.size=0}var aO=iO;function oO(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var sO=oO;function cO(e){return this.__data__.get(e)}var uO=cO;function lO(e){return this.__data__.has(e)}var fO=lO,dO=Za,hO=eu,pO=tu,vO=200;function yO(e,t){var r=this.__data__;if(r instanceof dO){var n=r.__data__;if(!hO||n.length<vO-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new pO(n)}return r.set(e,t),this.size=r.size,this}var mO=yO,gO=Za,bO=aO,xO=sO,wO=uO,OO=fO,AO=mO;function ln(e){var t=this.__data__=new gO(e);this.size=t.size}ln.prototype.clear=bO;ln.prototype.delete=xO;ln.prototype.get=wO;ln.prototype.has=OO;ln.prototype.set=AO;var ev=ln,SO="__lodash_hash_undefined__";function PO(e){return this.__data__.set(e,SO),this}var _O=PO;function jO(e){return this.__data__.has(e)}var $O=jO,TO=tu,EO=_O,NO=$O;function ra(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new TO;++t<r;)this.add(e[t])}ra.prototype.add=ra.prototype.push=EO;ra.prototype.has=NO;var tv=ra;function CO(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var rv=CO;function MO(e,t){return e.has(t)}var nv=MO,kO=tv,IO=rv,DO=nv,LO=1,RO=2;function BO(e,t,r,n,i,a){var o=r&LO,s=e.length,c=t.length;if(s!=c&&!(o&&c>s))return!1;var u=a.get(e),f=a.get(t);if(u&&f)return u==t&&f==e;var l=-1,d=!0,h=r&RO?new kO:void 0;for(a.set(e,t),a.set(t,e);++l<s;){var p=e[l],v=t[l];if(n)var m=o?n(v,p,l,t,e,a):n(p,v,l,e,t,a);if(m!==void 0){if(m)continue;d=!1;break}if(h){if(!IO(t,function(x,O){if(!DO(h,O)&&(p===x||i(p,x,r,n,a)))return h.push(O)})){d=!1;break}}else if(!(p===v||i(p,v,r,n,a))){d=!1;break}}return a.delete(e),a.delete(t),d}var iv=BO,FO=yt,UO=FO.Uint8Array,WO=UO;function zO(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var qO=zO;function HO(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var pu=HO,kl=mi,Il=WO,KO=Qc,GO=iv,VO=qO,XO=pu,YO=1,ZO=2,JO="[object Boolean]",QO="[object Date]",eA="[object Error]",tA="[object Map]",rA="[object Number]",nA="[object RegExp]",iA="[object Set]",aA="[object String]",oA="[object Symbol]",sA="[object ArrayBuffer]",cA="[object DataView]",Dl=kl?kl.prototype:void 0,Zo=Dl?Dl.valueOf:void 0;function uA(e,t,r,n,i,a,o){switch(r){case cA:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case sA:return!(e.byteLength!=t.byteLength||!a(new Il(e),new Il(t)));case JO:case QO:case rA:return KO(+e,+t);case eA:return e.name==t.name&&e.message==t.message;case nA:case aA:return e==t+"";case tA:var s=VO;case iA:var c=n&YO;if(s||(s=XO),e.size!=t.size&&!c)return!1;var u=o.get(e);if(u)return u==t;n|=ZO,o.set(e,t);var f=GO(s(e),s(t),n,i,a,o);return o.delete(e),f;case oA:if(Zo)return Zo.call(e)==Zo.call(t)}return!1}var lA=uA;function fA(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var av=fA,dA=av,hA=Fe;function pA(e,t,r){var n=t(e);return hA(e)?n:dA(n,r(e))}var vA=pA;function yA(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var mA=yA;function gA(){return[]}var bA=gA,xA=mA,wA=bA,OA=Object.prototype,AA=OA.propertyIsEnumerable,Ll=Object.getOwnPropertySymbols,SA=Ll?function(e){return e==null?[]:(e=Object(e),xA(Ll(e),function(t){return AA.call(e,t)}))}:wA,PA=SA;function _A(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var jA=_A,$A=Et,TA=Nt,EA="[object Arguments]";function NA(e){return TA(e)&&$A(e)==EA}var CA=NA,Rl=CA,MA=Nt,ov=Object.prototype,kA=ov.hasOwnProperty,IA=ov.propertyIsEnumerable,DA=Rl(function(){return arguments}())?Rl:function(e){return MA(e)&&kA.call(e,"callee")&&!IA.call(e,"callee")},vu=DA,na={exports:{}};function LA(){return!1}var RA=LA;na.exports;(function(e,t){var r=yt,n=RA,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,s=o?r.Buffer:void 0,c=s?s.isBuffer:void 0,u=c||n;e.exports=u})(na,na.exports);var sv=na.exports,BA=9007199254740991,FA=/^(?:0|[1-9]\d*)$/;function UA(e,t){var r=typeof e;return t=t??BA,!!t&&(r=="number"||r!="symbol"&&FA.test(e))&&e>-1&&e%1==0&&e<t}var yu=UA,WA=9007199254740991;function zA(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=WA}var mu=zA,qA=Et,HA=mu,KA=Nt,GA="[object Arguments]",VA="[object Array]",XA="[object Boolean]",YA="[object Date]",ZA="[object Error]",JA="[object Function]",QA="[object Map]",eS="[object Number]",tS="[object Object]",rS="[object RegExp]",nS="[object Set]",iS="[object String]",aS="[object WeakMap]",oS="[object ArrayBuffer]",sS="[object DataView]",cS="[object Float32Array]",uS="[object Float64Array]",lS="[object Int8Array]",fS="[object Int16Array]",dS="[object Int32Array]",hS="[object Uint8Array]",pS="[object Uint8ClampedArray]",vS="[object Uint16Array]",yS="[object Uint32Array]",he={};he[cS]=he[uS]=he[lS]=he[fS]=he[dS]=he[hS]=he[pS]=he[vS]=he[yS]=!0;he[GA]=he[VA]=he[oS]=he[XA]=he[sS]=he[YA]=he[ZA]=he[JA]=he[QA]=he[eS]=he[tS]=he[rS]=he[nS]=he[iS]=he[aS]=!1;function mS(e){return KA(e)&&HA(e.length)&&!!he[qA(e)]}var gS=mS;function bS(e){return function(t){return e(t)}}var cv=bS,ia={exports:{}};ia.exports;(function(e,t){var r=yp,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,s=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=s})(ia,ia.exports);var xS=ia.exports,wS=gS,OS=cv,Bl=xS,Fl=Bl&&Bl.isTypedArray,AS=Fl?OS(Fl):wS,uv=AS,SS=jA,PS=vu,_S=Fe,jS=sv,$S=yu,TS=uv,ES=Object.prototype,NS=ES.hasOwnProperty;function CS(e,t){var r=_S(e),n=!r&&PS(e),i=!r&&!n&&jS(e),a=!r&&!n&&!i&&TS(e),o=r||n||i||a,s=o?SS(e.length,String):[],c=s.length;for(var u in e)(t||NS.call(e,u))&&!(o&&(u=="length"||i&&(u=="offset"||u=="parent")||a&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||$S(u,c)))&&s.push(u);return s}var MS=CS,kS=Object.prototype;function IS(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||kS;return e===r}var DS=IS;function LS(e,t){return function(r){return e(t(r))}}var lv=LS,RS=lv,BS=RS(Object.keys,Object),FS=BS,US=DS,WS=FS,zS=Object.prototype,qS=zS.hasOwnProperty;function HS(e){if(!US(e))return WS(e);var t=[];for(var r in Object(e))qS.call(e,r)&&r!="constructor"&&t.push(r);return t}var KS=HS,GS=Jc,VS=mu;function XS(e){return e!=null&&VS(e.length)&&!GS(e)}var xi=XS,YS=MS,ZS=KS,JS=xi;function QS(e){return JS(e)?YS(e):ZS(e)}var vo=QS,eP=vA,tP=PA,rP=vo;function nP(e){return eP(e,rP,tP)}var iP=nP,Ul=iP,aP=1,oP=Object.prototype,sP=oP.hasOwnProperty;function cP(e,t,r,n,i,a){var o=r&aP,s=Ul(e),c=s.length,u=Ul(t),f=u.length;if(c!=f&&!o)return!1;for(var l=c;l--;){var d=s[l];if(!(o?d in t:sP.call(t,d)))return!1}var h=a.get(e),p=a.get(t);if(h&&p)return h==t&&p==e;var v=!0;a.set(e,t),a.set(t,e);for(var m=o;++l<c;){d=s[l];var x=e[d],O=t[d];if(n)var w=o?n(O,x,d,t,e,a):n(x,O,d,e,t,a);if(!(w===void 0?x===O||i(x,O,r,n,a):w)){v=!1;break}m||(m=d=="constructor")}if(v&&!m){var S=e.constructor,g=t.constructor;S!=g&&"constructor"in e&&"constructor"in t&&!(typeof S=="function"&&S instanceof S&&typeof g=="function"&&g instanceof g)&&(v=!1)}return a.delete(e),a.delete(t),v}var uP=cP,lP=hr,fP=yt,dP=lP(fP,"DataView"),hP=dP,pP=hr,vP=yt,yP=pP(vP,"Promise"),mP=yP,gP=hr,bP=yt,xP=gP(bP,"Set"),fv=xP,wP=hr,OP=yt,AP=wP(OP,"WeakMap"),SP=AP,Is=hP,Ds=eu,Ls=mP,Rs=fv,Bs=SP,dv=Et,fn=gp,Wl="[object Map]",PP="[object Object]",zl="[object Promise]",ql="[object Set]",Hl="[object WeakMap]",Kl="[object DataView]",_P=fn(Is),jP=fn(Ds),$P=fn(Ls),TP=fn(Rs),EP=fn(Bs),Zt=dv;(Is&&Zt(new Is(new ArrayBuffer(1)))!=Kl||Ds&&Zt(new Ds)!=Wl||Ls&&Zt(Ls.resolve())!=zl||Rs&&Zt(new Rs)!=ql||Bs&&Zt(new Bs)!=Hl)&&(Zt=function(e){var t=dv(e),r=t==PP?e.constructor:void 0,n=r?fn(r):"";if(n)switch(n){case _P:return Kl;case jP:return Wl;case $P:return zl;case TP:return ql;case EP:return Hl}return t});var NP=Zt,Jo=ev,CP=iv,MP=lA,kP=uP,Gl=NP,Vl=Fe,Xl=sv,IP=uv,DP=1,Yl="[object Arguments]",Zl="[object Array]",Ni="[object Object]",LP=Object.prototype,Jl=LP.hasOwnProperty;function RP(e,t,r,n,i,a){var o=Vl(e),s=Vl(t),c=o?Zl:Gl(e),u=s?Zl:Gl(t);c=c==Yl?Ni:c,u=u==Yl?Ni:u;var f=c==Ni,l=u==Ni,d=c==u;if(d&&Xl(e)){if(!Xl(t))return!1;o=!0,f=!1}if(d&&!f)return a||(a=new Jo),o||IP(e)?CP(e,t,r,n,i,a):MP(e,t,c,r,n,i,a);if(!(r&DP)){var h=f&&Jl.call(e,"__wrapped__"),p=l&&Jl.call(t,"__wrapped__");if(h||p){var v=h?e.value():e,m=p?t.value():t;return a||(a=new Jo),i(v,m,r,n,a)}}return d?(a||(a=new Jo),kP(e,t,r,n,i,a)):!1}var BP=RP,FP=BP,Ql=Nt;function hv(e,t,r,n,i){return e===t?!0:e==null||t==null||!Ql(e)&&!Ql(t)?e!==e&&t!==t:FP(e,t,r,n,hv,i)}var gu=hv,UP=ev,WP=gu,zP=1,qP=2;function HP(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var s=r[i];if(o&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<a;){s=r[i];var c=s[0],u=e[c],f=s[1];if(o&&s[2]){if(u===void 0&&!(c in e))return!1}else{var l=new UP;if(n)var d=n(u,f,c,e,t,l);if(!(d===void 0?WP(f,u,zP|qP,n,l):d))return!1}}return!0}var KP=HP,GP=Ft;function VP(e){return e===e&&!GP(e)}var pv=VP,XP=pv,YP=vo;function ZP(e){for(var t=YP(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,XP(i)]}return t}var JP=ZP;function QP(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var vv=QP,e_=KP,t_=JP,r_=vv;function n_(e){var t=t_(e);return t.length==1&&t[0][2]?r_(t[0][0],t[0][1]):function(r){return r===e||e_(r,e,t)}}var i_=n_;function a_(e,t){return e!=null&&t in Object(e)}var o_=a_,s_=Ap,c_=vu,u_=Fe,l_=yu,f_=mu,d_=Qa;function h_(e,t,r){t=s_(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=d_(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&f_(i)&&l_(o,i)&&(u_(e)||c_(e)))}var p_=h_,v_=o_,y_=p_;function m_(e,t){return e!=null&&y_(e,t,v_)}var g_=m_,b_=gu,x_=Sp,w_=g_,O_=Zc,A_=pv,S_=vv,P_=Qa,__=1,j_=2;function $_(e,t){return O_(e)&&A_(t)?S_(P_(e),t):function(r){var n=x_(r,e);return n===void 0&&n===t?w_(r,e):b_(t,n,__|j_)}}var T_=$_;function E_(e){return e}var dn=E_;function N_(e){return function(t){return t==null?void 0:t[e]}}var C_=N_,M_=iu;function k_(e){return function(t){return M_(t,e)}}var I_=k_,D_=C_,L_=I_,R_=Zc,B_=Qa;function F_(e){return R_(e)?D_(B_(e)):L_(e)}var U_=F_,W_=i_,z_=T_,q_=dn,H_=Fe,K_=U_;function G_(e){return typeof e=="function"?e:e==null?q_:typeof e=="object"?H_(e)?z_(e[0],e[1]):W_(e):K_(e)}var mt=G_;function V_(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var yv=V_;function X_(e){return e!==e}var Y_=X_;function Z_(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var J_=Z_,Q_=yv,ej=Y_,tj=J_;function rj(e,t,r){return t===t?tj(e,t,r):Q_(e,ej,r)}var nj=rj,ij=nj;function aj(e,t){var r=e==null?0:e.length;return!!r&&ij(e,t,0)>-1}var oj=aj;function sj(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var cj=sj;function uj(){}var lj=uj,Qo=fv,fj=lj,dj=pu,hj=1/0,pj=Qo&&1/dj(new Qo([,-0]))[1]==hj?function(e){return new Qo(e)}:fj,vj=pj,yj=tv,mj=oj,gj=cj,bj=nv,xj=vj,wj=pu,Oj=200;function Aj(e,t,r){var n=-1,i=mj,a=e.length,o=!0,s=[],c=s;if(r)o=!1,i=gj;else if(a>=Oj){var u=t?null:xj(e);if(u)return wj(u);o=!1,i=bj,c=new yj}else c=t?[]:s;e:for(;++n<a;){var f=e[n],l=t?t(f):f;if(f=r||f!==0?f:0,o&&l===l){for(var d=c.length;d--;)if(c[d]===l)continue e;t&&c.push(l),s.push(f)}else i(c,l,r)||(c!==s&&c.push(l),s.push(f))}return s}var Sj=Aj,Pj=mt,_j=Sj;function jj(e,t){return e&&e.length?_j(e,Pj(t)):[]}var $j=jj;const ef=ce($j);function mv(e,t,r){return t===!0?ef(e,r):Y(t)?ef(e,t):e}function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}var Tj=["ref"];function tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?tf(Object(r),!0).forEach(function(n){yo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ej(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rf(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bv(n.key),n)}}function Nj(e,t,r){return t&&rf(e.prototype,t),r&&rf(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Cj(e,t,r){return t=aa(t),Mj(e,gv()?Reflect.construct(t,r||[],aa(e).constructor):t.apply(e,r))}function Mj(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kj(e)}function kj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function gv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(gv=function(){return!!e})()}function aa(e){return aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},aa(e)}function Ij(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Fs(e,t)}function Fs(e,t){return Fs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Fs(e,t)}function yo(e,t,r){return t=bv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bv(e){var t=Dj(e,"string");return Mr(t)=="symbol"?t:t+""}function Dj(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Lj(e,t){if(e==null)return{};var r=Rj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Rj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Bj(e){return e.value}function Fj(e,t){if(j.isValidElement(e))return j.cloneElement(e,t);if(typeof e=="function")return j.createElement(e,t);t.ref;var r=Lj(t,Tj);return j.createElement(hu,r)}var nf=1,lt=function(e){function t(){var r;Ej(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Cj(this,t,[].concat(i)),yo(r,"lastBoundingBox",{width:-1,height:-1}),r}return Ij(t,e),Nj(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>nf||Math.abs(i.height-this.lastBoundingBox.height)>nf)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?gt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,s=i.verticalAlign,c=i.margin,u=i.chartWidth,f=i.chartHeight,l,d;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();l={left:((u||0)-h.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(s==="middle"){var p=this.getBBoxSnapshot();d={top:((f||0)-p.height)/2}}else d=s==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return gt(gt({},l),d)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,s=i.height,c=i.wrapperStyle,u=i.payloadUniqBy,f=i.payload,l=gt(gt({position:"absolute",width:o||"auto",height:s||"auto"},this.getDefaultPosition(c)),c);return j.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(h){n.wrapperNode=h}},Fj(a,gt(gt({},this.props),{},{payload:mv(f,u,Bj)})))}}],[{key:"getWithHeight",value:function(n,i){var a=gt(gt({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&B(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(I.PureComponent);yo(lt,"displayName","Legend");yo(lt,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var af=mi,Uj=vu,Wj=Fe,of=af?af.isConcatSpreadable:void 0;function zj(e){return Wj(e)||Uj(e)||!!(of&&e&&e[of])}var qj=zj,Hj=av,Kj=qj;function xv(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=Kj),i||(i=[]);++a<o;){var s=e[a];t>0&&r(s)?t>1?xv(s,t-1,r,n,i):Hj(i,s):n||(i[i.length]=s)}return i}var wv=xv;function Gj(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),s=o.length;s--;){var c=o[e?s:++i];if(r(a[c],c,a)===!1)break}return t}}var Vj=Gj,Xj=Vj,Yj=Xj(),Zj=Yj,Jj=Zj,Qj=vo;function e$(e,t){return e&&Jj(e,t,Qj)}var Ov=e$,t$=xi;function r$(e,t){return function(r,n){if(r==null)return r;if(!t$(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var n$=r$,i$=Ov,a$=n$,o$=a$(i$),bu=o$,s$=bu,c$=xi;function u$(e,t){var r=-1,n=c$(e)?Array(e.length):[];return s$(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var Av=u$;function l$(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var f$=l$,sf=nn;function d$(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=sf(e),o=t!==void 0,s=t===null,c=t===t,u=sf(t);if(!s&&!u&&!a&&e>t||a&&o&&c&&!s&&!u||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!u&&e<t||u&&r&&i&&!n&&!a||s&&r&&i||!o&&i||!c)return-1}return 0}var h$=d$,p$=h$;function v$(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,s=r.length;++n<o;){var c=p$(i[n],a[n]);if(c){if(n>=s)return c;var u=r[n];return c*(u=="desc"?-1:1)}}return e.index-t.index}var y$=v$,es=nu,m$=iu,g$=mt,b$=Av,x$=f$,w$=cv,O$=y$,A$=dn,S$=Fe;function P$(e,t,r){t.length?t=es(t,function(a){return S$(a)?function(o){return m$(o,a.length===1?a[0]:a)}:a}):t=[A$];var n=-1;t=es(t,w$(g$));var i=b$(e,function(a,o,s){var c=es(t,function(u){return u(a)});return{criteria:c,index:++n,value:a}});return x$(i,function(a,o){return O$(a,o,r)})}var _$=P$;function j$(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var $$=j$,T$=$$,cf=Math.max;function E$(e,t,r){return t=cf(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=cf(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var s=Array(t+1);++i<t;)s[i]=n[i];return s[t]=r(o),T$(e,this,s)}}var N$=E$;function C$(e){return function(){return e}}var M$=C$,k$=hr,I$=function(){try{var e=k$(Object,"defineProperty");return e({},"",{}),e}catch{}}(),Sv=I$,D$=M$,uf=Sv,L$=dn,R$=uf?function(e,t){return uf(e,"toString",{configurable:!0,enumerable:!1,value:D$(t),writable:!0})}:L$,B$=R$,F$=800,U$=16,W$=Date.now;function z$(e){var t=0,r=0;return function(){var n=W$(),i=U$-(n-r);if(r=n,i>0){if(++t>=F$)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var q$=z$,H$=B$,K$=q$,G$=K$(H$),V$=G$,X$=dn,Y$=N$,Z$=V$;function J$(e,t){return Z$(Y$(e,t,X$),e+"")}var Q$=J$,eT=Qc,tT=xi,rT=yu,nT=Ft;function iT(e,t,r){if(!nT(r))return!1;var n=typeof t;return(n=="number"?tT(r)&&rT(t,r.length):n=="string"&&t in r)?eT(r[t],e):!1}var mo=iT,aT=wv,oT=_$,sT=Q$,lf=mo,cT=sT(function(e,t){if(e==null)return[];var r=t.length;return r>1&&lf(e,t[0],t[1])?t=[]:r>2&&lf(t[0],t[1],t[2])&&(t=[t[0]]),oT(e,aT(t,1),[])}),uT=cT;const xu=ce(uT);function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function Us(){return Us=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Us.apply(this,arguments)}function lT(e,t){return pT(e)||hT(e,t)||dT(e,t)||fT()}function fT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dT(e,t){if(e){if(typeof e=="string")return ff(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ff(e,t)}}function ff(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function pT(e){if(Array.isArray(e))return e}function df(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ts(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?df(Object(r),!0).forEach(function(n){vT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):df(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vT(e,t,r){return t=yT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yT(e){var t=mT(e,"string");return Ln(t)=="symbol"?t:t+""}function mT(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function gT(e){return Array.isArray(e)&&Pe(e[0])&&Pe(e[1])?e.join(" ~ "):e}var bT=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,s=o===void 0?{}:o,c=t.labelStyle,u=c===void 0?{}:c,f=t.payload,l=t.formatter,d=t.itemSorter,h=t.wrapperClassName,p=t.labelClassName,v=t.label,m=t.labelFormatter,x=t.accessibilityLayer,O=x===void 0?!1:x,w=function(){if(f&&f.length){var T={padding:0,margin:0},M=(d?xu(f,d):f).map(function(k,C){if(k.type==="none")return null;var D=ts({display:"block",paddingTop:4,paddingBottom:4,color:k.color||"#000"},s),L=k.formatter||l||gT,F=k.value,U=k.name,H=F,V=U;if(L&&H!=null&&V!=null){var z=L(F,U,k,C,f);if(Array.isArray(z)){var X=lT(z,2);H=X[0],V=X[1]}else H=z}return j.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(C),style:D},Pe(V)?j.createElement("span",{className:"recharts-tooltip-item-name"},V):null,Pe(V)?j.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,j.createElement("span",{className:"recharts-tooltip-item-value"},H),j.createElement("span",{className:"recharts-tooltip-item-unit"},k.unit||""))});return j.createElement("ul",{className:"recharts-tooltip-item-list",style:T},M)}return null},S=ts({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),g=ts({margin:0},u),b=!Z(v),A=b?v:"",P=Q("recharts-default-tooltip",h),_=Q("recharts-tooltip-label",p);b&&m&&f!==void 0&&f!==null&&(A=m(v,f));var E=O?{role:"status","aria-live":"assertive"}:{};return j.createElement("div",Us({className:P,style:S},E),j.createElement("p",{className:_,style:g},j.isValidElement(A)?A:"".concat(A)),w())};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Ci(e,t,r){return t=xT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xT(e){var t=wT(e,"string");return Rn(t)=="symbol"?t:t+""}function wT(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var gn="recharts-tooltip-wrapper",OT={visibility:"hidden"};function AT(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return Q(gn,Ci(Ci(Ci(Ci({},"".concat(gn,"-right"),B(r)&&t&&B(t.x)&&r>=t.x),"".concat(gn,"-left"),B(r)&&t&&B(t.x)&&r<t.x),"".concat(gn,"-bottom"),B(n)&&t&&B(t.y)&&n>=t.y),"".concat(gn,"-top"),B(n)&&t&&B(t.y)&&n<t.y))}function hf(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,s=e.tooltipDimension,c=e.viewBox,u=e.viewBoxDimension;if(a&&B(a[n]))return a[n];var f=r[n]-s-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var d=f,h=c[n];return d<h?Math.max(l,c[n]):Math.max(f,c[n])}var p=l+s,v=c[n]+u;return p>v?Math.max(f,c[n]):Math.max(l,c[n])}function ST(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function PT(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,s=e.useTranslate3d,c=e.viewBox,u,f,l;return o.height>0&&o.width>0&&r?(f=hf({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=hf({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),u=ST({translateX:f,translateY:l,useTranslate3d:s})):u=OT,{cssProperties:u,cssClasses:AT({translateX:f,translateY:l,coordinate:r})}}function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function pf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pf(Object(r),!0).forEach(function(n){zs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _T(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_v(n.key),n)}}function $T(e,t,r){return t&&jT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function TT(e,t,r){return t=oa(t),ET(e,Pv()?Reflect.construct(t,r||[],oa(e).constructor):t.apply(e,r))}function ET(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return NT(e)}function NT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Pv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pv=function(){return!!e})()}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function CT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ws(e,t)}function Ws(e,t){return Ws=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ws(e,t)}function zs(e,t,r){return t=_v(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _v(e){var t=MT(e,"string");return kr(t)=="symbol"?t:t+""}function MT(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var yf=1,kT=function(e){function t(){var r;_T(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=TT(this,t,[].concat(i)),zs(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),zs(r,"handleKeyDown",function(o){if(o.key==="Escape"){var s,c,u,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(s=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&s!==void 0?s:0,y:(u=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&u!==void 0?u:0}})}}),r}return CT(t,e),$T(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>yf||Math.abs(n.height-this.state.lastBoundingBox.height)>yf)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,s=i.animationDuration,c=i.animationEasing,u=i.children,f=i.coordinate,l=i.hasPayload,d=i.isAnimationActive,h=i.offset,p=i.position,v=i.reverseDirection,m=i.useTranslate3d,x=i.viewBox,O=i.wrapperStyle,w=PT({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:p,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:m,viewBox:x}),S=w.cssClasses,g=w.cssProperties,b=vf(vf({transition:d&&a?"transform ".concat(s,"ms ").concat(c):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},O);return j.createElement("div",{tabIndex:-1,className:S,style:b,ref:function(P){n.wrapperNode=P}},u)}}])}(I.PureComponent),IT=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},pr={isSsr:IT()};function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function mf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mf(Object(r),!0).forEach(function(n){wu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function LT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$v(n.key),n)}}function RT(e,t,r){return t&&LT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function BT(e,t,r){return t=sa(t),FT(e,jv()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function FT(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return UT(e)}function UT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function jv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(jv=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function WT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qs(e,t)}function qs(e,t){return qs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},qs(e,t)}function wu(e,t,r){return t=$v(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $v(e){var t=zT(e,"string");return Ir(t)=="symbol"?t:t+""}function zT(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function qT(e){return e.dataKey}function HT(e,t){return j.isValidElement(e)?j.cloneElement(e,t):typeof e=="function"?j.createElement(e,t):j.createElement(bT,t)}var He=function(e){function t(){return DT(this,t),BT(this,t,arguments)}return WT(t,e),RT(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,s=i.animationDuration,c=i.animationEasing,u=i.content,f=i.coordinate,l=i.filterNull,d=i.isAnimationActive,h=i.offset,p=i.payload,v=i.payloadUniqBy,m=i.position,x=i.reverseDirection,O=i.useTranslate3d,w=i.viewBox,S=i.wrapperStyle,g=p??[];l&&g.length&&(g=mv(p.filter(function(A){return A.value!=null&&(A.hide!==!0||n.props.includeHidden)}),v,qT));var b=g.length>0;return j.createElement(kT,{allowEscapeViewBox:o,animationDuration:s,animationEasing:c,isAnimationActive:d,active:a,coordinate:f,hasPayload:b,offset:h,position:m,reverseDirection:x,useTranslate3d:O,viewBox:w,wrapperStyle:S},HT(u,gf(gf({},this.props),{},{payload:g})))}}])}(I.PureComponent);wu(He,"displayName","Tooltip");wu(He,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!pr.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var KT=yt,GT=function(){return KT.Date.now()},VT=GT,XT=/\s/;function YT(e){for(var t=e.length;t--&&XT.test(e.charAt(t)););return t}var ZT=YT,JT=ZT,QT=/^\s+/;function eE(e){return e&&e.slice(0,JT(e)+1).replace(QT,"")}var tE=eE,rE=tE,bf=Ft,nE=nn,xf=NaN,iE=/^[-+]0x[0-9a-f]+$/i,aE=/^0b[01]+$/i,oE=/^0o[0-7]+$/i,sE=parseInt;function cE(e){if(typeof e=="number")return e;if(nE(e))return xf;if(bf(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=bf(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=rE(e);var r=aE.test(e);return r||oE.test(e)?sE(e.slice(2),r?2:8):iE.test(e)?xf:+e}var Tv=cE,uE=Ft,rs=VT,wf=Tv,lE="Expected a function",fE=Math.max,dE=Math.min;function hE(e,t,r){var n,i,a,o,s,c,u=0,f=!1,l=!1,d=!0;if(typeof e!="function")throw new TypeError(lE);t=wf(t)||0,uE(r)&&(f=!!r.leading,l="maxWait"in r,a=l?fE(wf(r.maxWait)||0,t):a,d="trailing"in r?!!r.trailing:d);function h(b){var A=n,P=i;return n=i=void 0,u=b,o=e.apply(P,A),o}function p(b){return u=b,s=setTimeout(x,t),f?h(b):o}function v(b){var A=b-c,P=b-u,_=t-A;return l?dE(_,a-P):_}function m(b){var A=b-c,P=b-u;return c===void 0||A>=t||A<0||l&&P>=a}function x(){var b=rs();if(m(b))return O(b);s=setTimeout(x,v(b))}function O(b){return s=void 0,d&&n?h(b):(n=i=void 0,o)}function w(){s!==void 0&&clearTimeout(s),u=0,n=c=i=s=void 0}function S(){return s===void 0?o:O(rs())}function g(){var b=rs(),A=m(b);if(n=arguments,i=this,c=b,A){if(s===void 0)return p(c);if(l)return clearTimeout(s),s=setTimeout(x,t),h(c)}return s===void 0&&(s=setTimeout(x,t)),o}return g.cancel=w,g.flush=S,g}var pE=hE,vE=pE,yE=Ft,mE="Expected a function";function gE(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(mE);return yE(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),vE(e,t,{leading:n,maxWait:t,trailing:i})}var bE=gE;const Ev=ce(bE);function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function Of(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Of(Object(r),!0).forEach(function(n){xE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Of(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xE(e,t,r){return t=wE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wE(e){var t=OE(e,"string");return Bn(t)=="symbol"?t:t+""}function OE(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function AE(e,t){return jE(e)||_E(e,t)||PE(e,t)||SE()}function SE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function PE(e,t){if(e){if(typeof e=="string")return Af(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Af(e,t)}}function Af(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _E(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function jE(e){if(Array.isArray(e))return e}var ki=I.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,s=e.height,c=s===void 0?"100%":s,u=e.minWidth,f=u===void 0?0:u,l=e.minHeight,d=e.maxHeight,h=e.children,p=e.debounce,v=p===void 0?0:p,m=e.id,x=e.className,O=e.onResize,w=e.style,S=w===void 0?{}:w,g=I.useRef(null),b=I.useRef();b.current=O,I.useImperativeHandle(t,function(){return Object.defineProperty(g.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),g.current},configurable:!0})});var A=I.useState({containerWidth:i.width,containerHeight:i.height}),P=AE(A,2),_=P[0],E=P[1],$=I.useCallback(function(M,k){E(function(C){var D=Math.round(M),L=Math.round(k);return C.containerWidth===D&&C.containerHeight===L?C:{containerWidth:D,containerHeight:L}})},[]);I.useEffect(function(){var M=function(U){var H,V=U[0].contentRect,z=V.width,X=V.height;$(z,X),(H=b.current)===null||H===void 0||H.call(b,z,X)};v>0&&(M=Ev(M,v,{trailing:!0,leading:!1}));var k=new ResizeObserver(M),C=g.current.getBoundingClientRect(),D=C.width,L=C.height;return $(D,L),k.observe(g.current),function(){k.disconnect()}},[$,v]);var T=I.useMemo(function(){var M=_.containerWidth,k=_.containerHeight;if(M<0||k<0)return null;ct(er(o)||er(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),ct(!r||r>0,"The aspect(%s) must be greater than zero.",r);var C=er(o)?M:o,D=er(c)?k:c;r&&r>0&&(C?D=C/r:D&&(C=D*r),d&&D>d&&(D=d)),ct(C>0||D>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,C,D,o,c,f,l,r);var L=!Array.isArray(h)&&At(h.type).endsWith("Chart");return j.Children.map(h,function(F){return j.isValidElement(F)?I.cloneElement(F,Mi({width:C,height:D},L?{style:Mi({height:"100%",width:"100%",maxHeight:D,maxWidth:C},F.props.style)}:{})):F})},[r,h,c,d,l,f,_,o]);return j.createElement("div",{id:m?"".concat(m):void 0,className:Q("recharts-responsive-container",x),style:Mi(Mi({},S),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:d}),ref:g},T)}),go=function(t){return null};go.displayName="Cell";function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function Sf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sf(Object(r),!0).forEach(function(n){$E(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $E(e,t,r){return t=TE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TE(e){var t=EE(e,"string");return Fn(t)=="symbol"?t:t+""}function EE(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var br={widthCache:{},cacheCount:0},NE=2e3,CE={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Pf="recharts_measurement_span";function ME(e){var t=Hs({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var $n=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||pr.isSsr)return{width:0,height:0};var n=ME(r),i=JSON.stringify({text:t,copyStyle:n});if(br.widthCache[i])return br.widthCache[i];try{var a=document.getElementById(Pf);a||(a=document.createElement("span"),a.setAttribute("id",Pf),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Hs(Hs({},CE),n);Object.assign(a.style,o),a.textContent="".concat(t);var s=a.getBoundingClientRect(),c={width:s.width,height:s.height};return br.widthCache[i]=c,++br.cacheCount>NE&&(br.cacheCount=0,br.widthCache={}),c}catch{return{width:0,height:0}}},kE=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function ca(e,t){return RE(e)||LE(e,t)||DE(e,t)||IE()}function IE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DE(e,t){if(e){if(typeof e=="string")return _f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _f(e,t)}}function _f(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function LE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function RE(e){if(Array.isArray(e))return e}function BE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jf(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,UE(n.key),n)}}function FE(e,t,r){return t&&jf(e.prototype,t),r&&jf(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function UE(e){var t=WE(e,"string");return Un(t)=="symbol"?t:t+""}function WE(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var $f=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Tf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,zE=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,qE=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Nv={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},HE=Object.keys(Nv),Ar="NaN";function KE(e,t){return e*Nv[t]}var Ii=function(){function e(t,r){BE(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!zE.test(r)&&(this.num=NaN,this.unit=""),HE.includes(r)&&(this.num=KE(t,r),this.unit="px")}return FE(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=qE.exec(r))!==null&&n!==void 0?n:[],a=ca(i,3),o=a[1],s=a[2];return new e(parseFloat(o),s??"")}}])}();function Cv(e){if(e.includes(Ar))return Ar;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=$f.exec(t))!==null&&r!==void 0?r:[],i=ca(n,4),a=i[1],o=i[2],s=i[3],c=Ii.parse(a??""),u=Ii.parse(s??""),f=o==="*"?c.multiply(u):c.divide(u);if(f.isNaN())return Ar;t=t.replace($f,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,d=(l=Tf.exec(t))!==null&&l!==void 0?l:[],h=ca(d,4),p=h[1],v=h[2],m=h[3],x=Ii.parse(p??""),O=Ii.parse(m??""),w=v==="+"?x.add(O):x.subtract(O);if(w.isNaN())return Ar;t=t.replace(Tf,w.toString())}return t}var Ef=/\(([^()]*)\)/;function GE(e){for(var t=e;t.includes("(");){var r=Ef.exec(t),n=ca(r,2),i=n[1];t=t.replace(Ef,Cv(i))}return t}function VE(e){var t=e.replace(/\s+/g,"");return t=GE(t),t=Cv(t),t}function XE(e){try{return VE(e)}catch{return Ar}}function ns(e){var t=XE(e.slice(5,-1));return t===Ar?"":t}var YE=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],ZE=["dx","dy","angle","className","breakAll"];function Ks(){return Ks=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ks.apply(this,arguments)}function Nf(e,t){if(e==null)return{};var r=JE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Cf(e,t){return rN(e)||tN(e,t)||eN(e,t)||QE()}function QE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eN(e,t){if(e){if(typeof e=="string")return Mf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Mf(e,t)}}function Mf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function tN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function rN(e){if(Array.isArray(e))return e}var Mv=/[ \f\n\r\t\v\u2028\u2029]+/,kv=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Z(r)||(n?a=r.toString().split(""):a=r.toString().split(Mv));var o=a.map(function(c){return{word:c,width:$n(c,i).width}}),s=n?0:$n(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:s}}catch{return null}},nN=function(t,r,n,i,a){var o=t.maxLines,s=t.children,c=t.style,u=t.breakAll,f=B(o),l=s,d=function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return C.reduce(function(D,L){var F=L.word,U=L.width,H=D[D.length-1];if(H&&(i==null||a||H.width+U+n<Number(i)))H.words.push(F),H.width+=U+n;else{var V={words:[F],width:U};D.push(V)}return D},[])},h=d(r),p=function(C){return C.reduce(function(D,L){return D.width>L.width?D:L})};if(!f)return h;for(var v="…",m=function(C){var D=l.slice(0,C),L=kv({breakAll:u,style:c,children:D+v}).wordsWithComputedWidth,F=d(L),U=F.length>o||p(F).width>Number(i);return[U,F]},x=0,O=l.length-1,w=0,S;x<=O&&w<=l.length-1;){var g=Math.floor((x+O)/2),b=g-1,A=m(b),P=Cf(A,2),_=P[0],E=P[1],$=m(g),T=Cf($,1),M=T[0];if(!_&&!M&&(x=g+1),_&&M&&(O=g-1),!_&&M){S=E;break}w++}return S||h},kf=function(t){var r=Z(t)?[]:t.toString().split(Mv);return[{words:r}]},iN=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,s=t.maxLines;if((r||n)&&!pr.isSsr){var c,u,f=kv({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,d=f.spaceWidth;c=l,u=d}else return kf(i);return nN({breakAll:o,children:i,maxLines:s,style:a},c,u,r,n)}return kf(i)},If="#808080",ur=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,s=o===void 0?"1em":o,c=t.capHeight,u=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,d=t.textAnchor,h=d===void 0?"start":d,p=t.verticalAnchor,v=p===void 0?"end":p,m=t.fill,x=m===void 0?If:m,O=Nf(t,YE),w=I.useMemo(function(){return iN({breakAll:O.breakAll,children:O.children,maxLines:O.maxLines,scaleToFit:l,style:O.style,width:O.width})},[O.breakAll,O.children,O.maxLines,l,O.style,O.width]),S=O.dx,g=O.dy,b=O.angle,A=O.className,P=O.breakAll,_=Nf(O,ZE);if(!Pe(n)||!Pe(a))return null;var E=n+(B(S)?S:0),$=a+(B(g)?g:0),T;switch(v){case"start":T=ns("calc(".concat(u,")"));break;case"middle":T=ns("calc(".concat((w.length-1)/2," * -").concat(s," + (").concat(u," / 2))"));break;default:T=ns("calc(".concat(w.length-1," * -").concat(s,")"));break}var M=[];if(l){var k=w[0].width,C=O.width;M.push("scale(".concat((B(C)?C/k:1)/k,")"))}return b&&M.push("rotate(".concat(b,", ").concat(E,", ").concat($,")")),M.length&&(_.transform=M.join(" ")),j.createElement("text",Ks({},K(_,!0),{x:E,y:$,className:Q("recharts-text",A),textAnchor:h,fill:x.includes("url")?If:x}),w.map(function(D,L){var F=D.words.join(P?"":" ");return j.createElement("tspan",{x:E,dy:L===0?T:s,key:"".concat(F,"-").concat(L)},F)}))};function Bt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function aN(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Ou(e){let t,r,n;e.length!==2?(t=Bt,r=(s,c)=>Bt(e(s),c),n=(s,c)=>e(s)-c):(t=e===Bt||e===aN?e:oN,r=e,n=e);function i(s,c,u=0,f=s.length){if(u<f){if(t(c,c)!==0)return f;do{const l=u+f>>>1;r(s[l],c)<0?u=l+1:f=l}while(u<f)}return u}function a(s,c,u=0,f=s.length){if(u<f){if(t(c,c)!==0)return f;do{const l=u+f>>>1;r(s[l],c)<=0?u=l+1:f=l}while(u<f)}return u}function o(s,c,u=0,f=s.length){const l=i(s,c,u,f-1);return l>u&&n(s[l-1],c)>-n(s[l],c)?l-1:l}return{left:i,center:o,right:a}}function oN(){return 0}function Iv(e){return e===null?NaN:+e}function*sN(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const cN=Ou(Bt),wi=cN.right;Ou(Iv).center;class Df extends Map{constructor(t,r=fN){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Lf(this,t))}has(t){return super.has(Lf(this,t))}set(t,r){return super.set(uN(this,t),r)}delete(t){return super.delete(lN(this,t))}}function Lf({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function uN({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function lN({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function fN(e){return e!==null&&typeof e=="object"?e.valueOf():e}function dN(e=Bt){if(e===Bt)return Dv;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function Dv(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const hN=Math.sqrt(50),pN=Math.sqrt(10),vN=Math.sqrt(2);function ua(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=hN?10:a>=pN?5:a>=vN?2:1;let s,c,u;return i<0?(u=Math.pow(10,-i)/o,s=Math.round(e*u),c=Math.round(t*u),s/u<e&&++s,c/u>t&&--c,u=-u):(u=Math.pow(10,i)*o,s=Math.round(e/u),c=Math.round(t/u),s*u<e&&++s,c*u>t&&--c),c<s&&.5<=r&&r<2?ua(e,t,r*2):[s,c,u]}function Gs(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?ua(t,e,r):ua(e,t,r);if(!(a>=i))return[];const s=a-i+1,c=new Array(s);if(n)if(o<0)for(let u=0;u<s;++u)c[u]=(a-u)/-o;else for(let u=0;u<s;++u)c[u]=(a-u)*o;else if(o<0)for(let u=0;u<s;++u)c[u]=(i+u)/-o;else for(let u=0;u<s;++u)c[u]=(i+u)*o;return c}function Vs(e,t,r){return t=+t,e=+e,r=+r,ua(e,t,r)[2]}function Xs(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Vs(t,e,r):Vs(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Rf(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Bf(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function Lv(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?Dv:dN(i);n>r;){if(n-r>600){const c=n-r+1,u=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),d=.5*Math.sqrt(f*l*(c-l)/c)*(u-c/2<0?-1:1),h=Math.max(r,Math.floor(t-u*l/c+d)),p=Math.min(n,Math.floor(t+(c-u)*l/c+d));Lv(e,t,h,p,i)}const a=e[t];let o=r,s=n;for(bn(e,r,t),i(e[n],a)>0&&bn(e,r,n);o<s;){for(bn(e,o,s),++o,--s;i(e[o],a)<0;)++o;for(;i(e[s],a)>0;)--s}i(e[r],a)===0?bn(e,r,s):(++s,bn(e,s,n)),s<=t&&(r=s+1),t<=s&&(n=s-1)}return e}function bn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function yN(e,t,r){if(e=Float64Array.from(sN(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Bf(e);if(t>=1)return Rf(e);var n,i=(n-1)*t,a=Math.floor(i),o=Rf(Lv(e,a).subarray(0,a+1)),s=Bf(e.subarray(a+1));return o+(s-o)*(i-a)}}function mN(e,t,r=Iv){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),s=+r(e[a+1],a+1,e);return o+(s-o)*(i-a)}}function gN(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function rt(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Ct(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Ys=Symbol("implicit");function Au(){var e=new Df,t=[],r=[],n=Ys;function i(a){let o=e.get(a);if(o===void 0){if(n!==Ys)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Df;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Au(t,r).unknown(n)},rt.apply(i,arguments),i}function Wn(){var e=Au().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,s=!1,c=0,u=0,f=.5;delete e.unknown;function l(){var d=t().length,h=i<n,p=h?i:n,v=h?n:i;a=(v-p)/Math.max(1,d-c+u*2),s&&(a=Math.floor(a)),p+=(v-p-a*(d-c))*f,o=a*(1-c),s&&(p=Math.round(p),o=Math.round(o));var m=gN(d).map(function(x){return p+a*x});return r(h?m.reverse():m)}return e.domain=function(d){return arguments.length?(t(d),l()):t()},e.range=function(d){return arguments.length?([n,i]=d,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(d){return[n,i]=d,n=+n,i=+i,s=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(d){return arguments.length?(s=!!d,l()):s},e.padding=function(d){return arguments.length?(c=Math.min(1,u=+d),l()):c},e.paddingInner=function(d){return arguments.length?(c=Math.min(1,d),l()):c},e.paddingOuter=function(d){return arguments.length?(u=+d,l()):u},e.align=function(d){return arguments.length?(f=Math.max(0,Math.min(1,d)),l()):f},e.copy=function(){return Wn(t(),[n,i]).round(s).paddingInner(c).paddingOuter(u).align(f)},rt.apply(l(),arguments)}function Rv(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Rv(t())},e}function Tn(){return Rv(Wn.apply(null,arguments).paddingInner(1))}function Su(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Bv(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Oi(){}var zn=.7,la=1/zn,Tr="\\s*([+-]?\\d+)\\s*",qn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",dt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",bN=/^#([0-9a-f]{3,8})$/,xN=new RegExp(`^rgb\\(${Tr},${Tr},${Tr}\\)$`),wN=new RegExp(`^rgb\\(${dt},${dt},${dt}\\)$`),ON=new RegExp(`^rgba\\(${Tr},${Tr},${Tr},${qn}\\)$`),AN=new RegExp(`^rgba\\(${dt},${dt},${dt},${qn}\\)$`),SN=new RegExp(`^hsl\\(${qn},${dt},${dt}\\)$`),PN=new RegExp(`^hsla\\(${qn},${dt},${dt},${qn}\\)$`),Ff={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Su(Oi,Hn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Uf,formatHex:Uf,formatHex8:_N,formatHsl:jN,formatRgb:Wf,toString:Wf});function Uf(){return this.rgb().formatHex()}function _N(){return this.rgb().formatHex8()}function jN(){return Fv(this).formatHsl()}function Wf(){return this.rgb().formatRgb()}function Hn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=bN.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?zf(t):r===3?new Be(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Di(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Di(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=xN.exec(e))?new Be(t[1],t[2],t[3],1):(t=wN.exec(e))?new Be(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=ON.exec(e))?Di(t[1],t[2],t[3],t[4]):(t=AN.exec(e))?Di(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=SN.exec(e))?Kf(t[1],t[2]/100,t[3]/100,1):(t=PN.exec(e))?Kf(t[1],t[2]/100,t[3]/100,t[4]):Ff.hasOwnProperty(e)?zf(Ff[e]):e==="transparent"?new Be(NaN,NaN,NaN,0):null}function zf(e){return new Be(e>>16&255,e>>8&255,e&255,1)}function Di(e,t,r,n){return n<=0&&(e=t=r=NaN),new Be(e,t,r,n)}function $N(e){return e instanceof Oi||(e=Hn(e)),e?(e=e.rgb(),new Be(e.r,e.g,e.b,e.opacity)):new Be}function Zs(e,t,r,n){return arguments.length===1?$N(e):new Be(e,t,r,n??1)}function Be(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Su(Be,Zs,Bv(Oi,{brighter(e){return e=e==null?la:Math.pow(la,e),new Be(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?zn:Math.pow(zn,e),new Be(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Be(ir(this.r),ir(this.g),ir(this.b),fa(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:qf,formatHex:qf,formatHex8:TN,formatRgb:Hf,toString:Hf}));function qf(){return`#${tr(this.r)}${tr(this.g)}${tr(this.b)}`}function TN(){return`#${tr(this.r)}${tr(this.g)}${tr(this.b)}${tr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Hf(){const e=fa(this.opacity);return`${e===1?"rgb(":"rgba("}${ir(this.r)}, ${ir(this.g)}, ${ir(this.b)}${e===1?")":`, ${e})`}`}function fa(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ir(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function tr(e){return e=ir(e),(e<16?"0":"")+e.toString(16)}function Kf(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new st(e,t,r,n)}function Fv(e){if(e instanceof st)return new st(e.h,e.s,e.l,e.opacity);if(e instanceof Oi||(e=Hn(e)),!e)return new st;if(e instanceof st)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,s=a-i,c=(a+i)/2;return s?(t===a?o=(r-n)/s+(r<n)*6:r===a?o=(n-t)/s+2:o=(t-r)/s+4,s/=c<.5?a+i:2-a-i,o*=60):s=c>0&&c<1?0:o,new st(o,s,c,e.opacity)}function EN(e,t,r,n){return arguments.length===1?Fv(e):new st(e,t,r,n??1)}function st(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Su(st,EN,Bv(Oi,{brighter(e){return e=e==null?la:Math.pow(la,e),new st(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?zn:Math.pow(zn,e),new st(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Be(is(e>=240?e-240:e+120,i,n),is(e,i,n),is(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new st(Gf(this.h),Li(this.s),Li(this.l),fa(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=fa(this.opacity);return`${e===1?"hsl(":"hsla("}${Gf(this.h)}, ${Li(this.s)*100}%, ${Li(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Gf(e){return e=(e||0)%360,e<0?e+360:e}function Li(e){return Math.max(0,Math.min(1,e||0))}function is(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Pu=e=>()=>e;function NN(e,t){return function(r){return e+r*t}}function CN(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function MN(e){return(e=+e)==1?Uv:function(t,r){return r-t?CN(t,r,e):Pu(isNaN(t)?r:t)}}function Uv(e,t){var r=t-e;return r?NN(e,r):Pu(isNaN(e)?t:e)}const Vf=function e(t){var r=MN(t);function n(i,a){var o=r((i=Zs(i)).r,(a=Zs(a)).r),s=r(i.g,a.g),c=r(i.b,a.b),u=Uv(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=s(f),i.b=c(f),i.opacity=u(f),i+""}}return n.gamma=e,n}(1);function kN(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function IN(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function DN(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=hn(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(s){for(o=0;o<n;++o)a[o]=i[o](s);return a}}function LN(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function da(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function RN(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=hn(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Js=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,as=new RegExp(Js.source,"g");function BN(e){return function(){return e}}function FN(e){return function(t){return e(t)+""}}function UN(e,t){var r=Js.lastIndex=as.lastIndex=0,n,i,a,o=-1,s=[],c=[];for(e=e+"",t=t+"";(n=Js.exec(e))&&(i=as.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),s[o]?s[o]+=a:s[++o]=a),(n=n[0])===(i=i[0])?s[o]?s[o]+=i:s[++o]=i:(s[++o]=null,c.push({i:o,x:da(n,i)})),r=as.lastIndex;return r<t.length&&(a=t.slice(r),s[o]?s[o]+=a:s[++o]=a),s.length<2?c[0]?FN(c[0].x):BN(t):(t=c.length,function(u){for(var f=0,l;f<t;++f)s[(l=c[f]).i]=l.x(u);return s.join("")})}function hn(e,t){var r=typeof t,n;return t==null||r==="boolean"?Pu(t):(r==="number"?da:r==="string"?(n=Hn(t))?(t=n,Vf):UN:t instanceof Hn?Vf:t instanceof Date?LN:IN(t)?kN:Array.isArray(t)?DN:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?RN:da)(e,t)}function _u(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function WN(e,t){t===void 0&&(t=e,e=hn);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var s=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[s](o-s)}}function zN(e){return function(){return e}}function ha(e){return+e}var Xf=[0,1];function De(e){return e}function Qs(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:zN(isNaN(t)?NaN:.5)}function qN(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function HN(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Qs(i,n),a=r(o,a)):(n=Qs(n,i),a=r(a,o)),function(s){return a(n(s))}}function KN(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Qs(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(s){var c=wi(e,s,1,n)-1;return a[c](i[c](s))}}function Ai(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function bo(){var e=Xf,t=Xf,r=hn,n,i,a,o=De,s,c,u;function f(){var d=Math.min(e.length,t.length);return o!==De&&(o=qN(e[0],e[d-1])),s=d>2?KN:HN,c=u=null,l}function l(d){return d==null||isNaN(d=+d)?a:(c||(c=s(e.map(n),t,r)))(n(o(d)))}return l.invert=function(d){return o(i((u||(u=s(t,e.map(n),da)))(d)))},l.domain=function(d){return arguments.length?(e=Array.from(d,ha),f()):e.slice()},l.range=function(d){return arguments.length?(t=Array.from(d),f()):t.slice()},l.rangeRound=function(d){return t=Array.from(d),r=_u,f()},l.clamp=function(d){return arguments.length?(o=d?!0:De,f()):o!==De},l.interpolate=function(d){return arguments.length?(r=d,f()):r},l.unknown=function(d){return arguments.length?(a=d,l):a},function(d,h){return n=d,i=h,f()}}function ju(){return bo()(De,De)}function GN(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function pa(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Dr(e){return e=pa(Math.abs(e)),e?e[1]:NaN}function VN(e,t){return function(r,n){for(var i=r.length,a=[],o=0,s=e[0],c=0;i>0&&s>0&&(c+s+1>n&&(s=Math.max(1,n-c)),a.push(r.substring(i-=s,i+s)),!((c+=s+1)>n));)s=e[o=(o+1)%e.length];return a.reverse().join(t)}}function XN(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var YN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Kn(e){if(!(t=YN.exec(e)))throw new Error("invalid format: "+e);var t;return new $u({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Kn.prototype=$u.prototype;function $u(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}$u.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function ZN(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Wv;function JN(e,t){var r=pa(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Wv=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+pa(e,Math.max(0,t+a-1))[0]}function Yf(e,t){var r=pa(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Zf={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:GN,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Yf(e*100,t),r:Yf,s:JN,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Jf(e){return e}var Qf=Array.prototype.map,ed=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function QN(e){var t=e.grouping===void 0||e.thousands===void 0?Jf:VN(Qf.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Jf:XN(Qf.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",s=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function u(l){l=Kn(l);var d=l.fill,h=l.align,p=l.sign,v=l.symbol,m=l.zero,x=l.width,O=l.comma,w=l.precision,S=l.trim,g=l.type;g==="n"?(O=!0,g="g"):Zf[g]||(w===void 0&&(w=12),S=!0,g="g"),(m||d==="0"&&h==="=")&&(m=!0,d="0",h="=");var b=v==="$"?r:v==="#"&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",A=v==="$"?n:/[%p]/.test(g)?o:"",P=Zf[g],_=/[defgprs%]/.test(g);w=w===void 0?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function E($){var T=b,M=A,k,C,D;if(g==="c")M=P($)+M,$="";else{$=+$;var L=$<0||1/$<0;if($=isNaN($)?c:P(Math.abs($),w),S&&($=ZN($)),L&&+$==0&&p!=="+"&&(L=!1),T=(L?p==="("?p:s:p==="-"||p==="("?"":p)+T,M=(g==="s"?ed[8+Wv/3]:"")+M+(L&&p==="("?")":""),_){for(k=-1,C=$.length;++k<C;)if(D=$.charCodeAt(k),48>D||D>57){M=(D===46?i+$.slice(k+1):$.slice(k))+M,$=$.slice(0,k);break}}}O&&!m&&($=t($,1/0));var F=T.length+$.length+M.length,U=F<x?new Array(x-F+1).join(d):"";switch(O&&m&&($=t(U+$,U.length?x-M.length:1/0),U=""),h){case"<":$=T+$+M+U;break;case"=":$=T+U+$+M;break;case"^":$=U.slice(0,F=U.length>>1)+T+$+M+U.slice(F);break;default:$=U+T+$+M;break}return a($)}return E.toString=function(){return l+""},E}function f(l,d){var h=u((l=Kn(l),l.type="f",l)),p=Math.max(-8,Math.min(8,Math.floor(Dr(d)/3)))*3,v=Math.pow(10,-p),m=ed[8+p/3];return function(x){return h(v*x)+m}}return{format:u,formatPrefix:f}}var Ri,Tu,zv;eC({thousands:",",grouping:[3],currency:["$",""]});function eC(e){return Ri=QN(e),Tu=Ri.format,zv=Ri.formatPrefix,Ri}function tC(e){return Math.max(0,-Dr(Math.abs(e)))}function rC(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Dr(t)/3)))*3-Dr(Math.abs(e)))}function nC(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Dr(t)-Dr(e))+1}function qv(e,t,r,n){var i=Xs(e,t,r),a;switch(n=Kn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=rC(i,o))&&(n.precision=a),zv(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=nC(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=tC(i))&&(n.precision=a-(n.type==="%")*2);break}}return Tu(n)}function Ut(e){var t=e.domain;return e.ticks=function(r){var n=t();return Gs(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return qv(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],s=n[a],c,u,f=10;for(s<o&&(u=o,o=s,s=u,u=i,i=a,a=u);f-- >0;){if(u=Vs(o,s,r),u===c)return n[i]=o,n[a]=s,t(n);if(u>0)o=Math.floor(o/u)*u,s=Math.ceil(s/u)*u;else if(u<0)o=Math.ceil(o*u)/u,s=Math.floor(s*u)/u;else break;c=u}return e},e}function va(){var e=ju();return e.copy=function(){return Ai(e,va())},rt.apply(e,arguments),Ut(e)}function Hv(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,ha),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Hv(e).unknown(t)},e=arguments.length?Array.from(e,ha):[0,1],Ut(r)}function Kv(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function td(e){return Math.log(e)}function rd(e){return Math.exp(e)}function iC(e){return-Math.log(-e)}function aC(e){return-Math.exp(-e)}function oC(e){return isFinite(e)?+("1e"+e):e<0?0:e}function sC(e){return e===10?oC:e===Math.E?Math.exp:t=>Math.pow(e,t)}function cC(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function nd(e){return(t,r)=>-e(-t,r)}function Eu(e){const t=e(td,rd),r=t.domain;let n=10,i,a;function o(){return i=cC(n),a=sC(n),r()[0]<0?(i=nd(i),a=nd(a),e(iC,aC)):e(td,rd),t}return t.base=function(s){return arguments.length?(n=+s,o()):n},t.domain=function(s){return arguments.length?(r(s),o()):r()},t.ticks=s=>{const c=r();let u=c[0],f=c[c.length-1];const l=f<u;l&&([u,f]=[f,u]);let d=i(u),h=i(f),p,v;const m=s==null?10:+s;let x=[];if(!(n%1)&&h-d<m){if(d=Math.floor(d),h=Math.ceil(h),u>0){for(;d<=h;++d)for(p=1;p<n;++p)if(v=d<0?p/a(-d):p*a(d),!(v<u)){if(v>f)break;x.push(v)}}else for(;d<=h;++d)for(p=n-1;p>=1;--p)if(v=d>0?p/a(-d):p*a(d),!(v<u)){if(v>f)break;x.push(v)}x.length*2<m&&(x=Gs(u,f,m))}else x=Gs(d,h,Math.min(h-d,m)).map(a);return l?x.reverse():x},t.tickFormat=(s,c)=>{if(s==null&&(s=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=Kn(c)).precision==null&&(c.trim=!0),c=Tu(c)),s===1/0)return c;const u=Math.max(1,n*s/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=u?c(f):""}},t.nice=()=>r(Kv(r(),{floor:s=>a(Math.floor(i(s))),ceil:s=>a(Math.ceil(i(s)))})),t}function Gv(){const e=Eu(bo()).domain([1,10]);return e.copy=()=>Ai(e,Gv()).base(e.base()),rt.apply(e,arguments),e}function id(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ad(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Nu(e){var t=1,r=e(id(t),ad(t));return r.constant=function(n){return arguments.length?e(id(t=+n),ad(t)):t},Ut(r)}function Vv(){var e=Nu(bo());return e.copy=function(){return Ai(e,Vv()).constant(e.constant())},rt.apply(e,arguments)}function od(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function uC(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function lC(e){return e<0?-e*e:e*e}function Cu(e){var t=e(De,De),r=1;function n(){return r===1?e(De,De):r===.5?e(uC,lC):e(od(r),od(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Ut(t)}function Mu(){var e=Cu(bo());return e.copy=function(){return Ai(e,Mu()).exponent(e.exponent())},rt.apply(e,arguments),e}function fC(){return Mu.apply(null,arguments).exponent(.5)}function sd(e){return Math.sign(e)*e*e}function dC(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Xv(){var e=ju(),t=[0,1],r=!1,n;function i(a){var o=dC(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(sd(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,ha)).map(sd)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Xv(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},rt.apply(i,arguments),Ut(i)}function Yv(){var e=[],t=[],r=[],n;function i(){var o=0,s=Math.max(1,t.length);for(r=new Array(s-1);++o<s;)r[o-1]=mN(e,o/s);return a}function a(o){return o==null||isNaN(o=+o)?n:t[wi(r,o)]}return a.invertExtent=function(o){var s=t.indexOf(o);return s<0?[NaN,NaN]:[s>0?r[s-1]:e[0],s<r.length?r[s]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let s of o)s!=null&&!isNaN(s=+s)&&e.push(s);return e.sort(Bt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return Yv().domain(e).range(t).unknown(n)},rt.apply(a,arguments)}function Zv(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[wi(n,c,0,r)]:a}function s(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,s()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,s()):i.slice()},o.invertExtent=function(c){var u=i.indexOf(c);return u<0?[NaN,NaN]:u<1?[e,n[0]]:u>=r?[n[r-1],t]:[n[u-1],n[u]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Zv().domain([e,t]).range(i).unknown(a)},rt.apply(Ut(o),arguments)}function Jv(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[wi(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Jv().domain(e).range(t).unknown(r)},rt.apply(i,arguments)}const os=new Date,ss=new Date;function _e(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),s=i.ceil(a);return a-o<s-a?o:s},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,s)=>{const c=[];if(a=i.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return c;let u;do c.push(u=new Date(+a)),t(a,s),e(a);while(u<a&&a<o);return c},i.filter=a=>_e(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,s)=>{if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(os.setTime(+a),ss.setTime(+o),e(os),e(ss),Math.floor(r(os,ss))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const ya=_e(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);ya.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?_e(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):ya);ya.range;const xt=1e3,Qe=xt*60,wt=Qe*60,jt=wt*24,ku=jt*7,cd=jt*30,cs=jt*365,rr=_e(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*xt)},(e,t)=>(t-e)/xt,e=>e.getUTCSeconds());rr.range;const Iu=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*xt)},(e,t)=>{e.setTime(+e+t*Qe)},(e,t)=>(t-e)/Qe,e=>e.getMinutes());Iu.range;const Du=_e(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Qe)},(e,t)=>(t-e)/Qe,e=>e.getUTCMinutes());Du.range;const Lu=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*xt-e.getMinutes()*Qe)},(e,t)=>{e.setTime(+e+t*wt)},(e,t)=>(t-e)/wt,e=>e.getHours());Lu.range;const Ru=_e(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*wt)},(e,t)=>(t-e)/wt,e=>e.getUTCHours());Ru.range;const Si=_e(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Qe)/jt,e=>e.getDate()-1);Si.range;const xo=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/jt,e=>e.getUTCDate()-1);xo.range;const Qv=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/jt,e=>Math.floor(e/jt));Qv.range;function vr(e){return _e(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Qe)/ku)}const wo=vr(0),ma=vr(1),hC=vr(2),pC=vr(3),Lr=vr(4),vC=vr(5),yC=vr(6);wo.range;ma.range;hC.range;pC.range;Lr.range;vC.range;yC.range;function yr(e){return _e(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/ku)}const Oo=yr(0),ga=yr(1),mC=yr(2),gC=yr(3),Rr=yr(4),bC=yr(5),xC=yr(6);Oo.range;ga.range;mC.range;gC.range;Rr.range;bC.range;xC.range;const Bu=_e(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Bu.range;const Fu=_e(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Fu.range;const $t=_e(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());$t.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});$t.range;const Tt=_e(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());Tt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});Tt.range;function ey(e,t,r,n,i,a){const o=[[rr,1,xt],[rr,5,5*xt],[rr,15,15*xt],[rr,30,30*xt],[a,1,Qe],[a,5,5*Qe],[a,15,15*Qe],[a,30,30*Qe],[i,1,wt],[i,3,3*wt],[i,6,6*wt],[i,12,12*wt],[n,1,jt],[n,2,2*jt],[r,1,ku],[t,1,cd],[t,3,3*cd],[e,1,cs]];function s(u,f,l){const d=f<u;d&&([u,f]=[f,u]);const h=l&&typeof l.range=="function"?l:c(u,f,l),p=h?h.range(u,+f+1):[];return d?p.reverse():p}function c(u,f,l){const d=Math.abs(f-u)/l,h=Ou(([,,m])=>m).right(o,d);if(h===o.length)return e.every(Xs(u/cs,f/cs,l));if(h===0)return ya.every(Math.max(Xs(u,f,l),1));const[p,v]=o[d/o[h-1][2]<o[h][2]/d?h-1:h];return p.every(v)}return[s,c]}const[wC,OC]=ey(Tt,Fu,Oo,Qv,Ru,Du),[AC,SC]=ey($t,Bu,wo,Si,Lu,Iu);function us(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ls(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function xn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function PC(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,s=e.months,c=e.shortMonths,u=wn(i),f=On(i),l=wn(a),d=On(a),h=wn(o),p=On(o),v=wn(s),m=On(s),x=wn(c),O=On(c),w={a:L,A:F,b:U,B:H,c:null,d:pd,e:pd,f:VC,g:i2,G:o2,H:HC,I:KC,j:GC,L:ty,m:XC,M:YC,p:V,q:z,Q:md,s:gd,S:ZC,u:JC,U:QC,V:e2,w:t2,W:r2,x:null,X:null,y:n2,Y:a2,Z:s2,"%":yd},S={a:X,A:de,b:ge,B:Ue,c:null,d:vd,e:vd,f:f2,g:w2,G:A2,H:c2,I:u2,j:l2,L:ny,m:d2,M:h2,p:qt,q:Le,Q:md,s:gd,S:p2,u:v2,U:y2,V:m2,w:g2,W:b2,x:null,X:null,y:x2,Y:O2,Z:S2,"%":yd},g={a:E,A:$,b:T,B:M,c:k,d:dd,e:dd,f:UC,g:fd,G:ld,H:hd,I:hd,j:LC,L:FC,m:DC,M:RC,p:_,q:IC,Q:zC,s:qC,S:BC,u:EC,U:NC,V:CC,w:TC,W:MC,x:C,X:D,y:fd,Y:ld,Z:kC,"%":WC};w.x=b(r,w),w.X=b(n,w),w.c=b(t,w),S.x=b(r,S),S.X=b(n,S),S.c=b(t,S);function b(W,J){return function(ee){var R=[],ye=-1,te=0,we=W.length,Oe,Re,kt;for(ee instanceof Date||(ee=new Date(+ee));++ye<we;)W.charCodeAt(ye)===37&&(R.push(W.slice(te,ye)),(Re=ud[Oe=W.charAt(++ye)])!=null?Oe=W.charAt(++ye):Re=Oe==="e"?" ":"0",(kt=J[Oe])&&(Oe=kt(ee,Re)),R.push(Oe),te=ye+1);return R.push(W.slice(te,ye)),R.join("")}}function A(W,J){return function(ee){var R=xn(1900,void 0,1),ye=P(R,W,ee+="",0),te,we;if(ye!=ee.length)return null;if("Q"in R)return new Date(R.Q);if("s"in R)return new Date(R.s*1e3+("L"in R?R.L:0));if(J&&!("Z"in R)&&(R.Z=0),"p"in R&&(R.H=R.H%12+R.p*12),R.m===void 0&&(R.m="q"in R?R.q:0),"V"in R){if(R.V<1||R.V>53)return null;"w"in R||(R.w=1),"Z"in R?(te=ls(xn(R.y,0,1)),we=te.getUTCDay(),te=we>4||we===0?ga.ceil(te):ga(te),te=xo.offset(te,(R.V-1)*7),R.y=te.getUTCFullYear(),R.m=te.getUTCMonth(),R.d=te.getUTCDate()+(R.w+6)%7):(te=us(xn(R.y,0,1)),we=te.getDay(),te=we>4||we===0?ma.ceil(te):ma(te),te=Si.offset(te,(R.V-1)*7),R.y=te.getFullYear(),R.m=te.getMonth(),R.d=te.getDate()+(R.w+6)%7)}else("W"in R||"U"in R)&&("w"in R||(R.w="u"in R?R.u%7:"W"in R?1:0),we="Z"in R?ls(xn(R.y,0,1)).getUTCDay():us(xn(R.y,0,1)).getDay(),R.m=0,R.d="W"in R?(R.w+6)%7+R.W*7-(we+5)%7:R.w+R.U*7-(we+6)%7);return"Z"in R?(R.H+=R.Z/100|0,R.M+=R.Z%100,ls(R)):us(R)}}function P(W,J,ee,R){for(var ye=0,te=J.length,we=ee.length,Oe,Re;ye<te;){if(R>=we)return-1;if(Oe=J.charCodeAt(ye++),Oe===37){if(Oe=J.charAt(ye++),Re=g[Oe in ud?J.charAt(ye++):Oe],!Re||(R=Re(W,ee,R))<0)return-1}else if(Oe!=ee.charCodeAt(R++))return-1}return R}function _(W,J,ee){var R=u.exec(J.slice(ee));return R?(W.p=f.get(R[0].toLowerCase()),ee+R[0].length):-1}function E(W,J,ee){var R=h.exec(J.slice(ee));return R?(W.w=p.get(R[0].toLowerCase()),ee+R[0].length):-1}function $(W,J,ee){var R=l.exec(J.slice(ee));return R?(W.w=d.get(R[0].toLowerCase()),ee+R[0].length):-1}function T(W,J,ee){var R=x.exec(J.slice(ee));return R?(W.m=O.get(R[0].toLowerCase()),ee+R[0].length):-1}function M(W,J,ee){var R=v.exec(J.slice(ee));return R?(W.m=m.get(R[0].toLowerCase()),ee+R[0].length):-1}function k(W,J,ee){return P(W,t,J,ee)}function C(W,J,ee){return P(W,r,J,ee)}function D(W,J,ee){return P(W,n,J,ee)}function L(W){return o[W.getDay()]}function F(W){return a[W.getDay()]}function U(W){return c[W.getMonth()]}function H(W){return s[W.getMonth()]}function V(W){return i[+(W.getHours()>=12)]}function z(W){return 1+~~(W.getMonth()/3)}function X(W){return o[W.getUTCDay()]}function de(W){return a[W.getUTCDay()]}function ge(W){return c[W.getUTCMonth()]}function Ue(W){return s[W.getUTCMonth()]}function qt(W){return i[+(W.getUTCHours()>=12)]}function Le(W){return 1+~~(W.getUTCMonth()/3)}return{format:function(W){var J=b(W+="",w);return J.toString=function(){return W},J},parse:function(W){var J=A(W+="",!1);return J.toString=function(){return W},J},utcFormat:function(W){var J=b(W+="",S);return J.toString=function(){return W},J},utcParse:function(W){var J=A(W+="",!0);return J.toString=function(){return W},J}}}var ud={"-":"",_:" ",0:"0"},Te=/^\s*\d+/,_C=/^%/,jC=/[\\^$*+?|[\]().{}]/g;function ne(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function $C(e){return e.replace(jC,"\\$&")}function wn(e){return new RegExp("^(?:"+e.map($C).join("|")+")","i")}function On(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function TC(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function EC(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function NC(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function CC(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function MC(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function ld(e,t,r){var n=Te.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function fd(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function kC(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function IC(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function DC(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function dd(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function LC(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function hd(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function RC(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function BC(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function FC(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function UC(e,t,r){var n=Te.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function WC(e,t,r){var n=_C.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function zC(e,t,r){var n=Te.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function qC(e,t,r){var n=Te.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function pd(e,t){return ne(e.getDate(),t,2)}function HC(e,t){return ne(e.getHours(),t,2)}function KC(e,t){return ne(e.getHours()%12||12,t,2)}function GC(e,t){return ne(1+Si.count($t(e),e),t,3)}function ty(e,t){return ne(e.getMilliseconds(),t,3)}function VC(e,t){return ty(e,t)+"000"}function XC(e,t){return ne(e.getMonth()+1,t,2)}function YC(e,t){return ne(e.getMinutes(),t,2)}function ZC(e,t){return ne(e.getSeconds(),t,2)}function JC(e){var t=e.getDay();return t===0?7:t}function QC(e,t){return ne(wo.count($t(e)-1,e),t,2)}function ry(e){var t=e.getDay();return t>=4||t===0?Lr(e):Lr.ceil(e)}function e2(e,t){return e=ry(e),ne(Lr.count($t(e),e)+($t(e).getDay()===4),t,2)}function t2(e){return e.getDay()}function r2(e,t){return ne(ma.count($t(e)-1,e),t,2)}function n2(e,t){return ne(e.getFullYear()%100,t,2)}function i2(e,t){return e=ry(e),ne(e.getFullYear()%100,t,2)}function a2(e,t){return ne(e.getFullYear()%1e4,t,4)}function o2(e,t){var r=e.getDay();return e=r>=4||r===0?Lr(e):Lr.ceil(e),ne(e.getFullYear()%1e4,t,4)}function s2(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ne(t/60|0,"0",2)+ne(t%60,"0",2)}function vd(e,t){return ne(e.getUTCDate(),t,2)}function c2(e,t){return ne(e.getUTCHours(),t,2)}function u2(e,t){return ne(e.getUTCHours()%12||12,t,2)}function l2(e,t){return ne(1+xo.count(Tt(e),e),t,3)}function ny(e,t){return ne(e.getUTCMilliseconds(),t,3)}function f2(e,t){return ny(e,t)+"000"}function d2(e,t){return ne(e.getUTCMonth()+1,t,2)}function h2(e,t){return ne(e.getUTCMinutes(),t,2)}function p2(e,t){return ne(e.getUTCSeconds(),t,2)}function v2(e){var t=e.getUTCDay();return t===0?7:t}function y2(e,t){return ne(Oo.count(Tt(e)-1,e),t,2)}function iy(e){var t=e.getUTCDay();return t>=4||t===0?Rr(e):Rr.ceil(e)}function m2(e,t){return e=iy(e),ne(Rr.count(Tt(e),e)+(Tt(e).getUTCDay()===4),t,2)}function g2(e){return e.getUTCDay()}function b2(e,t){return ne(ga.count(Tt(e)-1,e),t,2)}function x2(e,t){return ne(e.getUTCFullYear()%100,t,2)}function w2(e,t){return e=iy(e),ne(e.getUTCFullYear()%100,t,2)}function O2(e,t){return ne(e.getUTCFullYear()%1e4,t,4)}function A2(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Rr(e):Rr.ceil(e),ne(e.getUTCFullYear()%1e4,t,4)}function S2(){return"+0000"}function yd(){return"%"}function md(e){return+e}function gd(e){return Math.floor(+e/1e3)}var xr,ay,oy;P2({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function P2(e){return xr=PC(e),ay=xr.format,xr.parse,oy=xr.utcFormat,xr.utcParse,xr}function _2(e){return new Date(e)}function j2(e){return e instanceof Date?+e:+new Date(+e)}function Uu(e,t,r,n,i,a,o,s,c,u){var f=ju(),l=f.invert,d=f.domain,h=u(".%L"),p=u(":%S"),v=u("%I:%M"),m=u("%I %p"),x=u("%a %d"),O=u("%b %d"),w=u("%B"),S=u("%Y");function g(b){return(c(b)<b?h:s(b)<b?p:o(b)<b?v:a(b)<b?m:n(b)<b?i(b)<b?x:O:r(b)<b?w:S)(b)}return f.invert=function(b){return new Date(l(b))},f.domain=function(b){return arguments.length?d(Array.from(b,j2)):d().map(_2)},f.ticks=function(b){var A=d();return e(A[0],A[A.length-1],b??10)},f.tickFormat=function(b,A){return A==null?g:u(A)},f.nice=function(b){var A=d();return(!b||typeof b.range!="function")&&(b=t(A[0],A[A.length-1],b??10)),b?d(Kv(A,b)):f},f.copy=function(){return Ai(f,Uu(e,t,r,n,i,a,o,s,c,u))},f}function $2(){return rt.apply(Uu(AC,SC,$t,Bu,wo,Si,Lu,Iu,rr,ay).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function T2(){return rt.apply(Uu(wC,OC,Tt,Fu,Oo,xo,Ru,Du,rr,oy).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ao(){var e=0,t=1,r,n,i,a,o=De,s=!1,c;function u(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,s?Math.max(0,Math.min(1,l)):l))}u.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),u):[e,t]},u.clamp=function(l){return arguments.length?(s=!!l,u):s},u.interpolator=function(l){return arguments.length?(o=l,u):o};function f(l){return function(d){var h,p;return arguments.length?([h,p]=d,o=l(h,p),u):[o(0),o(1)]}}return u.range=f(hn),u.rangeRound=f(_u),u.unknown=function(l){return arguments.length?(c=l,u):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),u}}function Wt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function sy(){var e=Ut(Ao()(De));return e.copy=function(){return Wt(e,sy())},Ct.apply(e,arguments)}function cy(){var e=Eu(Ao()).domain([1,10]);return e.copy=function(){return Wt(e,cy()).base(e.base())},Ct.apply(e,arguments)}function uy(){var e=Nu(Ao());return e.copy=function(){return Wt(e,uy()).constant(e.constant())},Ct.apply(e,arguments)}function Wu(){var e=Cu(Ao());return e.copy=function(){return Wt(e,Wu()).exponent(e.exponent())},Ct.apply(e,arguments)}function E2(){return Wu.apply(null,arguments).exponent(.5)}function ly(){var e=[],t=De;function r(n){if(n!=null&&!isNaN(n=+n))return t((wi(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Bt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>yN(e,a/n))},r.copy=function(){return ly(t).domain(e)},Ct.apply(r,arguments)}function So(){var e=0,t=.5,r=1,n=1,i,a,o,s,c,u=De,f,l=!1,d;function h(v){return isNaN(v=+v)?d:(v=.5+((v=+f(v))-a)*(n*v<n*a?s:c),u(l?Math.max(0,Math.min(1,v)):v))}h.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),s=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(v){return arguments.length?(l=!!v,h):l},h.interpolator=function(v){return arguments.length?(u=v,h):u};function p(v){return function(m){var x,O,w;return arguments.length?([x,O,w]=m,u=WN(v,[x,O,w]),h):[u(0),u(.5),u(1)]}}return h.range=p(hn),h.rangeRound=p(_u),h.unknown=function(v){return arguments.length?(d=v,h):d},function(v){return f=v,i=v(e),a=v(t),o=v(r),s=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function fy(){var e=Ut(So()(De));return e.copy=function(){return Wt(e,fy())},Ct.apply(e,arguments)}function dy(){var e=Eu(So()).domain([.1,1,10]);return e.copy=function(){return Wt(e,dy()).base(e.base())},Ct.apply(e,arguments)}function hy(){var e=Nu(So());return e.copy=function(){return Wt(e,hy()).constant(e.constant())},Ct.apply(e,arguments)}function zu(){var e=Cu(So());return e.copy=function(){return Wt(e,zu()).exponent(e.exponent())},Ct.apply(e,arguments)}function N2(){return zu.apply(null,arguments).exponent(.5)}const bd=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Wn,scaleDiverging:fy,scaleDivergingLog:dy,scaleDivergingPow:zu,scaleDivergingSqrt:N2,scaleDivergingSymlog:hy,scaleIdentity:Hv,scaleImplicit:Ys,scaleLinear:va,scaleLog:Gv,scaleOrdinal:Au,scalePoint:Tn,scalePow:Mu,scaleQuantile:Yv,scaleQuantize:Zv,scaleRadial:Xv,scaleSequential:sy,scaleSequentialLog:cy,scaleSequentialPow:Wu,scaleSequentialQuantile:ly,scaleSequentialSqrt:E2,scaleSequentialSymlog:uy,scaleSqrt:fC,scaleSymlog:Vv,scaleThreshold:Jv,scaleTime:$2,scaleUtc:T2,tickFormat:qv},Symbol.toStringTag,{value:"Module"}));var C2=nn;function M2(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(s===void 0?o===o&&!C2(o):r(o,s)))var s=o,c=a}return c}var Po=M2;function k2(e,t){return e>t}var py=k2,I2=Po,D2=py,L2=dn;function R2(e){return e&&e.length?I2(e,L2,D2):void 0}var B2=R2;const _o=ce(B2);function F2(e,t){return e<t}var vy=F2,U2=Po,W2=vy,z2=dn;function q2(e){return e&&e.length?U2(e,z2,W2):void 0}var H2=q2;const jo=ce(H2);var K2=nu,G2=mt,V2=Av,X2=Fe;function Y2(e,t){var r=X2(e)?K2:V2;return r(e,G2(t))}var Z2=Y2,J2=wv,Q2=Z2;function eM(e,t){return J2(Q2(e,t),1)}var tM=eM;const rM=ce(tM);var nM=gu;function iM(e,t){return nM(e,t)}var aM=iM;const Pi=ce(aM);var pn=1e9,oM={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Hu,ve=!0,et="[DecimalError] ",ar=et+"Invalid argument: ",qu=et+"Exponent out of range: ",vn=Math.floor,Jt=Math.pow,sM=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Ge,je=1e7,pe=7,yy=9007199254740991,ba=vn(yy/pe),q={};q.absoluteValue=q.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};q.comparedTo=q.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};q.decimalPlaces=q.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*pe;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};q.dividedBy=q.div=function(e){return St(this,new this.constructor(e))};q.dividedToIntegerBy=q.idiv=function(e){var t=this,r=t.constructor;return se(St(t,new r(e),0,1),r.precision)};q.equals=q.eq=function(e){return!this.cmp(e)};q.exponent=function(){return xe(this)};q.greaterThan=q.gt=function(e){return this.cmp(e)>0};q.greaterThanOrEqualTo=q.gte=function(e){return this.cmp(e)>=0};q.isInteger=q.isint=function(){return this.e>this.d.length-2};q.isNegative=q.isneg=function(){return this.s<0};q.isPositive=q.ispos=function(){return this.s>0};q.isZero=function(){return this.s===0};q.lessThan=q.lt=function(e){return this.cmp(e)<0};q.lessThanOrEqualTo=q.lte=function(e){return this.cmp(e)<1};q.logarithm=q.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Ge))throw Error(et+"NaN");if(r.s<1)throw Error(et+(r.s?"NaN":"-Infinity"));return r.eq(Ge)?new n(0):(ve=!1,t=St(Gn(r,a),Gn(e,a),a),ve=!0,se(t,i))};q.minus=q.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?by(t,e):my(t,(e.s=-e.s,e))};q.modulo=q.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(et+"NaN");return r.s?(ve=!1,t=St(r,e,0,1).times(e),ve=!0,r.minus(t)):se(new n(r),i)};q.naturalExponential=q.exp=function(){return gy(this)};q.naturalLogarithm=q.ln=function(){return Gn(this)};q.negated=q.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};q.plus=q.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?my(t,e):by(t,(e.s=-e.s,e))};q.precision=q.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(ar+e);if(t=xe(i)+1,n=i.d.length-1,r=n*pe+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};q.squareRoot=q.sqrt=function(){var e,t,r,n,i,a,o,s=this,c=s.constructor;if(s.s<1){if(!s.s)return new c(0);throw Error(et+"NaN")}for(e=xe(s),ve=!1,i=Math.sqrt(+s),i==0||i==1/0?(t=ft(s.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=vn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(St(s,a,o+2)).times(.5),ft(a.d).slice(0,o)===(t=ft(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(se(a,r+1,0),a.times(a).eq(s)){n=a;break}}else if(t!="9999")break;o+=4}return ve=!0,se(n,r)};q.times=q.mul=function(e){var t,r,n,i,a,o,s,c,u,f=this,l=f.constructor,d=f.d,h=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=d.length,u=h.length,c<u&&(a=d,d=h,h=a,o=c,c=u,u=o),a=[],o=c+u,n=o;n--;)a.push(0);for(n=u;--n>=0;){for(t=0,i=c+n;i>n;)s=a[i]+h[n]*d[i-n-1]+t,a[i--]=s%je|0,t=s/je|0;a[i]=(a[i]+t)%je|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,ve?se(e,l.precision):e};q.toDecimalPlaces=q.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(pt(e,0,pn),t===void 0?t=n.rounding:pt(t,0,8),se(r,e+xe(r)+1,t))};q.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=lr(n,!0):(pt(e,0,pn),t===void 0?t=i.rounding:pt(t,0,8),n=se(new i(n),e+1,t),r=lr(n,!0,e+1)),r};q.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?lr(i):(pt(e,0,pn),t===void 0?t=a.rounding:pt(t,0,8),n=se(new a(i),e+xe(i)+1,t),r=lr(n.abs(),!1,e+xe(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};q.toInteger=q.toint=function(){var e=this,t=e.constructor;return se(new t(e),xe(e)+1,t.rounding)};q.toNumber=function(){return+this};q.toPower=q.pow=function(e){var t,r,n,i,a,o,s=this,c=s.constructor,u=12,f=+(e=new c(e));if(!e.s)return new c(Ge);if(s=new c(s),!s.s){if(e.s<1)throw Error(et+"Infinity");return s}if(s.eq(Ge))return s;if(n=c.precision,e.eq(Ge))return se(s,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=s.s,o){if((r=f<0?-f:f)<=yy){for(i=new c(Ge),t=Math.ceil(n/pe+4),ve=!1;r%2&&(i=i.times(s),wd(i.d,t)),r=vn(r/2),r!==0;)s=s.times(s),wd(s.d,t);return ve=!0,e.s<0?new c(Ge).div(i):se(i,n)}}else if(a<0)throw Error(et+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,s.s=1,ve=!1,i=e.times(Gn(s,n+u)),ve=!0,i=gy(i),i.s=a,i};q.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=xe(i),n=lr(i,r<=a.toExpNeg||r>=a.toExpPos)):(pt(e,1,pn),t===void 0?t=a.rounding:pt(t,0,8),i=se(new a(i),e,t),r=xe(i),n=lr(i,e<=r||r<=a.toExpNeg,e)),n};q.toSignificantDigits=q.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(pt(e,1,pn),t===void 0?t=n.rounding:pt(t,0,8)),se(new n(r),e,t)};q.toString=q.valueOf=q.val=q.toJSON=q[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=xe(e),r=e.constructor;return lr(e,t<=r.toExpNeg||t>=r.toExpPos)};function my(e,t){var r,n,i,a,o,s,c,u,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),ve?se(t,l):t;if(c=e.d,u=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,s=u.length):(n=u,i=o,s=c.length),o=Math.ceil(l/pe),s=o>s?o+1:s+1,a>s&&(a=s,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(s=c.length,a=u.length,s-a<0&&(a=s,n=u,u=c,c=n),r=0;a;)r=(c[--a]=c[a]+u[a]+r)/je|0,c[a]%=je;for(r&&(c.unshift(r),++i),s=c.length;c[--s]==0;)c.pop();return t.d=c,t.e=i,ve?se(t,l):t}function pt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ar+e)}function ft(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=pe-n.length,r&&(a+=Dt(r)),a+=n;o=e[t],n=o+"",r=pe-n.length,r&&(a+=Dt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var St=function(){function e(n,i){var a,o=0,s=n.length;for(n=n.slice();s--;)a=n[s]*i+o,n[s]=a%je|0,o=a/je|0;return o&&n.unshift(o),n}function t(n,i,a,o){var s,c;if(a!=o)c=a>o?1:-1;else for(s=c=0;s<a;s++)if(n[s]!=i[s]){c=n[s]>i[s]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*je+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var s,c,u,f,l,d,h,p,v,m,x,O,w,S,g,b,A,P,_=n.constructor,E=n.s==i.s?1:-1,$=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(et+"Division by zero");for(c=n.e-i.e,A=T.length,g=$.length,h=new _(E),p=h.d=[],u=0;T[u]==($[u]||0);)++u;if(T[u]>($[u]||0)&&--c,a==null?O=a=_.precision:o?O=a+(xe(n)-xe(i))+1:O=a,O<0)return new _(0);if(O=O/pe+2|0,u=0,A==1)for(f=0,T=T[0],O++;(u<g||f)&&O--;u++)w=f*je+($[u]||0),p[u]=w/T|0,f=w%T|0;else{for(f=je/(T[0]+1)|0,f>1&&(T=e(T,f),$=e($,f),A=T.length,g=$.length),S=A,v=$.slice(0,A),m=v.length;m<A;)v[m++]=0;P=T.slice(),P.unshift(0),b=T[0],T[1]>=je/2&&++b;do f=0,s=t(T,v,A,m),s<0?(x=v[0],A!=m&&(x=x*je+(v[1]||0)),f=x/b|0,f>1?(f>=je&&(f=je-1),l=e(T,f),d=l.length,m=v.length,s=t(l,v,d,m),s==1&&(f--,r(l,A<d?P:T,d))):(f==0&&(s=f=1),l=T.slice()),d=l.length,d<m&&l.unshift(0),r(v,l,m),s==-1&&(m=v.length,s=t(T,v,A,m),s<1&&(f++,r(v,A<m?P:T,m))),m=v.length):s===0&&(f++,v=[0]),p[u++]=f,s&&v[0]?v[m++]=$[S]||0:(v=[$[S]],m=1);while((S++<g||v[0]!==void 0)&&O--)}return p[0]||p.shift(),h.e=c,se(h,o?a+xe(h)+1:a)}}();function gy(e,t){var r,n,i,a,o,s,c=0,u=0,f=e.constructor,l=f.precision;if(xe(e)>16)throw Error(qu+xe(e));if(!e.s)return new f(Ge);for(ve=!1,s=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),u+=5;for(n=Math.log(Jt(2,u))/Math.LN10*2+5|0,s+=n,r=i=a=new f(Ge),f.precision=s;;){if(i=se(i.times(e),s),r=r.times(++c),o=a.plus(St(i,r,s)),ft(o.d).slice(0,s)===ft(a.d).slice(0,s)){for(;u--;)a=se(a.times(a),s);return f.precision=l,t==null?(ve=!0,se(a,l)):a}a=o}}function xe(e){for(var t=e.e*pe,r=e.d[0];r>=10;r/=10)t++;return t}function fs(e,t,r){if(t>e.LN10.sd())throw ve=!0,r&&(e.precision=r),Error(et+"LN10 precision limit exceeded");return se(new e(e.LN10),t)}function Dt(e){for(var t="";e--;)t+="0";return t}function Gn(e,t){var r,n,i,a,o,s,c,u,f,l=1,d=10,h=e,p=h.d,v=h.constructor,m=v.precision;if(h.s<1)throw Error(et+(h.s?"NaN":"-Infinity"));if(h.eq(Ge))return new v(0);if(t==null?(ve=!1,u=m):u=t,h.eq(10))return t==null&&(ve=!0),fs(v,u);if(u+=d,v.precision=u,r=ft(p),n=r.charAt(0),a=xe(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=ft(h.d),n=r.charAt(0),l++;a=xe(h),n>1?(h=new v("0."+r),a++):h=new v(n+"."+r.slice(1))}else return c=fs(v,u+2,m).times(a+""),h=Gn(new v(n+"."+r.slice(1)),u-d).plus(c),v.precision=m,t==null?(ve=!0,se(h,m)):h;for(s=o=h=St(h.minus(Ge),h.plus(Ge),u),f=se(h.times(h),u),i=3;;){if(o=se(o.times(f),u),c=s.plus(St(o,new v(i),u)),ft(c.d).slice(0,u)===ft(s.d).slice(0,u))return s=s.times(2),a!==0&&(s=s.plus(fs(v,u+2,m).times(a+""))),s=St(s,new v(l),u),v.precision=m,t==null?(ve=!0,se(s,m)):s;s=c,i+=2}}function xd(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=vn(r/pe),e.d=[],n=(r+1)%pe,r<0&&(n+=pe),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=pe;n<i;)e.d.push(+t.slice(n,n+=pe));t=t.slice(n),n=pe-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),ve&&(e.e>ba||e.e<-ba))throw Error(qu+r)}else e.s=0,e.e=0,e.d=[0];return e}function se(e,t,r){var n,i,a,o,s,c,u,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=pe,i=t,u=l[f=0];else{if(f=Math.ceil((n+1)/pe),a=l.length,f>=a)return e;for(u=a=l[f],o=1;a>=10;a/=10)o++;n%=pe,i=n-pe+o}if(r!==void 0&&(a=Jt(10,o-i-1),s=u/a%10|0,c=t<0||l[f+1]!==void 0||u%a,c=r<4?(s||c)&&(r==0||r==(e.s<0?3:2)):s>5||s==5&&(r==4||c||r==6&&(n>0?i>0?u/Jt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=xe(e),l.length=1,t=t-a-1,l[0]=Jt(10,(pe-t%pe)%pe),e.e=vn(-t/pe)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Jt(10,pe-n),l[f]=i>0?(u/Jt(10,o-i)%Jt(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==je&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=je)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(ve&&(e.e>ba||e.e<-ba))throw Error(qu+xe(e));return e}function by(e,t){var r,n,i,a,o,s,c,u,f,l,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),ve?se(t,h):t;if(c=e.d,l=t.d,n=t.e,u=e.e,c=c.slice(),o=u-n,o){for(f=o<0,f?(r=c,o=-o,s=l.length):(r=l,n=u,s=c.length),i=Math.max(Math.ceil(h/pe),s)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,s=l.length,f=i<s,f&&(s=i),i=0;i<s;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),s=c.length,i=l.length-s;i>0;--i)c[s++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=je-1;--c[a],c[i]+=je}c[i]-=l[i]}for(;c[--s]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,ve?se(t,h):t):new d(0)}function lr(e,t,r){var n,i=xe(e),a=ft(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Dt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Dt(-i-1)+a,r&&(n=r-o)>0&&(a+=Dt(n))):i>=o?(a+=Dt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Dt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Dt(n))),e.s<0?"-"+a:a}function wd(e,t){if(e.length>t)return e.length=t,!0}function xy(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(ar+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return xd(o,a.toString())}else if(typeof a!="string")throw Error(ar+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,sM.test(a))xd(o,a);else throw Error(ar+a)}if(i.prototype=q,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=xy,i.config=i.set=cM,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function cM(e){if(!e||typeof e!="object")throw Error(et+"Object expected");var t,r,n,i=["precision",1,pn,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(vn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(ar+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(ar+r+": "+n);return this}var Hu=xy(oM);Ge=new Hu(1);const oe=Hu;function uM(e){return hM(e)||dM(e)||fM(e)||lM()}function lM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fM(e,t){if(e){if(typeof e=="string")return ec(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ec(e,t)}}function dM(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function hM(e){if(Array.isArray(e))return ec(e)}function ec(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var pM=function(t){return t},wy={},Oy=function(t){return t===wy},Od=function(t){return function r(){return arguments.length===0||arguments.length===1&&Oy(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},vM=function e(t,r){return t===1?r:Od(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(s){return s!==wy}).length;return o>=t?r.apply(void 0,i):e(t-o,Od(function(){for(var s=arguments.length,c=new Array(s),u=0;u<s;u++)c[u]=arguments[u];var f=i.map(function(l){return Oy(l)?c.shift():l});return r.apply(void 0,uM(f).concat(c))}))})},$o=function(t){return vM(t.length,t)},tc=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},yM=$o(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),mM=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return pM;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(s,c){return c(s)},a.apply(void 0,arguments))}},rc=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Ay=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(s,c){return s===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function gM(e){var t;return e===0?t=1:t=Math.floor(new oe(e).abs().log(10).toNumber())+1,t}function bM(e,t,r){for(var n=new oe(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var xM=$o(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),wM=$o(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),OM=$o(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const To={rangeStep:bM,getDigitCount:gM,interpolateNumber:xM,uninterpolateNumber:wM,uninterpolateTruncation:OM};function nc(e){return PM(e)||SM(e)||Sy(e)||AM()}function AM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SM(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function PM(e){if(Array.isArray(e))return ic(e)}function Vn(e,t){return $M(e)||jM(e,t)||Sy(e,t)||_M()}function _M(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sy(e,t){if(e){if(typeof e=="string")return ic(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ic(e,t)}}function ic(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function jM(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),s;!(n=(s=o.next()).done)&&(r.push(s.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function $M(e){if(Array.isArray(e))return e}function Py(e){var t=Vn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function _y(e,t,r){if(e.lte(0))return new oe(0);var n=To.getDigitCount(e.toNumber()),i=new oe(10).pow(n),a=e.div(i),o=n!==1?.05:.1,s=new oe(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=s.mul(i);return t?c:new oe(Math.ceil(c))}function TM(e,t,r){var n=1,i=new oe(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new oe(10).pow(To.getDigitCount(e)-1),i=new oe(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new oe(Math.floor(e)))}else e===0?i=new oe(Math.floor((t-1)/2)):r||(i=new oe(Math.floor(e)));var o=Math.floor((t-1)/2),s=mM(yM(function(c){return i.add(new oe(c-o).mul(n)).toNumber()}),tc);return s(0,t)}function jy(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new oe(0),tickMin:new oe(0),tickMax:new oe(0)};var a=_y(new oe(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new oe(0):(o=new oe(e).add(t).div(2),o=o.sub(new oe(o).mod(a)));var s=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new oe(t).sub(o).div(a).toNumber()),u=s+c+1;return u>r?jy(e,t,r,n,i+1):(u<r&&(c=t>0?c+(r-u):c,s=t>0?s:s+(r-u)),{step:a,tickMin:o.sub(new oe(s).mul(a)),tickMax:o.add(new oe(c).mul(a))})}function EM(e){var t=Vn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),s=Py([r,n]),c=Vn(s,2),u=c[0],f=c[1];if(u===-1/0||f===1/0){var l=f===1/0?[u].concat(nc(tc(0,i-1).map(function(){return 1/0}))):[].concat(nc(tc(0,i-1).map(function(){return-1/0})),[f]);return r>n?rc(l):l}if(u===f)return TM(u,i,a);var d=jy(u,f,o,a),h=d.step,p=d.tickMin,v=d.tickMax,m=To.rangeStep(p,v.add(new oe(.1).mul(h)),h);return r>n?rc(m):m}function NM(e,t){var r=Vn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Py([n,i]),s=Vn(o,2),c=s[0],u=s[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var f=Math.max(t,2),l=_y(new oe(u).sub(c).div(f-1),a,0),d=[].concat(nc(To.rangeStep(new oe(c),new oe(u).sub(new oe(.99).mul(l)),l)),[u]);return n>i?rc(d):d}var CM=Ay(EM),MM=Ay(NM),kM="Invariant failed";function fr(e,t){throw new Error(kM)}var IM=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function xa(){return xa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xa.apply(this,arguments)}function DM(e,t){return FM(e)||BM(e,t)||RM(e,t)||LM()}function LM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function RM(e,t){if(e){if(typeof e=="string")return Ad(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ad(e,t)}}function Ad(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BM(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function FM(e){if(Array.isArray(e))return e}function UM(e,t){if(e==null)return{};var r=WM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function WM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function zM(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function qM(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ey(n.key),n)}}function HM(e,t,r){return t&&qM(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function KM(e,t,r){return t=wa(t),GM(e,$y()?Reflect.construct(t,r||[],wa(e).constructor):t.apply(e,r))}function GM(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return VM(e)}function VM(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $y(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return($y=function(){return!!e})()}function wa(e){return wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},wa(e)}function XM(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ac(e,t)}function ac(e,t){return ac=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ac(e,t)}function Ty(e,t,r){return t=Ey(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ey(e){var t=YM(e,"string");return Br(t)=="symbol"?t:t+""}function YM(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var _i=function(e){function t(){return zM(this,t),KM(this,t,arguments)}return XM(t,e),HM(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,s=n.dataKey,c=n.data,u=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,d=UM(n,IM),h=K(d,!1);this.props.direction==="x"&&f.type!=="number"&&fr();var p=c.map(function(v){var m=u(v,s),x=m.x,O=m.y,w=m.value,S=m.errorVal;if(!S)return null;var g=[],b,A;if(Array.isArray(S)){var P=DM(S,2);b=P[0],A=P[1]}else b=A=S;if(a==="vertical"){var _=f.scale,E=O+i,$=E+o,T=E-o,M=_(w-b),k=_(w+A);g.push({x1:k,y1:$,x2:k,y2:T}),g.push({x1:M,y1:E,x2:k,y2:E}),g.push({x1:M,y1:$,x2:M,y2:T})}else if(a==="horizontal"){var C=l.scale,D=x+i,L=D-o,F=D+o,U=C(w-b),H=C(w+A);g.push({x1:L,y1:H,x2:F,y2:H}),g.push({x1:D,y1:U,x2:D,y2:H}),g.push({x1:L,y1:U,x2:F,y2:U})}return j.createElement(re,xa({className:"recharts-errorBar",key:"bar-".concat(g.map(function(V){return"".concat(V.x1,"-").concat(V.x2,"-").concat(V.y1,"-").concat(V.y2)}))},h),g.map(function(V){return j.createElement("line",xa({},V,{key:"line-".concat(V.x1,"-").concat(V.x2,"-").concat(V.y1,"-").concat(V.y2)}))}))});return j.createElement(re,{className:"recharts-errorBars"},p)}}])}(j.Component);Ty(_i,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Ty(_i,"displayName","ErrorBar");function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function Sd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sd(Object(r),!0).forEach(function(n){ZM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZM(e,t,r){return t=JM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function JM(e){var t=QM(e,"string");return Xn(t)=="symbol"?t:t+""}function QM(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ny=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=qe(r,lt);if(!o)return null;var s=lt.defaultProps,c=s!==void 0?Gt(Gt({},s),o.props):{},u;return o.props&&o.props.payload?u=o.props&&o.props.payload:a==="children"?u=(n||[]).reduce(function(f,l){var d=l.item,h=l.props,p=h.sectors||h.data||[];return f.concat(p.map(function(v){return{type:o.props.iconType||d.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):u=(n||[]).map(function(f){var l=f.item,d=l.type.defaultProps,h=d!==void 0?Gt(Gt({},d),l.props):{},p=h.dataKey,v=h.name,m=h.legendType,x=h.hide;return{inactive:x,dataKey:p,type:c.iconType||m||"square",color:Ku(l),value:v||p,payload:h}}),Gt(Gt(Gt({},c),lt.getWithHeight(o,i)),{},{payload:u,item:o})};function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function Pd(e){return nk(e)||rk(e)||tk(e)||ek()}function ek(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tk(e,t){if(e){if(typeof e=="string")return oc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oc(e,t)}}function rk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function nk(e){if(Array.isArray(e))return oc(e)}function oc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_d(Object(r),!0).forEach(function(n){Er(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_d(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Er(e,t,r){return t=ik(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ik(e){var t=ak(e,"string");return Yn(t)=="symbol"?t:t+""}function ak(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Se(e,t,r){return Z(e)||Z(t)?r:Pe(t)?Ve(e,t,r):Y(t)?t(e):r}function En(e,t,r,n){var i=rM(e,function(s){return Se(s,t)});if(r==="number"){var a=i.filter(function(s){return B(s)||parseFloat(s)});return a.length?[jo(a),_o(a)]:[1/0,-1/0]}var o=n?i.filter(function(s){return!Z(s)}):i;return o.map(function(s){return Pe(s)||s instanceof Date?s:""})}var ok=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,s=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(s<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,u=0;u<s;u++){var f=u>0?i[u-1].coordinate:i[s-1].coordinate,l=i[u].coordinate,d=u>=s-1?i[0].coordinate:i[u+1].coordinate,h=void 0;if(ke(l-f)!==ke(d-l)){var p=[];if(ke(d-l)===ke(c[1]-c[0])){h=d;var v=l+c[1]-c[0];p[0]=Math.min(v,(v+f)/2),p[1]=Math.max(v,(v+f)/2)}else{h=f;var m=d+c[1]-c[0];p[0]=Math.min(l,(m+l)/2),p[1]=Math.max(l,(m+l)/2)}var x=[Math.min(l,(h+l)/2),Math.max(l,(h+l)/2)];if(t>x[0]&&t<=x[1]||t>=p[0]&&t<=p[1]){o=i[u].index;break}}else{var O=Math.min(f,d),w=Math.max(f,d);if(t>(O+l)/2&&t<=(w+l)/2){o=i[u].index;break}}}else for(var S=0;S<s;S++)if(S===0&&t<=(n[S].coordinate+n[S+1].coordinate)/2||S>0&&S<s-1&&t>(n[S].coordinate+n[S-1].coordinate)/2&&t<=(n[S].coordinate+n[S+1].coordinate)/2||S===s-1&&t>(n[S].coordinate+n[S-1].coordinate)/2){o=n[S].index;break}return o},Ku=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?me(me({},t.type.defaultProps),t.props):t.props,o=a.stroke,s=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:s;break;default:c=s;break}return c},sk=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},s=Object.keys(a),c=0,u=s.length;c<u;c++)for(var f=a[s[c]].stackGroups,l=Object.keys(f),d=0,h=l.length;d<h;d++){var p=f[l[d]],v=p.items,m=p.cateAxisId,x=v.filter(function(A){return At(A.type).indexOf("Bar")>=0});if(x&&x.length){var O=x[0].type.defaultProps,w=O!==void 0?me(me({},O),x[0].props):x[0].props,S=w.barSize,g=w[m];o[g]||(o[g]=[]);var b=Z(S)?r:S;o[g].push({item:x[0],stackList:x.slice(1),barSize:Z(b)?void 0:Ie(b,n,0)})}}return o},ck=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,s=t.maxBarSize,c=o.length;if(c<1)return null;var u=Ie(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var d=!1,h=i/c,p=o.reduce(function(S,g){return S+g.barSize||0},0);p+=(c-1)*u,p>=i&&(p-=(c-1)*u,u=0),p>=i&&h>0&&(d=!0,h*=.9,p=c*h);var v=(i-p)/2>>0,m={offset:v-u,size:0};f=o.reduce(function(S,g){var b={item:g.item,position:{offset:m.offset+m.size+u,size:d?h:g.barSize}},A=[].concat(Pd(S),[b]);return m=A[A.length-1].position,g.stackList&&g.stackList.length&&g.stackList.forEach(function(P){A.push({item:P,position:m})}),A},l)}else{var x=Ie(n,i,0,!0);i-2*x-(c-1)*u<=0&&(u=0);var O=(i-2*x-(c-1)*u)/c;O>1&&(O>>=0);var w=s===+s?Math.min(O,s):O;f=o.reduce(function(S,g,b){var A=[].concat(Pd(S),[{item:g.item,position:{offset:x+(O+u)*b+(O-w)/2,size:w}}]);return g.stackList&&g.stackList.length&&g.stackList.forEach(function(P){A.push({item:P,position:A[A.length-1].position})}),A},l)}return f},uk=function(t,r,n,i){var a=n.children,o=n.width,s=n.margin,c=o-(s.left||0)-(s.right||0),u=Ny({children:a,legendWidth:c});if(u){var f=i||{},l=f.width,d=f.height,h=u.align,p=u.verticalAlign,v=u.layout;if((v==="vertical"||v==="horizontal"&&p==="middle")&&h!=="center"&&B(t[h]))return me(me({},t),{},Er({},h,t[h]+(l||0)));if((v==="horizontal"||v==="vertical"&&h==="center")&&p!=="middle"&&B(t[p]))return me(me({},t),{},Er({},p,t[p]+(d||0)))}return t},lk=function(t,r,n){return Z(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Cy=function(t,r,n,i,a){var o=r.props.children,s=Xe(o,_i).filter(function(u){return lk(i,a,u.props.direction)});if(s&&s.length){var c=s.map(function(u){return u.props.dataKey});return t.reduce(function(u,f){var l=Se(f,n);if(Z(l))return u;var d=Array.isArray(l)?[jo(l),_o(l)]:[l,l],h=c.reduce(function(p,v){var m=Se(f,v,0),x=d[0]-Math.abs(Array.isArray(m)?m[0]:m),O=d[1]+Math.abs(Array.isArray(m)?m[1]:m);return[Math.min(x,p[0]),Math.max(O,p[1])]},[1/0,-1/0]);return[Math.min(h[0],u[0]),Math.max(h[1],u[1])]},[1/0,-1/0])}return null},fk=function(t,r,n,i,a){var o=r.map(function(s){return Cy(t,s,n,a,i)}).filter(function(s){return!Z(s)});return o&&o.length?o.reduce(function(s,c){return[Math.min(s[0],c[0]),Math.max(s[1],c[1])]},[1/0,-1/0]):null},My=function(t,r,n,i,a){var o=r.map(function(c){var u=c.props.dataKey;return n==="number"&&u&&Cy(t,c,u,i)||En(t,u,n,a)});if(n==="number")return o.reduce(function(c,u){return[Math.min(c[0],u[0]),Math.max(c[1],u[1])]},[1/0,-1/0]);var s={};return o.reduce(function(c,u){for(var f=0,l=u.length;f<l;f++)s[u[f]]||(s[u[f]]=!0,c.push(u[f]));return c},[])},ky=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},Iy=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,s=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||s.push(r),o||s.push(n),s},Ot=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,s=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,u=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(u=t.axisType==="angleAxis"&&(s==null?void 0:s.length)>=2?ke(s[0]-s[1])*2*u:u,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var d=a?a.indexOf(l):l;return{coordinate:i(d)+u,value:l,offset:u}});return f.filter(function(l){return!bi(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,d){return{coordinate:i(l)+u,value:l,index:d,offset:u}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+u,value:l,offset:u}}):i.domain().map(function(l,d){return{coordinate:i(l)+u,value:a?a[l]:l,index:d,offset:u}})},ds=new WeakMap,Bi=function(t,r){if(typeof r!="function")return t;ds.has(t)||ds.set(t,new WeakMap);var n=ds.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Dy=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,s=t.axisType;if(i==="auto")return o==="radial"&&s==="radiusAxis"?{scale:Wn(),realScaleType:"band"}:o==="radial"&&s==="angleAxis"?{scale:va(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Tn(),realScaleType:"point"}:a==="category"?{scale:Wn(),realScaleType:"band"}:{scale:va(),realScaleType:"linear"};if(gi(i)){var c="scale".concat(lo(i));return{scale:(bd[c]||Tn)(),realScaleType:bd[c]?c:"point"}}return Y(i)?{scale:i}:{scale:Tn(),realScaleType:"point"}},jd=1e-4,Ly=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-jd,o=Math.max(i[0],i[1])+jd,s=t(r[0]),c=t(r[n-1]);(s<a||s>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},dk=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},hk=function(t,r){if(!r||r.length!==2||!B(r[0])||!B(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!B(t[0])||t[0]<n)&&(a[0]=n),(!B(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},pk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,s=0;s<r;++s){var c=bi(t[s][n][1])?t[s][n][0]:t[s][n][1];c>=0?(t[s][n][0]=a,t[s][n][1]=a+c,a=t[s][n][1]):(t[s][n][0]=o,t[s][n][1]=o+c,o=t[s][n][1])}},vk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var s=bi(t[o][n][1])?t[o][n][0]:t[o][n][1];s>=0?(t[o][n][0]=a,t[o][n][1]=a+s,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},yk={sign:pk,expand:I1,none:Nr,silhouette:D1,wiggle:L1,positive:vk},mk=function(t,r,n){var i=r.map(function(s){return s.props.dataKey}),a=yk[n],o=k1().keys(i).value(function(s,c){return+Se(s,c,0)}).order(Ns).offset(a);return o(t)},gk=function(t,r,n,i,a,o){if(!t)return null;var s=o?r.reverse():r,c={},u=s.reduce(function(l,d){var h,p=(h=d.type)!==null&&h!==void 0&&h.defaultProps?me(me({},d.type.defaultProps),d.props):d.props,v=p.stackId,m=p.hide;if(m)return l;var x=p[n],O=l[x]||{hasStack:!1,stackGroups:{}};if(Pe(v)){var w=O.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};w.items.push(d),O.hasStack=!0,O.stackGroups[v]=w}else O.stackGroups[un("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[d]};return me(me({},l),{},Er({},x,O))},c),f={};return Object.keys(u).reduce(function(l,d){var h=u[d];if(h.hasStack){var p={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(v,m){var x=h.stackGroups[m];return me(me({},v),{},Er({},m,{numericAxisId:n,cateAxisId:i,items:x.items,stackedData:mk(t,x.items,a)}))},p)}return me(me({},l),{},Er({},d,h))},f)},Ry=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,s=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var u=t.domain();if(!u.length)return null;var f=CM(u,a,s);return t.domain([jo(f),_o(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),d=MM(l,a,s);return{niceTicks:d}}return null};function $d(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Z(i[t.dataKey])){var s=Vi(r,"value",i[t.dataKey]);if(s)return s.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Se(i,Z(o)?t.dataKey:o);return Z(c)?null:t.scale(c)}var Td=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,s=t.index;if(r.type==="category")return n[s]?n[s].coordinate+i:null;var c=Se(o,r.dataKey,r.domain[s]);return Z(c)?null:r.scale(c)-a/2+i},bk=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},xk=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?me(me({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(Pe(a)){var o=r[a];if(o){var s=o.items.indexOf(t);return s>=0?o.stackedData[s]:null}}return null},wk=function(t){return t.reduce(function(r,n){return[jo(n.concat([r[0]]).filter(B)),_o(n.concat([r[1]]).filter(B))]},[1/0,-1/0])},By=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],s=o.stackedData,c=s.reduce(function(u,f){var l=wk(f.slice(r,n+1));return[Math.min(u[0],l[0]),Math.max(u[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Ed=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Nd=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sc=function(t,r,n){if(Y(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(B(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Ed.test(t[0])){var a=+Ed.exec(t[0])[1];i[0]=r[0]-a}else Y(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(B(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Nd.test(t[1])){var o=+Nd.exec(t[1])[1];i[1]=r[1]+o}else Y(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Oa=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=xu(r,function(l){return l.coordinate}),o=1/0,s=1,c=a.length;s<c;s++){var u=a[s],f=a[s-1];o=Math.min((u.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Cd=function(t,r,n){return!t||!t.length||Pi(t,Ve(n,"type.defaultProps.domain"))?r:t},Fy=function(t,r){var n=t.type.defaultProps?me(me({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,s=n.formatter,c=n.tooltipType,u=n.chartType,f=n.hide;return me(me({},K(t,!1)),{},{dataKey:i,unit:o,formatter:s,name:a||i,color:Ku(t),value:Se(r,i),type:c,payload:r,chartType:u,hide:f})};function Zn(e){"@babel/helpers - typeof";return Zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(e)}function Md(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Md(Object(r),!0).forEach(function(n){Uy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Md(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Uy(e,t,r){return t=Ok(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ok(e){var t=Ak(e,"string");return Zn(t)=="symbol"?t:t+""}function Ak(e,t){if(Zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Sk(e,t){return $k(e)||jk(e,t)||_k(e,t)||Pk()}function Pk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _k(e,t){if(e){if(typeof e=="string")return kd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kd(e,t)}}function kd(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function jk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function $k(e){if(Array.isArray(e))return e}var Aa=Math.PI/180,Tk=function(t){return t*180/Math.PI},fe=function(t,r,n,i){return{x:t+Math.cos(-Aa*i)*n,y:r+Math.sin(-Aa*i)*n}},Wy=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},Ek=function(t,r,n,i,a){var o=t.width,s=t.height,c=t.startAngle,u=t.endAngle,f=Ie(t.cx,o,o/2),l=Ie(t.cy,s,s/2),d=Wy(o,s,n),h=Ie(t.innerRadius,d,0),p=Ie(t.outerRadius,d,d*.8),v=Object.keys(r);return v.reduce(function(m,x){var O=r[x],w=O.domain,S=O.reversed,g;if(Z(O.range))i==="angleAxis"?g=[c,u]:i==="radiusAxis"&&(g=[h,p]),S&&(g=[g[1],g[0]]);else{g=O.range;var b=g,A=Sk(b,2);c=A[0],u=A[1]}var P=Dy(O,a),_=P.realScaleType,E=P.scale;E.domain(w).range(g),Ly(E);var $=Ry(E,bt(bt({},O),{},{realScaleType:_})),T=bt(bt(bt({},O),$),{},{range:g,radius:p,realScaleType:_,scale:E,cx:f,cy:l,innerRadius:h,outerRadius:p,startAngle:c,endAngle:u});return bt(bt({},m),{},Uy({},x,T))},{})},Nk=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},Ck=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,s=Nk({x:n,y:i},{x:a,y:o});if(s<=0)return{radius:s};var c=(n-a)/s,u=Math.acos(c);return i>o&&(u=2*Math.PI-u),{radius:s,angle:Tk(u),angleInRadian:u}},Mk=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},kk=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),s=Math.min(a,o);return t+s*360},Id=function(t,r){var n=t.x,i=t.y,a=Ck({x:n,y:i},r),o=a.radius,s=a.angle,c=r.innerRadius,u=r.outerRadius;if(o<c||o>u)return!1;if(o===0)return!0;var f=Mk(r),l=f.startAngle,d=f.endAngle,h=s,p;if(l<=d){for(;h>d;)h-=360;for(;h<l;)h+=360;p=h>=l&&h<=d}else{for(;h>l;)h-=360;for(;h<d;)h+=360;p=h>=d&&h<=l}return p?bt(bt({},r),{},{radius:o,angle:kk(h,r)}):null},zy=function(t){return!I.isValidElement(t)&&!Y(t)&&typeof t!="boolean"?t.className:""};function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}var Ik=["offset"];function Dk(e){return Fk(e)||Bk(e)||Rk(e)||Lk()}function Lk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rk(e,t){if(e){if(typeof e=="string")return cc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cc(e,t)}}function Bk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Fk(e){if(Array.isArray(e))return cc(e)}function cc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Uk(e,t){if(e==null)return{};var r=Wk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Wk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Dd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dd(Object(r),!0).forEach(function(n){zk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zk(e,t,r){return t=qk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qk(e){var t=Hk(e,"string");return Jn(t)=="symbol"?t:t+""}function Hk(e,t){if(Jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qn(){return Qn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qn.apply(this,arguments)}var Kk=function(t){var r=t.value,n=t.formatter,i=Z(t.children)?r:t.children;return Y(n)?n(i):i},Gk=function(t,r){var n=ke(r-t),i=Math.min(Math.abs(r-t),360);return n*i},Vk=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,s=t.className,c=a,u=c.cx,f=c.cy,l=c.innerRadius,d=c.outerRadius,h=c.startAngle,p=c.endAngle,v=c.clockWise,m=(l+d)/2,x=Gk(h,p),O=x>=0?1:-1,w,S;i==="insideStart"?(w=h+O*o,S=v):i==="insideEnd"?(w=p-O*o,S=!v):i==="end"&&(w=p+O*o,S=v),S=x<=0?S:!S;var g=fe(u,f,m,w),b=fe(u,f,m,w+(S?1:-1)*359),A="M".concat(g.x,",").concat(g.y,`
    A`).concat(m,",").concat(m,",0,1,").concat(S?0:1,`,
    `).concat(b.x,",").concat(b.y),P=Z(t.id)?un("recharts-radial-line-"):t.id;return j.createElement("text",Qn({},n,{dominantBaseline:"central",className:Q("recharts-radial-bar-label",s)}),j.createElement("defs",null,j.createElement("path",{id:P,d:A})),j.createElement("textPath",{xlinkHref:"#".concat(P)},r))},Xk=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,s=a.cy,c=a.innerRadius,u=a.outerRadius,f=a.startAngle,l=a.endAngle,d=(f+l)/2;if(i==="outside"){var h=fe(o,s,u+n,d),p=h.x,v=h.y;return{x:p,y:v,textAnchor:p>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:s,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:s,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:s,textAnchor:"middle",verticalAnchor:"end"};var m=(c+u)/2,x=fe(o,s,m,d),O=x.x,w=x.y;return{x:O,y:w,textAnchor:"middle",verticalAnchor:"middle"}},Yk=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,s=o.x,c=o.y,u=o.width,f=o.height,l=f>=0?1:-1,d=l*i,h=l>0?"end":"start",p=l>0?"start":"end",v=u>=0?1:-1,m=v*i,x=v>0?"end":"start",O=v>0?"start":"end";if(a==="top"){var w={x:s+u/2,y:c-l*i,textAnchor:"middle",verticalAnchor:h};return Ae(Ae({},w),n?{height:Math.max(c-n.y,0),width:u}:{})}if(a==="bottom"){var S={x:s+u/2,y:c+f+d,textAnchor:"middle",verticalAnchor:p};return Ae(Ae({},S),n?{height:Math.max(n.y+n.height-(c+f),0),width:u}:{})}if(a==="left"){var g={x:s-m,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return Ae(Ae({},g),n?{width:Math.max(g.x-n.x,0),height:f}:{})}if(a==="right"){var b={x:s+u+m,y:c+f/2,textAnchor:O,verticalAnchor:"middle"};return Ae(Ae({},b),n?{width:Math.max(n.x+n.width-b.x,0),height:f}:{})}var A=n?{width:u,height:f}:{};return a==="insideLeft"?Ae({x:s+m,y:c+f/2,textAnchor:O,verticalAnchor:"middle"},A):a==="insideRight"?Ae({x:s+u-m,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},A):a==="insideTop"?Ae({x:s+u/2,y:c+d,textAnchor:"middle",verticalAnchor:p},A):a==="insideBottom"?Ae({x:s+u/2,y:c+f-d,textAnchor:"middle",verticalAnchor:h},A):a==="insideTopLeft"?Ae({x:s+m,y:c+d,textAnchor:O,verticalAnchor:p},A):a==="insideTopRight"?Ae({x:s+u-m,y:c+d,textAnchor:x,verticalAnchor:p},A):a==="insideBottomLeft"?Ae({x:s+m,y:c+f-d,textAnchor:O,verticalAnchor:h},A):a==="insideBottomRight"?Ae({x:s+u-m,y:c+f-d,textAnchor:x,verticalAnchor:h},A):an(a)&&(B(a.x)||er(a.x))&&(B(a.y)||er(a.y))?Ae({x:s+Ie(a.x,u),y:c+Ie(a.y,f),textAnchor:"end",verticalAnchor:"end"},A):Ae({x:s+u/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},A)},Zk=function(t){return"cx"in t&&B(t.cx)};function $e(e){var t=e.offset,r=t===void 0?5:t,n=Uk(e,Ik),i=Ae({offset:r},n),a=i.viewBox,o=i.position,s=i.value,c=i.children,u=i.content,f=i.className,l=f===void 0?"":f,d=i.textBreakAll;if(!a||Z(s)&&Z(c)&&!I.isValidElement(u)&&!Y(u))return null;if(I.isValidElement(u))return I.cloneElement(u,i);var h;if(Y(u)){if(h=I.createElement(u,i),I.isValidElement(h))return h}else h=Kk(i);var p=Zk(a),v=K(i,!0);if(p&&(o==="insideStart"||o==="insideEnd"||o==="end"))return Vk(i,h,v);var m=p?Xk(i):Yk(i);return j.createElement(ur,Qn({className:Q("recharts-label",l)},v,m,{breakAll:d}),h)}$e.displayName="Label";var qy=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,s=t.r,c=t.radius,u=t.innerRadius,f=t.outerRadius,l=t.x,d=t.y,h=t.top,p=t.left,v=t.width,m=t.height,x=t.clockWise,O=t.labelViewBox;if(O)return O;if(B(v)&&B(m)){if(B(l)&&B(d))return{x:l,y:d,width:v,height:m};if(B(h)&&B(p))return{x:h,y:p,width:v,height:m}}return B(l)&&B(d)?{x:l,y:d,width:0,height:0}:B(r)&&B(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:u||0,outerRadius:f||c||s||0,clockWise:x}:t.viewBox?t.viewBox:{}},Jk=function(t,r){return t?t===!0?j.createElement($e,{key:"label-implicit",viewBox:r}):Pe(t)?j.createElement($e,{key:"label-implicit",viewBox:r,value:t}):I.isValidElement(t)?t.type===$e?I.cloneElement(t,{key:"label-implicit",viewBox:r}):j.createElement($e,{key:"label-implicit",content:t,viewBox:r}):Y(t)?j.createElement($e,{key:"label-implicit",content:t,viewBox:r}):an(t)?j.createElement($e,Qn({viewBox:r},t,{key:"label-implicit"})):null:null},Qk=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=qy(t),o=Xe(i,$e).map(function(c,u){return I.cloneElement(c,{viewBox:r||a,key:"label-".concat(u)})});if(!n)return o;var s=Jk(t.label,r||a);return[s].concat(Dk(o))};$e.parseViewBox=qy;$e.renderCallByParent=Qk;function eI(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var tI=eI;const rI=ce(tI);function ei(e){"@babel/helpers - typeof";return ei=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ei(e)}var nI=["valueAccessor"],iI=["data","dataKey","clockWise","id","textBreakAll"];function aI(e){return uI(e)||cI(e)||sI(e)||oI()}function oI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sI(e,t){if(e){if(typeof e=="string")return uc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uc(e,t)}}function cI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function uI(e){if(Array.isArray(e))return uc(e)}function uc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Sa(){return Sa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sa.apply(this,arguments)}function Ld(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ld(Object(r),!0).forEach(function(n){lI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ld(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lI(e,t,r){return t=fI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fI(e){var t=dI(e,"string");return ei(t)=="symbol"?t:t+""}function dI(e,t){if(ei(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ei(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Bd(e,t){if(e==null)return{};var r=hI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function hI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var pI=function(t){return Array.isArray(t.value)?rI(t.value):t.value};function Pt(e){var t=e.valueAccessor,r=t===void 0?pI:t,n=Bd(e,nI),i=n.data,a=n.dataKey,o=n.clockWise,s=n.id,c=n.textBreakAll,u=Bd(n,iI);return!i||!i.length?null:j.createElement(re,{className:"recharts-label-list"},i.map(function(f,l){var d=Z(a)?r(f,l):Se(f&&f.payload,a),h=Z(s)?{}:{id:"".concat(s,"-").concat(l)};return j.createElement($e,Sa({},K(f,!0),u,h,{parentViewBox:f.parentViewBox,value:d,textBreakAll:c,viewBox:$e.parseViewBox(Z(o)?f:Rd(Rd({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}Pt.displayName="LabelList";function vI(e,t){return e?e===!0?j.createElement(Pt,{key:"labelList-implicit",data:t}):j.isValidElement(e)||Y(e)?j.createElement(Pt,{key:"labelList-implicit",data:t,content:e}):an(e)?j.createElement(Pt,Sa({data:t},e,{key:"labelList-implicit"})):null:null}function yI(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Xe(n,Pt).map(function(o,s){return I.cloneElement(o,{data:t,key:"labelList-".concat(s)})});if(!r)return i;var a=vI(e.label,t);return[a].concat(aI(i))}Pt.renderCallByParent=yI;function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}function lc(){return lc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lc.apply(this,arguments)}function Fd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ud(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fd(Object(r),!0).forEach(function(n){mI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mI(e,t,r){return t=gI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gI(e){var t=bI(e,"string");return ti(t)=="symbol"?t:t+""}function bI(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var xI=function(t,r){var n=ke(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},Fi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,s=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,f=c*(s?1:-1)+i,l=Math.asin(c/f)/Aa,d=u?a:a+o*l,h=fe(r,n,f,d),p=fe(r,n,i,d),v=u?a-o*l:a,m=fe(r,n,f*Math.cos(l*Aa),v);return{center:h,circleTangency:p,lineTangency:m,theta:l}},Hy=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,s=t.endAngle,c=xI(o,s),u=o+c,f=fe(r,n,a,o),l=fe(r,n,a,u),d="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>u),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var h=fe(r,n,i,o),p=fe(r,n,i,u);d+="L ".concat(p.x,",").concat(p.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=u),`,
            `).concat(h.x,",").concat(h.y," Z")}else d+="L ".concat(r,",").concat(n," Z");return d},wI=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,s=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,f=t.endAngle,l=ke(f-u),d=Fi({cx:r,cy:n,radius:a,angle:u,sign:l,cornerRadius:o,cornerIsExternal:c}),h=d.circleTangency,p=d.lineTangency,v=d.theta,m=Fi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),x=m.circleTangency,O=m.lineTangency,w=m.theta,S=c?Math.abs(u-f):Math.abs(u-f)-v-w;if(S<0)return s?"M ".concat(p.x,",").concat(p.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Hy({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:u,endAngle:f});var g="M ".concat(p.x,",").concat(p.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(S>180),",").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(O.x,",").concat(O.y,`
  `);if(i>0){var b=Fi({cx:r,cy:n,radius:i,angle:u,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=b.circleTangency,P=b.lineTangency,_=b.theta,E=Fi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),$=E.circleTangency,T=E.lineTangency,M=E.theta,k=c?Math.abs(u-f):Math.abs(u-f)-_-M;if(k<0&&o===0)return"".concat(g,"L").concat(r,",").concat(n,"Z");g+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(k>180),",").concat(+(l>0),",").concat(A.x,",").concat(A.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,"Z")}else g+="L".concat(r,",").concat(n,"Z");return g},OI={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Ky=function(t){var r=Ud(Ud({},OI),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,s=r.cornerRadius,c=r.forceCornerRadius,u=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,d=r.className;if(o<a||f===l)return null;var h=Q("recharts-sector",d),p=o-a,v=Ie(s,p,0,!0),m;return v>0&&Math.abs(f-l)<360?m=wI({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,p/2),forceCornerRadius:c,cornerIsExternal:u,startAngle:f,endAngle:l}):m=Hy({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),j.createElement("path",lc({},K(r,!0),{className:h,d:m,role:"img"}))};function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}function fc(){return fc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fc.apply(this,arguments)}function Wd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wd(Object(r),!0).forEach(function(n){AI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function AI(e,t,r){return t=SI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function SI(e){var t=PI(e,"string");return ri(t)=="symbol"?t:t+""}function PI(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qd={curveBasisClosed:A1,curveBasisOpen:S1,curveBasis:O1,curveBumpX:c1,curveBumpY:u1,curveLinearClosed:P1,curveLinear:ho,curveMonotoneX:_1,curveMonotoneY:j1,curveNatural:$1,curveStep:T1,curveStepAfter:N1,curveStepBefore:E1},Ui=function(t){return t.x===+t.x&&t.y===+t.y},An=function(t){return t.x},Sn=function(t){return t.y},_I=function(t,r){if(Y(t))return t;var n="curve".concat(lo(t));return(n==="curveMonotone"||n==="curveBump")&&r?qd["".concat(n).concat(r==="vertical"?"Y":"X")]:qd[n]||ho},jI=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,s=t.layout,c=t.connectNulls,u=c===void 0?!1:c,f=_I(n,s),l=u?a.filter(function(v){return Ui(v)}):a,d;if(Array.isArray(o)){var h=u?o.filter(function(v){return Ui(v)}):o,p=l.map(function(v,m){return zd(zd({},v),{},{base:h[m]})});return s==="vertical"?d=Ei().y(Sn).x1(An).x0(function(v){return v.base.x}):d=Ei().x(An).y1(Sn).y0(function(v){return v.base.y}),d.defined(Ui).curve(f),d(p)}return s==="vertical"&&B(o)?d=Ei().y(Sn).x1(An).x0(o):B(o)?d=Ei().x(An).y1(Sn).y0(o):d=Fp().x(An).y(Sn),d.defined(Ui).curve(f),d(l)},Pa=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?jI(t):i;return j.createElement("path",fc({},K(t,!1),Xi(t),{className:Q("recharts-curve",r),d:o,ref:a}))},$I=Object.getOwnPropertyNames,TI=Object.getOwnPropertySymbols,EI=Object.prototype.hasOwnProperty;function Hd(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Wi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),s=a.get(n);if(o&&s)return o===n&&s===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function Kd(e){return $I(e).concat(TI(e))}var NI=Object.hasOwn||function(e,t){return EI.call(e,t)};function mr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var CI="__v",MI="__o",kI="_owner",Gd=Object.getOwnPropertyDescriptor,Vd=Object.keys;function II(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function DI(e,t){return mr(e.getTime(),t.getTime())}function LI(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function RI(e,t){return e===t}function Xd(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,s,c=0;(o=a.next())&&!o.done;){for(var u=t.entries(),f=!1,l=0;(s=u.next())&&!s.done;){if(i[l]){l++;continue}var d=o.value,h=s.value;if(r.equals(d[0],h[0],c,l,e,t,r)&&r.equals(d[1],h[1],d[0],h[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var BI=mr;function FI(e,t,r){var n=Vd(e),i=n.length;if(Vd(t).length!==i)return!1;for(;i-- >0;)if(!Gy(e,t,r,n[i]))return!1;return!0}function Pn(e,t,r){var n=Kd(e),i=n.length;if(Kd(t).length!==i)return!1;for(var a,o,s;i-- >0;)if(a=n[i],!Gy(e,t,r,a)||(o=Gd(e,a),s=Gd(t,a),(o||s)&&(!o||!s||o.configurable!==s.configurable||o.enumerable!==s.enumerable||o.writable!==s.writable)))return!1;return!0}function UI(e,t){return mr(e.valueOf(),t.valueOf())}function WI(e,t){return e.source===t.source&&e.flags===t.flags}function Yd(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,s;(o=a.next())&&!o.done;){for(var c=t.values(),u=!1,f=0;(s=c.next())&&!s.done;){if(!i[f]&&r.equals(o.value,s.value,o.value,s.value,e,t,r)){u=i[f]=!0;break}f++}if(!u)return!1}return!0}function zI(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function qI(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function Gy(e,t,r,n){return(n===kI||n===MI||n===CI)&&(e.$$typeof||t.$$typeof)?!0:NI(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var HI="[object Arguments]",KI="[object Boolean]",GI="[object Date]",VI="[object Error]",XI="[object Map]",YI="[object Number]",ZI="[object Object]",JI="[object RegExp]",QI="[object Set]",eD="[object String]",tD="[object URL]",rD=Array.isArray,Zd=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Jd=Object.assign,nD=Object.prototype.toString.call.bind(Object.prototype.toString);function iD(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,s=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,u=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,d=e.areUrlsEqual;return function(p,v,m){if(p===v)return!0;if(p==null||v==null)return!1;var x=typeof p;if(x!==typeof v)return!1;if(x!=="object")return x==="number"?o(p,v,m):x==="function"?i(p,v,m):!1;var O=p.constructor;if(O!==v.constructor)return!1;if(O===Object)return s(p,v,m);if(rD(p))return t(p,v,m);if(Zd!=null&&Zd(p))return l(p,v,m);if(O===Date)return r(p,v,m);if(O===RegExp)return u(p,v,m);if(O===Map)return a(p,v,m);if(O===Set)return f(p,v,m);var w=nD(p);return w===GI?r(p,v,m):w===JI?u(p,v,m):w===XI?a(p,v,m):w===QI?f(p,v,m):w===ZI?typeof p.then!="function"&&typeof v.then!="function"&&s(p,v,m):w===tD?d(p,v,m):w===VI?n(p,v,m):w===HI?s(p,v,m):w===KI||w===YI||w===eD?c(p,v,m):!1}}function aD(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Pn:II,areDatesEqual:DI,areErrorsEqual:LI,areFunctionsEqual:RI,areMapsEqual:n?Hd(Xd,Pn):Xd,areNumbersEqual:BI,areObjectsEqual:n?Pn:FI,arePrimitiveWrappersEqual:UI,areRegExpsEqual:WI,areSetsEqual:n?Hd(Yd,Pn):Yd,areTypedArraysEqual:n?Pn:zI,areUrlsEqual:qI};if(r&&(i=Jd({},i,r(i))),t){var a=Wi(i.areArraysEqual),o=Wi(i.areMapsEqual),s=Wi(i.areObjectsEqual),c=Wi(i.areSetsEqual);i=Jd({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:s,areSetsEqual:c})}return i}function oD(e){return function(t,r,n,i,a,o,s){return e(t,r,s)}}function sD(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,u){var f=n(),l=f.cache,d=l===void 0?t?new WeakMap:void 0:l,h=f.meta;return r(c,u,{cache:d,equals:i,meta:h,strict:a})};if(t)return function(c,u){return r(c,u,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,u){return r(c,u,o)}}var cD=zt();zt({strict:!0});zt({circular:!0});zt({circular:!0,strict:!0});zt({createInternalComparator:function(){return mr}});zt({strict:!0,createInternalComparator:function(){return mr}});zt({circular:!0,createInternalComparator:function(){return mr}});zt({circular:!0,createInternalComparator:function(){return mr},strict:!0});function zt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,s=aD(e),c=iD(s),u=n?n(c):oD(c);return sD({circular:r,comparator:c,createState:i,equals:u,strict:o})}function uD(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function Qd(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):uD(i)};requestAnimationFrame(n)}function dc(e){"@babel/helpers - typeof";return dc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dc(e)}function lD(e){return pD(e)||hD(e)||dD(e)||fD()}function fD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dD(e,t){if(e){if(typeof e=="string")return eh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eh(e,t)}}function eh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function pD(e){if(Array.isArray(e))return e}function vD(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,s=lD(o),c=s[0],u=s.slice(1);if(typeof c=="number"){Qd(i.bind(null,u),c);return}i(c),Qd(i.bind(null,u));return}dc(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function ni(e){"@babel/helpers - typeof";return ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ni(e)}function th(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function rh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?th(Object(r),!0).forEach(function(n){Vy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):th(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Vy(e,t,r){return t=yD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yD(e){var t=mD(e,"string");return ni(t)==="symbol"?t:String(t)}function mD(e,t){if(ni(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ni(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var gD=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},bD=function(t){return t},xD=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},Nn=function(t,r){return Object.keys(r).reduce(function(n,i){return rh(rh({},n),{},Vy({},i,t(i,r[i])))},{})},nh=function(t,r,n){return t.map(function(i){return"".concat(xD(i)," ").concat(r,"ms ").concat(n)}).join(",")};function wD(e,t){return SD(e)||AD(e,t)||Xy(e,t)||OD()}function OD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function AD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function SD(e){if(Array.isArray(e))return e}function PD(e){return $D(e)||jD(e)||Xy(e)||_D()}function _D(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xy(e,t){if(e){if(typeof e=="string")return hc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hc(e,t)}}function jD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function $D(e){if(Array.isArray(e))return hc(e)}function hc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var _a=1e-4,Yy=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},Zy=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},ih=function(t,r){return function(n){var i=Yy(t,r);return Zy(i,n)}},TD=function(t,r){return function(n){var i=Yy(t,r),a=[].concat(PD(i.map(function(o,s){return o*s}).slice(1)),[0]);return Zy(a,n)}},ah=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],s=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,s=1;break;case"ease":i=.25,a=.1,o=.25,s=1;break;case"ease-in":i=.42,a=0,o=1,s=1;break;case"ease-out":i=.42,a=0,o=.58,s=1;break;case"ease-in-out":i=0,a=0,o=.58,s=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var u=c[1].split(")")[0].split(",").map(function(m){return parseFloat(m)}),f=wD(u,4);i=f[0],a=f[1],o=f[2],s=f[3]}}}var l=ih(i,o),d=ih(a,s),h=TD(i,o),p=function(x){return x>1?1:x<0?0:x},v=function(x){for(var O=x>1?1:x,w=O,S=0;S<8;++S){var g=l(w)-O,b=h(w);if(Math.abs(g-O)<_a||b<_a)return d(w);w=p(w-g/b)}return d(w)};return v.isStepper=!1,v},ED=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,s=o===void 0?17:o,c=function(f,l,d){var h=-(f-l)*n,p=d*a,v=d+(h-p)*s/1e3,m=d*s/1e3+f;return Math.abs(m-l)<_a&&Math.abs(v)<_a?[l,0]:[m,v]};return c.isStepper=!0,c.dt=s,c},ND=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return ah(i);case"spring":return ED();default:if(i.split("(")[0]==="cubic-bezier")return ah(i)}return typeof i=="function"?i:null};function ii(e){"@babel/helpers - typeof";return ii=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(e)}function oh(e){return kD(e)||MD(e)||Jy(e)||CD()}function CD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function MD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function kD(e){if(Array.isArray(e))return vc(e)}function sh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sh(Object(r),!0).forEach(function(n){pc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pc(e,t,r){return t=ID(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ID(e){var t=DD(e,"string");return ii(t)==="symbol"?t:String(t)}function DD(e,t){if(ii(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ii(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LD(e,t){return FD(e)||BD(e,t)||Jy(e,t)||RD()}function RD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Jy(e,t){if(e){if(typeof e=="string")return vc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vc(e,t)}}function vc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function FD(e){if(Array.isArray(e))return e}var ja=function(t,r,n){return t+(r-t)*n},yc=function(t){var r=t.from,n=t.to;return r!==n},UD=function e(t,r,n){var i=Nn(function(a,o){if(yc(o)){var s=t(o.from,o.to,o.velocity),c=LD(s,2),u=c[0],f=c[1];return Ee(Ee({},o),{},{from:u,velocity:f})}return o},r);return n<1?Nn(function(a,o){return yc(o)?Ee(Ee({},o),{},{velocity:ja(o.velocity,i[a].velocity,n),from:ja(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const WD=function(e,t,r,n,i){var a=gD(e,t),o=a.reduce(function(m,x){return Ee(Ee({},m),{},pc({},x,[e[x],t[x]]))},{}),s=a.reduce(function(m,x){return Ee(Ee({},m),{},pc({},x,{from:e[x],velocity:0,to:t[x]}))},{}),c=-1,u,f,l=function(){return null},d=function(){return Nn(function(x,O){return O.from},s)},h=function(){return!Object.values(s).filter(yc).length},p=function(x){u||(u=x);var O=x-u,w=O/r.dt;s=UD(r,s,w),i(Ee(Ee(Ee({},e),t),d())),u=x,h()||(c=requestAnimationFrame(l))},v=function(x){f||(f=x);var O=(x-f)/n,w=Nn(function(g,b){return ja.apply(void 0,oh(b).concat([r(O)]))},o);if(i(Ee(Ee(Ee({},e),t),w)),O<1)c=requestAnimationFrame(l);else{var S=Nn(function(g,b){return ja.apply(void 0,oh(b).concat([r(1)]))},o);i(Ee(Ee(Ee({},e),t),S))}};return l=r.isStepper?p:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}var zD=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function qD(e,t){if(e==null)return{};var r=HD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function HD(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function hs(e){return XD(e)||VD(e)||GD(e)||KD()}function KD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GD(e,t){if(e){if(typeof e=="string")return mc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mc(e,t)}}function VD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function XD(e){if(Array.isArray(e))return mc(e)}function mc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ch(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function it(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ch(Object(r),!0).forEach(function(n){jn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ch(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jn(e,t,r){return t=Qy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function YD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ZD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qy(n.key),n)}}function JD(e,t,r){return t&&ZD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Qy(e){var t=QD(e,"string");return Fr(t)==="symbol"?t:String(t)}function QD(e,t){if(Fr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gc(e,t)}function gc(e,t){return gc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gc(e,t)}function tL(e){var t=rL();return function(){var n=$a(e),i;if(t){var a=$a(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return bc(this,i)}}function bc(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xc(e)}function xc(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rL(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function $a(e){return $a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$a(e)}var vt=function(e){eL(r,e);var t=tL(r);function r(n,i){var a;YD(this,r),a=t.call(this,n,i);var o=a.props,s=o.isActive,c=o.attributeName,u=o.from,f=o.to,l=o.steps,d=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(xc(a)),a.changeStyle=a.changeStyle.bind(xc(a)),!s||h<=0)return a.state={style:{}},typeof d=="function"&&(a.state={style:f}),bc(a);if(l&&l.length)a.state={style:l[0].style};else if(u){if(typeof d=="function")return a.state={style:u},bc(a);a.state={style:c?jn({},c,u):u}}else a.state={style:{}};return a}return JD(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,s=a.canBegin,c=a.attributeName,u=a.shouldReAnimate,f=a.to,l=a.from,d=this.state.style;if(s){if(!o){var h={style:c?jn({},c,f):f};this.state&&d&&(c&&d[c]!==f||!c&&d!==f)&&this.setState(h);return}if(!(cD(i.to,f)&&i.canBegin&&i.isActive)){var p=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=p||u?l:i.to;if(this.state&&d){var m={style:c?jn({},c,v):v};(c&&d[c]!==v||!c&&d!==v)&&this.setState(m)}this.runAnimation(it(it({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,s=i.to,c=i.duration,u=i.easing,f=i.begin,l=i.onAnimationEnd,d=i.onAnimationStart,h=WD(o,s,ND(u),c,this.changeStyle),p=function(){a.stopJSAnimation=h()};this.manager.start([d,f,p,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,s=i.begin,c=i.onAnimationStart,u=o[0],f=u.style,l=u.duration,d=l===void 0?0:l,h=function(v,m,x){if(x===0)return v;var O=m.duration,w=m.easing,S=w===void 0?"ease":w,g=m.style,b=m.properties,A=m.onAnimationEnd,P=x>0?o[x-1]:m,_=b||Object.keys(g);if(typeof S=="function"||S==="spring")return[].concat(hs(v),[a.runJSAnimation.bind(a,{from:P.style,to:g,duration:O,easing:S}),O]);var E=nh(_,O,S),$=it(it(it({},P.style),g),{},{transition:E});return[].concat(hs(v),[$,O,A]).filter(bD)};return this.manager.start([c].concat(hs(o.reduce(h,[f,Math.max(d,s)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=vD());var a=i.begin,o=i.duration,s=i.attributeName,c=i.to,u=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,d=i.steps,h=i.children,p=this.manager;if(this.unSubscribe=p.subscribe(this.handleStyleChange),typeof u=="function"||typeof h=="function"||u==="spring"){this.runJSAnimation(i);return}if(d.length>1){this.runStepAnimation(i);return}var v=s?jn({},s,c):c,m=nh(Object.keys(v),o,u);p.start([f,a,it(it({},v),{},{transition:m}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var s=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=qD(i,zD),u=I.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!s||u===0||o<=0)return a;var l=function(h){var p=h.props,v=p.style,m=v===void 0?{}:v,x=p.className,O=I.cloneElement(h,it(it({},c),{},{style:it(it({},m),f),className:x}));return O};return u===1?l(I.Children.only(a)):j.createElement("div",null,I.Children.map(a,function(d){return l(d)}))}}]),r}(I.PureComponent);vt.displayName="Animate";vt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};vt.propTypes={from:ie.oneOfType([ie.object,ie.string]),to:ie.oneOfType([ie.object,ie.string]),attributeName:ie.string,duration:ie.number,begin:ie.number,easing:ie.oneOfType([ie.string,ie.func]),steps:ie.arrayOf(ie.shape({duration:ie.number.isRequired,style:ie.object.isRequired,easing:ie.oneOfType([ie.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ie.func]),properties:ie.arrayOf("string"),onAnimationEnd:ie.func})),children:ie.oneOfType([ie.node,ie.func]),isActive:ie.bool,canBegin:ie.bool,onAnimationEnd:ie.func,shouldReAnimate:ie.bool,onAnimationStart:ie.func,onAnimationReStart:ie.func};function ai(e){"@babel/helpers - typeof";return ai=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ai(e)}function Ta(){return Ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ta.apply(this,arguments)}function nL(e,t){return sL(e)||oL(e,t)||aL(e,t)||iL()}function iL(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function aL(e,t){if(e){if(typeof e=="string")return uh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uh(e,t)}}function uh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function oL(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function sL(e){if(Array.isArray(e))return e}function lh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lh(Object(r),!0).forEach(function(n){cL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cL(e,t,r){return t=uL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uL(e){var t=lL(e,"string");return ai(t)=="symbol"?t:t+""}function lL(e,t){if(ai(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ai(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var dh=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),s=i>=0?1:-1,c=n>=0?1:-1,u=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],d=0,h=4;d<h;d++)l[d]=a[d]>o?o:a[d];f="M".concat(t,",").concat(r+s*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(u,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(u,`,
        `).concat(t+n,",").concat(r+s*l[1])),f+="L ".concat(t+n,",").concat(r+i-s*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(u,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(u,`,
        `).concat(t,",").concat(r+i-s*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var p=Math.min(o,a);f="M ".concat(t,",").concat(r+s*p,`
            A `).concat(p,",").concat(p,",0,0,").concat(u,",").concat(t+c*p,",").concat(r,`
            L `).concat(t+n-c*p,",").concat(r,`
            A `).concat(p,",").concat(p,",0,0,").concat(u,",").concat(t+n,",").concat(r+s*p,`
            L `).concat(t+n,",").concat(r+i-s*p,`
            A `).concat(p,",").concat(p,",0,0,").concat(u,",").concat(t+n-c*p,",").concat(r+i,`
            L `).concat(t+c*p,",").concat(r+i,`
            A `).concat(p,",").concat(p,",0,0,").concat(u,",").concat(t,",").concat(r+i-s*p," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},fL=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,s=r.width,c=r.height;if(Math.abs(s)>0&&Math.abs(c)>0){var u=Math.min(a,a+s),f=Math.max(a,a+s),l=Math.min(o,o+c),d=Math.max(o,o+c);return n>=u&&n<=f&&i>=l&&i<=d}return!1},dL={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Gu=function(t){var r=fh(fh({},dL),t),n=I.useRef(),i=I.useState(-1),a=nL(i,2),o=a[0],s=a[1];I.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var S=n.current.getTotalLength();S&&s(S)}catch{}},[]);var c=r.x,u=r.y,f=r.width,l=r.height,d=r.radius,h=r.className,p=r.animationEasing,v=r.animationDuration,m=r.animationBegin,x=r.isAnimationActive,O=r.isUpdateAnimationActive;if(c!==+c||u!==+u||f!==+f||l!==+l||f===0||l===0)return null;var w=Q("recharts-rectangle",h);return O?j.createElement(vt,{canBegin:o>0,from:{width:f,height:l,x:c,y:u},to:{width:f,height:l,x:c,y:u},duration:v,animationEasing:p,isActive:O},function(S){var g=S.width,b=S.height,A=S.x,P=S.y;return j.createElement(vt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:x,easing:p},j.createElement("path",Ta({},K(r,!0),{className:w,d:dh(A,P,g,b,d),ref:n})))}):j.createElement("path",Ta({},K(r,!0),{className:w,d:dh(c,u,f,l,d)}))},hL=["points","className","baseLinePoints","connectNulls"];function Sr(){return Sr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sr.apply(this,arguments)}function pL(e,t){if(e==null)return{};var r=vL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function vL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function hh(e){return bL(e)||gL(e)||mL(e)||yL()}function yL(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mL(e,t){if(e){if(typeof e=="string")return wc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wc(e,t)}}function gL(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function bL(e){if(Array.isArray(e))return wc(e)}function wc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ph=function(t){return t&&t.x===+t.x&&t.y===+t.y},xL=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){ph(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),ph(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},Cn=function(t,r){var n=xL(t);r&&(n=[n.reduce(function(a,o){return[].concat(hh(a),hh(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,s,c){return"".concat(o).concat(c===0?"M":"L").concat(s.x,",").concat(s.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},wL=function(t,r,n){var i=Cn(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(Cn(r.reverse(),n).slice(1))},OL=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=pL(t,hL);if(!r||!r.length)return null;var s=Q("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",u=wL(r,i,a);return j.createElement("g",{className:s},j.createElement("path",Sr({},K(o,!0),{fill:u.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:u})),c?j.createElement("path",Sr({},K(o,!0),{fill:"none",d:Cn(r,a)})):null,c?j.createElement("path",Sr({},K(o,!0),{fill:"none",d:Cn(i,a)})):null)}var f=Cn(r,a);return j.createElement("path",Sr({},K(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:s,d:f}))};function Oc(){return Oc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oc.apply(this,arguments)}var Eo=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=Q("recharts-dot",a);return r===+r&&n===+n&&i===+i?j.createElement("circle",Oc({},K(t,!1),Xi(t),{className:o,cx:r,cy:n,r:i})):null};function oi(e){"@babel/helpers - typeof";return oi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oi(e)}var AL=["x","y","top","left","width","height","className"];function Ac(){return Ac=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ac.apply(this,arguments)}function vh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function SL(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vh(Object(r),!0).forEach(function(n){PL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function PL(e,t,r){return t=_L(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _L(e){var t=jL(e,"string");return oi(t)=="symbol"?t:t+""}function jL(e,t){if(oi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(oi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $L(e,t){if(e==null)return{};var r=TL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function TL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var EL=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},NL=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,s=o===void 0?0:o,c=t.left,u=c===void 0?0:c,f=t.width,l=f===void 0?0:f,d=t.height,h=d===void 0?0:d,p=t.className,v=$L(t,AL),m=SL({x:n,y:a,top:s,left:u,width:l,height:h},v);return!B(n)||!B(a)||!B(l)||!B(h)||!B(s)||!B(u)?null:j.createElement("path",Ac({},K(m,!0),{className:Q("recharts-cross",p),d:EL(n,a,l,h,s,u)}))},CL=Po,ML=py,kL=mt;function IL(e,t){return e&&e.length?CL(e,kL(t),ML):void 0}var DL=IL;const LL=ce(DL);var RL=Po,BL=mt,FL=vy;function UL(e,t){return e&&e.length?RL(e,BL(t),FL):void 0}var WL=UL;const zL=ce(WL);var qL=["cx","cy","angle","ticks","axisLine"],HL=["ticks","tick","angle","tickFormatter","stroke"];function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function Mn(){return Mn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mn.apply(this,arguments)}function yh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yh(Object(r),!0).forEach(function(n){No(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mh(e,t){if(e==null)return{};var r=KL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function KL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function GL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tm(n.key),n)}}function VL(e,t,r){return t&&gh(e.prototype,t),r&&gh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function XL(e,t,r){return t=Ea(t),YL(e,em()?Reflect.construct(t,r||[],Ea(e).constructor):t.apply(e,r))}function YL(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ZL(e)}function ZL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function em(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(em=function(){return!!e})()}function Ea(e){return Ea=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ea(e)}function JL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Sc(e,t)}function Sc(e,t){return Sc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Sc(e,t)}function No(e,t,r){return t=tm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tm(e){var t=QL(e,"string");return Ur(t)=="symbol"?t:t+""}function QL(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Co=function(e){function t(){return GL(this,t),XL(this,t,arguments)}return JL(t,e),VL(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,s=a.cx,c=a.cy;return fe(s,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,s=n.ticks,c=LL(s,function(f){return f.coordinate||0}),u=zL(s,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:u.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,s=n.ticks,c=n.axisLine,u=mh(n,qL),f=s.reduce(function(p,v){return[Math.min(p[0],v.coordinate),Math.max(p[1],v.coordinate)]},[1/0,-1/0]),l=fe(i,a,f[0],o),d=fe(i,a,f[1],o),h=Vt(Vt(Vt({},K(u,!1)),{},{fill:"none"},K(c,!1)),{},{x1:l.x,y1:l.y,x2:d.x,y2:d.y});return j.createElement("line",Mn({className:"recharts-polar-radius-axis-line"},h))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,s=i.angle,c=i.tickFormatter,u=i.stroke,f=mh(i,HL),l=this.getTickTextAnchor(),d=K(f,!1),h=K(o,!1),p=a.map(function(v,m){var x=n.getTickValueCoord(v),O=Vt(Vt(Vt(Vt({textAnchor:l,transform:"rotate(".concat(90-s,", ").concat(x.x,", ").concat(x.y,")")},d),{},{stroke:"none",fill:u},h),{},{index:m},x),{},{payload:v});return j.createElement(re,Mn({className:Q("recharts-polar-radius-axis-tick",zy(o)),key:"tick-".concat(v.coordinate)},cr(n.props,v,m)),t.renderTickItem(o,O,c?c(v.value,m):v.value))});return j.createElement(re,{className:"recharts-polar-radius-axis-ticks"},p)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:j.createElement(re,{className:Q("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),$e.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return j.isValidElement(n)?o=j.cloneElement(n,i):Y(n)?o=n(i):o=j.createElement(ur,Mn({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(I.PureComponent);No(Co,"displayName","PolarRadiusAxis");No(Co,"axisType","radiusAxis");No(Co,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Wr(e){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wr(e)}function Qt(){return Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qt.apply(this,arguments)}function bh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bh(Object(r),!0).forEach(function(n){Mo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function e3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nm(n.key),n)}}function t3(e,t,r){return t&&xh(e.prototype,t),r&&xh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function r3(e,t,r){return t=Na(t),n3(e,rm()?Reflect.construct(t,r||[],Na(e).constructor):t.apply(e,r))}function n3(e,t){if(t&&(Wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return i3(e)}function i3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rm=function(){return!!e})()}function Na(e){return Na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Na(e)}function a3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pc(e,t)}function Pc(e,t){return Pc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pc(e,t)}function Mo(e,t,r){return t=nm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nm(e){var t=o3(e,"string");return Wr(t)=="symbol"?t:t+""}function o3(e,t){if(Wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var s3=Math.PI/180,c3=1e-5,ko=function(e){function t(){return e3(this,t),r3(this,t,arguments)}return a3(t,e),t3(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,s=i.radius,c=i.orientation,u=i.tickSize,f=u||8,l=fe(a,o,s,n.coordinate),d=fe(a,o,s+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:d.x,y2:d.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*s3),o;return a>c3?o=i==="outer"?"start":"end":a<-1e-5?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,s=n.axisLine,c=n.axisLineType,u=Xt(Xt({},K(this.props,!1)),{},{fill:"none"},K(s,!1));if(c==="circle")return j.createElement(Eo,Qt({className:"recharts-polar-angle-axis-line"},u,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(d){return fe(i,a,o,d.coordinate)});return j.createElement(OL,Qt({className:"recharts-polar-angle-axis-line"},u,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,s=i.tickLine,c=i.tickFormatter,u=i.stroke,f=K(this.props,!1),l=K(o,!1),d=Xt(Xt({},f),{},{fill:"none"},K(s,!1)),h=a.map(function(p,v){var m=n.getTickLineCoord(p),x=n.getTickTextAnchor(p),O=Xt(Xt(Xt({textAnchor:x},f),{},{stroke:"none",fill:u},l),{},{index:v,payload:p,x:m.x2,y:m.y2});return j.createElement(re,Qt({className:Q("recharts-polar-angle-axis-tick",zy(o)),key:"tick-".concat(p.coordinate)},cr(n.props,p,v)),s&&j.createElement("line",Qt({className:"recharts-polar-angle-axis-tick-line"},d,m)),o&&t.renderTickItem(o,O,c?c(p.value,v):p.value))});return j.createElement(re,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:j.createElement(re,{className:Q("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return j.isValidElement(n)?o=j.cloneElement(n,i):Y(n)?o=n(i):o=j.createElement(ur,Qt({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(I.PureComponent);Mo(ko,"displayName","PolarAngleAxis");Mo(ko,"axisType","angleAxis");Mo(ko,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var u3=lv,l3=u3(Object.getPrototypeOf,Object),f3=l3,d3=Et,h3=f3,p3=Nt,v3="[object Object]",y3=Function.prototype,m3=Object.prototype,im=y3.toString,g3=m3.hasOwnProperty,b3=im.call(Object);function x3(e){if(!p3(e)||d3(e)!=v3)return!1;var t=h3(e);if(t===null)return!0;var r=g3.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&im.call(r)==b3}var w3=x3;const O3=ce(w3);var A3=Et,S3=Nt,P3="[object Boolean]";function _3(e){return e===!0||e===!1||S3(e)&&A3(e)==P3}var j3=_3;const $3=ce(j3);function si(e){"@babel/helpers - typeof";return si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},si(e)}function Ca(){return Ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ca.apply(this,arguments)}function T3(e,t){return M3(e)||C3(e,t)||N3(e,t)||E3()}function E3(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function N3(e,t){if(e){if(typeof e=="string")return wh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wh(e,t)}}function wh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function C3(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function M3(e){if(Array.isArray(e))return e}function Oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ah(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Oh(Object(r),!0).forEach(function(n){k3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function k3(e,t,r){return t=I3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I3(e){var t=D3(e,"string");return si(t)=="symbol"?t:t+""}function D3(e,t){if(si(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(si(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Sh=function(t,r,n,i,a){var o=n-i,s;return s="M ".concat(t,",").concat(r),s+="L ".concat(t+n,",").concat(r),s+="L ".concat(t+n-o/2,",").concat(r+a),s+="L ".concat(t+n-o/2-i,",").concat(r+a),s+="L ".concat(t,",").concat(r," Z"),s},L3={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},R3=function(t){var r=Ah(Ah({},L3),t),n=I.useRef(),i=I.useState(-1),a=T3(i,2),o=a[0],s=a[1];I.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&s(w)}catch{}},[]);var c=r.x,u=r.y,f=r.upperWidth,l=r.lowerWidth,d=r.height,h=r.className,p=r.animationEasing,v=r.animationDuration,m=r.animationBegin,x=r.isUpdateAnimationActive;if(c!==+c||u!==+u||f!==+f||l!==+l||d!==+d||f===0&&l===0||d===0)return null;var O=Q("recharts-trapezoid",h);return x?j.createElement(vt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:d,x:c,y:u},to:{upperWidth:f,lowerWidth:l,height:d,x:c,y:u},duration:v,animationEasing:p,isActive:x},function(w){var S=w.upperWidth,g=w.lowerWidth,b=w.height,A=w.x,P=w.y;return j.createElement(vt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:p},j.createElement("path",Ca({},K(r,!0),{className:O,d:Sh(A,P,S,g,b),ref:n})))}):j.createElement("g",null,j.createElement("path",Ca({},K(r,!0),{className:O,d:Sh(c,u,f,l,d)})))},B3=["option","shapeType","propTransformer","activeClassName","isActive"];function ci(e){"@babel/helpers - typeof";return ci=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ci(e)}function F3(e,t){if(e==null)return{};var r=U3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function U3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ph(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ma(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ph(Object(r),!0).forEach(function(n){W3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ph(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function W3(e,t,r){return t=z3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z3(e){var t=q3(e,"string");return ci(t)=="symbol"?t:t+""}function q3(e,t){if(ci(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ci(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function H3(e,t){return Ma(Ma({},t),e)}function K3(e,t){return e==="symbols"}function _h(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return j.createElement(Gu,r);case"trapezoid":return j.createElement(R3,r);case"sector":return j.createElement(Ky,r);case"symbols":if(K3(t))return j.createElement(du,r);break;default:return null}}function G3(e){return I.isValidElement(e)?e.props:e}function am(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?H3:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,s=e.isActive,c=F3(e,B3),u;if(I.isValidElement(t))u=I.cloneElement(t,Ma(Ma({},c),G3(t)));else if(Y(t))u=t(c);else if(O3(t)&&!$3(t)){var f=i(t,c);u=j.createElement(_h,{shapeType:r,elementProps:f})}else{var l=c;u=j.createElement(_h,{shapeType:r,elementProps:l})}return s?j.createElement(re,{className:o},u):u}function Io(e,t){return t!=null&&"trapezoids"in e.props}function Do(e,t){return t!=null&&"sectors"in e.props}function ui(e,t){return t!=null&&"points"in e.props}function V3(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function X3(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function Y3(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function Z3(e,t){var r;return Io(e,t)?r=V3:Do(e,t)?r=X3:ui(e,t)&&(r=Y3),r}function J3(e,t){var r;return Io(e,t)?r="trapezoids":Do(e,t)?r="sectors":ui(e,t)&&(r="points"),r}function Q3(e,t){if(Io(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Do(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return ui(e,t)?t.payload:{}}function eR(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=J3(r,t),a=Q3(r,t),o=n.filter(function(c,u){var f=Pi(a,c),l=r.props[i].filter(function(p){var v=Z3(r,t);return v(p,t)}),d=r.props[i].indexOf(l[l.length-1]),h=u===d;return f&&h}),s=n.indexOf(o[o.length-1]);return s}var Ki;function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function Pr(){return Pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pr.apply(this,arguments)}function jh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ue(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jh(Object(r),!0).forEach(function(n){Je(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sm(n.key),n)}}function rR(e,t,r){return t&&$h(e.prototype,t),r&&$h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function nR(e,t,r){return t=ka(t),iR(e,om()?Reflect.construct(t,r||[],ka(e).constructor):t.apply(e,r))}function iR(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return aR(e)}function aR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function om(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(om=function(){return!!e})()}function ka(e){return ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ka(e)}function oR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_c(e,t)}function _c(e,t){return _c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_c(e,t)}function Je(e,t,r){return t=sm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sm(e){var t=sR(e,"string");return zr(t)=="symbol"?t:t+""}function sR(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Mt=function(e){function t(r){var n;return tR(this,t),n=nR(this,t,[r]),Je(n,"pieRef",null),Je(n,"sectorRefs",[]),Je(n,"id",un("recharts-pie-")),Je(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),Y(i)&&i()}),Je(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),Y(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return oR(t,e),rR(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,s=a.labelLine,c=a.dataKey,u=a.valueKey,f=K(this.props,!1),l=K(o,!1),d=K(s,!1),h=o&&o.offsetRadius||20,p=n.map(function(v,m){var x=(v.startAngle+v.endAngle)/2,O=fe(v.cx,v.cy,v.outerRadius+h,x),w=ue(ue(ue(ue({},f),v),{},{stroke:"none"},l),{},{index:m,textAnchor:t.getTextAnchor(O.x,v.cx)},O),S=ue(ue(ue(ue({},f),v),{},{fill:"none",stroke:v.fill},d),{},{index:m,points:[fe(v.cx,v.cy,v.outerRadius,x),O]}),g=c;return Z(c)&&Z(u)?g="value":Z(c)&&(g=u),j.createElement(re,{key:"label-".concat(v.startAngle,"-").concat(v.endAngle,"-").concat(v.midAngle,"-").concat(m)},s&&t.renderLabelLineItem(s,S,"line"),t.renderLabelItem(o,w,Se(v,g)))});return j.createElement(re,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,s=a.blendStroke,c=a.inactiveShape;return n.map(function(u,f){if((u==null?void 0:u.startAngle)===0&&(u==null?void 0:u.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),d=c&&i.hasActiveIndex()?c:null,h=l?o:d,p=ue(ue({},u),{},{stroke:s?u.fill:u.stroke,tabIndex:-1});return j.createElement(re,Pr({ref:function(m){m&&!i.sectorRefs.includes(m)&&i.sectorRefs.push(m)},tabIndex:-1,className:"recharts-pie-sector"},cr(i.props,u,f),{key:"sector-".concat(u==null?void 0:u.startAngle,"-").concat(u==null?void 0:u.endAngle,"-").concat(u.midAngle,"-").concat(f)}),j.createElement(am,Pr({option:h,isActive:l,shapeType:"sector"},p)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,s=i.animationBegin,c=i.animationDuration,u=i.animationEasing,f=i.animationId,l=this.state,d=l.prevSectors,h=l.prevIsAnimationActive;return j.createElement(vt,{begin:s,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(p){var v=p.t,m=[],x=a&&a[0],O=x.startAngle;return a.forEach(function(w,S){var g=d&&d[S],b=S>0?Ve(w,"paddingAngle",0):0;if(g){var A=Ke(g.endAngle-g.startAngle,w.endAngle-w.startAngle),P=ue(ue({},w),{},{startAngle:O+b,endAngle:O+A(v)+b});m.push(P),O=P.endAngle}else{var _=w.endAngle,E=w.startAngle,$=Ke(0,_-E),T=$(v),M=ue(ue({},w),{},{startAngle:O+b,endAngle:O+T+b});m.push(M),O=M.endAngle}}),j.createElement(re,null,n.renderSectorsStatically(m))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var s=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[s].focus(),i.setState({sectorToFocus:s});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!Pi(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,s=i.className,c=i.label,u=i.cx,f=i.cy,l=i.innerRadius,d=i.outerRadius,h=i.isAnimationActive,p=this.state.isAnimationFinished;if(a||!o||!o.length||!B(u)||!B(f)||!B(l)||!B(d))return null;var v=Q("recharts-pie",s);return j.createElement(re,{tabIndex:this.props.rootTabIndex,className:v,ref:function(x){n.pieRef=x}},this.renderSectors(),c&&this.renderLabels(o),$e.renderCallByParent(this.props,null,!1),(!h||p)&&Pt.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(j.isValidElement(n))return j.cloneElement(n,i);if(Y(n))return n(i);var o=Q("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return j.createElement(Pa,Pr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(j.isValidElement(n))return j.cloneElement(n,i);var o=a;if(Y(n)&&(o=n(i),j.isValidElement(o)))return o;var s=Q("recharts-pie-label-text",typeof n!="boolean"&&!Y(n)?n.className:"");return j.createElement(ur,Pr({},i,{alignmentBaseline:"middle",className:s}),o)}}])}(I.PureComponent);Ki=Mt;Je(Mt,"displayName","Pie");Je(Mt,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!pr.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Je(Mt,"parseDeltaAngle",function(e,t){var r=ke(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Je(Mt,"getRealPieData",function(e){var t=e.data,r=e.children,n=K(e,!1),i=Xe(r,go);return t&&t.length?t.map(function(a,o){return ue(ue(ue({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ue(ue({},n),a.props)}):[]});Je(Mt,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=Wy(i,a),s=n+Ie(e.cx,i,i/2),c=r+Ie(e.cy,a,a/2),u=Ie(e.innerRadius,o,0),f=Ie(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:s,cy:c,innerRadius:u,outerRadius:f,maxRadius:l}});Je(Mt,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ue(ue({},t.type.defaultProps),t.props):t.props,i=Ki.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,s=n.endAngle,c=n.paddingAngle,u=n.dataKey,f=n.nameKey,l=n.valueKey,d=n.tooltipType,h=Math.abs(n.minAngle),p=Ki.parseCoordinateOfPie(n,r),v=Ki.parseDeltaAngle(o,s),m=Math.abs(v),x=u;Z(u)&&Z(l)?(ct(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),x="value"):Z(u)&&(ct(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),x=l);var O=i.filter(function(P){return Se(P,x,0)!==0}).length,w=(m>=360?O:O-1)*c,S=m-O*h-w,g=i.reduce(function(P,_){var E=Se(_,x,0);return P+(B(E)?E:0)},0),b;if(g>0){var A;b=i.map(function(P,_){var E=Se(P,x,0),$=Se(P,f,_),T=(B(E)?E:0)/g,M;_?M=A.endAngle+ke(v)*c*(E!==0?1:0):M=o;var k=M+ke(v)*((E!==0?h:0)+T*S),C=(M+k)/2,D=(p.innerRadius+p.outerRadius)/2,L=[{name:$,value:E,payload:P,dataKey:x,type:d}],F=fe(p.cx,p.cy,D,C);return A=ue(ue(ue({percent:T,cornerRadius:a,name:$,tooltipPayload:L,midAngle:C,middleRadius:D,tooltipPosition:F},P),p),{},{value:Se(P,x),startAngle:M,endAngle:k,payload:P,paddingAngle:ke(v)*c}),A})}return ue(ue({},p),{},{sectors:b,data:i})});var cR=Math.ceil,uR=Math.max;function lR(e,t,r,n){for(var i=-1,a=uR(cR((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var fR=lR,dR=Tv,hR=1/0,pR=17976931348623157e292;function vR(e){if(!e)return e===0?e:0;if(e=dR(e),e===hR||e===-1/0){var t=e<0?-1:1;return t*pR}return e===e?e:0}var cm=vR,yR=fR,mR=mo,ps=cm;function gR(e){return function(t,r,n){return n&&typeof n!="number"&&mR(t,r,n)&&(r=n=void 0),t=ps(t),r===void 0?(r=t,t=0):r=ps(r),n=n===void 0?t<r?1:-1:ps(n),yR(t,r,n,e)}}var bR=gR,xR=bR,wR=xR(),OR=wR;const Ia=ce(OR);function li(e){"@babel/helpers - typeof";return li=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},li(e)}function Th(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Eh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Th(Object(r),!0).forEach(function(n){um(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Th(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function um(e,t,r){return t=AR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AR(e){var t=SR(e,"string");return li(t)=="symbol"?t:t+""}function SR(e,t){if(li(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(li(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var PR=["Webkit","Moz","O","ms"],_R=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=PR.reduce(function(a,o){return Eh(Eh({},a),{},um({},o+n,r))},{});return i[t]=r,i};function qr(e){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qr(e)}function Da(){return Da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Da.apply(this,arguments)}function Nh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nh(Object(r),!0).forEach(function(n){ze(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ch(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fm(n.key),n)}}function $R(e,t,r){return t&&Ch(e.prototype,t),r&&Ch(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function TR(e,t,r){return t=La(t),ER(e,lm()?Reflect.construct(t,r||[],La(e).constructor):t.apply(e,r))}function ER(e,t){if(t&&(qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return NR(e)}function NR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(lm=function(){return!!e})()}function La(e){return La=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},La(e)}function CR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jc(e,t)}function jc(e,t){return jc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jc(e,t)}function ze(e,t,r){return t=fm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fm(e){var t=MR(e,"string");return qr(t)=="symbol"?t:t+""}function MR(e,t){if(qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var kR=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,s=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,u=Tn().domain(Ia(0,c)).range([a,a+o-s]),f=u.domain().map(function(l){return u(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(n),endX:u(i),scale:u,scaleValues:f}},Mh=function(t){return t.changedTouches&&!!t.changedTouches.length},Hr=function(e){function t(r){var n;return jR(this,t),n=TR(this,t,[r]),ze(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),ze(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),ze(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,s=i.startIndex;o==null||o({endIndex:a,startIndex:s})}),n.detachDragEndListener()}),ze(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),ze(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),ze(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),ze(n,"handleSlideDragStart",function(i){var a=Mh(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return CR(t,e),$R(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,s=this.props,c=s.gap,u=s.data,f=u.length-1,l=Math.min(i,a),d=Math.max(i,a),h=t.getIndexInRange(o,l),p=t.getIndexInRange(o,d);return{startIndex:h-h%c,endIndex:p===f?f:p-p%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,s=i.dataKey,c=Se(a[n],s,n);return Y(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,s=i.endX,c=this.props,u=c.x,f=c.width,l=c.travellerWidth,d=c.startIndex,h=c.endIndex,p=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,u+f-l-s,u+f-l-o):v<0&&(v=Math.max(v,u-o,u-s));var m=this.getIndex({startX:o+v,endX:s+v});(m.startIndex!==d||m.endIndex!==h)&&p&&p(m),this.setState({startX:o+v,endX:s+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Mh(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,s=i.endX,c=i.startX,u=this.state[o],f=this.props,l=f.x,d=f.width,h=f.travellerWidth,p=f.onChange,v=f.gap,m=f.data,x={startX:this.state.startX,endX:this.state.endX},O=n.pageX-a;O>0?O=Math.min(O,l+d-h-u):O<0&&(O=Math.max(O,l-u)),x[o]=u+O;var w=this.getIndex(x),S=w.startIndex,g=w.endIndex,b=function(){var P=m.length-1;return o==="startX"&&(s>c?S%v===0:g%v===0)||s<c&&g===P||o==="endX"&&(s>c?g%v===0:S%v===0)||s>c&&g===P};this.setState(ze(ze({},o,u+O),"brushMoveStartX",n.pageX),function(){p&&b()&&p(w)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,s=o.scaleValues,c=o.startX,u=o.endX,f=this.state[i],l=s.indexOf(f);if(l!==-1){var d=l+n;if(!(d===-1||d>=s.length)){var h=s[d];i==="startX"&&h>=u||i==="endX"&&h<=c||this.setState(ze({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,s=n.height,c=n.fill,u=n.stroke;return j.createElement("rect",{stroke:u,fill:c,x:i,y:a,width:o,height:s})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,s=n.height,c=n.data,u=n.children,f=n.padding,l=I.Children.only(u);return l?j.cloneElement(l,{x:i,y:a,width:o,height:s,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,s=this,c=this.props,u=c.y,f=c.travellerWidth,l=c.height,d=c.traveller,h=c.ariaLabel,p=c.data,v=c.startIndex,m=c.endIndex,x=Math.max(n,this.props.x),O=vs(vs({},K(this.props,!1)),{},{x,y:u,width:f,height:l}),w=h||"Min value: ".concat((a=p[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=p[m])===null||o===void 0?void 0:o.name);return j.createElement(re,{tabIndex:0,role:"slider","aria-label":w,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(g){["ArrowLeft","ArrowRight"].includes(g.key)&&(g.preventDefault(),g.stopPropagation(),s.handleTravellerMoveKeyboard(g.key==="ArrowRight"?1:-1,i))},onFocus:function(){s.setState({isTravellerFocused:!0})},onBlur:function(){s.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(d,O))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,s=a.height,c=a.stroke,u=a.travellerWidth,f=Math.min(n,i)+u,l=Math.max(Math.abs(i-n)-u,0);return j.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:s})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,s=n.height,c=n.travellerWidth,u=n.stroke,f=this.state,l=f.startX,d=f.endX,h=5,p={pointerEvents:"none",fill:u};return j.createElement(re,{className:"recharts-brush-texts"},j.createElement(ur,Da({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,d)-h,y:o+s/2},p),this.getTextOfTick(i)),j.createElement(ur,Da({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,d)+c+h,y:o+s/2},p),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,s=n.x,c=n.y,u=n.width,f=n.height,l=n.alwaysShowText,d=this.state,h=d.startX,p=d.endX,v=d.isTextActive,m=d.isSlideMoving,x=d.isTravellerMoving,O=d.isTravellerFocused;if(!i||!i.length||!B(s)||!B(c)||!B(u)||!B(f)||u<=0||f<=0)return null;var w=Q("recharts-brush",a),S=j.Children.count(o)===1,g=_R("userSelect","none");return j.createElement(re,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),S&&this.renderPanorama(),this.renderSlide(h,p),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(p,"endX"),(v||m||x||O||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,s=n.height,c=n.stroke,u=Math.floor(a+s/2)-1;return j.createElement(j.Fragment,null,j.createElement("rect",{x:i,y:a,width:o,height:s,fill:c,stroke:"none"}),j.createElement("line",{x1:i+1,y1:u,x2:i+o-1,y2:u,fill:"none",stroke:"#fff"}),j.createElement("line",{x1:i+1,y1:u+2,x2:i+o-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return j.isValidElement(n)?a=j.cloneElement(n,i):Y(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,s=n.x,c=n.travellerWidth,u=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||u!==i.prevUpdateId)return vs({prevData:a,prevTravellerWidth:c,prevUpdateId:u,prevX:s,prevWidth:o},a&&a.length?kR({data:a,width:o,x:s,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||s!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([s,s+o-c]);var d=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:u,prevX:s,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:d}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,s=a-1;s-o>1;){var c=Math.floor((o+s)/2);n[c]>i?s=c:o=c}return i>=n[s]?s:o}}])}(I.PureComponent);ze(Hr,"displayName","Brush");ze(Hr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var IR=bu;function DR(e,t){var r;return IR(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var LR=DR,RR=rv,BR=mt,FR=LR,UR=Fe,WR=mo;function zR(e,t,r){var n=UR(e)?RR:FR;return r&&WR(e,t,r)&&(t=void 0),n(e,BR(t))}var qR=zR;const HR=ce(qR);var ht=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},kh=Sv;function KR(e,t,r){t=="__proto__"&&kh?kh(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var GR=KR,VR=GR,XR=Ov,YR=mt;function ZR(e,t){var r={};return t=YR(t),XR(e,function(n,i,a){VR(r,i,t(n,i,a))}),r}var JR=ZR;const QR=ce(JR);function eB(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var tB=eB,rB=bu;function nB(e,t){var r=!0;return rB(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var iB=nB,aB=tB,oB=iB,sB=mt,cB=Fe,uB=mo;function lB(e,t,r){var n=cB(e)?aB:oB;return r&&uB(e,t,r)&&(t=void 0),n(e,sB(t))}var fB=lB;const dm=ce(fB);var dB=["x","y"];function fi(e){"@babel/helpers - typeof";return fi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fi(e)}function $c(){return $c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$c.apply(this,arguments)}function Ih(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _n(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ih(Object(r),!0).forEach(function(n){hB(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ih(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hB(e,t,r){return t=pB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pB(e){var t=vB(e,"string");return fi(t)=="symbol"?t:t+""}function vB(e,t){if(fi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function yB(e,t){if(e==null)return{};var r=mB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function gB(e,t){var r=e.x,n=e.y,i=yB(e,dB),a="".concat(r),o=parseInt(a,10),s="".concat(n),c=parseInt(s,10),u="".concat(t.height||i.height),f=parseInt(u,10),l="".concat(t.width||i.width),d=parseInt(l,10);return _n(_n(_n(_n(_n({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:d,name:t.name,radius:t.radius})}function Dh(e){return j.createElement(am,$c({shapeType:"rectangle",propTransformer:gB,activeClassName:"recharts-active-bar"},e))}var bB=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||fr(),r)}},xB=["value","background"],hm;function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function wB(e,t){if(e==null)return{};var r=OB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function OB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ra(){return Ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ra.apply(this,arguments)}function Lh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function be(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lh(Object(r),!0).forEach(function(n){Rt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function AB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Rh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vm(n.key),n)}}function SB(e,t,r){return t&&Rh(e.prototype,t),r&&Rh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function PB(e,t,r){return t=Ba(t),_B(e,pm()?Reflect.construct(t,r||[],Ba(e).constructor):t.apply(e,r))}function _B(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return jB(e)}function jB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(pm=function(){return!!e})()}function Ba(e){return Ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ba(e)}function $B(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tc(e,t)}function Tc(e,t){return Tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tc(e,t)}function Rt(e,t,r){return t=vm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vm(e){var t=TB(e,"string");return Kr(t)=="symbol"?t:t+""}function TB(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var _t=function(e){function t(){var r;AB(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=PB(this,t,[].concat(i)),Rt(r,"state",{isAnimationFinished:!1}),Rt(r,"id",un("recharts-bar-")),Rt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Rt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return $B(t,e),SB(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,s=a.dataKey,c=a.activeIndex,u=a.activeBar,f=K(this.props,!1);return n&&n.map(function(l,d){var h=d===c,p=h?u:o,v=be(be(be({},f),l),{},{isActive:h,option:p,index:d,dataKey:s,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return j.createElement(re,Ra({className:"recharts-bar-rectangle"},cr(i.props,l,d),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value)}),j.createElement(Dh,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,s=i.isAnimationActive,c=i.animationBegin,u=i.animationDuration,f=i.animationEasing,l=i.animationId,d=this.state.prevData;return j.createElement(vt,{begin:c,duration:u,isActive:s,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var p=h.t,v=a.map(function(m,x){var O=d&&d[x];if(O){var w=Ke(O.x,m.x),S=Ke(O.y,m.y),g=Ke(O.width,m.width),b=Ke(O.height,m.height);return be(be({},m),{},{x:w(p),y:S(p),width:g(p),height:b(p)})}if(o==="horizontal"){var A=Ke(0,m.height),P=A(p);return be(be({},m),{},{y:m.y+m.height-P,height:P})}var _=Ke(0,m.width),E=_(p);return be(be({},m),{},{width:E})});return j.createElement(re,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Pi(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,s=i.activeIndex,c=K(this.props.background,!1);return a.map(function(u,f){u.value;var l=u.background,d=wB(u,xB);if(!l)return null;var h=be(be(be(be(be({},d),{},{fill:"#eee"},l),c),cr(n.props,u,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return j.createElement(Dh,Ra({key:"background-bar-".concat(f),option:n.props.background,isActive:f===s},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,s=a.xAxis,c=a.yAxis,u=a.layout,f=a.children,l=Xe(f,_i);if(!l)return null;var d=u==="vertical"?o[0].height/2:o[0].width/2,h=function(m,x){var O=Array.isArray(m.value)?m.value[1]:m.value;return{x:m.x,y:m.y,value:O,errorVal:Se(m,x)}},p={clipPath:n?"url(#clipPath-".concat(i,")"):null};return j.createElement(re,p,l.map(function(v){return j.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:s,yAxis:c,layout:u,offset:d,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,s=n.xAxis,c=n.yAxis,u=n.left,f=n.top,l=n.width,d=n.height,h=n.isAnimationActive,p=n.background,v=n.id;if(i||!a||!a.length)return null;var m=this.state.isAnimationFinished,x=Q("recharts-bar",o),O=s&&s.allowDataOverflow,w=c&&c.allowDataOverflow,S=O||w,g=Z(v)?this.id:v;return j.createElement(re,{className:x},O||w?j.createElement("defs",null,j.createElement("clipPath",{id:"clipPath-".concat(g)},j.createElement("rect",{x:O?u:u-l/2,y:w?f:f-d/2,width:O?l:l*2,height:w?d:d*2}))):null,j.createElement(re,{className:"recharts-bar-rectangles",clipPath:S?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(S,g),(!h||m)&&Pt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(I.PureComponent);hm=_t;Rt(_t,"displayName","Bar");Rt(_t,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!pr.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Rt(_t,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,s=e.xAxisTicks,c=e.yAxisTicks,u=e.stackedData,f=e.dataStartIndex,l=e.displayedData,d=e.offset,h=dk(n,r);if(!h)return null;var p=t.layout,v=r.type.defaultProps,m=v!==void 0?be(be({},v),r.props):r.props,x=m.dataKey,O=m.children,w=m.minPointSize,S=p==="horizontal"?o:a,g=u?S.scale.domain():null,b=bk({numericAxis:S}),A=Xe(O,go),P=l.map(function(_,E){var $,T,M,k,C,D;u?$=hk(u[f+E],g):($=Se(_,x),Array.isArray($)||($=[b,$]));var L=bB(w,hm.defaultProps.minPointSize)($[1],E);if(p==="horizontal"){var F,U=[o.scale($[0]),o.scale($[1])],H=U[0],V=U[1];T=Td({axis:a,ticks:s,bandSize:i,offset:h.offset,entry:_,index:E}),M=(F=V??H)!==null&&F!==void 0?F:void 0,k=h.size;var z=H-V;if(C=Number.isNaN(z)?0:z,D={x:T,y:o.y,width:k,height:o.height},Math.abs(L)>0&&Math.abs(C)<Math.abs(L)){var X=ke(C||L)*(Math.abs(L)-Math.abs(C));M-=X,C+=X}}else{var de=[a.scale($[0]),a.scale($[1])],ge=de[0],Ue=de[1];if(T=ge,M=Td({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:_,index:E}),k=Ue-ge,C=h.size,D={x:a.x,y:M,width:a.width,height:C},Math.abs(L)>0&&Math.abs(k)<Math.abs(L)){var qt=ke(k||L)*(Math.abs(L)-Math.abs(k));k+=qt}}return be(be(be({},_),{},{x:T,y:M,width:k,height:C,value:u?$:$[1],payload:_,background:D},A&&A[E]&&A[E].props),{},{tooltipPayload:[Fy(r,_)],tooltipPosition:{x:T+k/2,y:M+C/2}})});return be({data:P,layout:p},d)});function di(e){"@babel/helpers - typeof";return di=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},di(e)}function EB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ym(n.key),n)}}function NB(e,t,r){return t&&Bh(e.prototype,t),r&&Bh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Fh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function at(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fh(Object(r),!0).forEach(function(n){Lo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Lo(e,t,r){return t=ym(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ym(e){var t=CB(e,"string");return di(t)=="symbol"?t:t+""}function CB(e,t){if(di(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(di(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var mm=function(t,r,n,i,a){var o=t.width,s=t.height,c=t.layout,u=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:s-n.bottom,bottomMirror:s-n.bottom},d=!!qe(u,_t);return f.reduce(function(h,p){var v=r[p],m=v.orientation,x=v.domain,O=v.padding,w=O===void 0?{}:O,S=v.mirror,g=v.reversed,b="".concat(m).concat(S?"Mirror":""),A,P,_,E,$;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var T=x[1]-x[0],M=1/0,k=v.categoricalDomain.sort();if(k.forEach(function(de,ge){ge>0&&(M=Math.min((de||0)-(k[ge-1]||0),M))}),Number.isFinite(M)){var C=M/T,D=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(A=C*D/2),v.padding==="no-gap"){var L=Ie(t.barCategoryGap,C*D),F=C*D/2;A=F-L-(F-L)/D*L}}}i==="xAxis"?P=[n.left+(w.left||0)+(A||0),n.left+n.width-(w.right||0)-(A||0)]:i==="yAxis"?P=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(A||0),n.top+n.height-(w.bottom||0)-(A||0)]:P=v.range,g&&(P=[P[1],P[0]]);var U=Dy(v,a,d),H=U.scale,V=U.realScaleType;H.domain(x).range(P),Ly(H);var z=Ry(H,at(at({},v),{},{realScaleType:V}));i==="xAxis"?($=m==="top"&&!S||m==="bottom"&&S,_=n.left,E=l[b]-$*v.height):i==="yAxis"&&($=m==="left"&&!S||m==="right"&&S,_=l[b]-$*v.width,E=n.top);var X=at(at(at({},v),z),{},{realScaleType:V,x:_,y:E,scale:H,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return X.bandSize=Oa(X,z),!v.hide&&i==="xAxis"?l[b]+=($?-1:1)*X.height:v.hide||(l[b]+=($?-1:1)*X.width),at(at({},h),{},Lo({},p,X))},{})},gm=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},MB=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return gm({x:r,y:n},{x:i,y:a})},bm=function(){function e(t){EB(this,e),this.scale=t}return NB(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var s=this.bandwidth?this.bandwidth():0;return this.scale(r)+s}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();Lo(bm,"EPS",1e-4);var Vu=function(t){var r=Object.keys(t).reduce(function(n,i){return at(at({},n),{},Lo({},i,bm.create(t[i])))},{});return at(at({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,s=a.position;return QR(i,function(c,u){return r[u].apply(c,{bandAware:o,position:s})})},isInRange:function(i){return dm(i,function(a,o){return r[o].isInRange(a)})}})};function kB(e){return(e%180+180)%180}var IB=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=kB(i),o=a*Math.PI/180,s=Math.atan(n/r),c=o>s&&o<Math.PI-s?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},DB=mt,LB=xi,RB=vo;function BB(e){return function(t,r,n){var i=Object(t);if(!LB(t)){var a=DB(r);t=RB(t),r=function(s){return a(i[s],s,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var FB=BB,UB=cm;function WB(e){var t=UB(e),r=t%1;return t===t?r?t-r:t:0}var zB=WB,qB=yv,HB=mt,KB=zB,GB=Math.max;function VB(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:KB(r);return i<0&&(i=GB(n+i,0)),qB(e,HB(t),i)}var XB=VB,YB=FB,ZB=XB,JB=YB(ZB),QB=JB;const e4=ce(QB);var t4=tx(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Xu=I.createContext(void 0),Yu=I.createContext(void 0),xm=I.createContext(void 0),wm=I.createContext({}),Om=I.createContext(void 0),Am=I.createContext(0),Sm=I.createContext(0),Uh=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,s=t.children,c=t.width,u=t.height,f=t4(a);return j.createElement(Xu.Provider,{value:n},j.createElement(Yu.Provider,{value:i},j.createElement(wm.Provider,{value:a},j.createElement(xm.Provider,{value:f},j.createElement(Om.Provider,{value:o},j.createElement(Am.Provider,{value:u},j.createElement(Sm.Provider,{value:c},s)))))))},r4=function(){return I.useContext(Om)},Pm=function(t){var r=I.useContext(Xu);r==null&&fr();var n=r[t];return n==null&&fr(),n},n4=function(){var t=I.useContext(Xu);return Lt(t)},i4=function(){var t=I.useContext(Yu),r=e4(t,function(n){return dm(n.domain,Number.isFinite)});return r||Lt(t)},_m=function(t){var r=I.useContext(Yu);r==null&&fr();var n=r[t];return n==null&&fr(),n},a4=function(){var t=I.useContext(xm);return t},o4=function(){return I.useContext(wm)},Zu=function(){return I.useContext(Sm)},Ju=function(){return I.useContext(Am)};function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function s4(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c4(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$m(n.key),n)}}function u4(e,t,r){return t&&c4(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function l4(e,t,r){return t=Fa(t),f4(e,jm()?Reflect.construct(t,r||[],Fa(e).constructor):t.apply(e,r))}function f4(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return d4(e)}function d4(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function jm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(jm=function(){return!!e})()}function Fa(e){return Fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Fa(e)}function h4(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ec(e,t)}function Ec(e,t){return Ec=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ec(e,t)}function Wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wh(Object(r),!0).forEach(function(n){Qu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Qu(e,t,r){return t=$m(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $m(e){var t=p4(e,"string");return Gr(t)=="symbol"?t:t+""}function p4(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function v4(e,t){return b4(e)||g4(e,t)||m4(e,t)||y4()}function y4(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function m4(e,t){if(e){if(typeof e=="string")return qh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qh(e,t)}}function qh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function g4(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function b4(e){if(Array.isArray(e))return e}function Nc(){return Nc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Nc.apply(this,arguments)}var x4=function(t,r){var n;return j.isValidElement(t)?n=j.cloneElement(t,r):Y(t)?n=t(r):n=j.createElement("line",Nc({},r,{className:"recharts-reference-line-line"})),n},w4=function(t,r,n,i,a,o,s,c,u){var f=a.x,l=a.y,d=a.width,h=a.height;if(n){var p=u.y,v=t.y.apply(p,{position:o});if(ht(u,"discard")&&!t.y.isInRange(v))return null;var m=[{x:f+d,y:v},{x:f,y:v}];return c==="left"?m.reverse():m}if(r){var x=u.x,O=t.x.apply(x,{position:o});if(ht(u,"discard")&&!t.x.isInRange(O))return null;var w=[{x:O,y:l+h},{x:O,y:l}];return s==="top"?w.reverse():w}if(i){var S=u.segment,g=S.map(function(b){return t.apply(b,{position:o})});return ht(u,"discard")&&HR(g,function(b){return!t.isInRange(b)})?null:g}return null};function O4(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,s=e.className,c=e.alwaysShow,u=r4(),f=Pm(i),l=_m(a),d=a4();if(!u||!d)return null;ct(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=Vu({x:f.scale,y:l.scale}),p=Pe(t),v=Pe(r),m=n&&n.length===2,x=w4(h,p,v,m,d,e.position,f.orientation,l.orientation,e);if(!x)return null;var O=v4(x,2),w=O[0],S=w.x,g=w.y,b=O[1],A=b.x,P=b.y,_=ht(e,"hidden")?"url(#".concat(u,")"):void 0,E=zh(zh({clipPath:_},K(e,!0)),{},{x1:S,y1:g,x2:A,y2:P});return j.createElement(re,{className:Q("recharts-reference-line",s)},x4(o,E),$e.renderCallByParent(e,MB({x1:S,y1:g,x2:A,y2:P})))}var el=function(e){function t(){return s4(this,t),l4(this,t,arguments)}return h4(t,e),u4(t,[{key:"render",value:function(){return j.createElement(O4,this.props)}}])}(j.Component);Qu(el,"displayName","ReferenceLine");Qu(el,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Cc(){return Cc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cc.apply(this,arguments)}function Vr(e){"@babel/helpers - typeof";return Vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vr(e)}function Hh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hh(Object(r),!0).forEach(function(n){Ro(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function A4(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function S4(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Em(n.key),n)}}function P4(e,t,r){return t&&S4(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _4(e,t,r){return t=Ua(t),j4(e,Tm()?Reflect.construct(t,r||[],Ua(e).constructor):t.apply(e,r))}function j4(e,t){if(t&&(Vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return $4(e)}function $4(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Tm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Tm=function(){return!!e})()}function Ua(e){return Ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ua(e)}function T4(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mc(e,t)}function Mc(e,t){return Mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Mc(e,t)}function Ro(e,t,r){return t=Em(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Em(e){var t=E4(e,"string");return Vr(t)=="symbol"?t:t+""}function E4(e,t){if(Vr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var N4=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Vu({x:i.scale,y:a.scale}),s=o.apply({x:r,y:n},{bandAware:!0});return ht(t,"discard")&&!o.isInRange(s)?null:s},Bo=function(e){function t(){return A4(this,t),_4(this,t,arguments)}return T4(t,e),P4(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,s=n.alwaysShow,c=n.clipPathId,u=Pe(i),f=Pe(a);if(ct(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!f)return null;var l=N4(this.props);if(!l)return null;var d=l.x,h=l.y,p=this.props,v=p.shape,m=p.className,x=ht(this.props,"hidden")?"url(#".concat(c,")"):void 0,O=Kh(Kh({clipPath:x},K(this.props,!0)),{},{cx:d,cy:h});return j.createElement(re,{className:Q("recharts-reference-dot",m)},t.renderDot(v,O),$e.renderCallByParent(this.props,{x:d-o,y:h-o,width:2*o,height:2*o}))}}])}(j.Component);Ro(Bo,"displayName","ReferenceDot");Ro(Bo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});Ro(Bo,"renderDot",function(e,t){var r;return j.isValidElement(e)?r=j.cloneElement(e,t):Y(e)?r=e(t):r=j.createElement(Eo,Cc({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function kc(){return kc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kc.apply(this,arguments)}function Xr(e){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(e)}function Gh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gh(Object(r),!0).forEach(function(n){Fo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function C4(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function M4(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Cm(n.key),n)}}function k4(e,t,r){return t&&M4(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function I4(e,t,r){return t=Wa(t),D4(e,Nm()?Reflect.construct(t,r||[],Wa(e).constructor):t.apply(e,r))}function D4(e,t){if(t&&(Xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return L4(e)}function L4(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Nm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Nm=function(){return!!e})()}function Wa(e){return Wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wa(e)}function R4(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ic(e,t)}function Ic(e,t){return Ic=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ic(e,t)}function Fo(e,t,r){return t=Cm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cm(e){var t=B4(e,"string");return Xr(t)=="symbol"?t:t+""}function B4(e,t){if(Xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var F4=function(t,r,n,i,a){var o=a.x1,s=a.x2,c=a.y1,u=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var d=Vu({x:f.scale,y:l.scale}),h={x:t?d.x.apply(o,{position:"start"}):d.x.rangeMin,y:n?d.y.apply(c,{position:"start"}):d.y.rangeMin},p={x:r?d.x.apply(s,{position:"end"}):d.x.rangeMax,y:i?d.y.apply(u,{position:"end"}):d.y.rangeMax};return ht(a,"discard")&&(!d.isInRange(h)||!d.isInRange(p))?null:gm(h,p)},Uo=function(e){function t(){return C4(this,t),I4(this,t,arguments)}return R4(t,e),k4(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,s=n.y2,c=n.className,u=n.alwaysShow,f=n.clipPathId;ct(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Pe(i),d=Pe(a),h=Pe(o),p=Pe(s),v=this.props.shape;if(!l&&!d&&!h&&!p&&!v)return null;var m=F4(l,d,h,p,this.props);if(!m&&!v)return null;var x=ht(this.props,"hidden")?"url(#".concat(f,")"):void 0;return j.createElement(re,{className:Q("recharts-reference-area",c)},t.renderRect(v,Vh(Vh({clipPath:x},K(this.props,!0)),m)),$e.renderCallByParent(this.props,m))}}])}(j.Component);Fo(Uo,"displayName","ReferenceArea");Fo(Uo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Fo(Uo,"renderRect",function(e,t){var r;return j.isValidElement(e)?r=j.cloneElement(e,t):Y(e)?r=e(t):r=j.createElement(Gu,kc({},t,{className:"recharts-reference-area-rect"})),r});function Mm(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function U4(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return IB(n,r)}function W4(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,s=e.height;return t===1?{start:n?i:a,end:n?i+o:a+s}:{start:n?i+o:a+s,end:n?i:a}}function za(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function z4(e,t){return Mm(e,t+1)}function q4(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,s=t.end,c=0,u=1,f=o,l=function(){var p=n==null?void 0:n[c];if(p===void 0)return{v:Mm(n,u)};var v=c,m,x=function(){return m===void 0&&(m=r(p,v)),m},O=p.coordinate,w=c===0||za(e,O,x,f,s);w||(c=0,f=o,u+=1),w&&(f=O+e*(x()/2+i),c+=u)},d;u<=a.length;)if(d=l(),d)return d.v;return[]}function hi(e){"@babel/helpers - typeof";return hi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hi(e)}function Xh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ne(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xh(Object(r),!0).forEach(function(n){H4(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H4(e,t,r){return t=K4(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function K4(e){var t=G4(e,"string");return hi(t)=="symbol"?t:t+""}function G4(e,t){if(hi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(hi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function V4(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,s=t.start,c=t.end,u=function(d){var h=a[d],p,v=function(){return p===void 0&&(p=r(h,d)),p};if(d===o-1){var m=e*(h.coordinate+e*v()/2-c);a[d]=h=Ne(Ne({},h),{},{tickCoord:m>0?h.coordinate-m*e:h.coordinate})}else a[d]=h=Ne(Ne({},h),{},{tickCoord:h.coordinate});var x=za(e,h.tickCoord,v,s,c);x&&(c=h.tickCoord-e*(v()/2+i),a[d]=Ne(Ne({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)u(f);return a}function X4(e,t,r,n,i,a){var o=(n||[]).slice(),s=o.length,c=t.start,u=t.end;if(a){var f=n[s-1],l=r(f,s-1),d=e*(f.coordinate+e*l/2-u);o[s-1]=f=Ne(Ne({},f),{},{tickCoord:d>0?f.coordinate-d*e:f.coordinate});var h=za(e,f.tickCoord,function(){return l},c,u);h&&(u=f.tickCoord-e*(l/2+i),o[s-1]=Ne(Ne({},f),{},{isShow:!0}))}for(var p=a?s-1:s,v=function(O){var w=o[O],S,g=function(){return S===void 0&&(S=r(w,O)),S};if(O===0){var b=e*(w.coordinate-e*g()/2-c);o[O]=w=Ne(Ne({},w),{},{tickCoord:b<0?w.coordinate-b*e:w.coordinate})}else o[O]=w=Ne(Ne({},w),{},{tickCoord:w.coordinate});var A=za(e,w.tickCoord,g,c,u);A&&(c=w.tickCoord+e*(g()/2+i),o[O]=Ne(Ne({},w),{},{isShow:!0}))},m=0;m<p;m++)v(m);return o}function tl(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,s=e.orientation,c=e.interval,u=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(B(c)||pr.isSsr)return z4(i,typeof c=="number"&&B(c)?c:0);var d=[],h=s==="top"||s==="bottom"?"width":"height",p=f&&h==="width"?$n(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(w,S){var g=Y(u)?u(w.value,S):w.value;return h==="width"?U4($n(g,{fontSize:t,letterSpacing:r}),p,l):$n(g,{fontSize:t,letterSpacing:r})[h]},m=i.length>=2?ke(i[1].coordinate-i[0].coordinate):1,x=W4(a,m,h);return c==="equidistantPreserveStart"?q4(m,x,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?d=X4(m,x,v,i,o,c==="preserveStartEnd"):d=V4(m,x,v,i,o),d.filter(function(O){return O.isShow}))}var Y4=["viewBox"],Z4=["viewBox"],J4=["ticks"];function Yr(e){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(e)}function _r(){return _r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_r.apply(this,arguments)}function Yh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yh(Object(r),!0).forEach(function(n){rl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ys(e,t){if(e==null)return{};var r=Q4(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Q4(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function e8(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Zh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Im(n.key),n)}}function t8(e,t,r){return t&&Zh(e.prototype,t),r&&Zh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function r8(e,t,r){return t=qa(t),n8(e,km()?Reflect.construct(t,r||[],qa(e).constructor):t.apply(e,r))}function n8(e,t){if(t&&(Yr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return i8(e)}function i8(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function km(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(km=function(){return!!e})()}function qa(e){return qa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},qa(e)}function a8(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Dc(e,t)}function Dc(e,t){return Dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Dc(e,t)}function rl(e,t,r){return t=Im(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Im(e){var t=o8(e,"string");return Yr(t)=="symbol"?t:t+""}function o8(e,t){if(Yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var yn=function(e){function t(r){var n;return e8(this,t),n=r8(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return a8(t,e),t8(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=ys(n,Y4),s=this.props,c=s.viewBox,u=ys(s,Z4);return!$r(a,c)||!$r(o,u)||!$r(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,s=i.width,c=i.height,u=i.orientation,f=i.tickSize,l=i.mirror,d=i.tickMargin,h,p,v,m,x,O,w=l?-1:1,S=n.tickSize||f,g=B(n.tickCoord)?n.tickCoord:n.coordinate;switch(u){case"top":h=p=n.coordinate,m=o+ +!l*c,v=m-w*S,O=v-w*d,x=g;break;case"left":v=m=n.coordinate,p=a+ +!l*s,h=p-w*S,x=h-w*d,O=g;break;case"right":v=m=n.coordinate,p=a+ +l*s,h=p+w*S,x=h+w*d,O=g;break;default:h=p=n.coordinate,m=o+ +l*c,v=m+w*S,O=v+w*d,x=g;break}return{line:{x1:h,y1:v,x2:p,y2:m},tick:{x,y:O}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,s=n.height,c=n.orientation,u=n.mirror,f=n.axisLine,l=Me(Me(Me({},K(this.props,!1)),K(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var d=+(c==="top"&&!u||c==="bottom"&&u);l=Me(Me({},l),{},{x1:i,y1:a+d*s,x2:i+o,y2:a+d*s})}else{var h=+(c==="left"&&!u||c==="right"&&u);l=Me(Me({},l),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+s})}return j.createElement("line",_r({},l,{className:Q("recharts-cartesian-axis-line",Ve(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,s=this.props,c=s.tickLine,u=s.stroke,f=s.tick,l=s.tickFormatter,d=s.unit,h=tl(Me(Me({},this.props),{},{ticks:n}),i,a),p=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),m=K(this.props,!1),x=K(f,!1),O=Me(Me({},m),{},{fill:"none"},K(c,!1)),w=h.map(function(S,g){var b=o.getTickLineCoord(S),A=b.line,P=b.tick,_=Me(Me(Me(Me({textAnchor:p,verticalAnchor:v},m),{},{stroke:"none",fill:u},x),P),{},{index:g,payload:S,visibleTicksCount:h.length,tickFormatter:l});return j.createElement(re,_r({className:"recharts-cartesian-axis-tick",key:"tick-".concat(S.value,"-").concat(S.coordinate,"-").concat(S.tickCoord)},cr(o.props,S,g)),c&&j.createElement("line",_r({},O,A,{className:Q("recharts-cartesian-axis-tick-line",Ve(c,"className"))})),f&&t.renderTickItem(f,_,"".concat(Y(l)?l(S.value,g):S.value).concat(d||"")))});return j.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,s=i.height,c=i.ticksGenerator,u=i.className,f=i.hide;if(f)return null;var l=this.props,d=l.ticks,h=ys(l,J4),p=d;return Y(c)&&(p=d&&d.length>0?c(this.props):c(h)),o<=0||s<=0||!p||!p.length?null:j.createElement(re,{className:Q("recharts-cartesian-axis",u),ref:function(m){n.layerReference=m}},a&&this.renderAxisLine(),this.renderTicks(p,this.state.fontSize,this.state.letterSpacing),$e.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return j.isValidElement(n)?o=j.cloneElement(n,i):Y(n)?o=n(i):o=j.createElement(ur,_r({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(I.Component);rl(yn,"displayName","CartesianAxis");rl(yn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var s8=["x1","y1","x2","y2","key"],c8=["offset"];function dr(e){"@babel/helpers - typeof";return dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dr(e)}function Jh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jh(Object(r),!0).forEach(function(n){u8(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function u8(e,t,r){return t=l8(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l8(e){var t=f8(e,"string");return dr(t)=="symbol"?t:t+""}function f8(e,t){if(dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nr.apply(this,arguments)}function Qh(e,t){if(e==null)return{};var r=d8(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function d8(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var h8=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,s=t.height,c=t.ry;return j.createElement("rect",{x:i,y:a,ry:c,width:o,height:s,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Dm(e,t){var r;if(j.isValidElement(e))r=j.cloneElement(e,t);else if(Y(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,s=t.key,c=Qh(t,s8),u=K(c,!1);u.offset;var f=Qh(u,c8);r=j.createElement("line",nr({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:s}))}return r}function p8(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(s,c){var u=Ce(Ce({},e),{},{x1:t,y1:s,x2:t+r,y2:s,key:"line-".concat(c),index:c});return Dm(i,u)});return j.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function v8(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(s,c){var u=Ce(Ce({},e),{},{x1:s,y1:t,x2:s,y2:t+r,key:"line-".concat(c),index:c});return Dm(i,u)});return j.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function y8(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,s=e.horizontalPoints,c=e.horizontal,u=c===void 0?!0:c;if(!u||!t||!t.length)return null;var f=s.map(function(d){return Math.round(d+i-i)}).sort(function(d,h){return d-h});i!==f[0]&&f.unshift(0);var l=f.map(function(d,h){var p=!f[h+1],v=p?i+o-d:f[h+1]-d;if(v<=0)return null;var m=h%t.length;return j.createElement("rect",{key:"react-".concat(h),y:d,x:n,height:v,width:a,stroke:"none",fill:t[m],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return j.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function m8(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,s=e.width,c=e.height,u=e.verticalPoints;if(!r||!n||!n.length)return null;var f=u.map(function(d){return Math.round(d+a-a)}).sort(function(d,h){return d-h});a!==f[0]&&f.unshift(0);var l=f.map(function(d,h){var p=!f[h+1],v=p?a+s-d:f[h+1]-d;if(v<=0)return null;var m=h%n.length;return j.createElement("rect",{key:"react-".concat(h),x:d,y:o,width:v,height:c,stroke:"none",fill:n[m],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return j.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var g8=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return Iy(tl(Ce(Ce(Ce({},yn.defaultProps),n),{},{ticks:Ot(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},b8=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return Iy(tl(Ce(Ce(Ce({},yn.defaultProps),n),{},{ticks:Ot(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},wr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Gi(e){var t,r,n,i,a,o,s=Zu(),c=Ju(),u=o4(),f=Ce(Ce({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:wr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:wr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:wr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:wr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:wr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:wr.verticalFill,x:B(e.x)?e.x:u.left,y:B(e.y)?e.y:u.top,width:B(e.width)?e.width:u.width,height:B(e.height)?e.height:u.height}),l=f.x,d=f.y,h=f.width,p=f.height,v=f.syncWithTicks,m=f.horizontalValues,x=f.verticalValues,O=n4(),w=i4();if(!B(h)||h<=0||!B(p)||p<=0||!B(l)||l!==+l||!B(d)||d!==+d)return null;var S=f.verticalCoordinatesGenerator||g8,g=f.horizontalCoordinatesGenerator||b8,b=f.horizontalPoints,A=f.verticalPoints;if((!b||!b.length)&&Y(g)){var P=m&&m.length,_=g({yAxis:w?Ce(Ce({},w),{},{ticks:P?m:w.ticks}):void 0,width:s,height:c,offset:u},P?!0:v);ct(Array.isArray(_),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dr(_),"]")),Array.isArray(_)&&(b=_)}if((!A||!A.length)&&Y(S)){var E=x&&x.length,$=S({xAxis:O?Ce(Ce({},O),{},{ticks:E?x:O.ticks}):void 0,width:s,height:c,offset:u},E?!0:v);ct(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dr($),"]")),Array.isArray($)&&(A=$)}return j.createElement("g",{className:"recharts-cartesian-grid"},j.createElement(h8,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),j.createElement(p8,nr({},f,{offset:u,horizontalPoints:b,xAxis:O,yAxis:w})),j.createElement(v8,nr({},f,{offset:u,verticalPoints:A,xAxis:O,yAxis:w})),j.createElement(y8,nr({},f,{horizontalPoints:b})),j.createElement(m8,nr({},f,{verticalPoints:A})))}Gi.displayName="CartesianGrid";var x8=["type","layout","connectNulls","ref"],w8=["key"];function Zr(e){"@babel/helpers - typeof";return Zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zr(e)}function ep(e,t){if(e==null)return{};var r=O8(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function O8(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function kn(){return kn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kn.apply(this,arguments)}function tp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function We(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?tp(Object(r),!0).forEach(function(n){ot(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Or(e){return _8(e)||P8(e)||S8(e)||A8()}function A8(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function S8(e,t){if(e){if(typeof e=="string")return Lc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lc(e,t)}}function P8(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _8(e){if(Array.isArray(e))return Lc(e)}function Lc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function j8(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rm(n.key),n)}}function $8(e,t,r){return t&&rp(e.prototype,t),r&&rp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function T8(e,t,r){return t=Ha(t),E8(e,Lm()?Reflect.construct(t,r||[],Ha(e).constructor):t.apply(e,r))}function E8(e,t){if(t&&(Zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return N8(e)}function N8(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Lm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Lm=function(){return!!e})()}function Ha(e){return Ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ha(e)}function C8(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rc(e,t)}function Rc(e,t){return Rc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Rc(e,t)}function ot(e,t,r){return t=Rm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rm(e){var t=M8(e,"string");return Zr(t)=="symbol"?t:t+""}function M8(e,t){if(Zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ji=function(e){function t(){var r;j8(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=T8(this,t,[].concat(i)),ot(r,"state",{isAnimationFinished:!0,totalLength:0}),ot(r,"generateSimpleStrokeDasharray",function(o,s){return"".concat(s,"px ").concat(o-s,"px")}),ot(r,"getStrokeDasharray",function(o,s,c){var u=c.reduce(function(x,O){return x+O});if(!u)return r.generateSimpleStrokeDasharray(s,o);for(var f=Math.floor(o/u),l=o%u,d=s-o,h=[],p=0,v=0;p<c.length;v+=c[p],++p)if(v+c[p]>l){h=[].concat(Or(c.slice(0,p)),[l-v]);break}var m=h.length%2===0?[0,d]:[d];return[].concat(Or(t.repeat(c,f)),Or(h),m).map(function(x){return"".concat(x,"px")}).join(", ")}),ot(r,"id",un("recharts-line-")),ot(r,"pathRef",function(o){r.mainCurve=o}),ot(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),ot(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return C8(t,e),$8(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,s=a.xAxis,c=a.yAxis,u=a.layout,f=a.children,l=Xe(f,_i);if(!l)return null;var d=function(v,m){return{x:v.x,y:v.y,value:v.value,errorVal:Se(v.payload,m)}},h={clipPath:n?"url(#clipPath-".concat(i,")"):null};return j.createElement(re,h,l.map(function(p){return j.cloneElement(p,{key:"bar-".concat(p.props.dataKey),data:o,xAxis:s,yAxis:c,layout:u,dataPointFormatter:d})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var s=this.props,c=s.dot,u=s.points,f=s.dataKey,l=K(this.props,!1),d=K(c,!0),h=u.map(function(v,m){var x=We(We(We({key:"dot-".concat(m),r:3},l),d),{},{value:v.value,dataKey:f,cx:v.x,cy:v.y,index:m,payload:v.payload});return t.renderDotItem(c,x)}),p={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return j.createElement(re,kn({className:"recharts-line-dots",key:"dots"},p),h)}},{key:"renderCurveStatically",value:function(n,i,a,o){var s=this.props,c=s.type,u=s.layout,f=s.connectNulls;s.ref;var l=ep(s,x8),d=We(We(We({},K(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:u,connectNulls:f});return j.createElement(Pa,kn({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,s=o.points,c=o.strokeDasharray,u=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,d=o.animationEasing,h=o.animationId,p=o.animateNewValues,v=o.width,m=o.height,x=this.state,O=x.prevPoints,w=x.totalLength;return j.createElement(vt,{begin:f,duration:l,isActive:u,easing:d,from:{t:0},to:{t:1},key:"line-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(S){var g=S.t;if(O){var b=O.length/s.length,A=s.map(function(T,M){var k=Math.floor(M*b);if(O[k]){var C=O[k],D=Ke(C.x,T.x),L=Ke(C.y,T.y);return We(We({},T),{},{x:D(g),y:L(g)})}if(p){var F=Ke(v*2,T.x),U=Ke(m/2,T.y);return We(We({},T),{},{x:F(g),y:U(g)})}return We(We({},T),{},{x:T.x,y:T.y})});return a.renderCurveStatically(A,n,i)}var P=Ke(0,w),_=P(g),E;if(c){var $="".concat(c).split(/[,\s]+/gim).map(function(T){return parseFloat(T)});E=a.getStrokeDasharray(_,w,$)}else E=a.generateSimpleStrokeDasharray(w,_);return a.renderCurveStatically(s,n,i,{strokeDasharray:E})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,s=a.isAnimationActive,c=this.state,u=c.prevPoints,f=c.totalLength;return s&&o&&o.length&&(!u&&f>0||!Pi(u,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,s=i.points,c=i.className,u=i.xAxis,f=i.yAxis,l=i.top,d=i.left,h=i.width,p=i.height,v=i.isAnimationActive,m=i.id;if(a||!s||!s.length)return null;var x=this.state.isAnimationFinished,O=s.length===1,w=Q("recharts-line",c),S=u&&u.allowDataOverflow,g=f&&f.allowDataOverflow,b=S||g,A=Z(m)?this.id:m,P=(n=K(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},_=P.r,E=_===void 0?3:_,$=P.strokeWidth,T=$===void 0?2:$,M=aw(o)?o:{},k=M.clipDot,C=k===void 0?!0:k,D=E*2+T;return j.createElement(re,{className:w},S||g?j.createElement("defs",null,j.createElement("clipPath",{id:"clipPath-".concat(A)},j.createElement("rect",{x:S?d:d-h/2,y:g?l:l-p/2,width:S?h:h*2,height:g?p:p*2})),!C&&j.createElement("clipPath",{id:"clipPath-dots-".concat(A)},j.createElement("rect",{x:d-D/2,y:l-D/2,width:h+D,height:p+D}))):null,!O&&this.renderCurve(b,A),this.renderErrorBar(b,A),(O||o)&&this.renderDots(b,C,A),(!v||x)&&Pt.renderCallByParent(this.props,s))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(Or(n),[0]):n,o=[],s=0;s<i;++s)o=[].concat(Or(o),Or(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(j.isValidElement(n))a=j.cloneElement(n,i);else if(Y(n))a=n(i);else{var o=i.key,s=ep(i,w8),c=Q("recharts-line-dot",typeof n!="boolean"?n.className:"");a=j.createElement(Eo,kn({key:o},s,{className:c}))}return a}}])}(I.PureComponent);ot(ji,"displayName","Line");ot(ji,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!pr.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});ot(ji,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,s=e.bandSize,c=e.displayedData,u=e.offset,f=t.layout,l=c.map(function(d,h){var p=Se(d,o);return f==="horizontal"?{x:$d({axis:r,ticks:i,bandSize:s,entry:d,index:h}),y:Z(p)?null:n.scale(p),value:p,payload:d}:{x:Z(p)?null:r.scale(p),y:$d({axis:n,ticks:a,bandSize:s,entry:d,index:h}),value:p,payload:d}});return We({points:l,layout:f},u)});function Jr(e){"@babel/helpers - typeof";return Jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jr(e)}function k8(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function I8(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Um(n.key),n)}}function D8(e,t,r){return t&&I8(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function L8(e,t,r){return t=Ka(t),R8(e,Bm()?Reflect.construct(t,r||[],Ka(e).constructor):t.apply(e,r))}function R8(e,t){if(t&&(Jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return B8(e)}function B8(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Bm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Bm=function(){return!!e})()}function Ka(e){return Ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ka(e)}function F8(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bc(e,t)}function Bc(e,t){return Bc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Bc(e,t)}function Fm(e,t,r){return t=Um(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Um(e){var t=U8(e,"string");return Jr(t)=="symbol"?t:t+""}function U8(e,t){if(Jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Fc(){return Fc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fc.apply(this,arguments)}function W8(e){var t=e.xAxisId,r=Zu(),n=Ju(),i=Pm(t);return i==null?null:j.createElement(yn,Fc({},i,{className:Q("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return Ot(o,!0)}}))}var or=function(e){function t(){return k8(this,t),L8(this,t,arguments)}return F8(t,e),D8(t,[{key:"render",value:function(){return j.createElement(W8,this.props)}}])}(j.Component);Fm(or,"displayName","XAxis");Fm(or,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Qr(e){"@babel/helpers - typeof";return Qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qr(e)}function z8(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function q8(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,qm(n.key),n)}}function H8(e,t,r){return t&&q8(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function K8(e,t,r){return t=Ga(t),G8(e,Wm()?Reflect.construct(t,r||[],Ga(e).constructor):t.apply(e,r))}function G8(e,t){if(t&&(Qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return V8(e)}function V8(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Wm=function(){return!!e})()}function Ga(e){return Ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ga(e)}function X8(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Uc(e,t)}function Uc(e,t){return Uc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Uc(e,t)}function zm(e,t,r){return t=qm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qm(e){var t=Y8(e,"string");return Qr(t)=="symbol"?t:t+""}function Y8(e,t){if(Qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Wc(){return Wc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wc.apply(this,arguments)}var Z8=function(t){var r=t.yAxisId,n=Zu(),i=Ju(),a=_m(r);return a==null?null:j.createElement(yn,Wc({},a,{className:Q("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(s){return Ot(s,!0)}}))},sr=function(e){function t(){return z8(this,t),K8(this,t,arguments)}return X8(t,e),H8(t,[{key:"render",value:function(){return j.createElement(Z8,this.props)}}])}(j.Component);zm(sr,"displayName","YAxis");zm(sr,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function np(e){return tF(e)||eF(e)||Q8(e)||J8()}function J8(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Q8(e,t){if(e){if(typeof e=="string")return zc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zc(e,t)}}function eF(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function tF(e){if(Array.isArray(e))return zc(e)}function zc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var qc=function(t,r,n,i,a){var o=Xe(t,el),s=Xe(t,Bo),c=[].concat(np(o),np(s)),u=Xe(t,Uo),f="".concat(i,"Id"),l=i[0],d=r;if(c.length&&(d=c.reduce(function(v,m){if(m.props[f]===n&&ht(m.props,"extendDomain")&&B(m.props[l])){var x=m.props[l];return[Math.min(v[0],x),Math.max(v[1],x)]}return v},d)),u.length){var h="".concat(l,"1"),p="".concat(l,"2");d=u.reduce(function(v,m){if(m.props[f]===n&&ht(m.props,"extendDomain")&&B(m.props[h])&&B(m.props[p])){var x=m.props[h],O=m.props[p];return[Math.min(v[0],x,O),Math.max(v[1],x,O)]}return v},d)}return a&&a.length&&(d=a.reduce(function(v,m){return B(m)?[Math.min(v[0],m),Math.max(v[1],m)]:v},d)),d},Hm={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,u,f){this.fn=c,this.context=u,this.once=f||!1}function a(c,u,f,l,d){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,l||c,d),p=r?r+u:u;return c._events[p]?c._events[p].fn?c._events[p]=[c._events[p],h]:c._events[p].push(h):(c._events[p]=h,c._eventsCount++),c}function o(c,u){--c._eventsCount===0?c._events=new n:delete c._events[u]}function s(){this._events=new n,this._eventsCount=0}s.prototype.eventNames=function(){var u=[],f,l;if(this._eventsCount===0)return u;for(l in f=this._events)t.call(f,l)&&u.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(f)):u},s.prototype.listeners=function(u){var f=r?r+u:u,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var d=0,h=l.length,p=new Array(h);d<h;d++)p[d]=l[d].fn;return p},s.prototype.listenerCount=function(u){var f=r?r+u:u,l=this._events[f];return l?l.fn?1:l.length:0},s.prototype.emit=function(u,f,l,d,h,p){var v=r?r+u:u;if(!this._events[v])return!1;var m=this._events[v],x=arguments.length,O,w;if(m.fn){switch(m.once&&this.removeListener(u,m.fn,void 0,!0),x){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,f),!0;case 3:return m.fn.call(m.context,f,l),!0;case 4:return m.fn.call(m.context,f,l,d),!0;case 5:return m.fn.call(m.context,f,l,d,h),!0;case 6:return m.fn.call(m.context,f,l,d,h,p),!0}for(w=1,O=new Array(x-1);w<x;w++)O[w-1]=arguments[w];m.fn.apply(m.context,O)}else{var S=m.length,g;for(w=0;w<S;w++)switch(m[w].once&&this.removeListener(u,m[w].fn,void 0,!0),x){case 1:m[w].fn.call(m[w].context);break;case 2:m[w].fn.call(m[w].context,f);break;case 3:m[w].fn.call(m[w].context,f,l);break;case 4:m[w].fn.call(m[w].context,f,l,d);break;default:if(!O)for(g=1,O=new Array(x-1);g<x;g++)O[g-1]=arguments[g];m[w].fn.apply(m[w].context,O)}}return!0},s.prototype.on=function(u,f,l){return a(this,u,f,l,!1)},s.prototype.once=function(u,f,l){return a(this,u,f,l,!0)},s.prototype.removeListener=function(u,f,l,d){var h=r?r+u:u;if(!this._events[h])return this;if(!f)return o(this,h),this;var p=this._events[h];if(p.fn)p.fn===f&&(!d||p.once)&&(!l||p.context===l)&&o(this,h);else{for(var v=0,m=[],x=p.length;v<x;v++)(p[v].fn!==f||d&&!p[v].once||l&&p[v].context!==l)&&m.push(p[v]);m.length?this._events[h]=m.length===1?m[0]:m:o(this,h)}return this},s.prototype.removeAllListeners=function(u){var f;return u?(f=r?r+u:u,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s})(Hm);var rF=Hm.exports;const nF=ce(rF);var ms=new nF,gs="recharts.syncMouseEvents";function pi(e){"@babel/helpers - typeof";return pi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pi(e)}function iF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function aF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Km(n.key),n)}}function oF(e,t,r){return t&&aF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function bs(e,t,r){return t=Km(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Km(e){var t=sF(e,"string");return pi(t)=="symbol"?t:t+""}function sF(e,t){if(pi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(pi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var cF=function(){function e(){iF(this,e),bs(this,"activeIndex",0),bs(this,"coordinateList",[]),bs(this,"layout","horizontal")}return oF(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,s=o===void 0?null:o,c=r.layout,u=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,d=r.mouseHandlerCallback,h=d===void 0?null:d;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=s??this.container,this.layout=u??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,s=i.height,c=this.coordinateList[this.activeIndex].coordinate,u=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+u,d=o+this.offset.top+s/2+f;this.mouseHandlerCallback({pageX:l,pageY:d})}}}])}();function uF(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&B(n)&&B(i))return!0}return!1}function lF(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function Gm(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=fe(t,r,n,i),s=fe(t,r,n,a);return{points:[o,s],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function fF(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var s=t.cx,c=t.cy,u=t.innerRadius,f=t.outerRadius,l=t.angle,d=fe(s,c,u,l),h=fe(s,c,f,l);n=d.x,i=d.y,a=h.x,o=h.y}else return Gm(t);return[{x:n,y:i},{x:a,y:o}]}function vi(e){"@babel/helpers - typeof";return vi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vi(e)}function ip(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ip(Object(r),!0).forEach(function(n){dF(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ip(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dF(e,t,r){return t=hF(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hF(e){var t=pF(e,"string");return vi(t)=="symbol"?t:t+""}function pF(e,t){if(vi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function vF(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,s=e.activePayload,c=e.offset,u=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,d=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||d!=="ScatterChart"&&i!=="axis")return null;var p,v=Pa;if(d==="ScatterChart")p=o,v=NL;else if(d==="BarChart")p=lF(l,o,c,f),v=Gu;else if(l==="radial"){var m=Gm(o),x=m.cx,O=m.cy,w=m.radius,S=m.startAngle,g=m.endAngle;p={cx:x,cy:O,startAngle:S,endAngle:g,innerRadius:w,outerRadius:w},v=Ky}else p={points:fF(l,o,c)},v=Pa;var b=zi(zi(zi(zi({stroke:"#ccc",pointerEvents:"none"},c),p),K(h,!1)),{},{payload:s,payloadIndex:u,className:Q("recharts-tooltip-cursor",h.className)});return I.isValidElement(h)?I.cloneElement(h,b):I.createElement(v,b)}var yF=["item"],mF=["children","className","width","height","style","compact","title","desc"];function en(e){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},en(e)}function jr(){return jr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jr.apply(this,arguments)}function ap(e,t){return xF(e)||bF(e,t)||Xm(e,t)||gF()}function gF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(f){u=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}function xF(e){if(Array.isArray(e))return e}function op(e,t){if(e==null)return{};var r=wF(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function wF(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function OF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function AF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ym(n.key),n)}}function SF(e,t,r){return t&&AF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function PF(e,t,r){return t=Va(t),_F(e,Vm()?Reflect.construct(t,r||[],Va(e).constructor):t.apply(e,r))}function _F(e,t){if(t&&(en(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return jF(e)}function jF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vm=function(){return!!e})()}function Va(e){return Va=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Va(e)}function $F(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Hc(e,t)}function Hc(e,t){return Hc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Hc(e,t)}function tn(e){return NF(e)||EF(e)||Xm(e)||TF()}function TF(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xm(e,t){if(e){if(typeof e=="string")return Kc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Kc(e,t)}}function EF(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function NF(e){if(Array.isArray(e))return Kc(e)}function Kc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function sp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sp(Object(r),!0).forEach(function(n){G(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function G(e,t,r){return t=Ym(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ym(e){var t=CF(e,"string");return en(t)=="symbol"?t:t+""}function CF(e,t){if(en(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(en(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var MF={xAxis:["bottom","top"],yAxis:["left","right"]},kF={width:"100%",height:"100%"},Zm={x:0,y:0};function qi(e){return e}var IF=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},DF=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,s=i.radius;return N(N(N({},i),fe(i.cx,i.cy,s,o)),{},{angle:o,radius:s})}var c=a.coordinate,u=i.angle;return N(N(N({},i),fe(i.cx,i.cy,c,u)),{},{angle:u,radius:c})}return Zm},Wo=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(s,c){var u=c.props.data;return u&&u.length?[].concat(tn(s),tn(u)):s},[]);return o.length>0?o:t&&t.length&&B(i)&&B(a)?t.slice(i,a+1):[]};function Jm(e){return e==="number"?[0,"auto"]:void 0}var Gc=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,s=Wo(r,t);return n<0||!a||!a.length||n>=s.length?null:a.reduce(function(c,u){var f,l=(f=u.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var d;if(o.dataKey&&!o.allowDuplicatedCategory){var h=l===void 0?s:l;d=Vi(h,o.dataKey,i)}else d=l&&l[n]||s[n];return d?[].concat(tn(c),[Fy(u,d)]):c},[])},cp=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=IF(a,n),s=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,f=ok(o,s,u,c);if(f>=0&&u){var l=u[f]&&u[f].value,d=Gc(t,r,f,l),h=DF(n,s,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:d,activeCoordinate:h}}return null},LF=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,s=r.stackGroups,c=r.dataStartIndex,u=r.dataEndIndex,f=t.layout,l=t.children,d=t.stackOffset,h=ky(f,a);return n.reduce(function(p,v){var m,x=v.type.defaultProps!==void 0?N(N({},v.type.defaultProps),v.props):v.props,O=x.type,w=x.dataKey,S=x.allowDataOverflow,g=x.allowDuplicatedCategory,b=x.scale,A=x.ticks,P=x.includeHidden,_=x[o];if(p[_])return p;var E=Wo(t.data,{graphicalItems:i.filter(function(z){var X,de=o in z.props?z.props[o]:(X=z.type.defaultProps)===null||X===void 0?void 0:X[o];return de===_}),dataStartIndex:c,dataEndIndex:u}),$=E.length,T,M,k;uF(x.domain,S,O)&&(T=sc(x.domain,null,S),h&&(O==="number"||b!=="auto")&&(k=En(E,w,"category")));var C=Jm(O);if(!T||T.length===0){var D,L=(D=x.domain)!==null&&D!==void 0?D:C;if(w){if(T=En(E,w,O),O==="category"&&h){var F=Yx(T);g&&F?(M=T,T=Ia(0,$)):g||(T=Cd(L,T,v).reduce(function(z,X){return z.indexOf(X)>=0?z:[].concat(tn(z),[X])},[]))}else if(O==="category")g?T=T.filter(function(z){return z!==""&&!Z(z)}):T=Cd(L,T,v).reduce(function(z,X){return z.indexOf(X)>=0||X===""||Z(X)?z:[].concat(tn(z),[X])},[]);else if(O==="number"){var U=fk(E,i.filter(function(z){var X,de,ge=o in z.props?z.props[o]:(X=z.type.defaultProps)===null||X===void 0?void 0:X[o],Ue="hide"in z.props?z.props.hide:(de=z.type.defaultProps)===null||de===void 0?void 0:de.hide;return ge===_&&(P||!Ue)}),w,a,f);U&&(T=U)}h&&(O==="number"||b!=="auto")&&(k=En(E,w,"category"))}else h?T=Ia(0,$):s&&s[_]&&s[_].hasStack&&O==="number"?T=d==="expand"?[0,1]:By(s[_].stackGroups,c,u):T=My(E,i.filter(function(z){var X=o in z.props?z.props[o]:z.type.defaultProps[o],de="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return X===_&&(P||!de)}),O,f,!0);if(O==="number")T=qc(l,T,_,a,A),L&&(T=sc(L,T,S));else if(O==="category"&&L){var H=L,V=T.every(function(z){return H.indexOf(z)>=0});V&&(T=H)}}return N(N({},p),{},G({},_,N(N({},x),{},{axisType:a,domain:T,categoricalDomain:k,duplicateDomain:M,originalDomain:(m=x.domain)!==null&&m!==void 0?m:C,isCategorical:h,layout:f})))},{})},RF=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,s=r.stackGroups,c=r.dataStartIndex,u=r.dataEndIndex,f=t.layout,l=t.children,d=Wo(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:u}),h=d.length,p=ky(f,a),v=-1;return n.reduce(function(m,x){var O=x.type.defaultProps!==void 0?N(N({},x.type.defaultProps),x.props):x.props,w=O[o],S=Jm("number");if(!m[w]){v++;var g;return p?g=Ia(0,h):s&&s[w]&&s[w].hasStack?(g=By(s[w].stackGroups,c,u),g=qc(l,g,w,a)):(g=sc(S,My(d,n.filter(function(b){var A,P,_=o in b.props?b.props[o]:(A=b.type.defaultProps)===null||A===void 0?void 0:A[o],E="hide"in b.props?b.props.hide:(P=b.type.defaultProps)===null||P===void 0?void 0:P.hide;return _===w&&!E}),"number",f),i.defaultProps.allowDataOverflow),g=qc(l,g,w,a)),N(N({},m),{},G({},w,N(N({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ve(MF,"".concat(a,".").concat(v%2),null),domain:g,originalDomain:S,isCategorical:p,layout:f})))}return m},{})},BF=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,s=r.stackGroups,c=r.dataStartIndex,u=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),d=Xe(f,a),h={};return d&&d.length?h=LF(t,{axes:d,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:s,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(h=RF(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:s,dataStartIndex:c,dataEndIndex:u})),h},FF=function(t){var r=Lt(t),n=Ot(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:xu(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Oa(r,n)}},up=function(t){var r=t.children,n=t.defaultShowTooltip,i=qe(r,Hr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},UF=function(t){return!t||!t.length?!1:t.some(function(r){var n=At(r&&r.type);return n&&n.indexOf("Bar")>=0})},lp=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},WF=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,s=t.yAxisMap,c=s===void 0?{}:s,u=n.width,f=n.height,l=n.children,d=n.margin||{},h=qe(l,Hr),p=qe(l,lt),v=Object.keys(c).reduce(function(g,b){var A=c[b],P=A.orientation;return!A.mirror&&!A.hide?N(N({},g),{},G({},P,g[P]+A.width)):g},{left:d.left||0,right:d.right||0}),m=Object.keys(o).reduce(function(g,b){var A=o[b],P=A.orientation;return!A.mirror&&!A.hide?N(N({},g),{},G({},P,Ve(g,"".concat(P))+A.height)):g},{top:d.top||0,bottom:d.bottom||0}),x=N(N({},m),v),O=x.bottom;h&&(x.bottom+=h.props.height||Hr.defaultProps.height),p&&r&&(x=uk(x,i,n,r));var w=u-x.left-x.right,S=f-x.top-x.bottom;return N(N({brushBottom:O},x),{},{width:Math.max(w,0),height:Math.max(S,0)})},zF=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},nl=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,s=o===void 0?["axis"]:o,c=t.axisComponents,u=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,d=function(x,O){var w=O.graphicalItems,S=O.stackGroups,g=O.offset,b=O.updateId,A=O.dataStartIndex,P=O.dataEndIndex,_=x.barSize,E=x.layout,$=x.barGap,T=x.barCategoryGap,M=x.maxBarSize,k=lp(E),C=k.numericAxisName,D=k.cateAxisName,L=UF(w),F=[];return w.forEach(function(U,H){var V=Wo(x.data,{graphicalItems:[U],dataStartIndex:A,dataEndIndex:P}),z=U.type.defaultProps!==void 0?N(N({},U.type.defaultProps),U.props):U.props,X=z.dataKey,de=z.maxBarSize,ge=z["".concat(C,"Id")],Ue=z["".concat(D,"Id")],qt={},Le=c.reduce(function(Ht,Kt){var zo=O["".concat(Kt.axisType,"Map")],il=z["".concat(Kt.axisType,"Id")];zo&&zo[il]||Kt.axisType==="zAxis"||fr();var al=zo[il];return N(N({},Ht),{},G(G({},Kt.axisType,al),"".concat(Kt.axisType,"Ticks"),Ot(al)))},qt),W=Le[D],J=Le["".concat(D,"Ticks")],ee=S&&S[ge]&&S[ge].hasStack&&xk(U,S[ge].stackGroups),R=At(U.type).indexOf("Bar")>=0,ye=Oa(W,J),te=[],we=L&&sk({barSize:_,stackGroups:S,totalSize:zF(Le,D)});if(R){var Oe,Re,kt=Z(de)?M:de,gr=(Oe=(Re=Oa(W,J,!0))!==null&&Re!==void 0?Re:kt)!==null&&Oe!==void 0?Oe:0;te=ck({barGap:$,barCategoryGap:T,bandSize:gr!==ye?gr:ye,sizeList:we[Ue],maxBarSize:kt}),gr!==ye&&(te=te.map(function(Ht){return N(N({},Ht),{},{position:N(N({},Ht.position),{},{offset:Ht.position.offset-gr/2})})}))}var $i=U&&U.type&&U.type.getComposedData;$i&&F.push({props:N(N({},$i(N(N({},Le),{},{displayedData:V,props:x,dataKey:X,item:U,bandSize:ye,barPosition:te,offset:g,stackedData:ee,layout:E,dataStartIndex:A,dataEndIndex:P}))),{},G(G(G({key:U.key||"item-".concat(H)},C,Le[C]),D,Le[D]),"animationId",b)),childIndex:cw(U,x.children),item:U})}),F},h=function(x,O){var w=x.props,S=x.dataStartIndex,g=x.dataEndIndex,b=x.updateId;if(!Sl({props:w}))return null;var A=w.children,P=w.layout,_=w.stackOffset,E=w.data,$=w.reverseStackOrder,T=lp(P),M=T.numericAxisName,k=T.cateAxisName,C=Xe(A,n),D=gk(E,C,"".concat(M,"Id"),"".concat(k,"Id"),_,$),L=c.reduce(function(z,X){var de="".concat(X.axisType,"Map");return N(N({},z),{},G({},de,BF(w,N(N({},X),{},{graphicalItems:C,stackGroups:X.axisType===M&&D,dataStartIndex:S,dataEndIndex:g}))))},{}),F=WF(N(N({},L),{},{props:w,graphicalItems:C}),O==null?void 0:O.legendBBox);Object.keys(L).forEach(function(z){L[z]=f(w,L[z],F,z.replace("Map",""),r)});var U=L["".concat(k,"Map")],H=FF(U),V=d(w,N(N({},L),{},{dataStartIndex:S,dataEndIndex:g,updateId:b,graphicalItems:C,stackGroups:D,offset:F}));return N(N({formattedGraphicalItems:V,graphicalItems:C,offset:F,stackGroups:D},H),L)},p=function(m){function x(O){var w,S,g;return OF(this,x),g=PF(this,x,[O]),G(g,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),G(g,"accessibilityManager",new cF),G(g,"handleLegendBBoxUpdate",function(b){if(b){var A=g.state,P=A.dataStartIndex,_=A.dataEndIndex,E=A.updateId;g.setState(N({legendBBox:b},h({props:g.props,dataStartIndex:P,dataEndIndex:_,updateId:E},N(N({},g.state),{},{legendBBox:b}))))}}),G(g,"handleReceiveSyncEvent",function(b,A,P){if(g.props.syncId===b){if(P===g.eventEmitterSymbol&&typeof g.props.syncMethod!="function")return;g.applySyncEvent(A)}}),G(g,"handleBrushChange",function(b){var A=b.startIndex,P=b.endIndex;if(A!==g.state.dataStartIndex||P!==g.state.dataEndIndex){var _=g.state.updateId;g.setState(function(){return N({dataStartIndex:A,dataEndIndex:P},h({props:g.props,dataStartIndex:A,dataEndIndex:P,updateId:_},g.state))}),g.triggerSyncEvent({dataStartIndex:A,dataEndIndex:P})}}),G(g,"handleMouseEnter",function(b){var A=g.getMouseInfo(b);if(A){var P=N(N({},A),{},{isTooltipActive:!0});g.setState(P),g.triggerSyncEvent(P);var _=g.props.onMouseEnter;Y(_)&&_(P,b)}}),G(g,"triggeredAfterMouseMove",function(b){var A=g.getMouseInfo(b),P=A?N(N({},A),{},{isTooltipActive:!0}):{isTooltipActive:!1};g.setState(P),g.triggerSyncEvent(P);var _=g.props.onMouseMove;Y(_)&&_(P,b)}),G(g,"handleItemMouseEnter",function(b){g.setState(function(){return{isTooltipActive:!0,activeItem:b,activePayload:b.tooltipPayload,activeCoordinate:b.tooltipPosition||{x:b.cx,y:b.cy}}})}),G(g,"handleItemMouseLeave",function(){g.setState(function(){return{isTooltipActive:!1}})}),G(g,"handleMouseMove",function(b){b.persist(),g.throttleTriggeredAfterMouseMove(b)}),G(g,"handleMouseLeave",function(b){g.throttleTriggeredAfterMouseMove.cancel();var A={isTooltipActive:!1};g.setState(A),g.triggerSyncEvent(A);var P=g.props.onMouseLeave;Y(P)&&P(A,b)}),G(g,"handleOuterEvent",function(b){var A=sw(b),P=Ve(g.props,"".concat(A));if(A&&Y(P)){var _,E;/.*touch.*/i.test(A)?E=g.getMouseInfo(b.changedTouches[0]):E=g.getMouseInfo(b),P((_=E)!==null&&_!==void 0?_:{},b)}}),G(g,"handleClick",function(b){var A=g.getMouseInfo(b);if(A){var P=N(N({},A),{},{isTooltipActive:!0});g.setState(P),g.triggerSyncEvent(P);var _=g.props.onClick;Y(_)&&_(P,b)}}),G(g,"handleMouseDown",function(b){var A=g.props.onMouseDown;if(Y(A)){var P=g.getMouseInfo(b);A(P,b)}}),G(g,"handleMouseUp",function(b){var A=g.props.onMouseUp;if(Y(A)){var P=g.getMouseInfo(b);A(P,b)}}),G(g,"handleTouchMove",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&g.throttleTriggeredAfterMouseMove(b.changedTouches[0])}),G(g,"handleTouchStart",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&g.handleMouseDown(b.changedTouches[0])}),G(g,"handleTouchEnd",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&g.handleMouseUp(b.changedTouches[0])}),G(g,"handleDoubleClick",function(b){var A=g.props.onDoubleClick;if(Y(A)){var P=g.getMouseInfo(b);A(P,b)}}),G(g,"handleContextMenu",function(b){var A=g.props.onContextMenu;if(Y(A)){var P=g.getMouseInfo(b);A(P,b)}}),G(g,"triggerSyncEvent",function(b){g.props.syncId!==void 0&&ms.emit(gs,g.props.syncId,b,g.eventEmitterSymbol)}),G(g,"applySyncEvent",function(b){var A=g.props,P=A.layout,_=A.syncMethod,E=g.state.updateId,$=b.dataStartIndex,T=b.dataEndIndex;if(b.dataStartIndex!==void 0||b.dataEndIndex!==void 0)g.setState(N({dataStartIndex:$,dataEndIndex:T},h({props:g.props,dataStartIndex:$,dataEndIndex:T,updateId:E},g.state)));else if(b.activeTooltipIndex!==void 0){var M=b.chartX,k=b.chartY,C=b.activeTooltipIndex,D=g.state,L=D.offset,F=D.tooltipTicks;if(!L)return;if(typeof _=="function")C=_(F,b);else if(_==="value"){C=-1;for(var U=0;U<F.length;U++)if(F[U].value===b.activeLabel){C=U;break}}var H=N(N({},L),{},{x:L.left,y:L.top}),V=Math.min(M,H.x+H.width),z=Math.min(k,H.y+H.height),X=F[C]&&F[C].value,de=Gc(g.state,g.props.data,C),ge=F[C]?{x:P==="horizontal"?F[C].coordinate:V,y:P==="horizontal"?z:F[C].coordinate}:Zm;g.setState(N(N({},b),{},{activeLabel:X,activeCoordinate:ge,activePayload:de,activeTooltipIndex:C}))}else g.setState(b)}),G(g,"renderCursor",function(b){var A,P=g.state,_=P.isTooltipActive,E=P.activeCoordinate,$=P.activePayload,T=P.offset,M=P.activeTooltipIndex,k=P.tooltipAxisBandSize,C=g.getTooltipEventType(),D=(A=b.props.active)!==null&&A!==void 0?A:_,L=g.props.layout,F=b.key||"_recharts-cursor";return j.createElement(vF,{key:F,activeCoordinate:E,activePayload:$,activeTooltipIndex:M,chartName:r,element:b,isActive:D,layout:L,offset:T,tooltipAxisBandSize:k,tooltipEventType:C})}),G(g,"renderPolarAxis",function(b,A,P){var _=Ve(b,"type.axisType"),E=Ve(g.state,"".concat(_,"Map")),$=b.type.defaultProps,T=$!==void 0?N(N({},$),b.props):b.props,M=E&&E[T["".concat(_,"Id")]];return I.cloneElement(b,N(N({},M),{},{className:Q(_,M.className),key:b.key||"".concat(A,"-").concat(P),ticks:Ot(M,!0)}))}),G(g,"renderPolarGrid",function(b){var A=b.props,P=A.radialLines,_=A.polarAngles,E=A.polarRadius,$=g.state,T=$.radiusAxisMap,M=$.angleAxisMap,k=Lt(T),C=Lt(M),D=C.cx,L=C.cy,F=C.innerRadius,U=C.outerRadius;return I.cloneElement(b,{polarAngles:Array.isArray(_)?_:Ot(C,!0).map(function(H){return H.coordinate}),polarRadius:Array.isArray(E)?E:Ot(k,!0).map(function(H){return H.coordinate}),cx:D,cy:L,innerRadius:F,outerRadius:U,key:b.key||"polar-grid",radialLines:P})}),G(g,"renderLegend",function(){var b=g.state.formattedGraphicalItems,A=g.props,P=A.children,_=A.width,E=A.height,$=g.props.margin||{},T=_-($.left||0)-($.right||0),M=Ny({children:P,formattedGraphicalItems:b,legendWidth:T,legendContent:u});if(!M)return null;var k=M.item,C=op(M,yF);return I.cloneElement(k,N(N({},C),{},{chartWidth:_,chartHeight:E,margin:$,onBBoxUpdate:g.handleLegendBBoxUpdate}))}),G(g,"renderTooltip",function(){var b,A=g.props,P=A.children,_=A.accessibilityLayer,E=qe(P,He);if(!E)return null;var $=g.state,T=$.isTooltipActive,M=$.activeCoordinate,k=$.activePayload,C=$.activeLabel,D=$.offset,L=(b=E.props.active)!==null&&b!==void 0?b:T;return I.cloneElement(E,{viewBox:N(N({},D),{},{x:D.left,y:D.top}),active:L,label:C,payload:L?k:[],coordinate:M,accessibilityLayer:_})}),G(g,"renderBrush",function(b){var A=g.props,P=A.margin,_=A.data,E=g.state,$=E.offset,T=E.dataStartIndex,M=E.dataEndIndex,k=E.updateId;return I.cloneElement(b,{key:b.key||"_recharts-brush",onChange:Bi(g.handleBrushChange,b.props.onChange),data:_,x:B(b.props.x)?b.props.x:$.left,y:B(b.props.y)?b.props.y:$.top+$.height+$.brushBottom-(P.bottom||0),width:B(b.props.width)?b.props.width:$.width,startIndex:T,endIndex:M,updateId:"brush-".concat(k)})}),G(g,"renderReferenceElement",function(b,A,P){if(!b)return null;var _=g,E=_.clipPathId,$=g.state,T=$.xAxisMap,M=$.yAxisMap,k=$.offset,C=b.type.defaultProps||{},D=b.props,L=D.xAxisId,F=L===void 0?C.xAxisId:L,U=D.yAxisId,H=U===void 0?C.yAxisId:U;return I.cloneElement(b,{key:b.key||"".concat(A,"-").concat(P),xAxis:T[F],yAxis:M[H],viewBox:{x:k.left,y:k.top,width:k.width,height:k.height},clipPathId:E})}),G(g,"renderActivePoints",function(b){var A=b.item,P=b.activePoint,_=b.basePoint,E=b.childIndex,$=b.isRange,T=[],M=A.props.key,k=A.item.type.defaultProps!==void 0?N(N({},A.item.type.defaultProps),A.item.props):A.item.props,C=k.activeDot,D=k.dataKey,L=N(N({index:E,dataKey:D,cx:P.x,cy:P.y,r:4,fill:Ku(A.item),strokeWidth:2,stroke:"#fff",payload:P.payload,value:P.value},K(C,!1)),Xi(C));return T.push(x.renderActiveDot(C,L,"".concat(M,"-activePoint-").concat(E))),_?T.push(x.renderActiveDot(C,N(N({},L),{},{cx:_.x,cy:_.y}),"".concat(M,"-basePoint-").concat(E))):$&&T.push(null),T}),G(g,"renderGraphicChild",function(b,A,P){var _=g.filterFormatItem(b,A,P);if(!_)return null;var E=g.getTooltipEventType(),$=g.state,T=$.isTooltipActive,M=$.tooltipAxis,k=$.activeTooltipIndex,C=$.activeLabel,D=g.props.children,L=qe(D,He),F=_.props,U=F.points,H=F.isRange,V=F.baseLine,z=_.item.type.defaultProps!==void 0?N(N({},_.item.type.defaultProps),_.item.props):_.item.props,X=z.activeDot,de=z.hide,ge=z.activeBar,Ue=z.activeShape,qt=!!(!de&&T&&L&&(X||ge||Ue)),Le={};E!=="axis"&&L&&L.props.trigger==="click"?Le={onClick:Bi(g.handleItemMouseEnter,b.props.onClick)}:E!=="axis"&&(Le={onMouseLeave:Bi(g.handleItemMouseLeave,b.props.onMouseLeave),onMouseEnter:Bi(g.handleItemMouseEnter,b.props.onMouseEnter)});var W=I.cloneElement(b,N(N({},_.props),Le));function J(Kt){return typeof M.dataKey=="function"?M.dataKey(Kt.payload):null}if(qt)if(k>=0){var ee,R;if(M.dataKey&&!M.allowDuplicatedCategory){var ye=typeof M.dataKey=="function"?J:"payload.".concat(M.dataKey.toString());ee=Vi(U,ye,C),R=H&&V&&Vi(V,ye,C)}else ee=U==null?void 0:U[k],R=H&&V&&V[k];if(Ue||ge){var te=b.props.activeIndex!==void 0?b.props.activeIndex:k;return[I.cloneElement(b,N(N(N({},_.props),Le),{},{activeIndex:te})),null,null]}if(!Z(ee))return[W].concat(tn(g.renderActivePoints({item:_,activePoint:ee,basePoint:R,childIndex:k,isRange:H})))}else{var we,Oe=(we=g.getItemByXY(g.state.activeCoordinate))!==null&&we!==void 0?we:{graphicalItem:W},Re=Oe.graphicalItem,kt=Re.item,gr=kt===void 0?b:kt,$i=Re.childIndex,Ht=N(N(N({},_.props),Le),{},{activeIndex:$i});return[I.cloneElement(gr,Ht),null,null]}return H?[W,null,null]:[W,null]}),G(g,"renderCustomized",function(b,A,P){return I.cloneElement(b,N(N({key:"recharts-customized-".concat(P)},g.props),g.state))}),G(g,"renderMap",{CartesianGrid:{handler:qi,once:!0},ReferenceArea:{handler:g.renderReferenceElement},ReferenceLine:{handler:qi},ReferenceDot:{handler:g.renderReferenceElement},XAxis:{handler:qi},YAxis:{handler:qi},Brush:{handler:g.renderBrush,once:!0},Bar:{handler:g.renderGraphicChild},Line:{handler:g.renderGraphicChild},Area:{handler:g.renderGraphicChild},Radar:{handler:g.renderGraphicChild},RadialBar:{handler:g.renderGraphicChild},Scatter:{handler:g.renderGraphicChild},Pie:{handler:g.renderGraphicChild},Funnel:{handler:g.renderGraphicChild},Tooltip:{handler:g.renderCursor,once:!0},PolarGrid:{handler:g.renderPolarGrid,once:!0},PolarAngleAxis:{handler:g.renderPolarAxis},PolarRadiusAxis:{handler:g.renderPolarAxis},Customized:{handler:g.renderCustomized}}),g.clipPathId="".concat((w=O.id)!==null&&w!==void 0?w:un("recharts"),"-clip"),g.throttleTriggeredAfterMouseMove=Ev(g.triggeredAfterMouseMove,(S=O.throttleDelay)!==null&&S!==void 0?S:1e3/60),g.state={},g}return $F(x,m),SF(x,[{key:"componentDidMount",value:function(){var w,S;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(w=this.props.margin.left)!==null&&w!==void 0?w:0,top:(S=this.props.margin.top)!==null&&S!==void 0?S:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var w=this.props,S=w.children,g=w.data,b=w.height,A=w.layout,P=qe(S,He);if(P){var _=P.props.defaultIndex;if(!(typeof _!="number"||_<0||_>this.state.tooltipTicks.length-1)){var E=this.state.tooltipTicks[_]&&this.state.tooltipTicks[_].value,$=Gc(this.state,g,_,E),T=this.state.tooltipTicks[_].coordinate,M=(this.state.offset.top+b)/2,k=A==="horizontal",C=k?{x:T,y:M}:{y:T,x:M},D=this.state.formattedGraphicalItems.find(function(F){var U=F.item;return U.type.name==="Scatter"});D&&(C=N(N({},C),D.props.points[_].tooltipPosition),$=D.props.points[_].tooltipPayload);var L={activeTooltipIndex:_,isTooltipActive:!0,activeLabel:E,activePayload:$,activeCoordinate:C};this.setState(L),this.renderCursor(P),this.accessibilityManager.setIndex(_)}}}},{key:"getSnapshotBeforeUpdate",value:function(w,S){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==S.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==w.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==w.margin){var g,b;this.accessibilityManager.setDetails({offset:{left:(g=this.props.margin.left)!==null&&g!==void 0?g:0,top:(b=this.props.margin.top)!==null&&b!==void 0?b:0}})}return null}},{key:"componentDidUpdate",value:function(w){Os([qe(w.children,He)],[qe(this.props.children,He)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var w=qe(this.props.children,He);if(w&&typeof w.props.shared=="boolean"){var S=w.props.shared?"axis":"item";return s.indexOf(S)>=0?S:a}return a}},{key:"getMouseInfo",value:function(w){if(!this.container)return null;var S=this.container,g=S.getBoundingClientRect(),b=kE(g),A={chartX:Math.round(w.pageX-b.left),chartY:Math.round(w.pageY-b.top)},P=g.width/S.offsetWidth||1,_=this.inRange(A.chartX,A.chartY,P);if(!_)return null;var E=this.state,$=E.xAxisMap,T=E.yAxisMap,M=this.getTooltipEventType();if(M!=="axis"&&$&&T){var k=Lt($).scale,C=Lt(T).scale,D=k&&k.invert?k.invert(A.chartX):null,L=C&&C.invert?C.invert(A.chartY):null;return N(N({},A),{},{xValue:D,yValue:L})}var F=cp(this.state,this.props.data,this.props.layout,_);return F?N(N({},A),F):null}},{key:"inRange",value:function(w,S){var g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,b=this.props.layout,A=w/g,P=S/g;if(b==="horizontal"||b==="vertical"){var _=this.state.offset,E=A>=_.left&&A<=_.left+_.width&&P>=_.top&&P<=_.top+_.height;return E?{x:A,y:P}:null}var $=this.state,T=$.angleAxisMap,M=$.radiusAxisMap;if(T&&M){var k=Lt(T);return Id({x:A,y:P},k)}return null}},{key:"parseEventsOfWrapper",value:function(){var w=this.props.children,S=this.getTooltipEventType(),g=qe(w,He),b={};g&&S==="axis"&&(g.props.trigger==="click"?b={onClick:this.handleClick}:b={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var A=Xi(this.props,this.handleOuterEvent);return N(N({},A),b)}},{key:"addListener",value:function(){ms.on(gs,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){ms.removeListener(gs,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(w,S,g){for(var b=this.state.formattedGraphicalItems,A=0,P=b.length;A<P;A++){var _=b[A];if(_.item===w||_.props.key===w.key||S===At(_.item.type)&&g===_.childIndex)return _}return null}},{key:"renderClipPath",value:function(){var w=this.clipPathId,S=this.state.offset,g=S.left,b=S.top,A=S.height,P=S.width;return j.createElement("defs",null,j.createElement("clipPath",{id:w},j.createElement("rect",{x:g,y:b,height:A,width:P})))}},{key:"getXScales",value:function(){var w=this.state.xAxisMap;return w?Object.entries(w).reduce(function(S,g){var b=ap(g,2),A=b[0],P=b[1];return N(N({},S),{},G({},A,P.scale))},{}):null}},{key:"getYScales",value:function(){var w=this.state.yAxisMap;return w?Object.entries(w).reduce(function(S,g){var b=ap(g,2),A=b[0],P=b[1];return N(N({},S),{},G({},A,P.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(w){var S;return(S=this.state.xAxisMap)===null||S===void 0||(S=S[w])===null||S===void 0?void 0:S.scale}},{key:"getYScaleByAxisId",value:function(w){var S;return(S=this.state.yAxisMap)===null||S===void 0||(S=S[w])===null||S===void 0?void 0:S.scale}},{key:"getItemByXY",value:function(w){var S=this.state,g=S.formattedGraphicalItems,b=S.activeItem;if(g&&g.length)for(var A=0,P=g.length;A<P;A++){var _=g[A],E=_.props,$=_.item,T=$.type.defaultProps!==void 0?N(N({},$.type.defaultProps),$.props):$.props,M=At($.type);if(M==="Bar"){var k=(E.data||[]).find(function(F){return fL(w,F)});if(k)return{graphicalItem:_,payload:k}}else if(M==="RadialBar"){var C=(E.data||[]).find(function(F){return Id(w,F)});if(C)return{graphicalItem:_,payload:C}}else if(Io(_,b)||Do(_,b)||ui(_,b)){var D=eR({graphicalItem:_,activeTooltipItem:b,itemData:T.data}),L=T.activeIndex===void 0?D:T.activeIndex;return{graphicalItem:N(N({},_),{},{childIndex:L}),payload:ui(_,b)?T.data[D]:_.props.data[D]}}}return null}},{key:"render",value:function(){var w=this;if(!Sl(this))return null;var S=this.props,g=S.children,b=S.className,A=S.width,P=S.height,_=S.style,E=S.compact,$=S.title,T=S.desc,M=op(S,mF),k=K(M,!1);if(E)return j.createElement(Uh,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},j.createElement(Ss,jr({},k,{width:A,height:P,title:$,desc:T}),this.renderClipPath(),_l(g,this.renderMap)));if(this.props.accessibilityLayer){var C,D;k.tabIndex=(C=this.props.tabIndex)!==null&&C!==void 0?C:0,k.role=(D=this.props.role)!==null&&D!==void 0?D:"application",k.onKeyDown=function(F){w.accessibilityManager.keyboardEvent(F)},k.onFocus=function(){w.accessibilityManager.focus()}}var L=this.parseEventsOfWrapper();return j.createElement(Uh,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},j.createElement("div",jr({className:Q("recharts-wrapper",b),style:N({position:"relative",cursor:"default",width:A,height:P},_)},L,{ref:function(U){w.container=U}}),j.createElement(Ss,jr({},k,{width:A,height:P,title:$,desc:T,style:kF}),this.renderClipPath(),_l(g,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(I.Component);G(p,"displayName",r),G(p,"defaultProps",N({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),G(p,"getDerivedStateFromProps",function(m,x){var O=m.dataKey,w=m.data,S=m.children,g=m.width,b=m.height,A=m.layout,P=m.stackOffset,_=m.margin,E=x.dataStartIndex,$=x.dataEndIndex;if(x.updateId===void 0){var T=up(m);return N(N(N({},T),{},{updateId:0},h(N(N({props:m},T),{},{updateId:0}),x)),{},{prevDataKey:O,prevData:w,prevWidth:g,prevHeight:b,prevLayout:A,prevStackOffset:P,prevMargin:_,prevChildren:S})}if(O!==x.prevDataKey||w!==x.prevData||g!==x.prevWidth||b!==x.prevHeight||A!==x.prevLayout||P!==x.prevStackOffset||!$r(_,x.prevMargin)){var M=up(m),k={chartX:x.chartX,chartY:x.chartY,isTooltipActive:x.isTooltipActive},C=N(N({},cp(x,w,A)),{},{updateId:x.updateId+1}),D=N(N(N({},M),k),C);return N(N(N({},D),h(N({props:m},D),x)),{},{prevDataKey:O,prevData:w,prevWidth:g,prevHeight:b,prevLayout:A,prevStackOffset:P,prevMargin:_,prevChildren:S})}if(!Os(S,x.prevChildren)){var L,F,U,H,V=qe(S,Hr),z=V&&(L=(F=V.props)===null||F===void 0?void 0:F.startIndex)!==null&&L!==void 0?L:E,X=V&&(U=(H=V.props)===null||H===void 0?void 0:H.endIndex)!==null&&U!==void 0?U:$,de=z!==E||X!==$,ge=!Z(w),Ue=ge&&!de?x.updateId:x.updateId+1;return N(N({updateId:Ue},h(N(N({props:m},x),{},{updateId:Ue,dataStartIndex:z,dataEndIndex:X}),x)),{},{prevChildren:S,dataStartIndex:z,dataEndIndex:X})}return null}),G(p,"renderActiveDot",function(m,x,O){var w;return I.isValidElement(m)?w=I.cloneElement(m,x):Y(m)?w=m(x):w=j.createElement(Eo,x),j.createElement(re,{className:"recharts-active-dot",key:O},w)});var v=I.forwardRef(function(x,O){return j.createElement(p,jr({},x,{ref:O}))});return v.displayName=p.displayName,v},qF=nl({chartName:"LineChart",GraphicalChild:ji,axisComponents:[{axisType:"xAxis",AxisComp:or},{axisType:"yAxis",AxisComp:sr}],formatAxisMap:mm}),fp=nl({chartName:"BarChart",GraphicalChild:_t,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:or},{axisType:"yAxis",AxisComp:sr}],formatAxisMap:mm}),HF=nl({chartName:"PieChart",GraphicalChild:Mt,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:ko},{axisType:"radiusAxis",AxisComp:Co}],formatAxisMap:Ek,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});const XF=()=>{const{darkMode:e}=rn(),[t,r]=I.useState({userStats:[],subscriptionStats:[],revenueStats:[],contentStats:[]}),[n,i]=I.useState(!0),[a,o]=I.useState("month"),s=["#0088FE","#00C49F","#FFBB28","#FF8042"],c=[{name:"Free",value:65},{name:"Premium",value:25},{name:"Business",value:10}],u=[{month:"Jan",revenue:25e3},{month:"Feb",revenue:35e3},{month:"Mar",revenue:45e3},{month:"Apr",revenue:4e4},{month:"May",revenue:5e4},{month:"Jun",revenue:65e3}],f=[{month:"Jan",users:120},{month:"Feb",users:180},{month:"Mar",users:250},{month:"Apr",users:310},{month:"May",users:390},{month:"Jun",users:450}],l=[{name:"Web Security",completed:75,inProgress:25},{name:"Network Security",completed:60,inProgress:40},{name:"Cryptography",completed:45,inProgress:55},{name:"OSINT",completed:80,inProgress:20},{name:"Reverse Engineering",completed:30,inProgress:70}];return I.useEffect(()=>{(async()=>{try{i(!0),r({userStats:f,subscriptionStats:c,revenueStats:u,contentStats:l})}catch(h){console.error("Error fetching stats:",h)}finally{i(!1)}})()},[a]),n?y.jsx("div",{className:"flex justify-center items-center h-64",children:y.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14]"})}):y.jsxs("div",{className:"space-y-8",children:[y.jsx("div",{className:"flex justify-end mb-4",children:y.jsxs("div",{className:`inline-flex rounded-md shadow-sm ${e?"bg-[#1A1F35]":"bg-white"}`,children:[y.jsx("button",{onClick:()=>o("week"),className:`px-4 py-2 text-sm font-medium rounded-l-md ${a==="week"?"bg-[#88cc14] text-black":e?"text-gray-400 hover:text-white hover:bg-gray-800":"text-gray-700 hover:bg-gray-50"}`,children:"Week"}),y.jsx("button",{onClick:()=>o("month"),className:`px-4 py-2 text-sm font-medium ${a==="month"?"bg-[#88cc14] text-black":e?"text-gray-400 hover:text-white hover:bg-gray-800":"text-gray-700 hover:bg-gray-50"}`,children:"Month"}),y.jsx("button",{onClick:()=>o("year"),className:`px-4 py-2 text-sm font-medium rounded-r-md ${a==="year"?"bg-[#88cc14] text-black":e?"text-gray-400 hover:text-white hover:bg-gray-800":"text-gray-700 hover:bg-gray-50"}`,children:"Year"})]})}),y.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} p-4 rounded-lg border`,children:[y.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Subscription Distribution"}),y.jsx(ki,{width:"100%",height:300,children:y.jsxs(HF,{children:[y.jsx(Mt,{data:t.subscriptionStats,cx:"50%",cy:"50%",labelLine:!1,outerRadius:80,fill:"#8884d8",dataKey:"value",label:({name:d,percent:h})=>`${d}: ${(h*100).toFixed(0)}%`,children:t.subscriptionStats.map((d,h)=>y.jsx(go,{fill:s[h%s.length]},`cell-${h}`))}),y.jsx(He,{contentStyle:{backgroundColor:e?"#1A1F35":"white",borderColor:e?"#374151":"#E5E7EB",color:e?"white":"black"}}),y.jsx(lt,{})]})})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} p-4 rounded-lg border`,children:[y.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Revenue Trends"}),y.jsx(ki,{width:"100%",height:300,children:y.jsxs(fp,{data:t.revenueStats,children:[y.jsx(Gi,{strokeDasharray:"3 3",stroke:e?"#374151":"#E5E7EB"}),y.jsx(or,{dataKey:"month",tick:{fill:e?"#9CA3AF":"#4B5563"}}),y.jsx(sr,{tick:{fill:e?"#9CA3AF":"#4B5563"}}),y.jsx(He,{contentStyle:{backgroundColor:e?"#1A1F35":"white",borderColor:e?"#374151":"#E5E7EB",color:e?"white":"black"}}),y.jsx(lt,{}),y.jsx(_t,{dataKey:"revenue",fill:"#88cc14"})]})})]})]}),y.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} p-4 rounded-lg border`,children:[y.jsx("h3",{className:"text-lg font-semibold mb-4",children:"User Growth"}),y.jsx(ki,{width:"100%",height:300,children:y.jsxs(qF,{data:t.userStats,children:[y.jsx(Gi,{strokeDasharray:"3 3",stroke:e?"#374151":"#E5E7EB"}),y.jsx(or,{dataKey:"month",tick:{fill:e?"#9CA3AF":"#4B5563"}}),y.jsx(sr,{tick:{fill:e?"#9CA3AF":"#4B5563"}}),y.jsx(He,{contentStyle:{backgroundColor:e?"#1A1F35":"white",borderColor:e?"#374151":"#E5E7EB",color:e?"white":"black"}}),y.jsx(lt,{}),y.jsx(ji,{type:"monotone",dataKey:"users",stroke:"#0088FE",strokeWidth:2})]})})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} p-4 rounded-lg border`,children:[y.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Content Engagement"}),y.jsx(ki,{width:"100%",height:300,children:y.jsxs(fp,{data:t.contentStats,layout:"vertical",margin:{top:5,right:30,left:20,bottom:5},children:[y.jsx(Gi,{strokeDasharray:"3 3",stroke:e?"#374151":"#E5E7EB"}),y.jsx(or,{type:"number",tick:{fill:e?"#9CA3AF":"#4B5563"}}),y.jsx(sr,{dataKey:"name",type:"category",tick:{fill:e?"#9CA3AF":"#4B5563"},width:120}),y.jsx(He,{contentStyle:{backgroundColor:e?"#1A1F35":"white",borderColor:e?"#374151":"#E5E7EB",color:e?"white":"black"}}),y.jsx(lt,{}),y.jsx(_t,{dataKey:"completed",stackId:"a",fill:"#00C49F"}),y.jsx(_t,{dataKey:"inProgress",stackId:"a",fill:"#FFBB28"})]})})]})]})]})},YF=()=>{const{darkMode:e}=rn(),[t,r]=I.useState(""),[n,i]=I.useState("all"),o=[{id:"u1",email:"<EMAIL>",username:"free_user",fullName:"Free User",subscriptionTier:"free",coins:100,lastLogin:"2023-10-15T14:30:00Z",status:"active",createdAt:"2023-09-01T10:00:00Z"},{id:"u2",email:"<EMAIL>",username:"premium_user",fullName:"Premium User",subscriptionTier:"premium",coins:500,lastLogin:"2023-10-16T09:15:00Z",status:"active",createdAt:"2023-09-05T11:30:00Z"},{id:"u3",email:"<EMAIL>",username:"business_user",fullName:"Business User",subscriptionTier:"business",coins:1e3,lastLogin:"2023-10-14T16:45:00Z",status:"active",createdAt:"2023-08-20T14:20:00Z"},{id:"u4",email:"<EMAIL>",username:"chitti",fullName:"Goutham Kumar Chitti",subscriptionTier:"free",coins:100,lastLogin:"2023-10-16T11:10:00Z",status:"active",createdAt:"2023-10-01T09:00:00Z"},{id:"u5",email:"<EMAIL>",username:"inactive_user",fullName:"Inactive User",subscriptionTier:"free",coins:50,lastLogin:"2023-09-01T08:20:00Z",status:"inactive",createdAt:"2023-07-15T13:40:00Z"}].filter(c=>{const u=c.email.toLowerCase().includes(t.toLowerCase())||c.username.toLowerCase().includes(t.toLowerCase())||c.fullName.toLowerCase().includes(t.toLowerCase()),f=n==="all"||c.subscriptionTier===n||n==="inactive"&&c.status==="inactive";return u&&f}),s=c=>{const u=new Date(c);return u.toLocaleDateString()+" "+u.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})};return y.jsxs("div",{children:[y.jsxs("div",{className:"flex justify-between items-center mb-6",children:[y.jsx("h2",{className:"text-2xl font-bold",children:"User Management"}),y.jsxs("button",{className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center",children:[y.jsx(Qm,{className:"mr-2"})," Add New User"]})]}),y.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[y.jsx("div",{className:"flex-1",children:y.jsxs("div",{className:`flex items-center ${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg px-3 py-2`,children:[y.jsx(yi,{className:`mr-2 ${e?"text-gray-400":"text-gray-500"}`}),y.jsx("input",{type:"text",placeholder:"Search users...",className:`w-full bg-transparent focus:outline-none ${e?"text-white":"text-gray-900"}`,value:t,onChange:c=>r(c.target.value)})]})}),y.jsx("div",{children:y.jsxs("select",{className:`px-3 py-2 rounded-lg border ${e?"bg-[#1A1F35] border-gray-800 text-white":"bg-white border-gray-200 text-gray-900"}`,value:n,onChange:c=>i(c.target.value),children:[y.jsx("option",{value:"all",children:"All Users"}),y.jsx("option",{value:"free",children:"Free Tier"}),y.jsx("option",{value:"premium",children:"Premium Tier"}),y.jsx("option",{value:"business",children:"Business Tier"}),y.jsx("option",{value:"inactive",children:"Inactive Users"})]})}),y.jsxs("button",{className:`px-3 py-2 rounded-lg border flex items-center ${e?"bg-[#1A1F35] border-gray-800 hover:bg-gray-800":"bg-white border-gray-200 hover:bg-gray-100"}`,children:[y.jsx(eg,{className:"mr-2"})," Export"]})]}),y.jsx("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:y.jsx("div",{className:"overflow-x-auto",children:y.jsxs("table",{className:"w-full",children:[y.jsx("thead",{children:y.jsxs("tr",{className:`${e?"bg-gray-800":"bg-gray-50"}`,children:[y.jsx("th",{className:"px-4 py-3 text-left",children:"User"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Email"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Subscription"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Coins"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Last Login"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Status"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Actions"})]})}),y.jsxs("tbody",{children:[o.map(c=>y.jsxs("tr",{className:`border-t ${e?"border-gray-800 hover:bg-gray-800":"border-gray-200 hover:bg-gray-50"}`,children:[y.jsx("td",{className:"px-4 py-3",children:y.jsxs("div",{children:[y.jsx("div",{className:"font-medium",children:c.fullName}),y.jsxs("div",{className:`text-sm ${e?"text-gray-400":"text-gray-500"}`,children:["@",c.username]})]})}),y.jsx("td",{className:"px-4 py-3",children:c.email}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("span",{className:`px-2 py-1 rounded text-xs ${c.subscriptionTier==="premium"?"bg-green-100 text-green-800":c.subscriptionTier==="business"?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:c.subscriptionTier.charAt(0).toUpperCase()+c.subscriptionTier.slice(1)})}),y.jsx("td",{className:"px-4 py-3",children:c.coins}),y.jsx("td",{className:"px-4 py-3",children:s(c.lastLogin)}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("span",{className:`px-2 py-1 rounded text-xs ${c.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:c.status.charAt(0).toUpperCase()+c.status.slice(1)})}),y.jsx("td",{className:"px-4 py-3",children:y.jsxs("div",{className:"flex space-x-2",children:[y.jsx("button",{className:"p-1 text-blue-500 hover:text-blue-700",children:y.jsx(Hi,{})}),y.jsx("button",{className:"p-1 text-red-500 hover:text-red-700",children:y.jsx(tg,{})})]})})]},c.id)),o.length===0&&y.jsx("tr",{children:y.jsx("td",{colSpan:"7",className:"px-4 py-3 text-center",children:"No users found matching your search criteria."})})]})]})})})]})},KF=({modules:e=[],challenges:t=[],blogPosts:r=[],onAddContent:n,onEditContent:i,onDeleteContent:a,onViewContent:o})=>{dp();const[s,c]=I.useState("modules"),[u,f]=I.useState(""),l=()=>{const p=u.toLowerCase();switch(s){case"modules":return e.filter(v=>v.title.toLowerCase().includes(p)||v.description.toLowerCase().includes(p));case"challenges":return t.filter(v=>v.title.toLowerCase().includes(p)||v.description.toLowerCase().includes(p));case"blog":return r.filter(v=>v.title.toLowerCase().includes(p)||v.excerpt.toLowerCase().includes(p));default:return[]}},d=p=>{switch(p){case"modules":return y.jsx(sl,{});case"challenges":return y.jsx(cl,{});case"blog":return y.jsx(ul,{});default:return y.jsx(ol,{})}},h=p=>{switch(p){case It.PREMIUM:return y.jsx("span",{className:"text-xs bg-yellow-100 text-yellow-800 py-0.5 px-1.5 rounded",children:"Premium"});case It.BUSINESS:return y.jsx("span",{className:"text-xs bg-blue-100 text-blue-800 py-0.5 px-1.5 rounded",children:"Business"});default:return y.jsx("span",{className:"text-xs bg-green-100 text-green-800 py-0.5 px-1.5 rounded",children:"Free"})}};return y.jsx(rg.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:y.jsxs("div",{className:"p-5",children:[y.jsxs("div",{className:"flex items-center justify-between mb-4",children:[y.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:[y.jsx(ol,{className:"mr-2 text-blue-600 dark:text-blue-400"}),"Content Management"]}),y.jsxs("button",{className:"text-sm bg-blue-600 text-white px-3 py-1 rounded flex items-center hover:bg-blue-700 transition-colors",onClick:()=>n(s),children:[y.jsx(Vc,{className:"mr-1"}),"Add New"]})]}),y.jsxs("div",{className:"flex border-b border-gray-200 dark:border-gray-700 mb-4",children:[y.jsxs("button",{className:`px-4 py-2 text-sm font-medium ${s==="modules"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,onClick:()=>c("modules"),children:[y.jsx(sl,{className:"inline mr-1"}),"Modules"]}),y.jsxs("button",{className:`px-4 py-2 text-sm font-medium ${s==="challenges"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,onClick:()=>c("challenges"),children:[y.jsx(cl,{className:"inline mr-1"}),"Challenges"]}),y.jsxs("button",{className:`px-4 py-2 text-sm font-medium ${s==="blog"?"text-blue-600 border-b-2 border-blue-600 dark:text-blue-400 dark:border-blue-400":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,onClick:()=>c("blog"),children:[y.jsx(ul,{className:"inline mr-1"}),"Blog"]})]}),y.jsxs("div",{className:"relative mb-4",children:[y.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:y.jsx(yi,{className:"text-gray-400"})}),y.jsx("input",{type:"text",className:"bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5",placeholder:`Search ${s}...`,value:u,onChange:p=>f(p.target.value)})]}),y.jsxs("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:[l().slice(0,5).map((p,v)=>y.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex items-center justify-between",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"mr-3 text-blue-600 dark:text-blue-400",children:d(s)}),y.jsxs("div",{children:[y.jsxs("h5",{className:"font-medium text-gray-900 dark:text-white flex items-center",children:[p.title,y.jsx("span",{className:"ml-2",children:h(p.tier)})]}),y.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs",children:p.description||p.excerpt})]})]}),y.jsxs("div",{className:"flex space-x-2",children:[y.jsx("button",{className:"p-1.5 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",onClick:()=>o(s,p.id),title:"View",children:y.jsx(ng,{})}),y.jsx("button",{className:"p-1.5 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400",onClick:()=>i(s,p.id),title:"Edit",children:y.jsx(Xc,{})}),y.jsx("button",{className:"p-1.5 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400",onClick:()=>a(s,p.id),title:"Delete",children:y.jsx(Yc,{})})]})]},v)),l().length===0&&y.jsxs("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400",children:["No ",s," found matching your search."]})]}),y.jsx("div",{className:"mt-4 text-center",children:y.jsxs(ig,{to:`/admin/${s}`,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center",children:["Manage All ",s.charAt(0).toUpperCase()+s.slice(1),y.jsx(ag,{className:"ml-1 text-xs"})]})})]})})},ZF=()=>{const e=dp(),{darkMode:t}=rn(),[r,n]=I.useState(""),[i,a]=I.useState("all"),[o,s]=I.useState("learning"),c=[{id:"lm1",title:"Introduction to Cybersecurity",category:"Fundamentals",difficulty:"Beginner",status:"published",author:"Admin",lastUpdated:"2023-10-01T10:00:00Z",views:1250,completions:780},{id:"lm2",title:"Network Security Basics",category:"Network Security",difficulty:"Intermediate",status:"published",author:"Admin",lastUpdated:"2023-09-15T14:30:00Z",views:980,completions:540},{id:"lm3",title:"Web Application Security",category:"Web Security",difficulty:"Intermediate",status:"published",author:"Admin",lastUpdated:"2023-09-20T09:15:00Z",views:1100,completions:620},{id:"lm4",title:"Advanced Penetration Testing",category:"Offensive Security",difficulty:"Advanced",status:"draft",author:"Admin",lastUpdated:"2023-10-10T16:45:00Z",views:0,completions:0}],u=[{id:"ch1",title:"SQL Injection Basics",category:"Web Security",difficulty:"Beginner",status:"published",author:"Admin",lastUpdated:"2023-09-05T11:30:00Z",attempts:950,completions:420},{id:"ch2",title:"Password Cracking Challenge",category:"Cryptography",difficulty:"Intermediate",status:"published",author:"Admin",lastUpdated:"2023-09-10T13:45:00Z",attempts:780,completions:310},{id:"ch3",title:"Network Traffic Analysis",category:"Network Security",difficulty:"Intermediate",status:"published",author:"Admin",lastUpdated:"2023-09-18T10:20:00Z",attempts:650,completions:280},{id:"ch4",title:"Advanced Exploit Development",category:"Offensive Security",difficulty:"Advanced",status:"draft",author:"Admin",lastUpdated:"2023-10-12T15:10:00Z",attempts:0,completions:0}],f=[{id:"sh1",title:"Build a Simple Firewall",category:"Network Security",difficulty:"Beginner",status:"published",author:"Admin",lastUpdated:"2023-09-08T09:30:00Z",starts:680,completions:320},{id:"sh2",title:"Create a Password Manager",category:"Application Development",difficulty:"Intermediate",status:"published",author:"Admin",lastUpdated:"2023-09-12T14:15:00Z",starts:540,completions:210},{id:"sh3",title:"Develop a Network Scanner",category:"Network Security",difficulty:"Intermediate",status:"published",author:"Admin",lastUpdated:"2023-09-22T11:40:00Z",starts:490,completions:180},{id:"sh4",title:"Build a Threat Intelligence Platform",category:"Defensive Security",difficulty:"Advanced",status:"draft",author:"Admin",lastUpdated:"2023-10-14T13:25:00Z",starts:0,completions:0}],l=()=>{switch(o){case"challenges":return u;case"starthack":return f;case"learning":default:return c}},d=b=>{e(`/admin/${{learning:"modules",challenges:"challenges",blogs:"blog"}[b]||b}/new`)},h=(b,A)=>{e(`/admin/${{learning:"modules",challenges:"challenges",blogs:"blog"}[b]||b}/edit/${A}`)},p=(b,A)=>{window.confirm(`Are you sure you want to delete this ${b}?`)&&console.log(`Deleting ${b} with ID: ${A}`)},v=(b,A)=>{e(`/${{learning:"global-learn",challenges:"global-challenges",blogs:"blog"}[b]||b}?id=${A}`)},m=l().filter(b=>{const A=b.title.toLowerCase().includes(r.toLowerCase())||b.category.toLowerCase().includes(r.toLowerCase()),P=i==="all"||b.status===i||b.difficulty.toLowerCase()===i.toLowerCase();return A&&P}),x=b=>new Date(b).toLocaleDateString(),O=()=>{const b=c.map(_=>({id:_.id,title:_.title,description:`${_.category} - ${_.difficulty}`,tier:_.difficulty==="Advanced"?It.BUSINESS:_.difficulty==="Intermediate"?It.PREMIUM:It.FREE,status:_.status})),A=u.map(_=>({id:_.id,title:_.title,description:`${_.category} - ${_.difficulty}`,tier:_.difficulty==="Advanced"?It.BUSINESS:_.difficulty==="Intermediate"?It.PREMIUM:It.FREE,difficulty:_.difficulty.toLowerCase(),status:_.status})),P=f.map(_=>({id:_.id,title:_.title,excerpt:`${_.category} - ${_.difficulty}`,author:_.author,status:_.status}));return{modules:b,challenges:A,blogPosts:P}},{modules:w,challenges:S,blogPosts:g}=O();return y.jsxs("div",{children:[y.jsx("div",{className:"mb-8",children:y.jsx(KF,{modules:w,challenges:S,blogPosts:g,onAddContent:d,onEditContent:h,onDeleteContent:p,onViewContent:v})}),y.jsxs("div",{className:"flex justify-between items-center mb-6",children:[y.jsx("h2",{className:"text-2xl font-bold",children:"Content Management"}),y.jsxs("button",{className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center",children:[y.jsx(Vc,{className:"mr-2"})," Add New Content"]})]}),y.jsxs("div",{className:"flex mb-6 border-b border-gray-200 dark:border-gray-700",children:[y.jsxs("button",{className:`px-4 py-2 font-medium ${o==="learning"?"border-b-2 border-[#88cc14] text-[#88cc14]":t?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>s("learning"),children:[y.jsx(og,{className:"inline mr-2"})," Learning Modules"]}),y.jsxs("button",{className:`px-4 py-2 font-medium ${o==="challenges"?"border-b-2 border-[#88cc14] text-[#88cc14]":t?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>s("challenges"),children:[y.jsx(sg,{className:"inline mr-2"})," Challenges"]}),y.jsxs("button",{className:`px-4 py-2 font-medium ${o==="starthack"?"border-b-2 border-[#88cc14] text-[#88cc14]":t?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>s("starthack"),children:[y.jsx(cg,{className:"inline mr-2"})," Start Hack"]})]}),y.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[y.jsx("div",{className:"flex-1",children:y.jsxs("div",{className:`flex items-center ${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg px-3 py-2`,children:[y.jsx(yi,{className:`mr-2 ${t?"text-gray-400":"text-gray-500"}`}),y.jsx("input",{type:"text",placeholder:"Search content...",className:`w-full bg-transparent focus:outline-none ${t?"text-white":"text-gray-900"}`,value:r,onChange:b=>n(b.target.value)})]})}),y.jsx("div",{children:y.jsxs("select",{className:`px-3 py-2 rounded-lg border ${t?"bg-[#1A1F35] border-gray-800 text-white":"bg-white border-gray-200 text-gray-900"}`,value:i,onChange:b=>a(b.target.value),children:[y.jsx("option",{value:"all",children:"All Content"}),y.jsx("option",{value:"published",children:"Published"}),y.jsx("option",{value:"draft",children:"Drafts"}),y.jsx("option",{value:"beginner",children:"Beginner"}),y.jsx("option",{value:"intermediate",children:"Intermediate"}),y.jsx("option",{value:"advanced",children:"Advanced"})]})})]}),y.jsx("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:y.jsx("div",{className:"overflow-x-auto",children:y.jsxs("table",{className:"w-full",children:[y.jsx("thead",{children:y.jsxs("tr",{className:`${t?"bg-gray-800":"bg-gray-50"}`,children:[y.jsx("th",{className:"px-4 py-3 text-left",children:"Title"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Category"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Difficulty"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Status"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Last Updated"}),y.jsx("th",{className:"px-4 py-3 text-left",children:o==="learning"?"Views/Completions":o==="challenges"?"Attempts/Completions":"Starts/Completions"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Actions"})]})}),y.jsxs("tbody",{children:[m.map(b=>y.jsxs("tr",{className:`border-t ${t?"border-gray-800 hover:bg-gray-800":"border-gray-200 hover:bg-gray-50"}`,children:[y.jsx("td",{className:"px-4 py-3 font-medium",children:b.title}),y.jsx("td",{className:"px-4 py-3",children:b.category}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("span",{className:`px-2 py-1 rounded text-xs ${b.difficulty==="Beginner"?"bg-green-100 text-green-800":b.difficulty==="Intermediate"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:b.difficulty})}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("span",{className:`px-2 py-1 rounded text-xs ${b.status==="published"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:b.status.charAt(0).toUpperCase()+b.status.slice(1)})}),y.jsx("td",{className:"px-4 py-3",children:x(b.lastUpdated)}),y.jsx("td",{className:"px-4 py-3",children:o==="learning"?`${b.views} / ${b.completions}`:o==="challenges"?`${b.attempts} / ${b.completions}`:`${b.starts} / ${b.completions}`}),y.jsx("td",{className:"px-4 py-3",children:y.jsxs("div",{className:"flex space-x-2",children:[y.jsx("button",{className:"p-1 text-blue-500 hover:text-blue-700",children:y.jsx(Xc,{})}),y.jsx("button",{className:"p-1 text-red-500 hover:text-red-700",children:y.jsx(Yc,{})})]})})]},b.id)),m.length===0&&y.jsx("tr",{children:y.jsx("td",{colSpan:"7",className:"px-4 py-3 text-center",children:"No content found matching your search criteria."})})]})]})})})]})},JF=()=>{const{darkMode:e}=rn(),[t,r]=I.useState(""),[n,i]=I.useState("all"),[a,o]=I.useState("all"),c=[{id:"n1",title:"New Learning Module Available",message:'Check out our new "Advanced Penetration Testing" module to enhance your skills!',type:"announcement",target:"all",status:"active",scheduled:!1,createdAt:"2023-10-15T10:00:00Z",expiresAt:"2023-11-15T10:00:00Z",views:450,clicks:120},{id:"n2",title:"Premium Subscription Discount",message:"Upgrade to Premium now and get 20% off for the first 3 months!",type:"promotion",target:"free",status:"active",scheduled:!1,createdAt:"2023-10-10T14:30:00Z",expiresAt:"2023-10-25T14:30:00Z",views:320,clicks:85},{id:"n3",title:"System Maintenance",message:"The platform will be undergoing maintenance on October 20th from 2:00 AM to 4:00 AM UTC.",type:"system",target:"all",status:"active",scheduled:!0,scheduledFor:"2023-10-18T10:00:00Z",createdAt:"2023-10-12T09:15:00Z",expiresAt:"2023-10-21T09:15:00Z",views:0,clicks:0},{id:"n4",title:"New Challenge Released",message:'Test your skills with our new "Network Traffic Analysis" challenge!',type:"announcement",target:"premium",status:"active",scheduled:!1,createdAt:"2023-10-08T16:45:00Z",expiresAt:"2023-11-08T16:45:00Z",views:280,clicks:95},{id:"n5",title:"Welcome to Business Tier",message:"Thank you for upgrading to Business tier! Explore team features and advanced content.",type:"welcome",target:"business",status:"active",scheduled:!1,createdAt:"2023-10-05T11:30:00Z",expiresAt:"2023-11-05T11:30:00Z",views:65,clicks:40},{id:"n6",title:"Holiday Special Event",message:"Join our upcoming holiday special event with exclusive challenges and rewards!",type:"event",target:"all",status:"draft",scheduled:!0,scheduledFor:"2023-12-01T00:00:00Z",createdAt:"2023-10-16T13:20:00Z",expiresAt:"2023-12-31T23:59:59Z",views:0,clicks:0}].filter(l=>{const d=l.title.toLowerCase().includes(t.toLowerCase())||l.message.toLowerCase().includes(t.toLowerCase()),h=n==="all"||l.status===n||l.type===n,p=a==="all"||a==="scheduled"&&l.scheduled||a==="active"&&l.status==="active"&&!l.scheduled||a==="draft"&&l.status==="draft";return d&&h&&p}),u=l=>{const d=new Date(l);return d.toLocaleDateString()+" "+d.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},f=l=>{switch(l){case"free":return y.jsxs("span",{className:"flex items-center",children:[y.jsx(Ho,{className:"mr-1 text-blue-500"})," Free Users"]});case"premium":return y.jsxs("span",{className:"flex items-center",children:[y.jsx(Ho,{className:"mr-1 text-green-500"})," Premium Users"]});case"business":return y.jsxs("span",{className:"flex items-center",children:[y.jsx(Ho,{className:"mr-1 text-purple-500"})," Business Users"]});case"all":default:return y.jsxs("span",{className:"flex items-center",children:[y.jsx(ug,{className:"mr-1 text-gray-500"})," All Users"]})}};return y.jsxs("div",{children:[y.jsxs("div",{className:"flex justify-between items-center mb-6",children:[y.jsx("h2",{className:"text-2xl font-bold",children:"Notification System"}),y.jsxs("button",{className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black rounded-lg flex items-center",children:[y.jsx(Vc,{className:"mr-2"})," Create Notification"]})]}),y.jsxs("div",{className:"flex mb-6 border-b border-gray-200 dark:border-gray-700",children:[y.jsx("button",{className:`px-4 py-2 font-medium ${a==="all"?"border-b-2 border-[#88cc14] text-[#88cc14]":e?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>o("all"),children:"All Notifications"}),y.jsx("button",{className:`px-4 py-2 font-medium ${a==="active"?"border-b-2 border-[#88cc14] text-[#88cc14]":e?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>o("active"),children:"Active"}),y.jsx("button",{className:`px-4 py-2 font-medium ${a==="scheduled"?"border-b-2 border-[#88cc14] text-[#88cc14]":e?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>o("scheduled"),children:"Scheduled"}),y.jsx("button",{className:`px-4 py-2 font-medium ${a==="draft"?"border-b-2 border-[#88cc14] text-[#88cc14]":e?"text-gray-400 hover:text-gray-300":"text-gray-500 hover:text-gray-700"}`,onClick:()=>o("draft"),children:"Drafts"})]}),y.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[y.jsx("div",{className:"flex-1",children:y.jsxs("div",{className:`flex items-center ${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg px-3 py-2`,children:[y.jsx(yi,{className:`mr-2 ${e?"text-gray-400":"text-gray-500"}`}),y.jsx("input",{type:"text",placeholder:"Search notifications...",className:`w-full bg-transparent focus:outline-none ${e?"text-white":"text-gray-900"}`,value:t,onChange:l=>r(l.target.value)})]})}),y.jsx("div",{children:y.jsxs("select",{className:`px-3 py-2 rounded-lg border ${e?"bg-[#1A1F35] border-gray-800 text-white":"bg-white border-gray-200 text-gray-900"}`,value:n,onChange:l=>i(l.target.value),children:[y.jsx("option",{value:"all",children:"All Types"}),y.jsx("option",{value:"announcement",children:"Announcements"}),y.jsx("option",{value:"promotion",children:"Promotions"}),y.jsx("option",{value:"system",children:"System Notifications"}),y.jsx("option",{value:"event",children:"Events"}),y.jsx("option",{value:"welcome",children:"Welcome Messages"})]})})]}),y.jsx("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:y.jsx("div",{className:"overflow-x-auto",children:y.jsxs("table",{className:"w-full",children:[y.jsx("thead",{children:y.jsxs("tr",{className:`${e?"bg-gray-800":"bg-gray-50"}`,children:[y.jsx("th",{className:"px-4 py-3 text-left",children:"Title"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Type"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Target"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Status"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Created/Scheduled"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Expires"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Views/Clicks"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Actions"})]})}),y.jsxs("tbody",{children:[c.map(l=>y.jsxs("tr",{className:`border-t ${e?"border-gray-800 hover:bg-gray-800":"border-gray-200 hover:bg-gray-50"}`,children:[y.jsx("td",{className:"px-4 py-3 font-medium",children:l.title}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("span",{className:`px-2 py-1 rounded text-xs ${l.type==="announcement"?"bg-blue-100 text-blue-800":l.type==="promotion"?"bg-green-100 text-green-800":l.type==="system"?"bg-red-100 text-red-800":l.type==="event"?"bg-purple-100 text-purple-800":"bg-yellow-100 text-yellow-800"}`,children:l.type.charAt(0).toUpperCase()+l.type.slice(1)})}),y.jsx("td",{className:"px-4 py-3",children:f(l.target)}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("span",{className:`px-2 py-1 rounded text-xs ${l.status==="active"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:l.scheduled?"Scheduled":l.status.charAt(0).toUpperCase()+l.status.slice(1)})}),y.jsx("td",{className:"px-4 py-3",children:l.scheduled?u(l.scheduledFor):u(l.createdAt)}),y.jsx("td",{className:"px-4 py-3",children:u(l.expiresAt)}),y.jsxs("td",{className:"px-4 py-3",children:[l.views," / ",l.clicks]}),y.jsx("td",{className:"px-4 py-3",children:y.jsxs("div",{className:"flex space-x-2",children:[y.jsx("button",{className:"p-1 text-blue-500 hover:text-blue-700",children:y.jsx(Xc,{})}),y.jsx("button",{className:"p-1 text-red-500 hover:text-red-700",children:y.jsx(Yc,{})})]})})]},l.id)),c.length===0&&y.jsx("tr",{children:y.jsx("td",{colSpan:"8",className:"px-4 py-3 text-center",children:"No notifications found matching your search criteria."})})]})]})})}),y.jsxs("div",{className:"mt-8",children:[y.jsx("h3",{className:"text-xl font-bold mb-4",children:"Notification Templates"}),y.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-4`,children:[y.jsxs("div",{className:"flex items-center mb-3",children:[y.jsx(qo,{className:"text-blue-500 mr-2"}),y.jsx("h4",{className:"font-medium",children:"Announcement Template"})]}),y.jsx("p",{className:`text-sm ${e?"text-gray-400":"text-gray-600"} mb-3`,children:"Use this template for general announcements about new features, content, or updates."}),y.jsx("button",{className:`text-sm ${e?"text-blue-400 hover:text-blue-300":"text-blue-600 hover:text-blue-800"}`,children:"Use Template"})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-4`,children:[y.jsxs("div",{className:"flex items-center mb-3",children:[y.jsx(qo,{className:"text-green-500 mr-2"}),y.jsx("h4",{className:"font-medium",children:"Promotion Template"})]}),y.jsx("p",{className:`text-sm ${e?"text-gray-400":"text-gray-600"} mb-3`,children:"Use this template for promotional offers, discounts, and special deals."}),y.jsx("button",{className:`text-sm ${e?"text-blue-400 hover:text-blue-300":"text-blue-600 hover:text-blue-800"}`,children:"Use Template"})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-4`,children:[y.jsxs("div",{className:"flex items-center mb-3",children:[y.jsx(qo,{className:"text-red-500 mr-2"}),y.jsx("h4",{className:"font-medium",children:"System Notification Template"})]}),y.jsx("p",{className:`text-sm ${e?"text-gray-400":"text-gray-600"} mb-3`,children:"Use this template for system maintenance, downtime, or important updates."}),y.jsx("button",{className:`text-sm ${e?"text-blue-400 hover:text-blue-300":"text-blue-600 hover:text-blue-800"}`,children:"Use Template"})]})]})]})]})},QF=()=>{const{darkMode:e}=rn(),[t,r]=I.useState(!1),[n,i]=I.useState(!1),[a,o]=I.useState(null),[s,c]=I.useState({general:{siteName:"XCerberus",siteDescription:"Cybersecurity Learning Platform",maintenanceMode:!1,allowRegistration:!0},subscription:{freeTierChallenges:5,freeTierModules:3,premiumPrice:6,businessPrice:999,allowCoinPurchase:!0},email:{enableNotifications:!0,welcomeEmailEnabled:!0,subscriptionReminderDays:3},security:{maxLoginAttempts:5,sessionTimeoutMinutes:60,requireEmailVerification:!0}}),u=(h,p,v)=>{c(m=>({...m,[h]:{...m[h],[p]:v}})),i(!1),o(null)},f=(h,p)=>{c(v=>({...v,[h]:{...v[h],[p]:!v[h][p]}})),i(!1),o(null)},l=async h=>{h.preventDefault();try{r(!0),i(!1),o(null),await new Promise(p=>setTimeout(p,1e3)),i(!0)}catch(p){console.error("Error saving settings:",p),o("Failed to save settings. Please try again.")}finally{r(!1)}},d=()=>{c({general:{siteName:"XCerberus",siteDescription:"Cybersecurity Learning Platform",maintenanceMode:!1,allowRegistration:!0},subscription:{freeTierChallenges:5,freeTierModules:3,premiumPrice:6,businessPrice:999,allowCoinPurchase:!0},email:{enableNotifications:!0,welcomeEmailEnabled:!0,subscriptionReminderDays:3},security:{maxLoginAttempts:5,sessionTimeoutMinutes:60,requireEmailVerification:!0}}),i(!1),o(null)};return y.jsxs("div",{children:[y.jsxs("div",{className:"flex justify-between items-center mb-6",children:[y.jsx("h2",{className:"text-2xl font-bold",children:"System Settings"}),y.jsxs("div",{className:"flex gap-2",children:[y.jsxs("button",{onClick:d,className:`px-4 py-2 rounded-lg flex items-center gap-2 ${e?"bg-gray-700 text-white hover:bg-gray-600":"bg-gray-200 text-gray-800 hover:bg-gray-300"}`,children:[y.jsx(lg,{}),y.jsx("span",{children:"Reset"})]}),y.jsx("button",{onClick:l,disabled:t,className:`px-4 py-2 rounded-lg flex items-center gap-2 ${t?"bg-gray-400 cursor-not-allowed":"bg-[#88cc14] hover:bg-[#7ab811] text-black"}`,children:t?y.jsxs(y.Fragment,{children:[y.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-black"}),y.jsx("span",{children:"Saving..."})]}):y.jsxs(y.Fragment,{children:[y.jsx(fg,{}),y.jsx("span",{children:"Save Changes"})]})})]})]}),n&&y.jsxs("div",{className:"mb-4 p-3 bg-green-100 text-green-800 rounded-lg flex items-center gap-2",children:[y.jsx(hp,{}),y.jsx("span",{children:"Settings saved successfully!"})]}),a&&y.jsxs("div",{className:"mb-4 p-3 bg-red-100 text-red-800 rounded-lg flex items-center gap-2",children:[y.jsx(pp,{}),y.jsx("span",{children:a})]}),y.jsx("form",{onSubmit:l,children:y.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:[y.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[y.jsx(dg,{className:"text-blue-500"}),y.jsx("h3",{className:"text-lg font-semibold",children:"General Settings"})]}),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Site Name"}),y.jsx("input",{type:"text",value:s.general.siteName,onChange:h=>u("general","siteName",h.target.value),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Site Description"}),y.jsx("input",{type:"text",value:s.general.siteDescription,onChange:h=>u("general","siteDescription",h.target.value),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"maintenanceMode",checked:s.general.maintenanceMode,onChange:()=>f("general","maintenanceMode"),className:"h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"}),y.jsx("label",{htmlFor:"maintenanceMode",className:`ml-2 block text-sm ${e?"text-gray-300":"text-gray-700"}`,children:"Maintenance Mode"})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"allowRegistration",checked:s.general.allowRegistration,onChange:()=>f("general","allowRegistration"),className:"h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"}),y.jsx("label",{htmlFor:"allowRegistration",className:`ml-2 block text-sm ${e?"text-gray-300":"text-gray-700"}`,children:"Allow New Registrations"})]})]})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:[y.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[y.jsx(hg,{className:"text-green-500"}),y.jsx("h3",{className:"text-lg font-semibold",children:"Subscription Settings"})]}),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Free Tier Challenges"}),y.jsx("input",{type:"number",min:"0",value:s.subscription.freeTierChallenges,onChange:h=>u("subscription","freeTierChallenges",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Free Tier Modules"}),y.jsx("input",{type:"number",min:"0",value:s.subscription.freeTierModules,onChange:h=>u("subscription","freeTierModules",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Premium Price (INR)"}),y.jsx("input",{type:"number",min:"0",value:s.subscription.premiumPrice,onChange:h=>u("subscription","premiumPrice",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Business Price (INR)"}),y.jsx("input",{type:"number",min:"0",value:s.subscription.businessPrice,onChange:h=>u("subscription","businessPrice",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"allowCoinPurchase",checked:s.subscription.allowCoinPurchase,onChange:()=>f("subscription","allowCoinPurchase"),className:"h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"}),y.jsx("label",{htmlFor:"allowCoinPurchase",className:`ml-2 block text-sm ${e?"text-gray-300":"text-gray-700"}`,children:"Allow Coin Purchases"})]})]})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:[y.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[y.jsx(pg,{className:"text-yellow-500"}),y.jsx("h3",{className:"text-lg font-semibold",children:"Email Settings"})]}),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"enableNotifications",checked:s.email.enableNotifications,onChange:()=>f("email","enableNotifications"),className:"h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"}),y.jsx("label",{htmlFor:"enableNotifications",className:`ml-2 block text-sm ${e?"text-gray-300":"text-gray-700"}`,children:"Enable Email Notifications"})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"welcomeEmailEnabled",checked:s.email.welcomeEmailEnabled,onChange:()=>f("email","welcomeEmailEnabled"),className:"h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"}),y.jsx("label",{htmlFor:"welcomeEmailEnabled",className:`ml-2 block text-sm ${e?"text-gray-300":"text-gray-700"}`,children:"Send Welcome Email"})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Subscription Reminder Days"}),y.jsx("input",{type:"number",min:"1",max:"30",value:s.email.subscriptionReminderDays,onChange:h=>u("email","subscriptionReminderDays",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]})]})]}),y.jsxs("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:[y.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[y.jsx(vg,{className:"text-red-500"}),y.jsx("h3",{className:"text-lg font-semibold",children:"Security Settings"})]}),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Max Login Attempts"}),y.jsx("input",{type:"number",min:"1",max:"10",value:s.security.maxLoginAttempts,onChange:h=>u("security","maxLoginAttempts",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Session Timeout (minutes)"}),y.jsx("input",{type:"number",min:"5",max:"1440",value:s.security.sessionTimeoutMinutes,onChange:h=>u("security","sessionTimeoutMinutes",parseInt(h.target.value)),className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"requireEmailVerification",checked:s.security.requireEmailVerification,onChange:()=>f("security","requireEmailVerification"),className:"h-4 w-4 text-[#88cc14] focus:ring-[#88cc14] border-gray-300 rounded"}),y.jsx("label",{htmlFor:"requireEmailVerification",className:`ml-2 block text-sm ${e?"text-gray-300":"text-gray-700"}`,children:"Require Email Verification"})]})]})]})]})})]})},e5=()=>{var A;const{darkMode:e}=rn(),[t,r]=I.useState([]),[n,i]=I.useState(!0),[a,o]=I.useState(null),[s,c]=I.useState(""),[u,f]=I.useState(null),[l,d]=I.useState(""),[h,p]=I.useState(!1),[v,m]=I.useState(null);I.useEffect(()=>{(async()=>{try{i(!0);const{data:_,error:E}=await ll.from("profiles").select("id, username, full_name, avatar_url, subscription_tier, created_at").order("created_at",{ascending:!1});if(E)throw E;r(_||[])}catch(_){console.error("Error fetching users:",_),o("Failed to load users. Please try again later.")}finally{i(!1)}})()},[]);const x=t.filter(P=>{var E,$,T;const _=s.toLowerCase();return((E=P.username)==null?void 0:E.toLowerCase().includes(_))||(($=P.full_name)==null?void 0:$.toLowerCase().includes(_))||((T=P.subscription_tier)==null?void 0:T.toLowerCase().includes(_))}),O=P=>{f(P),d(P.subscription_tier),p(!1),m(null)},w=P=>{d(P.target.value)},S=async()=>{if(!(!u||!l))try{i(!0),p(!1),m(null);const{data:P,error:_}=await ll.rpc("update_user_subscription_tier",{p_user_id:u.id,p_tier:l});if(_)throw _;r(t.map(E=>E.id===u.id?{...E,subscription_tier:l}:E)),f({...u,subscription_tier:l}),p(!0)}catch(P){console.error("Error updating subscription tier:",P),m("Failed to update subscription tier. Please try again.")}finally{i(!1)}},g=P=>{const _=new Date(P);return _.toLocaleDateString()+" "+_.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},b=P=>{switch(P){case"premium":return y.jsx(gg,{className:"text-yellow-500"});case"business":return y.jsx(mg,{className:"text-purple-500"});default:return y.jsx(yg,{className:"text-blue-500"})}};return y.jsxs("div",{className:"space-y-6",children:[y.jsxs("div",{className:"flex justify-between items-center",children:[y.jsx("h2",{className:"text-2xl font-bold",children:"User Subscription Manager"}),y.jsxs("div",{className:"relative",children:[y.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:y.jsx(yi,{className:e?"text-gray-400":"text-gray-500"})}),y.jsx("input",{type:"text",placeholder:"Search users...",value:s,onChange:P=>c(P.target.value),className:`pl-10 pr-4 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`})]})]}),y.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[y.jsx("div",{className:`lg:col-span-2 ${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border overflow-hidden`,children:y.jsx("div",{className:"overflow-x-auto",children:y.jsxs("table",{className:"w-full",children:[y.jsx("thead",{children:y.jsxs("tr",{className:`${e?"bg-gray-800":"bg-gray-50"}`,children:[y.jsx("th",{className:"px-4 py-3 text-left",children:"User"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Subscription"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Joined"}),y.jsx("th",{className:"px-4 py-3 text-left",children:"Actions"})]})}),y.jsx("tbody",{children:n&&t.length===0?y.jsx("tr",{children:y.jsx("td",{colSpan:"4",className:"px-4 py-3 text-center",children:y.jsx("div",{className:"flex justify-center py-4",children:y.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-[#88cc14]"})})})}):a?y.jsx("tr",{children:y.jsx("td",{colSpan:"4",className:"px-4 py-3 text-center text-red-500",children:a})}):x.length===0?y.jsx("tr",{children:y.jsx("td",{colSpan:"4",className:"px-4 py-3 text-center",children:"No users found matching your search."})}):x.map(P=>{var _;return y.jsxs("tr",{className:`border-t ${e?"border-gray-800 hover:bg-gray-800":"border-gray-200 hover:bg-gray-50"} ${(u==null?void 0:u.id)===P.id?"bg-[#88cc14]/10":""}`,onClick:()=>O(P),children:[y.jsx("td",{className:"px-4 py-3",children:y.jsxs("div",{className:"flex items-center gap-3",children:[y.jsx("div",{className:"w-10 h-10 rounded-full bg-[#88cc14]/20 flex items-center justify-center",children:P.avatar_url?y.jsx("img",{src:P.avatar_url,alt:P.username,className:"w-full h-full rounded-full object-cover"}):y.jsx("span",{className:"text-[#88cc14]",children:((_=P.username)==null?void 0:_.charAt(0).toUpperCase())||"U"})}),y.jsxs("div",{children:[y.jsx("p",{className:"font-medium",children:P.username}),y.jsx("p",{className:`text-sm ${e?"text-gray-400":"text-gray-500"}`,children:P.full_name||"No name provided"})]})]})}),y.jsx("td",{className:"px-4 py-3",children:y.jsxs("div",{className:"flex items-center gap-2",children:[b(P.subscription_tier),y.jsx("span",{className:"capitalize",children:P.subscription_tier})]})}),y.jsx("td",{className:"px-4 py-3",children:g(P.created_at)}),y.jsx("td",{className:"px-4 py-3",children:y.jsx("button",{onClick:E=>{E.stopPropagation(),O(P)},className:"p-2 text-blue-500 hover:text-blue-700 rounded-full hover:bg-blue-100",children:y.jsx(Hi,{})})})]},P.id)})})]})})}),y.jsx("div",{className:`${e?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-4`,children:u?y.jsxs("div",{children:[y.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[y.jsx("div",{className:"w-16 h-16 rounded-full bg-[#88cc14]/20 flex items-center justify-center",children:u.avatar_url?y.jsx("img",{src:u.avatar_url,alt:u.username,className:"w-full h-full rounded-full object-cover"}):y.jsx("span",{className:"text-[#88cc14] text-2xl",children:((A=u.username)==null?void 0:A.charAt(0).toUpperCase())||"U"})}),y.jsxs("div",{children:[y.jsx("h3",{className:"text-xl font-bold",children:u.username}),y.jsx("p",{className:`${e?"text-gray-400":"text-gray-500"}`,children:u.full_name||"No name provided"})]})]}),y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Current Subscription"}),y.jsxs("div",{className:`px-4 py-2 rounded-lg ${e?"bg-gray-800":"bg-gray-100"} flex items-center gap-2`,children:[b(u.subscription_tier),y.jsx("span",{className:"capitalize",children:u.subscription_tier})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:`block text-sm font-medium ${e?"text-gray-300":"text-gray-700"} mb-1`,children:"Change Subscription Tier"}),y.jsxs("select",{value:l,onChange:w,className:`w-full px-3 py-2 rounded-lg ${e?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border focus:outline-none focus:ring-2 focus:ring-[#88cc14]`,children:[y.jsx("option",{value:"free",children:"Free"}),y.jsx("option",{value:"premium",children:"Premium"}),y.jsx("option",{value:"business",children:"Business"})]})]}),y.jsx("button",{onClick:S,disabled:n||l===u.subscription_tier,className:`w-full px-4 py-2 rounded-lg flex items-center justify-center gap-2 ${n||l===u.subscription_tier?"bg-gray-400 cursor-not-allowed":"bg-[#88cc14] hover:bg-[#7ab811] text-black"}`,children:n?y.jsxs(y.Fragment,{children:[y.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-black"}),y.jsx("span",{children:"Updating..."})]}):y.jsxs(y.Fragment,{children:[y.jsx(Hi,{}),y.jsx("span",{children:"Update Subscription"})]})}),h&&y.jsxs("div",{className:"flex items-center gap-2 text-green-500 mt-2",children:[y.jsx(hp,{}),y.jsx("span",{children:"Subscription updated successfully!"})]}),v&&y.jsxs("div",{className:"flex items-center gap-2 text-red-500 mt-2",children:[y.jsx(pp,{}),y.jsx("span",{children:v})]})]})]}):y.jsxs("div",{className:"flex flex-col items-center justify-center h-64",children:[y.jsx(Hi,{className:"text-4xl mb-4 text-gray-400"}),y.jsx("p",{className:`text-center ${e?"text-gray-400":"text-gray-500"}`,children:"Select a user to manage their subscription"})]})})]})]})};export{ZF as C,JF as N,XF as S,YF as U,e5 as a,QF as b};
