import{u as S,w as Q,j as e,L as y,W as w,c as f,G as R,N as D,aQ as H,r as o,af as B,l as O,y as v,a4 as P,E as W,J as M,a1 as U,b3 as z,a3 as Y}from"./index-CVvVjHWF.js";const G=()=>{const{darkMode:s}=S(),[c,u]=o.useState(""),[l,a]=o.useState(""),[x,$]=o.useState(0),[d,b]=o.useState(null),[g,k]=o.useState(!1),[C,A]=o.useState(!1),[m,F]=o.useState(0),[L]=o.useState(new Date),[E,T]=o.useState([{type:"info",text:"SQL Injection Challenge initialized..."},{type:"info",text:"Target: BankSecure Login Portal"},{type:"info",text:"Objective: Bypass authentication without valid credentials"},{type:"system",text:"Type your SQL injection payload in the username field."}]),q=(t,r)=>{const i=`SELECT * FROM users WHERE username='${t}' AND password='${r}'`;n("query",`Executing: ${i}`);const p=["' or '","' or 1=","' or 1=1","' --","'#","' or true","admin'--",'" or "'],j=t.includes("'")||t.includes('"'),h=p.some(N=>t.toLowerCase().includes(N.toLowerCase()));return j?h?{success:!0,query:i}:{success:!1,error:"SQL syntax error",query:i}:{success:!1,error:"Invalid credentials",query:i}},n=(t,r)=>{T(i=>[...i,{type:t,text:r}]),setTimeout(()=>{const i=document.getElementById("sql-console");i&&(i.scrollTop=i.scrollHeight)},100)},_=t=>{t.preventDefault(),$(r=>r+1),n("system","Sending login request..."),setTimeout(()=>{try{const r=q(c,l);if(r.success){k(!0),b({type:"success",text:"Authentication bypassed! You have successfully exploited the SQL injection vulnerability."}),n("success","Authentication bypassed successfully!"),n("success","Accessing admin panel..."),n("success","Flag found: flag{sql_injection_master_2023}");const p=Math.floor((new Date-L)/1e3),j=Math.floor(p/60),h=p%60,N=`${j}:${h<10?"0"+h:h}`;n("info",`Challenge completed in ${N}`)}else r.error==="SQL syntax error"?(b({type:"warning",text:"SQL syntax error in your injection attempt. You're on the right track!"}),n("error",'Database returned: SQL syntax error near "'+c+'"')):(b({type:"error",text:"Login failed: Invalid credentials"}),n("error","Authentication failed: Invalid username or password"))}catch(r){console.error("Error in challenge simulation:",r),b({type:"error",text:"An unexpected error occurred. Please try again."}),n("error","System error: "+(r.message||"Unknown error"))}},800)},I=()=>{F(r=>r+1),A(!0),n("system","Hint requested...");const t=["Try entering special characters like a single quote (') in the username field to see how the application responds.","The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'","Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true.","Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)"];m<t.length&&n("hint",t[m])};return e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${s?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[g?e.jsx(B,{className:"text-green-500 mr-2"}):e.jsx(O,{className:"text-red-500 mr-2"}),"SQL Injection Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:I,className:`px-3 py-1 rounded-lg flex items-center ${s?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(v,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 p-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border mb-4`,children:[e.jsx("h3",{className:"font-bold mb-4",children:"BankSecure Login Portal"}),d&&e.jsx("div",{className:`p-3 rounded-lg mb-4 ${d.type==="success"?s?"bg-green-900/30 text-green-400":"bg-green-100 text-green-800":d.type==="warning"?s?"bg-yellow-900/30 text-yellow-400":"bg-yellow-100 text-yellow-800":s?"bg-red-900/30 text-red-400":"bg-red-100 text-red-800"}`,children:e.jsxs("div",{className:"flex items-start",children:[d.type==="success"&&e.jsx(P,{className:"mt-1 mr-2"}),d.type==="warning"&&e.jsx(W,{className:"mt-1 mr-2"}),d.type==="error"&&e.jsx(M,{className:"mt-1 mr-2"}),e.jsx("p",{children:d.text})]})}),e.jsxs("form",{onSubmit:_,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"username",className:"block mb-2 font-medium",children:"Username"}),e.jsx("input",{type:"text",id:"username",className:`w-full p-2 rounded-lg ${s?"bg-[#1A1F35] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:c,onChange:t=>u(t.target.value),placeholder:"Enter username",required:!0})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"password",className:"block mb-2 font-medium",children:"Password"}),e.jsx("input",{type:"password",id:"password",className:`w-full p-2 rounded-lg ${s?"bg-[#1A1F35] border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,value:l,onChange:t=>a(t.target.value),placeholder:"Enter password"})]}),e.jsx("button",{type:"submit",className:`w-full py-2 px-4 rounded-lg ${g?"bg-green-600 hover:bg-green-700":"bg-[#88cc14] hover:bg-[#7ab811]"} text-black font-medium`,disabled:g,children:g?"Authentication Bypassed":"Login"})]})]}),C&&e.jsx("div",{className:`p-4 rounded-lg ${s?"bg-yellow-900/20 border-yellow-900/30":"bg-yellow-50 border-yellow-200"} border`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(v,{className:`mt-1 mr-2 ${s?"text-yellow-400":"text-yellow-600"}`}),e.jsxs("div",{children:[e.jsxs("h4",{className:`font-medium ${s?"text-yellow-300":"text-yellow-800"}`,children:["Hint ",m,":"]}),m===1&&e.jsx("p",{className:s?"text-yellow-200":"text-yellow-700",children:"Try entering special characters like a single quote (') in the username field to see how the application responds."}),m===2&&e.jsx("p",{className:s?"text-yellow-200":"text-yellow-700",children:"The login query might look like: SELECT * FROM users WHERE username='[input]' AND password='[input]'"}),m===3&&e.jsx("p",{className:s?"text-yellow-200":"text-yellow-700",children:"Using a statement like ' OR '1'='1 might help you bypass the authentication by making the WHERE clause always true."}),m===4&&e.jsx("p",{className:s?"text-yellow-200":"text-yellow-700",children:"Try this payload in the username field: ' OR '1'='1'-- (The -- comments out the rest of the query)"})]})]})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:`rounded-lg overflow-hidden border ${s?"border-gray-700":"border-gray-300"}`,children:[e.jsxs("div",{className:"bg-black p-2 text-xs text-gray-400 border-b border-gray-700 flex justify-between items-center",children:[e.jsx("span",{children:"SQL Injection Challenge Console"}),e.jsxs("span",{className:"px-2 py-0.5 rounded bg-gray-800 text-gray-400",children:["Attempts: ",x]})]}),e.jsx("div",{id:"sql-console",className:"bg-black text-green-400 p-4 font-mono text-sm h-80 overflow-y-auto",children:E.map((t,r)=>e.jsxs("div",{className:`mb-1 ${t.type==="error"?"text-red-400":t.type==="success"?"text-green-400":t.type==="query"?"text-blue-400":t.type==="hint"?"text-yellow-400":"text-green-400"}`,children:[t.type==="error"&&"[!] ",t.type==="success"&&"[+] ",t.type==="query"&&"[>] ",t.type==="hint"&&"[?] ",t.type==="system"&&"[*] ",t.type==="info"&&"[i] ",t.text]},r))})]}),g&&e.jsx("div",{className:`mt-4 p-4 rounded-lg ${s?"bg-green-900/20 border-green-900/30":"bg-green-50 border-green-200"} border`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(f,{className:`mt-1 mr-2 ${s?"text-yellow-400":"text-yellow-600"}`}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${s?"text-green-300":"text-green-800"}`,children:"Challenge Completed!"}),e.jsx("p",{className:s?"text-green-200":"text-green-700",children:"You've successfully exploited the SQL injection vulnerability. In a real application, this would give you unauthorized access to user accounts."}),e.jsxs("div",{className:"mt-2",children:[e.jsx("span",{className:"font-medium",children:"Flag: "}),e.jsx("code",{className:"px-2 py-1 rounded bg-black text-green-400",children:"flag{sql_injection_master_2023}"})]}),e.jsxs("div",{className:`mt-4 pt-4 border-t ${s?"border-green-900/30":"border-green-200"}`,children:[e.jsx("h4",{className:`font-medium ${s?"text-green-300":"text-green-800"} mb-2`,children:"Ready for more challenges?"}),e.jsx("p",{className:`${s?"text-green-200":"text-green-700"} mb-3`,children:"This is just a taste of what XCerberus has to offer. Sign up to access dozens more challenges across different cybersecurity domains!"}),e.jsx(y,{to:"/signup",className:"inline-block px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors",children:"Create Free Account"})]})]})]})})]})]})]})},K=()=>{const{darkMode:s}=S(),{profile:c}=Q(),u=[{id:"web",name:"Web Security",icon:e.jsx(U,{className:"text-blue-500"}),description:"Exploit vulnerabilities in web applications, including SQL injection, XSS, CSRF, and more.",challenges:[{title:"SQL Injection Basics",difficulty:"Beginner",points:100,participants:1243,completion_rate:68,estimated_time:30},{title:"XSS Challenge",difficulty:"Intermediate",points:250,participants:876,completion_rate:42,estimated_time:45},{title:"Advanced CSRF Attack",difficulty:"Advanced",points:500,participants:432,completion_rate:21,estimated_time:60}]},{id:"crypto",name:"Cryptography",icon:e.jsx(z,{className:"text-purple-500"}),description:"Break cryptographic implementations, crack ciphers, and solve encryption challenges.",challenges:[{title:"Caesar Cipher",difficulty:"Beginner",points:75,participants:1567,completion_rate:82,estimated_time:20},{title:"RSA Implementation Flaws",difficulty:"Intermediate",points:300,participants:654,completion_rate:35,estimated_time:50}]},{id:"network",name:"Network Security",icon:e.jsx(Y,{className:"text-green-500"}),description:"Analyze network traffic, exploit protocol vulnerabilities, and secure network infrastructure.",challenges:[{title:"Packet Analysis",difficulty:"Intermediate",points:200,participants:876,completion_rate:58,estimated_time:40},{title:"Man-in-the-Middle Attack",difficulty:"Advanced",points:450,participants:321,completion_rate:28,estimated_time:75}]}];return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Cybersecurity Challenges"}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Test Your Skills with Real-World Challenges"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Put your cybersecurity knowledge to the test with our hands-on challenges. Solve realistic scenarios, exploit vulnerabilities, and improve your skills in a safe environment."}),!c&&e.jsxs("div",{className:"flex flex-wrap gap-3 mt-4",children:[e.jsx(y,{to:"/login",className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors",children:"Sign In to Start Challenges"}),e.jsx(y,{to:"/signup",className:"px-4 py-2 border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10 font-medium rounded-lg transition-colors",children:"Create Free Account"})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Interactive Demo: SQL Injection Challenge"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-6`,children:"Try this fully functional SQL injection challenge without creating an account. Bypass the login form by exploiting a SQL injection vulnerability."}),e.jsx(G,{})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:u.map(l=>e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden flex flex-col h-full`,children:e.jsxs("div",{className:"p-6 flex flex-col flex-grow",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-opacity-20 rounded-full flex items-center justify-center",style:{backgroundColor:"rgba(136, 204, 20, 0.1)"},children:l.icon}),e.jsx("h3",{className:"text-xl font-bold",children:l.name})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:l.description}),e.jsxs("div",{className:"flex items-center text-sm mb-4",children:[e.jsxs("span",{className:"flex items-center mr-4",children:[e.jsx(w,{className:"mr-1"})," ",l.challenges.reduce((a,x)=>a+x.participants,0)," participants"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(f,{className:"mr-1"})," ",l.challenges.length," challenges"]})]}),e.jsx("div",{className:"mt-auto pt-4",children:e.jsx(y,{to:"/login",className:"block w-full text-center px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors",children:"View Challenges"})})]})},l.id))}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Featured Challenges"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.flatMap(l=>l.challenges.slice(0,1).map((a,x)=>e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-opacity-20 rounded-full flex items-center justify-center",style:{backgroundColor:"rgba(136, 204, 20, 0.1)"},children:l.icon}),e.jsx("h3",{className:"font-bold",children:a.title})]}),e.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs ${X(a.difficulty,s)}`,children:a.difficulty})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm mt-3",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(f,{className:"mr-1"})," ",a.points," pts"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(w,{className:"mr-1"})," ",a.participants]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(R,{className:"mr-1"})," ",a.estimated_time," min"]})]}),e.jsxs("span",{className:`${a.completion_rate>50?"text-green-500":"text-yellow-500"}`,children:[a.completion_rate,"% completion"]})]})]},`${l.id}-${x}`)))})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"How Challenges Work"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-4",children:e.jsx(D,{className:"text-blue-500 text-2xl"})}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Secure Environment"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"All challenges run in isolated environments, allowing you to practice offensive techniques safely and legally."})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4",children:e.jsx(f,{className:"text-green-500 text-2xl"})}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Earn Points & Badges"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Complete challenges to earn points, unlock badges, and climb the global leaderboard."})]}),e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mb-4",children:e.jsx(H,{className:"text-purple-500 text-2xl"})}),e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Learn by Doing"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Each challenge includes hints, resources, and detailed solutions to help you learn as you solve."})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Challenge Difficulty Levels"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border-l-4 border-green-500`,children:[e.jsx("h3",{className:"font-bold text-green-500 mb-2",children:"Beginner"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"Perfect for those new to cybersecurity. These challenges teach fundamental concepts and basic techniques."})]}),e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border-l-4 border-yellow-500`,children:[e.jsx("h3",{className:"font-bold text-yellow-500 mb-2",children:"Intermediate"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"For those with some experience. These challenges require deeper knowledge and more advanced techniques."})]}),e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg border-l-4 border-red-500`,children:[e.jsx("h3",{className:"font-bold text-red-500 mb-2",children:"Advanced"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"For experienced security professionals. These challenges simulate complex, real-world scenarios."})]})]})]})]})})},X=(s,c)=>{switch(s.toLowerCase()){case"beginner":return c?"bg-green-900/30 text-green-400":"bg-green-100 text-green-800";case"intermediate":return c?"bg-yellow-900/30 text-yellow-400":"bg-yellow-100 text-yellow-800";case"advanced":return c?"bg-red-900/30 text-red-400":"bg-red-100 text-red-800";default:return c?"bg-blue-900/30 text-blue-400":"bg-blue-100 text-blue-800"}};export{K as default};
