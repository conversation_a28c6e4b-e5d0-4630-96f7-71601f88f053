import{u as m,r as u,j as e,m as a,aR as x,L as p,z as b,N as g,a3 as y,b4 as v,ac as h,aV as f,l as j,aQ as w,x as N}from"./index-CVvVjHWF.js";import{C}from"./CyberForceSEO-C79kd8_3.js";import"./index-Cc2BstNK.js";const A=()=>{const{darkMode:t}=m(),[s,n]=u.useState("all"),l={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},d={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5}}},c=[{id:"all",name:"All Programs"},{id:"beginner",name:"Beginner"},{id:"intermediate",name:"Intermediate"},{id:"advanced",name:"Advanced"},{id:"certification",name:"Certification Prep"}],r=[{id:"cyber-fundamentals",title:"Cybersecurity Fundamentals",description:"Learn the core concepts and principles of cybersecurity, including threat landscapes, basic security controls, and security frameworks.",icon:e.jsx(g,{}),level:"beginner",duration:"4 weeks",modules:12,isPremium:!1},{id:"network-security",title:"Network Security",description:"Master the techniques to secure networks, detect intrusions, and implement effective network defense strategies.",icon:e.jsx(y,{}),level:"intermediate",duration:"6 weeks",modules:18,isPremium:!0},{id:"web-app-security",title:"Web Application Security",description:"Learn to identify and exploit common web vulnerabilities, and implement secure coding practices.",icon:e.jsx(v,{}),level:"intermediate",duration:"5 weeks",modules:15,isPremium:!0},{id:"cloud-security",title:"Cloud Security",description:"Secure cloud environments across major platforms including AWS, Azure, and Google Cloud.",icon:e.jsx(h,{}),level:"advanced",duration:"8 weeks",modules:24,isPremium:!0},{id:"database-security",title:"Database Security",description:"Protect critical data assets by implementing database security controls and best practices.",icon:e.jsx(f,{}),level:"intermediate",duration:"4 weeks",modules:12,isPremium:!0},{id:"cryptography",title:"Applied Cryptography",description:"Understand encryption algorithms, digital signatures, and secure key management.",icon:e.jsx(j,{}),level:"advanced",duration:"6 weeks",modules:16,isPremium:!0},{id:"penetration-testing",title:"Penetration Testing",description:"Learn ethical hacking techniques to identify and exploit vulnerabilities in systems and networks.",icon:e.jsx(w,{}),level:"advanced",duration:"10 weeks",modules:30,isPremium:!0},{id:"cissp-prep",title:"CISSP Certification Prep",description:"Comprehensive preparation for the Certified Information Systems Security Professional exam.",icon:e.jsx(N,{}),level:"certification",duration:"12 weeks",modules:36,isPremium:!0}],o=s==="all"?r:r.filter(i=>i.level===s);return e.jsxs("div",{className:`min-h-screen ${t?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"}`,children:[e.jsx(C,{title:"CyberForce Programs | Cybersecurity Training",description:"Explore our comprehensive cybersecurity training programs, from beginner fundamentals to advanced penetration testing and certification preparation.",keywords:["cybersecurity training","security programs","CISSP prep","penetration testing","network security"]}),e.jsxs("div",{className:`${t?"bg-[#0F172A]":"bg-blue-900"} text-white py-20 relative overflow-hidden`,children:[e.jsxs("div",{className:"absolute inset-0 opacity-10",children:[e.jsx("div",{className:"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-blue-500/30 to-transparent"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-[#F5B93F]/20 to-transparent"})]}),e.jsx("div",{className:"container mx-auto px-4 relative z-10",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx(a.h1,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5},className:"text-4xl md:text-5xl font-bold mb-4",children:"CyberForce Training Programs"}),e.jsx(a.p,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,delay:.2},className:"text-xl text-gray-200 mb-8",children:"Comprehensive cybersecurity training designed to build real-world skills and advance your career"})]})})]}),e.jsxs("div",{className:"container mx-auto px-4 py-16",children:[e.jsx("div",{className:"mb-12",children:e.jsx("div",{className:"flex flex-wrap justify-center gap-2",children:c.map(i=>e.jsx("button",{onClick:()=>n(i.id),className:`px-4 py-2 rounded-full text-sm font-medium transition-all ${s===i.id?"bg-[#0066cc] text-white":t?"bg-[#1A1F35] text-gray-300 hover:bg-[#1A1F35]/80":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:i.name},i.id))})}),e.jsx(a.div,{variants:l,initial:"hidden",animate:"visible",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:o.map(i=>e.jsxs(a.div,{variants:d,className:`rounded-xl overflow-hidden shadow-lg ${t?"bg-[#1A1F35] border border-gray-800":"bg-white"}`,children:[e.jsx("div",{className:`h-2 ${i.level==="beginner"?"bg-green-500":i.level==="intermediate"?"bg-blue-500":i.level==="advanced"?"bg-purple-500":"bg-yellow-500"}`}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-12 h-12 rounded-lg flex items-center justify-center mr-4 ${i.level==="beginner"?"bg-green-500/10 text-green-500":i.level==="intermediate"?"bg-blue-500/10 text-blue-500":i.level==="advanced"?"bg-purple-500/10 text-purple-500":"bg-yellow-500/10 text-yellow-500"}`,children:e.jsx("span",{className:"text-2xl",children:i.icon})}),e.jsx("h3",{className:"text-xl font-bold",children:i.title})]}),i.isPremium&&e.jsx("div",{className:"bg-[#F5B93F]/10 p-1 rounded",children:e.jsx(x,{className:"text-[#F5B93F]"})})]}),e.jsx("p",{className:`mb-6 ${t?"text-gray-300":"text-gray-600"}`,children:i.description}),e.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[e.jsx("div",{className:`px-3 py-1 rounded-full text-xs ${t?"bg-gray-800":"bg-gray-100"}`,children:i.duration}),e.jsxs("div",{className:`px-3 py-1 rounded-full text-xs ${t?"bg-gray-800":"bg-gray-100"}`,children:[i.modules," modules"]}),e.jsx("div",{className:`px-3 py-1 rounded-full text-xs ${i.level==="beginner"?"bg-green-500/10 text-green-500":i.level==="intermediate"?"bg-blue-500/10 text-blue-500":i.level==="advanced"?"bg-purple-500/10 text-purple-500":"bg-yellow-500/10 text-yellow-500"}`,children:i.level.charAt(0).toUpperCase()+i.level.slice(1)})]}),e.jsxs(p,{to:`/learn/program/${i.id}`,className:`inline-flex items-center px-4 py-2 rounded-lg font-medium ${i.isPremium?"bg-[#0066cc] hover:bg-[#0055aa] text-white":t?"bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:["Explore Program ",e.jsx(b,{className:"ml-2"})]})]})]},i.id))})]})]})};export{A as default};
