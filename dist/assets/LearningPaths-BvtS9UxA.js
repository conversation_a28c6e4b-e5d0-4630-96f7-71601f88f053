import{u as C,j as e,m as k,G as S,bq as F,C as _,N as E,a1 as q,x as $,w as A,b as R,r as d,$ as M,br as z,bs as D}from"./index-CVvVjHWF.js";import{P as s}from"./index-Cc2BstNK.js";import{L as G}from"./LearningLayout-nZy_7HLr.js";import{g as T,d as U}from"./learningApiOverride-effR4Get.js";import{l as H,s as I}from"./learning_paths-C7A2oYL7.js";import"./learning-paths-structure-YI8Fv9cD.js";const P=({path:x,userProgress:r,onClick:h,className:f=""})=>{const{darkMode:a}=C(),{id:v,title:m,description:j,short_description:b,category:n,difficulty:p,estimated_hours:l,modules_count:i,icon:o}=x,c=(r==null?void 0:r.progress_percentage)||0,y=()=>{switch(n){case"fundamentals":return e.jsx($,{className:"text-blue-500"});case"offensive":return e.jsx(q,{className:"text-red-500"});case"defensive":return e.jsx(E,{className:"text-green-500"});default:return e.jsx(_,{className:"text-purple-500"})}},N=()=>{switch(p){case"beginner":return"text-green-500";case"intermediate":return"text-yellow-500";case"advanced":return"text-orange-500";case"expert":return"text-red-500";default:return"text-gray-500"}},t=()=>{switch(n){case"fundamentals":return"text-blue-500";case"offensive":return"text-red-500";case"defensive":return"text-green-500";default:return"text-purple-500"}};return e.jsx(k.div,{className:`rounded-lg overflow-hidden shadow-md cursor-pointer ${a?"bg-gray-800 hover:bg-gray-700":"bg-white hover:bg-gray-50"} transition-colors ${f}`,whileHover:{y:-5,transition:{duration:.2}},onClick:()=>h&&h(v),children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${n==="fundamentals"?"bg-blue-100 dark:bg-blue-900/30":n==="offensive"?"bg-red-100 dark:bg-red-900/30":n==="defensive"?"bg-green-100 dark:bg-green-900/30":"bg-purple-100 dark:bg-purple-900/30"}`,children:o?e.jsx("img",{src:o,alt:m,className:"w-6 h-6"}):y()}),e.jsxs("div",{children:[e.jsx("h3",{className:`text-lg font-semibold ${a?"text-white":"text-gray-800"}`,children:m}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx("span",{className:`capitalize ${t()}`,children:n}),e.jsx("span",{className:`mx-2 ${a?"text-gray-400":"text-gray-500"}`,children:"•"}),e.jsx("span",{className:`capitalize ${N()}`,children:p})]})]})]}),e.jsx("p",{className:`text-sm mb-4 line-clamp-2 ${a?"text-gray-300":"text-gray-600"}`,children:b||j}),e.jsx("div",{className:`text-sm mb-4 ${a?"text-gray-400":"text-gray-500"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(S,{className:"mr-1"}),e.jsxs("span",{children:[l," Hours"]}),i&&e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"mx-2",children:"•"}),e.jsxs("span",{children:[i," Modules"]})]})]})}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:a?"text-gray-300":"text-gray-600",children:"Progress"}),e.jsxs("span",{className:"text-blue-500",children:[c,"%"]})]}),e.jsx(F,{value:c,size:"md",color:"primary",showLabel:!1,animate:!1})]}),e.jsx("button",{className:`mt-4 w-full py-2 px-4 rounded-md transition-colors ${c>0?"bg-blue-500 hover:bg-blue-600 text-white":a?"bg-gray-700 hover:bg-gray-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"}`,children:c>0?"Continue":"Enroll Now"})]})})};P.propTypes={path:s.shape({id:s.string.isRequired,title:s.string.isRequired,description:s.string.isRequired,short_description:s.string,category:s.string.isRequired,difficulty:s.string.isRequired,estimated_hours:s.number,modules_count:s.number,icon:s.string}).isRequired,userProgress:s.shape({progress_percentage:s.number}),onClick:s.func,className:s.string};const W=()=>{const{user:x}=A(),{darkMode:r}=C(),h=R(),[f,a]=d.useState([]),[v,m]=d.useState({}),[j,b]=d.useState(!0),[n,p]=d.useState(null),[l,i]=d.useState("all"),[o,c]=d.useState("");d.useEffect(()=>{(async()=>{try{b(!0);let g=[],u={};try{g=await T(),x&&(u=await U(x.id))}catch(L){console.warn("API fetch failed, using seed data:",L),g=H,x&&I.path_progress.forEach(w=>{u[w.learning_path_id]=w})}a(g),m(u)}catch(g){console.error("Error fetching learning paths:",g),p("Failed to load learning paths. Please try again later.")}finally{b(!1)}})()},[x]);const y=f.filter(t=>{const g=l==="all"||t.category===l,u=!o||t.title.toLowerCase().includes(o.toLowerCase())||t.description.toLowerCase().includes(o.toLowerCase());return g&&u}),N=t=>{h(`/learning-paths/${t}`)};return e.jsxs(G,{children:[e.jsx("h1",{className:`text-3xl font-bold mb-6 ${r?"text-white":"text-gray-800"}`,children:"Learning Paths"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:[e.jsxs("div",{className:"relative w-full md:w-96",children:[e.jsx("input",{type:"text",placeholder:"Search learning paths...",className:`w-full pl-10 pr-4 py-2 rounded-lg ${r?"bg-gray-700 text-white border-gray-600 focus:border-blue-500":"bg-white text-gray-800 border-gray-300 focus:border-blue-500"} border focus:outline-none focus:ring-2 focus:ring-blue-500`,value:o,onChange:t=>c(t.target.value)}),e.jsx(M,{className:`absolute left-3 top-1/2 transform -translate-y-1/2 ${r?"text-gray-400":"text-gray-500"}`})]}),e.jsxs("div",{className:"flex space-x-2 overflow-x-auto pb-2",children:[e.jsx("button",{className:`px-4 py-2 rounded-lg transition-colors ${l==="all"?"bg-blue-500 text-white":r?"bg-gray-700 text-gray-300 hover:bg-gray-600":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,onClick:()=>i("all"),children:"All Paths"}),e.jsx("button",{className:`px-4 py-2 rounded-lg transition-colors ${l==="fundamentals"?"bg-blue-500 text-white":r?"bg-gray-700 text-gray-300 hover:bg-gray-600":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,onClick:()=>i("fundamentals"),children:"Fundamentals"}),e.jsx("button",{className:`px-4 py-2 rounded-lg transition-colors ${l==="offensive"?"bg-blue-500 text-white":r?"bg-gray-700 text-gray-300 hover:bg-gray-600":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,onClick:()=>i("offensive"),children:"Offensive"}),e.jsx("button",{className:`px-4 py-2 rounded-lg transition-colors ${l==="defensive"?"bg-blue-500 text-white":r?"bg-gray-700 text-gray-300 hover:bg-gray-600":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,onClick:()=>i("defensive"),children:"Defensive"}),e.jsx("button",{className:`px-4 py-2 rounded-lg transition-colors ${l==="general"?"bg-blue-500 text-white":r?"bg-gray-700 text-gray-300 hover:bg-gray-600":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,onClick:()=>i("general"),children:"General"})]})]})}),j?e.jsx("div",{className:"flex justify-center items-center py-20",children:e.jsx(z,{size:"lg"})}):n?e.jsx("div",{className:`p-6 rounded-lg ${r?"bg-red-900/20 text-red-200":"bg-red-100 text-red-700"}`,children:e.jsx("p",{children:n})}):y.length===0?e.jsx(D,{icon:e.jsx($,{className:"text-5xl"}),title:"No learning paths found",description:o?`No results for "${o}"`:"No learning paths available for this filter",actionText:"Clear filters",onAction:()=>{c(""),i("all")}}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(t=>e.jsx(P,{path:t,userProgress:v[t.id],onClick:N},t.id))})]})};export{W as default};
