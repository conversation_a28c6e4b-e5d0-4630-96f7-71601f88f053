import{u as i,j as e,m as n,t as o,F as c,z as x}from"./index-CVvVjHWF.js";const m=({path:t,isOpen:s,onClose:l,onStart:r})=>{const{darkMode:a}=i();return!s||!t?null:e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50",children:e.jsxs(n.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:`
          relative w-full max-w-md rounded-lg shadow-xl overflow-hidden
          ${a?"bg-gray-800 text-white":"bg-white text-gray-800"}
        `,children:[e.jsx("button",{onClick:l,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors","aria-label":"Close",children:e.jsx(o,{})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx(c,{className:"text-4xl text-primary"})}),e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Ready to Start Learning"}),e.jsxs("p",{className:`${a?"text-gray-300":"text-gray-600"}`,children:["You're now enrolled in the ",t.title," learning path. Click the button below to start exploring the modules."]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsxs("button",{onClick:r,className:`
                px-6 py-2 bg-primary hover:bg-primary-hover text-black font-medium
                rounded-lg flex items-center transition-colors
              `,children:["View Modules ",e.jsx(x,{className:"ml-2"})]})})]})]})})};export{m as default};
