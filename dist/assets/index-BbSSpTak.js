const i={id:"bb-1",pathId:"bug-bounty",title:"Introduction to Bug Bounty Hunting",description:"Learn the fundamentals of bug bounty hunting, including platforms, methodologies, and ethical considerations.",objectives:["Understand bug bounty programs and their purpose","Learn about major bug bounty platforms","Master ethical hacking principles","Understand legal and responsible disclosure","Learn bug bounty hunting methodologies","Develop a professional mindset for security research"],difficulty:"Beginner",estimatedTime:90,sections:[{title:"What is Bug Bounty Hunting?",content:`
        <h2>What is Bug Bounty Hunting?</h2>
        <p>Bug bounty hunting is the practice of finding and reporting security vulnerabilities in applications, websites, and systems in exchange for rewards.</p>
        
        <h3>Key Concepts</h3>
        <ul>
          <li><strong>Vulnerability Research:</strong> Systematic search for security flaws</li>
          <li><strong>Responsible Disclosure:</strong> Ethical reporting of vulnerabilities</li>
          <li><strong>Reward Programs:</strong> Financial incentives for valid findings</li>
          <li><strong>Community Collaboration:</strong> Learning and sharing knowledge</li>
        </ul>
        
        <h3>Benefits of Bug Bounty Programs</h3>
        <ul>
          <li><strong>For Organizations:</strong>
            <ul>
              <li>Cost-effective security testing</li>
              <li>Access to diverse security expertise</li>
              <li>Continuous security assessment</li>
              <li>Improved security posture</li>
            </ul>
          </li>
          <li><strong>For Researchers:</strong>
            <ul>
              <li>Financial rewards for findings</li>
              <li>Skill development and learning</li>
              <li>Recognition in security community</li>
              <li>Career advancement opportunities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Types of Bug Bounty Programs</h3>
        <ul>
          <li><strong>Public Programs:</strong> Open to all researchers</li>
          <li><strong>Private Programs:</strong> Invitation-only programs</li>
          <li><strong>Continuous Programs:</strong> Always accepting submissions</li>
          <li><strong>Time-Limited Programs:</strong> Specific duration contests</li>
        </ul>
      `,type:"text"},{title:"Bug Bounty Platforms",content:`
        <h2>Major Bug Bounty Platforms</h2>
        <p>Several platforms connect security researchers with organizations running bug bounty programs.</p>
        
        <h3>Popular Platforms</h3>
        <ul>
          <li><strong>HackerOne:</strong>
            <ul>
              <li>Largest bug bounty platform</li>
              <li>Wide variety of programs</li>
              <li>Strong community features</li>
              <li>Comprehensive reporting tools</li>
            </ul>
          </li>
          <li><strong>Bugcrowd:</strong>
            <ul>
              <li>Crowdsourced security platform</li>
              <li>Focus on continuous testing</li>
              <li>Advanced analytics and insights</li>
              <li>Researcher skill development</li>
            </ul>
          </li>
          <li><strong>Synack:</strong>
            <ul>
              <li>Invitation-only platform</li>
              <li>Vetted researcher community</li>
              <li>High-value targets</li>
              <li>Advanced testing tools</li>
            </ul>
          </li>
          <li><strong>Intigriti:</strong>
            <ul>
              <li>European-focused platform</li>
              <li>Strong compliance features</li>
              <li>Educational resources</li>
              <li>Community events</li>
            </ul>
          </li>
        </ul>
        
        <h3>Platform Selection Criteria</h3>
        <ul>
          <li>Program variety and quality</li>
          <li>Payment terms and reliability</li>
          <li>Community and support</li>
          <li>Tools and resources provided</li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary purpose of responsible disclosure in bug bounty hunting?",options:["To maximize financial rewards","To gain public recognition","To ethically report vulnerabilities without causing harm","To compete with other researchers"],correctAnswer:2,explanation:"Responsible disclosure ensures that vulnerabilities are reported ethically to help organizations fix security issues without causing harm or exposing users to risk."}]},type:"quiz"}]},e={id:"bb-2",pathId:"bug-bounty",title:"Ethical Hacking Fundamentals",description:"Master the principles and practices of ethical hacking, including legal frameworks, responsible disclosure, and professional conduct in security research.",objectives:["Understand the principles and ethics of ethical hacking","Learn legal frameworks and compliance requirements","Master responsible disclosure processes and timelines","Understand the difference between ethical and malicious hacking","Learn professional conduct and industry standards","Develop a security researcher mindset and methodology"],difficulty:"Beginner",estimatedTime:95,sections:[{title:"Ethical Hacking Principles",content:`
        <h2>Ethical Hacking Principles and Philosophy</h2>
        <p>Ethical hacking is the practice of intentionally probing systems and networks for vulnerabilities in a legal and responsible manner to improve security.</p>
        
        <h3>Core Principles of Ethical Hacking</h3>
        <ul>
          <li><strong>Authorization:</strong>
            <ul>
              <li>Always obtain explicit permission before testing</li>
              <li>Respect scope and boundaries defined by the organization</li>
              <li>Document authorization and maintain proof</li>
              <li>Never exceed authorized testing parameters</li>
            </ul>
          </li>
          <li><strong>Responsible Disclosure:</strong>
            <ul>
              <li>Report vulnerabilities to the appropriate parties</li>
              <li>Allow reasonable time for remediation</li>
              <li>Protect sensitive information during the process</li>
              <li>Follow established disclosure timelines</li>
            </ul>
          </li>
          <li><strong>Do No Harm:</strong>
            <ul>
              <li>Minimize impact on systems and users</li>
              <li>Avoid data destruction or service disruption</li>
              <li>Use least intrusive testing methods</li>
              <li>Respect privacy and confidentiality</li>
            </ul>
          </li>
          <li><strong>Professional Integrity:</strong>
            <ul>
              <li>Maintain honesty and transparency</li>
              <li>Avoid conflicts of interest</li>
              <li>Respect intellectual property</li>
              <li>Uphold professional standards</li>
            </ul>
          </li>
        </ul>
        
        <h3>Ethical vs. Malicious Hacking</h3>
        <div class="comparison-table">
          <h4>Ethical Hacking:</h4>
          <ul>
            <li>Authorized and legal testing</li>
            <li>Constructive intent to improve security</li>
            <li>Responsible disclosure of findings</li>
            <li>Respect for privacy and data</li>
            <li>Professional conduct and standards</li>
            <li>Collaboration with organizations</li>
          </ul>
          
          <h4>Malicious Hacking:</h4>
          <ul>
            <li>Unauthorized access and illegal activities</li>
            <li>Intent to cause harm or gain unauthorized benefit</li>
            <li>Exploitation without disclosure</li>
            <li>Data theft or destruction</li>
            <li>Disregard for legal and ethical boundaries</li>
            <li>Adversarial relationship with targets</li>
          </ul>
        </div>
        
        <h3>The Security Researcher Mindset</h3>
        <ul>
          <li><strong>Curiosity and Persistence:</strong>
            <ul>
              <li>Question assumptions and explore edge cases</li>
              <li>Persistent investigation of potential vulnerabilities</li>
              <li>Creative thinking and problem-solving</li>
              <li>Continuous learning and skill development</li>
            </ul>
          </li>
          <li><strong>Systematic Approach:</strong>
            <ul>
              <li>Methodical testing and documentation</li>
              <li>Reproducible proof-of-concept development</li>
              <li>Risk assessment and impact analysis</li>
              <li>Clear communication of findings</li>
            </ul>
          </li>
          <li><strong>Empathy and Collaboration:</strong>
            <ul>
              <li>Understanding organizational constraints</li>
              <li>Constructive feedback and recommendations</li>
              <li>Patience with remediation timelines</li>
              <li>Building trust with security teams</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Legal Frameworks and Compliance",content:`
        <h2>Legal Frameworks and Compliance</h2>
        <p>Understanding the legal landscape is crucial for ethical hackers to operate within legal boundaries and avoid criminal liability.</p>
        
        <h3>Key Legal Considerations</h3>
        <ul>
          <li><strong>Computer Fraud and Abuse Act (CFAA) - United States:</strong>
            <ul>
              <li>Federal law governing computer crimes</li>
              <li>Prohibits unauthorized access to protected computers</li>
              <li>Severe penalties for violations</li>
              <li>Importance of explicit authorization</li>
            </ul>
          </li>
          <li><strong>General Data Protection Regulation (GDPR) - European Union:</strong>
            <ul>
              <li>Data protection and privacy regulations</li>
              <li>Requirements for handling personal data</li>
              <li>Consent and lawful basis for processing</li>
              <li>Data subject rights and obligations</li>
            </ul>
          </li>
          <li><strong>International Laws and Jurisdictions:</strong>
            <ul>
              <li>Varying laws across different countries</li>
              <li>Cross-border legal implications</li>
              <li>Extradition and international cooperation</li>
              <li>Local compliance requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Authorization and Scope</h3>
        <ul>
          <li><strong>Written Authorization:</strong>
            <ul>
              <li>Formal written permission from authorized personnel</li>
              <li>Clear definition of scope and boundaries</li>
              <li>Specific systems and applications included</li>
              <li>Time limitations and testing windows</li>
            </ul>
          </li>
          <li><strong>Bug Bounty Program Terms:</strong>
            <ul>
              <li>Program-specific rules and guidelines</li>
              <li>Scope definition and exclusions</li>
              <li>Prohibited activities and testing methods</li>
              <li>Reporting requirements and timelines</li>
            </ul>
          </li>
          <li><strong>Safe Harbor Provisions:</strong>
            <ul>
              <li>Legal protection for good faith security research</li>
              <li>Compliance with program terms and conditions</li>
              <li>Responsible disclosure requirements</li>
              <li>Limitations and exceptions</li>
            </ul>
          </li>
        </ul>
        
        <h3>Professional Standards and Certifications</h3>
        <ul>
          <li><strong>Industry Certifications:</strong>
            <ul>
              <li>Certified Ethical Hacker (CEH)</li>
              <li>Offensive Security Certified Professional (OSCP)</li>
              <li>GIAC Penetration Tester (GPEN)</li>
              <li>Certified Information Systems Security Professional (CISSP)</li>
            </ul>
          </li>
          <li><strong>Professional Organizations:</strong>
            <ul>
              <li>International Association of Computer Security Professionals (IACSP)</li>
              <li>Information Systems Security Association (ISSA)</li>
              <li>SANS Institute and community</li>
              <li>Local security professional groups</li>
            </ul>
          </li>
          <li><strong>Codes of Ethics:</strong>
            <ul>
              <li>Professional conduct standards</li>
              <li>Confidentiality and integrity requirements</li>
              <li>Continuing education obligations</li>
              <li>Peer accountability and reporting</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Responsible Disclosure Process",content:`
        <h2>Responsible Disclosure Process</h2>
        <p>Responsible disclosure is a cornerstone of ethical hacking, ensuring that vulnerabilities are reported and remediated in a way that protects users while giving organizations time to fix issues.</p>
        
        <h3>Disclosure Timeline and Process</h3>
        <ul>
          <li><strong>Initial Discovery and Validation:</strong>
            <ul>
              <li>Confirm the vulnerability exists and is reproducible</li>
              <li>Assess the potential impact and severity</li>
              <li>Document the vulnerability with clear evidence</li>
              <li>Avoid unnecessary exploitation or data access</li>
            </ul>
          </li>
          <li><strong>Initial Report (Day 0):</strong>
            <ul>
              <li>Submit detailed vulnerability report</li>
              <li>Include proof-of-concept and reproduction steps</li>
              <li>Provide impact assessment and recommendations</li>
              <li>Request acknowledgment and timeline for response</li>
            </ul>
          </li>
          <li><strong>Vendor Response (Day 1-7):</strong>
            <ul>
              <li>Acknowledgment of report receipt</li>
              <li>Initial triage and validation</li>
              <li>Assignment of tracking identifier</li>
              <li>Communication of remediation timeline</li>
            </ul>
          </li>
          <li><strong>Remediation Period (Day 7-90):</strong>
            <ul>
              <li>Vendor develops and tests fixes</li>
              <li>Regular communication and status updates</li>
              <li>Researcher cooperation and additional testing</li>
              <li>Coordination of disclosure timeline</li>
            </ul>
          </li>
        </ul>
        
        <h3>Disclosure Best Practices</h3>
        <ul>
          <li><strong>Clear Communication:</strong>
            <ul>
              <li>Professional and respectful tone</li>
              <li>Technical accuracy and completeness</li>
              <li>Clear impact assessment</li>
              <li>Constructive remediation suggestions</li>
            </ul>
          </li>
          <li><strong>Documentation Standards:</strong>
            <ul>
              <li>Detailed vulnerability description</li>
              <li>Step-by-step reproduction instructions</li>
              <li>Screenshots and proof-of-concept code</li>
              <li>Risk assessment and business impact</li>
            </ul>
          </li>
          <li><strong>Coordination and Patience:</strong>
            <ul>
              <li>Reasonable remediation timelines</li>
              <li>Flexibility for complex fixes</li>
              <li>Coordination with multiple stakeholders</li>
              <li>Respect for organizational processes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Public Disclosure Considerations</h3>
        <ul>
          <li><strong>Full Disclosure:</strong>
            <ul>
              <li>Complete technical details after remediation</li>
              <li>Educational value for security community</li>
              <li>Transparency and accountability</li>
              <li>Risk of exploitation by malicious actors</li>
            </ul>
          </li>
          <li><strong>Coordinated Disclosure:</strong>
            <ul>
              <li>Collaboration between researcher and vendor</li>
              <li>Agreed-upon disclosure timeline</li>
              <li>Balanced approach to transparency and security</li>
              <li>Joint public disclosure when appropriate</li>
            </ul>
          </li>
          <li><strong>Responsible Timing:</strong>
            <ul>
              <li>Allow sufficient time for remediation</li>
              <li>Consider user impact and exposure</li>
              <li>Coordinate with security advisories</li>
              <li>Respect vendor communication preferences</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the most important prerequisite before conducting any security testing?",options:["Having the right tools","Obtaining explicit written authorization","Understanding the technology","Having sufficient time"],correctAnswer:1,explanation:"Explicit written authorization is the most critical prerequisite for ethical hacking. Without proper authorization, security testing could be illegal and result in criminal charges."},{question:"What is the typical timeline for initial vendor response in responsible disclosure?",options:["Within 24 hours","Within 1-7 days","Within 30 days","Within 90 days"],correctAnswer:1,explanation:"Vendors typically acknowledge vulnerability reports within 1-7 days, providing initial triage and validation of the reported issue."},{question:"Which principle best describes the 'Do No Harm' concept in ethical hacking?",options:["Never test production systems","Only use automated tools","Minimize impact on systems and users while testing","Always report vulnerabilities publicly"],correctAnswer:2,explanation:"'Do No Harm' means minimizing impact on systems and users during testing, avoiding data destruction or service disruption while still conducting effective security research."}]},type:"quiz"}]},t={id:"bb-3",pathId:"bug-bounty",title:"Web Application Security Fundamentals",description:"Master web application security concepts, architecture, and common vulnerabilities. Learn how web applications work and where security weaknesses typically occur.",objectives:["Understand web application architecture and components","Learn HTTP protocol and security implications","Master client-server communication security","Understand authentication and session management","Learn input validation and output encoding principles","Identify common web application attack vectors"],difficulty:"Beginner",estimatedTime:105,sections:[{title:"Web Application Architecture",content:`
        <h2>Web Application Architecture and Security</h2>
        <p>Understanding web application architecture is fundamental to identifying security vulnerabilities and implementing effective security measures.</p>
        
        <h3>Web Application Components</h3>
        <ul>
          <li><strong>Client-Side Components:</strong>
            <ul>
              <li>Web browsers and user agents</li>
              <li>HTML, CSS, and JavaScript</li>
              <li>Client-side frameworks (React, Angular, Vue)</li>
              <li>Mobile applications and APIs</li>
            </ul>
          </li>
          <li><strong>Server-Side Components:</strong>
            <ul>
              <li>Web servers (Apache, Nginx, IIS)</li>
              <li>Application servers and runtime environments</li>
              <li>Server-side languages (PHP, Python, Java, .NET)</li>
              <li>Frameworks and libraries</li>
            </ul>
          </li>
          <li><strong>Database Layer:</strong>
            <ul>
              <li>Relational databases (MySQL, PostgreSQL, SQL Server)</li>
              <li>NoSQL databases (MongoDB, Redis, Elasticsearch)</li>
              <li>Database management systems</li>
              <li>Data access layers and ORMs</li>
            </ul>
          </li>
          <li><strong>Infrastructure Components:</strong>
            <ul>
              <li>Load balancers and reverse proxies</li>
              <li>Content delivery networks (CDNs)</li>
              <li>Firewalls and security appliances</li>
              <li>Cloud services and containers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Web Application Security Models</h3>
        <ul>
          <li><strong>Same-Origin Policy:</strong>
            <ul>
              <li>Fundamental browser security mechanism</li>
              <li>Restricts cross-origin resource access</li>
              <li>Origin defined by protocol, domain, and port</li>
              <li>Exceptions and bypass mechanisms</li>
            </ul>
          </li>
          <li><strong>Content Security Policy (CSP):</strong>
            <ul>
              <li>HTTP header-based security mechanism</li>
              <li>Controls resource loading and execution</li>
              <li>Prevents XSS and data injection attacks</li>
              <li>Policy directives and implementation</li>
            </ul>
          </li>
          <li><strong>Cross-Origin Resource Sharing (CORS):</strong>
            <ul>
              <li>Mechanism for controlled cross-origin access</li>
              <li>Preflight requests and headers</li>
              <li>Security implications and misconfigurations</li>
              <li>Best practices for implementation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Architecture Patterns</h3>
        <ul>
          <li><strong>Multi-Tier Architecture:</strong>
            <ul>
              <li>Presentation, business logic, and data tiers</li>
              <li>Separation of concerns and security boundaries</li>
              <li>Trust boundaries and validation points</li>
              <li>Attack surface considerations</li>
            </ul>
          </li>
          <li><strong>Microservices Architecture:</strong>
            <ul>
              <li>Distributed services and APIs</li>
              <li>Service-to-service communication</li>
              <li>Authentication and authorization challenges</li>
              <li>Network security and service mesh</li>
            </ul>
          </li>
          <li><strong>Single Page Applications (SPAs):</strong>
            <ul>
              <li>Client-side rendering and routing</li>
              <li>API-driven architecture</li>
              <li>Token-based authentication</li>
              <li>Client-side security considerations</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"HTTP Protocol and Security",content:`
        <h2>HTTP Protocol and Security Implications</h2>
        <p>The HTTP protocol is the foundation of web communication, and understanding its security features and limitations is crucial for web application security.</p>
        
        <h3>HTTP Protocol Fundamentals</h3>
        <ul>
          <li><strong>HTTP Methods:</strong>
            <ul>
              <li>GET, POST, PUT, DELETE, PATCH, OPTIONS</li>
              <li>Idempotent vs. non-idempotent methods</li>
              <li>Security implications of different methods</li>
              <li>Method override and security bypass</li>
            </ul>
          </li>
          <li><strong>HTTP Headers:</strong>
            <ul>
              <li>Request and response headers</li>
              <li>Security-related headers</li>
              <li>Custom headers and information disclosure</li>
              <li>Header injection vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Status Codes:</strong>
            <ul>
              <li>1xx Informational, 2xx Success, 3xx Redirection</li>
              <li>4xx Client Error, 5xx Server Error</li>
              <li>Security implications of status codes</li>
              <li>Information leakage through error messages</li>
            </ul>
          </li>
        </ul>
        
        <h3>HTTPS and Transport Security</h3>
        <ul>
          <li><strong>TLS/SSL Encryption:</strong>
            <ul>
              <li>Encryption of data in transit</li>
              <li>Certificate validation and trust</li>
              <li>Perfect Forward Secrecy (PFS)</li>
              <li>TLS version and cipher suite security</li>
            </ul>
          </li>
          <li><strong>HTTP Strict Transport Security (HSTS):</strong>
            <ul>
              <li>Enforces HTTPS connections</li>
              <li>Prevents protocol downgrade attacks</li>
              <li>Preload lists and browser support</li>
              <li>Implementation best practices</li>
            </ul>
          </li>
          <li><strong>Certificate Security:</strong>
            <ul>
              <li>Certificate authorities and trust chains</li>
              <li>Certificate pinning and validation</li>
              <li>Extended Validation (EV) certificates</li>
              <li>Certificate transparency and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Headers</h3>
        <ul>
          <li><strong>Content Security Policy (CSP):</strong>
            <ul>
              <li>Controls resource loading and execution</li>
              <li>Prevents XSS and injection attacks</li>
              <li>Policy directives and nonce/hash usage</li>
              <li>Reporting and monitoring violations</li>
            </ul>
          </li>
          <li><strong>X-Frame-Options:</strong>
            <ul>
              <li>Prevents clickjacking attacks</li>
              <li>Controls iframe embedding</li>
              <li>DENY, SAMEORIGIN, ALLOW-FROM values</li>
              <li>Frame-ancestors CSP directive</li>
            </ul>
          </li>
          <li><strong>X-Content-Type-Options:</strong>
            <ul>
              <li>Prevents MIME type sniffing</li>
              <li>nosniff directive</li>
              <li>Content type confusion attacks</li>
              <li>File upload security implications</li>
            </ul>
          </li>
          <li><strong>Referrer Policy:</strong>
            <ul>
              <li>Controls referrer information leakage</li>
              <li>Privacy and security implications</li>
              <li>Policy values and browser support</li>
              <li>Cross-origin referrer handling</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Authentication and Session Management",content:`
        <h2>Authentication and Session Management Security</h2>
        <p>Proper authentication and session management are critical for web application security, controlling user access and maintaining secure user sessions.</p>
        
        <h3>Authentication Mechanisms</h3>
        <ul>
          <li><strong>Password-Based Authentication:</strong>
            <ul>
              <li>Username and password validation</li>
              <li>Password strength requirements</li>
              <li>Secure password storage (hashing and salting)</li>
              <li>Password reset and recovery mechanisms</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication (MFA):</strong>
            <ul>
              <li>Something you know, have, and are</li>
              <li>TOTP, SMS, and hardware tokens</li>
              <li>Biometric authentication</li>
              <li>Backup codes and recovery methods</li>
            </ul>
          </li>
          <li><strong>Token-Based Authentication:</strong>
            <ul>
              <li>JSON Web Tokens (JWT)</li>
              <li>OAuth 2.0 and OpenID Connect</li>
              <li>API keys and bearer tokens</li>
              <li>Token validation and expiration</li>
            </ul>
          </li>
          <li><strong>Certificate-Based Authentication:</strong>
            <ul>
              <li>Client certificates and mutual TLS</li>
              <li>Smart cards and hardware tokens</li>
              <li>Certificate validation and revocation</li>
              <li>PKI infrastructure requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Session Management</h3>
        <ul>
          <li><strong>Session Lifecycle:</strong>
            <ul>
              <li>Session creation and initialization</li>
              <li>Session maintenance and renewal</li>
              <li>Session termination and cleanup</li>
              <li>Concurrent session handling</li>
            </ul>
          </li>
          <li><strong>Session Storage:</strong>
            <ul>
              <li>Server-side session storage</li>
              <li>Database and in-memory storage</li>
              <li>Distributed session management</li>
              <li>Session clustering and replication</li>
            </ul>
          </li>
          <li><strong>Session Security:</strong>
            <ul>
              <li>Session ID generation and entropy</li>
              <li>Session fixation prevention</li>
              <li>Session hijacking protection</li>
              <li>Secure cookie attributes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Authentication Vulnerabilities</h3>
        <ul>
          <li><strong>Broken Authentication:</strong>
            <ul>
              <li>Weak password policies</li>
              <li>Credential stuffing and brute force</li>
              <li>Session management flaws</li>
              <li>Authentication bypass vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Session Attacks:</strong>
            <ul>
              <li>Session fixation attacks</li>
              <li>Session hijacking and sidejacking</li>
              <li>Cross-site request forgery (CSRF)</li>
              <li>Session replay attacks</li>
            </ul>
          </li>
          <li><strong>Authorization Flaws:</strong>
            <ul>
              <li>Privilege escalation</li>
              <li>Insecure direct object references</li>
              <li>Missing function-level access control</li>
              <li>Business logic bypass</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary purpose of the Same-Origin Policy in web browsers?",options:["To improve website performance","To restrict cross-origin resource access for security","To enable cross-domain communication","To manage browser cookies"],correctAnswer:1,explanation:"The Same-Origin Policy is a fundamental browser security mechanism that restricts cross-origin resource access to prevent malicious websites from accessing sensitive data from other origins."},{question:"Which HTTP security header helps prevent clickjacking attacks?",options:["Content-Security-Policy","X-Frame-Options","X-Content-Type-Options","Strict-Transport-Security"],correctAnswer:1,explanation:"X-Frame-Options header prevents clickjacking attacks by controlling whether a page can be embedded in an iframe, with values like DENY or SAMEORIGIN."},{question:"What is the most secure way to store passwords in a web application?",options:["Plain text storage","Simple MD5 hashing","Salted and hashed using strong algorithms like bcrypt","Base64 encoding"],correctAnswer:2,explanation:"Passwords should be stored using strong hashing algorithms like bcrypt, scrypt, or Argon2 with unique salts to protect against rainbow table and brute force attacks."}]},type:"quiz"}]},n={id:"bb-4",pathId:"bug-bounty",title:"OWASP Top 10 Vulnerabilities",description:"Master the OWASP Top 10 web application security risks, understanding how to identify, exploit, and report these critical vulnerabilities in bug bounty programs.",objectives:["Understand the OWASP Top 10 framework and methodology","Learn to identify and exploit injection vulnerabilities","Master authentication and session management flaws","Develop skills in finding security misconfigurations","Learn to exploit broken access controls","Create comprehensive vulnerability reports for each category"],difficulty:"Intermediate",estimatedTime:120,sections:[{title:"OWASP Top 10 Framework",content:`
        <h2>OWASP Top 10 Web Application Security Risks</h2>
        <p>The OWASP Top 10 represents the most critical web application security risks, providing a foundation for bug bounty hunters to focus their efforts on high-impact vulnerabilities.</p>
        
        <h3>OWASP Top 10 2021 Categories</h3>
        <ul>
          <li><strong>A01:2021 - Broken Access Control:</strong>
            <ul>
              <li>Vertical privilege escalation</li>
              <li>Horizontal privilege escalation</li>
              <li>Insecure direct object references (IDOR)</li>
              <li>Missing function-level access controls</li>
            </ul>
          </li>
          <li><strong>A02:2021 - Cryptographic Failures:</strong>
            <ul>
              <li>Weak encryption algorithms</li>
              <li>Improper key management</li>
              <li>Data transmission without encryption</li>
              <li>Weak random number generation</li>
            </ul>
          </li>
          <li><strong>A03:2021 - Injection:</strong>
            <ul>
              <li>SQL injection (SQLi)</li>
              <li>Cross-site scripting (XSS)</li>
              <li>Command injection</li>
              <li>LDAP and NoSQL injection</li>
            </ul>
          </li>
          <li><strong>A04:2021 - Insecure Design:</strong>
            <ul>
              <li>Missing security controls</li>
              <li>Ineffective control design</li>
              <li>Threat modeling failures</li>
              <li>Secure design pattern violations</li>
            </ul>
          </li>
          <li><strong>A05:2021 - Security Misconfiguration:</strong>
            <ul>
              <li>Default configurations</li>
              <li>Incomplete configurations</li>
              <li>Open cloud storage</li>
              <li>Verbose error messages</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remaining OWASP Top 10 Categories</h3>
        <ul>
          <li><strong>A06:2021 - Vulnerable and Outdated Components:</strong>
            <ul>
              <li>Outdated software versions</li>
              <li>Vulnerable dependencies</li>
              <li>Unsupported components</li>
              <li>Missing security patches</li>
            </ul>
          </li>
          <li><strong>A07:2021 - Identification and Authentication Failures:</strong>
            <ul>
              <li>Weak password policies</li>
              <li>Session management flaws</li>
              <li>Credential stuffing vulnerabilities</li>
              <li>Missing multi-factor authentication</li>
            </ul>
          </li>
          <li><strong>A08:2021 - Software and Data Integrity Failures:</strong>
            <ul>
              <li>Unsigned software updates</li>
              <li>Insecure CI/CD pipelines</li>
              <li>Auto-update without verification</li>
              <li>Serialization vulnerabilities</li>
            </ul>
          </li>
          <li><strong>A09:2021 - Security Logging and Monitoring Failures:</strong>
            <ul>
              <li>Insufficient logging</li>
              <li>Missing alerting mechanisms</li>
              <li>Inadequate incident response</li>
              <li>Log tampering vulnerabilities</li>
            </ul>
          </li>
          <li><strong>A10:2021 - Server-Side Request Forgery (SSRF):</strong>
            <ul>
              <li>Internal network access</li>
              <li>Cloud metadata exploitation</li>
              <li>Port scanning and service discovery</li>
              <li>Blind SSRF techniques</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Injection Vulnerabilities",content:`
        <h2>Injection Vulnerability Hunting</h2>
        <p>Injection flaws occur when untrusted data is sent to an interpreter as part of a command or query, allowing attackers to execute unintended commands or access data.</p>
        
        <h3>SQL Injection (SQLi)</h3>
        <ul>
          <li><strong>Detection Techniques:</strong>
            <ul>
              <li>Error-based SQL injection</li>
              <li>Boolean-based blind SQLi</li>
              <li>Time-based blind SQLi</li>
              <li>Union-based SQLi</li>
            </ul>
          </li>
          <li><strong>Common Injection Points:</strong>
            <ul>
              <li>URL parameters and form inputs</li>
              <li>HTTP headers and cookies</li>
              <li>JSON and XML data</li>
              <li>File upload functionality</li>
            </ul>
          </li>
          <li><strong>Advanced SQLi Techniques:</strong>
            <ul>
              <li>Second-order SQL injection</li>
              <li>NoSQL injection attacks</li>
              <li>Stored procedure exploitation</li>
              <li>Database-specific payloads</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cross-Site Scripting (XSS)</h3>
        <ul>
          <li><strong>XSS Types:</strong>
            <ul>
              <li>Reflected XSS (Non-persistent)</li>
              <li>Stored XSS (Persistent)</li>
              <li>DOM-based XSS</li>
              <li>Blind XSS</li>
            </ul>
          </li>
          <li><strong>XSS Discovery Methods:</strong>
            <ul>
              <li>Manual payload testing</li>
              <li>Automated scanning tools</li>
              <li>Source code analysis</li>
              <li>Browser developer tools</li>
            </ul>
          </li>
          <li><strong>Bypass Techniques:</strong>
            <ul>
              <li>Filter evasion methods</li>
              <li>Encoding and obfuscation</li>
              <li>Context-specific payloads</li>
              <li>WAF bypass techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Command Injection</h3>
        <ul>
          <li><strong>OS Command Injection:</strong>
            <ul>
              <li>Direct command execution</li>
              <li>Command chaining techniques</li>
              <li>Blind command injection</li>
              <li>Time-based detection</li>
            </ul>
          </li>
          <li><strong>Code Injection:</strong>
            <ul>
              <li>Server-side template injection</li>
              <li>Expression language injection</li>
              <li>PHP code injection</li>
              <li>Python/Ruby code injection</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Access Control and Authentication Flaws",content:`
        <h2>Access Control and Authentication Vulnerabilities</h2>
        <p>Broken access control and authentication failures are among the most critical vulnerabilities, often leading to complete system compromise.</p>
        
        <h3>Broken Access Control (A01:2021)</h3>
        <ul>
          <li><strong>Vertical Privilege Escalation:</strong>
            <ul>
              <li>Admin panel access bypass</li>
              <li>Role-based access control flaws</li>
              <li>Function-level authorization bypass</li>
              <li>API endpoint privilege escalation</li>
            </ul>
          </li>
          <li><strong>Horizontal Privilege Escalation:</strong>
            <ul>
              <li>Insecure Direct Object References (IDOR)</li>
              <li>User ID enumeration and manipulation</li>
              <li>Session token manipulation</li>
              <li>Parameter tampering attacks</li>
            </ul>
          </li>
          <li><strong>Testing Methodologies:</strong>
            <ul>
              <li>Parameter fuzzing and manipulation</li>
              <li>HTTP method tampering</li>
              <li>URL path traversal</li>
              <li>Cookie and token analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Authentication Failures (A07:2021)</h3>
        <ul>
          <li><strong>Weak Authentication Mechanisms:</strong>
            <ul>
              <li>Weak password policies</li>
              <li>Default credentials</li>
              <li>Predictable session tokens</li>
              <li>Missing account lockout</li>
            </ul>
          </li>
          <li><strong>Session Management Flaws:</strong>
            <ul>
              <li>Session fixation attacks</li>
              <li>Session hijacking vulnerabilities</li>
              <li>Insufficient session timeout</li>
              <li>Concurrent session handling</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication Bypass:</strong>
            <ul>
              <li>MFA implementation flaws</li>
              <li>Backup code vulnerabilities</li>
              <li>SMS/Email OTP bypass</li>
              <li>TOTP synchronization issues</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Misconfiguration (A05:2021)</h3>
        <ul>
          <li><strong>Common Misconfigurations:</strong>
            <ul>
              <li>Default application settings</li>
              <li>Unnecessary features enabled</li>
              <li>Missing security headers</li>
              <li>Verbose error messages</li>
            </ul>
          </li>
          <li><strong>Cloud Security Misconfigurations:</strong>
            <ul>
              <li>Open S3 buckets</li>
              <li>Misconfigured cloud databases</li>
              <li>Insecure API gateways</li>
              <li>Overprivileged IAM roles</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which OWASP Top 10 2021 category covers Insecure Direct Object References (IDOR)?",options:["A03:2021 - Injection","A01:2021 - Broken Access Control","A07:2021 - Identification and Authentication Failures","A05:2021 - Security Misconfiguration"],correctAnswer:1,explanation:"IDOR vulnerabilities fall under A01:2021 - Broken Access Control, as they involve unauthorized access to objects or resources by manipulating references."},{question:"What is the primary difference between reflected and stored XSS?",options:["Reflected XSS is more dangerous","Stored XSS persists on the server while reflected XSS is temporary","Reflected XSS affects more users","There is no difference"],correctAnswer:1,explanation:"Stored XSS persists on the server and affects multiple users who view the infected content, while reflected XSS is temporary and typically affects only the user who clicks a malicious link."},{question:"Which technique is most effective for detecting blind SQL injection?",options:["Error message analysis","Union-based queries","Time-based delays","Direct database access"],correctAnswer:2,explanation:"Time-based delays are most effective for detecting blind SQL injection because they don't rely on visible error messages or data output, making them useful when other detection methods fail."}]},type:"quiz"}]},l={id:"bb-5",pathId:"bug-bounty",title:"Network Infrastructure Testing",description:"Master network infrastructure vulnerability assessment and penetration testing techniques for bug bounty hunting, including port scanning, service enumeration, and network-based attacks.",objectives:["Understand network reconnaissance and enumeration techniques","Learn port scanning and service identification methods","Master network protocol vulnerabilities and exploits","Develop skills in network-based attack vectors","Learn to identify and exploit network misconfigurations","Create comprehensive network security assessments"],difficulty:"Intermediate",estimatedTime:130,sections:[{title:"Network Reconnaissance and Enumeration",content:`
        <h2>Network Reconnaissance and Enumeration</h2>
        <p>Network reconnaissance is the foundation of infrastructure testing, involving systematic discovery and enumeration of network assets, services, and potential attack vectors.</p>
        
        <h3>Network Discovery Techniques</h3>
        <ul>
          <li><strong>Passive Reconnaissance:</strong>
            <ul>
              <li>DNS enumeration and zone transfers</li>
              <li>WHOIS database queries</li>
              <li>Search engine dorking</li>
              <li>Social media and public information gathering</li>
            </ul>
          </li>
          <li><strong>Active Reconnaissance:</strong>
            <ul>
              <li>Network range identification</li>
              <li>Subdomain enumeration</li>
              <li>IP address scanning</li>
              <li>Network topology mapping</li>
            </ul>
          </li>
          <li><strong>DNS Enumeration:</strong>
            <ul>
              <li>DNS record enumeration (A, AAAA, MX, TXT, etc.)</li>
              <li>DNS zone transfer attempts</li>
              <li>Reverse DNS lookups</li>
              <li>DNS cache snooping</li>
            </ul>
          </li>
        </ul>
        
        <h3>Port Scanning and Service Detection</h3>
        <ul>
          <li><strong>Port Scanning Techniques:</strong>
            <ul>
              <li>TCP SYN scanning (stealth scanning)</li>
              <li>TCP connect scanning</li>
              <li>UDP port scanning</li>
              <li>FIN, NULL, and Xmas scans</li>
            </ul>
          </li>
          <li><strong>Service Enumeration:</strong>
            <ul>
              <li>Banner grabbing and service identification</li>
              <li>Version detection and fingerprinting</li>
              <li>Operating system detection</li>
              <li>Service-specific enumeration</li>
            </ul>
          </li>
          <li><strong>Nmap Advanced Techniques:</strong>
            <ul>
              <li>NSE (Nmap Scripting Engine) usage</li>
              <li>Custom timing and evasion techniques</li>
              <li>Firewall and IDS evasion</li>
              <li>Output formatting and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Protocol Analysis</h3>
        <ul>
          <li><strong>Protocol Vulnerability Assessment:</strong>
            <ul>
              <li>HTTP/HTTPS service analysis</li>
              <li>SSH service enumeration</li>
              <li>FTP and TFTP vulnerabilities</li>
              <li>SMTP and email service testing</li>
            </ul>
          </li>
          <li><strong>Database Service Testing:</strong>
            <ul>
              <li>MySQL and PostgreSQL enumeration</li>
              <li>MSSQL service vulnerabilities</li>
              <li>MongoDB and NoSQL databases</li>
              <li>Redis and cache service testing</li>
            </ul>
          </li>
          <li><strong>Network File Services:</strong>
            <ul>
              <li>SMB/CIFS share enumeration</li>
              <li>NFS mount point discovery</li>
              <li>SNMP community string testing</li>
              <li>LDAP directory service enumeration</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Network Attack Vectors",content:`
        <h2>Network-Based Attack Vectors</h2>
        <p>Understanding and exploiting network-based vulnerabilities requires knowledge of various attack techniques and their practical implementation.</p>
        
        <h3>Man-in-the-Middle (MITM) Attacks</h3>
        <ul>
          <li><strong>ARP Spoofing and Poisoning:</strong>
            <ul>
              <li>ARP table manipulation</li>
              <li>Network traffic interception</li>
              <li>Gateway impersonation</li>
              <li>DHCP spoofing attacks</li>
            </ul>
          </li>
          <li><strong>SSL/TLS Attacks:</strong>
            <ul>
              <li>SSL stripping attacks</li>
              <li>Certificate pinning bypass</li>
              <li>Weak cipher exploitation</li>
              <li>TLS downgrade attacks</li>
            </ul>
          </li>
          <li><strong>DNS Attacks:</strong>
            <ul>
              <li>DNS spoofing and cache poisoning</li>
              <li>DNS tunneling techniques</li>
              <li>DNS rebinding attacks</li>
              <li>Subdomain takeover vulnerabilities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Service Exploitation</h3>
        <ul>
          <li><strong>SSH Service Attacks:</strong>
            <ul>
              <li>SSH brute force attacks</li>
              <li>SSH key-based authentication bypass</li>
              <li>SSH tunneling and port forwarding</li>
              <li>SSH configuration vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Web Server Exploitation:</strong>
            <ul>
              <li>HTTP method tampering</li>
              <li>Virtual host enumeration</li>
              <li>Web server misconfiguration</li>
              <li>CGI and script vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Database Service Attacks:</strong>
            <ul>
              <li>Default credential testing</li>
              <li>Database-specific vulnerabilities</li>
              <li>Privilege escalation techniques</li>
              <li>Data extraction methods</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Evasion Techniques</h3>
        <ul>
          <li><strong>Firewall Evasion:</strong>
            <ul>
              <li>Packet fragmentation</li>
              <li>Source port manipulation</li>
              <li>Protocol tunneling</li>
              <li>Timing-based evasion</li>
            </ul>
          </li>
          <li><strong>IDS/IPS Evasion:</strong>
            <ul>
              <li>Signature evasion techniques</li>
              <li>Polymorphic payload generation</li>
              <li>Traffic obfuscation</li>
              <li>Decoy and noise generation</li>
            </ul>
          </li>
          <li><strong>Network Pivoting:</strong>
            <ul>
              <li>Internal network discovery</li>
              <li>Lateral movement techniques</li>
              <li>Proxy chaining</li>
              <li>Covert channel communication</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Infrastructure Security Assessment",content:`
        <h2>Infrastructure Security Assessment</h2>
        <p>Comprehensive infrastructure security assessment involves systematic evaluation of network components, configurations, and security controls.</p>
        
        <h3>Network Architecture Analysis</h3>
        <ul>
          <li><strong>Network Segmentation Assessment:</strong>
            <ul>
              <li>VLAN configuration analysis</li>
              <li>Network zone isolation testing</li>
              <li>DMZ security evaluation</li>
              <li>Internal network access controls</li>
            </ul>
          </li>
          <li><strong>Routing and Switching Security:</strong>
            <ul>
              <li>Router configuration vulnerabilities</li>
              <li>Switch security features</li>
              <li>VLAN hopping attacks</li>
              <li>Spanning tree protocol attacks</li>
            </ul>
          </li>
          <li><strong>Wireless Network Security:</strong>
            <ul>
              <li>WiFi encryption assessment</li>
              <li>Access point configuration</li>
              <li>Wireless authentication bypass</li>
              <li>Rogue access point detection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Control Evaluation</h3>
        <ul>
          <li><strong>Firewall Rule Analysis:</strong>
            <ul>
              <li>Rule set effectiveness</li>
              <li>Default deny policies</li>
              <li>Unnecessary open ports</li>
              <li>Rule ordering and conflicts</li>
            </ul>
          </li>
          <li><strong>Network Monitoring and Logging:</strong>
            <ul>
              <li>Log collection and analysis</li>
              <li>Network traffic monitoring</li>
              <li>Intrusion detection effectiveness</li>
              <li>Security event correlation</li>
            </ul>
          </li>
          <li><strong>Access Control Assessment:</strong>
            <ul>
              <li>Network access control (NAC)</li>
              <li>VPN security configuration</li>
              <li>Remote access policies</li>
              <li>Privileged account management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Vulnerability Assessment Tools</h3>
        <ul>
          <li><strong>Network Scanning Tools:</strong>
            <ul>
              <li>Nmap for port scanning and enumeration</li>
              <li>Masscan for large-scale scanning</li>
              <li>Zmap for internet-wide scanning</li>
              <li>Unicornscan for advanced scanning</li>
            </ul>
          </li>
          <li><strong>Vulnerability Scanners:</strong>
            <ul>
              <li>Nessus for comprehensive scanning</li>
              <li>OpenVAS for open-source scanning</li>
              <li>Nuclei for fast vulnerability detection</li>
              <li>Custom script development</li>
            </ul>
          </li>
          <li><strong>Network Analysis Tools:</strong>
            <ul>
              <li>Wireshark for packet analysis</li>
              <li>tcpdump for command-line capture</li>
              <li>Ettercap for MITM attacks</li>
              <li>Bettercap for network reconnaissance</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which Nmap scan type is considered the most stealthy for port scanning?",options:["TCP Connect scan (-sT)","TCP SYN scan (-sS)","UDP scan (-sU)","TCP FIN scan (-sF)"],correctAnswer:1,explanation:"TCP SYN scan (-sS) is considered the most stealthy because it doesn't complete the TCP handshake, making it less likely to be logged by target systems while still providing reliable results."},{question:"What is the primary purpose of ARP spoofing in network attacks?",options:["To crash network devices","To intercept network traffic between hosts","To scan for open ports","To enumerate network services"],correctAnswer:1,explanation:"ARP spoofing is primarily used to intercept network traffic between hosts by associating the attacker's MAC address with the IP address of another host, enabling man-in-the-middle attacks."},{question:"Which technique is most effective for evading signature-based intrusion detection systems?",options:["Using default attack tools","Increasing attack speed","Polymorphic payload generation","Using only encrypted protocols"],correctAnswer:2,explanation:"Polymorphic payload generation is most effective for evading signature-based IDS because it creates variations of the same attack that don't match known signatures while maintaining the same functionality."}]},type:"quiz"}]},o={id:"bb-6",pathId:"bug-bounty",title:"Cloud Security Testing",description:"Master cloud security assessment techniques for AWS, Azure, and GCP environments, including cloud-specific vulnerabilities, misconfigurations, and attack vectors in bug bounty programs.",objectives:["Understand cloud security models and shared responsibility","Learn cloud service enumeration and reconnaissance","Master cloud-specific vulnerability assessment techniques","Develop skills in cloud misconfiguration detection","Learn container and serverless security testing","Create comprehensive cloud security assessments"],difficulty:"Advanced",estimatedTime:140,sections:[{title:"Cloud Security Fundamentals",content:`
        <h2>Cloud Security Assessment Fundamentals</h2>
        <p>Cloud security testing requires understanding of cloud service models, shared responsibility, and cloud-specific attack vectors that differ from traditional infrastructure.</p>
        
        <h3>Cloud Service Models</h3>
        <ul>
          <li><strong>Infrastructure as a Service (IaaS):</strong>
            <ul>
              <li>Virtual machines and compute instances</li>
              <li>Network and storage services</li>
              <li>Security group and firewall configurations</li>
              <li>Identity and access management (IAM)</li>
            </ul>
          </li>
          <li><strong>Platform as a Service (PaaS):</strong>
            <ul>
              <li>Application deployment platforms</li>
              <li>Database and middleware services</li>
              <li>Development and runtime environments</li>
              <li>API gateways and service mesh</li>
            </ul>
          </li>
          <li><strong>Software as a Service (SaaS):</strong>
            <ul>
              <li>Web-based applications</li>
              <li>Multi-tenant architectures</li>
              <li>Data isolation and privacy</li>
              <li>Integration and API security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Shared Responsibility Model</h3>
        <ul>
          <li><strong>Cloud Provider Responsibilities:</strong>
            <ul>
              <li>Physical infrastructure security</li>
              <li>Hypervisor and host OS security</li>
              <li>Network infrastructure protection</li>
              <li>Service availability and resilience</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>Guest OS and application security</li>
              <li>Data encryption and protection</li>
              <li>Identity and access management</li>
              <li>Network traffic protection</li>
            </ul>
          </li>
          <li><strong>Shared Responsibilities:</strong>
            <ul>
              <li>Patch management (varies by service)</li>
              <li>Configuration management</li>
              <li>Network controls and firewalls</li>
              <li>Platform and application-level controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Attack Surface</h3>
        <ul>
          <li><strong>Management Interfaces:</strong>
            <ul>
              <li>Cloud console and web interfaces</li>
              <li>Command-line tools and APIs</li>
              <li>Mobile applications</li>
              <li>Third-party management tools</li>
            </ul>
          </li>
          <li><strong>Service APIs:</strong>
            <ul>
              <li>REST and GraphQL APIs</li>
              <li>Authentication and authorization</li>
              <li>Rate limiting and throttling</li>
              <li>API versioning and deprecation</li>
            </ul>
          </li>
          <li><strong>Data Storage:</strong>
            <ul>
              <li>Object storage (S3, Blob, Cloud Storage)</li>
              <li>Database services (RDS, CosmosDB, Cloud SQL)</li>
              <li>File systems and network storage</li>
              <li>Backup and archival services</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Cloud Reconnaissance and Enumeration",content:`
        <h2>Cloud Reconnaissance and Service Enumeration</h2>
        <p>Cloud reconnaissance involves discovering cloud assets, services, and configurations using both passive and active techniques specific to cloud environments.</p>
        
        <h3>Cloud Asset Discovery</h3>
        <ul>
          <li><strong>DNS and Subdomain Enumeration:</strong>
            <ul>
              <li>Cloud service subdomain patterns</li>
              <li>Certificate transparency logs</li>
              <li>DNS brute forcing for cloud services</li>
              <li>Reverse DNS lookups</li>
            </ul>
          </li>
          <li><strong>Cloud Storage Enumeration:</strong>
            <ul>
              <li>S3 bucket discovery and enumeration</li>
              <li>Azure Blob storage containers</li>
              <li>Google Cloud Storage buckets</li>
              <li>Public storage misconfiguration detection</li>
            </ul>
          </li>
          <li><strong>Cloud Service Fingerprinting:</strong>
            <ul>
              <li>HTTP header analysis</li>
              <li>Error message patterns</li>
              <li>Service-specific indicators</li>
              <li>Cloud provider identification</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Security Assessment</h3>
        <ul>
          <li><strong>AWS Service Enumeration:</strong>
            <ul>
              <li>EC2 instance metadata service</li>
              <li>S3 bucket permissions and policies</li>
              <li>Lambda function discovery</li>
              <li>API Gateway endpoint enumeration</li>
            </ul>
          </li>
          <li><strong>IAM Security Testing:</strong>
            <ul>
              <li>Role and policy enumeration</li>
              <li>Cross-account access testing</li>
              <li>Privilege escalation paths</li>
              <li>Temporary credential abuse</li>
            </ul>
          </li>
          <li><strong>AWS-Specific Vulnerabilities:</strong>
            <ul>
              <li>SSRF via metadata service</li>
              <li>S3 bucket takeover</li>
              <li>Lambda function injection</li>
              <li>CloudFormation template injection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure and GCP Assessment</h3>
        <ul>
          <li><strong>Azure Security Testing:</strong>
            <ul>
              <li>Azure AD enumeration</li>
              <li>Storage account assessment</li>
              <li>Function app security testing</li>
              <li>Key Vault access testing</li>
            </ul>
          </li>
          <li><strong>Google Cloud Platform (GCP):</strong>
            <ul>
              <li>GCP project enumeration</li>
              <li>Cloud Storage bucket testing</li>
              <li>Cloud Function assessment</li>
              <li>IAM and service account testing</li>
            </ul>
          </li>
          <li><strong>Multi-Cloud Considerations:</strong>
            <ul>
              <li>Cross-cloud service integration</li>
              <li>Hybrid cloud architectures</li>
              <li>Cloud-to-cloud data transfer</li>
              <li>Federated identity management</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Container and Serverless Security",content:`
        <h2>Container and Serverless Security Testing</h2>
        <p>Modern cloud applications increasingly use containers and serverless architectures, introducing new attack vectors and security considerations.</p>
        
        <h3>Container Security Assessment</h3>
        <ul>
          <li><strong>Docker Security Testing:</strong>
            <ul>
              <li>Container image vulnerability scanning</li>
              <li>Docker daemon security assessment</li>
              <li>Container escape techniques</li>
              <li>Registry security evaluation</li>
            </ul>
          </li>
          <li><strong>Kubernetes Security:</strong>
            <ul>
              <li>Cluster configuration assessment</li>
              <li>Pod security policy evaluation</li>
              <li>RBAC (Role-Based Access Control) testing</li>
              <li>Network policy assessment</li>
            </ul>
          </li>
          <li><strong>Container Runtime Security:</strong>
            <ul>
              <li>Privilege escalation in containers</li>
              <li>Host filesystem access</li>
              <li>Inter-container communication</li>
              <li>Resource exhaustion attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Serverless Security Testing</h3>
        <ul>
          <li><strong>Function-as-a-Service (FaaS) Security:</strong>
            <ul>
              <li>AWS Lambda security assessment</li>
              <li>Azure Functions testing</li>
              <li>Google Cloud Functions evaluation</li>
              <li>Function permission analysis</li>
            </ul>
          </li>
          <li><strong>Serverless Attack Vectors:</strong>
            <ul>
              <li>Event injection attacks</li>
              <li>Function timeout and resource abuse</li>
              <li>Cold start vulnerabilities</li>
              <li>Dependency confusion attacks</li>
            </ul>
          </li>
          <li><strong>API Gateway Security:</strong>
            <ul>
              <li>Authentication and authorization bypass</li>
              <li>Rate limiting and throttling</li>
              <li>Input validation and injection</li>
              <li>CORS misconfiguration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Security Tools and Techniques</h3>
        <ul>
          <li><strong>Cloud Security Scanners:</strong>
            <ul>
              <li>ScoutSuite for multi-cloud assessment</li>
              <li>Prowler for AWS security auditing</li>
              <li>CloudSploit for configuration assessment</li>
              <li>Pacu for AWS exploitation</li>
            </ul>
          </li>
          <li><strong>Container Security Tools:</strong>
            <ul>
              <li>Trivy for vulnerability scanning</li>
              <li>Clair for static analysis</li>
              <li>Falco for runtime security</li>
              <li>kube-hunter for Kubernetes testing</li>
            </ul>
          </li>
          <li><strong>Custom Tool Development:</strong>
            <ul>
              <li>Cloud API automation scripts</li>
              <li>Custom enumeration tools</li>
              <li>Serverless function testing</li>
              <li>Container escape proof-of-concepts</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"In the cloud shared responsibility model, who is responsible for guest OS security?",options:["Cloud provider only","Customer only","Both cloud provider and customer","Third-party security vendors"],correctAnswer:1,explanation:"In the shared responsibility model, the customer is responsible for guest OS security, including patching, configuration, and security controls within their virtual machines."},{question:"Which AWS service is commonly exploited through SSRF attacks?",options:["S3 buckets","EC2 metadata service","Lambda functions","RDS databases"],correctAnswer:1,explanation:"The EC2 metadata service (***************) is commonly exploited through SSRF attacks to retrieve instance metadata, IAM credentials, and other sensitive information."},{question:"What is a primary security concern with serverless functions?",options:["Physical server access","Event injection and function timeout abuse","Network firewall configuration","Hardware vulnerability patching"],correctAnswer:1,explanation:"Event injection and function timeout abuse are primary security concerns with serverless functions, as attackers can manipulate input events and exploit resource limitations to cause denial of service or unauthorized access."}]},type:"quiz"}]},s={id:"bb-7",pathId:"bug-bounty",title:"Automation and Scripting",description:"Master automation techniques and scripting for bug bounty hunting, including custom tool development, workflow automation, and scaling vulnerability discovery efforts.",objectives:["Understand automation opportunities in bug bounty hunting","Learn scripting languages for security testing","Master custom tool development and integration","Develop automated reconnaissance workflows","Learn to scale vulnerability discovery efforts","Create efficient bug bounty automation pipelines"],difficulty:"Advanced",estimatedTime:135,sections:[{title:"Bug Bounty Automation Fundamentals",content:`
        <h2>Bug Bounty Automation and Scripting</h2>
        <p>Automation is essential for scaling bug bounty efforts, enabling hunters to efficiently process large attack surfaces and focus on high-value manual testing.</p>
        
        <h3>Automation Opportunities</h3>
        <ul>
          <li><strong>Reconnaissance Automation:</strong>
            <ul>
              <li>Subdomain enumeration and monitoring</li>
              <li>Port scanning and service detection</li>
              <li>Technology stack identification</li>
              <li>Asset discovery and inventory</li>
            </ul>
          </li>
          <li><strong>Vulnerability Scanning:</strong>
            <ul>
              <li>Automated vulnerability detection</li>
              <li>Custom payload generation</li>
              <li>False positive filtering</li>
              <li>Result correlation and analysis</li>
            </ul>
          </li>
          <li><strong>Monitoring and Alerting:</strong>
            <ul>
              <li>New asset discovery alerts</li>
              <li>Program scope changes</li>
              <li>Vulnerability disclosure monitoring</li>
              <li>Competitor activity tracking</li>
            </ul>
          </li>
        </ul>
        
        <h3>Scripting Languages for Security</h3>
        <ul>
          <li><strong>Python for Bug Bounty:</strong>
            <ul>
              <li>Requests library for HTTP automation</li>
              <li>BeautifulSoup for web scraping</li>
              <li>Selenium for browser automation</li>
              <li>Threading and async programming</li>
            </ul>
          </li>
          <li><strong>Bash Scripting:</strong>
            <ul>
              <li>Command-line tool integration</li>
              <li>Pipeline and workflow automation</li>
              <li>System administration tasks</li>
              <li>Log processing and analysis</li>
            </ul>
          </li>
          <li><strong>JavaScript and Node.js:</strong>
            <ul>
              <li>Browser-based testing automation</li>
              <li>API interaction and testing</li>
              <li>Headless browser control</li>
              <li>Real-time data processing</li>
            </ul>
          </li>
          <li><strong>Go for Performance:</strong>
            <ul>
              <li>High-performance scanning tools</li>
              <li>Concurrent processing</li>
              <li>Network programming</li>
              <li>Cross-platform tool development</li>
            </ul>
          </li>
        </ul>
        
        <h3>Tool Integration and Workflows</h3>
        <ul>
          <li><strong>Command-Line Tool Integration:</strong>
            <ul>
              <li>Subfinder, Amass, and Assetfinder</li>
              <li>Nmap and Masscan integration</li>
              <li>Nuclei and custom templates</li>
              <li>Burp Suite Professional API</li>
            </ul>
          </li>
          <li><strong>API Integration:</strong>
            <ul>
              <li>Bug bounty platform APIs</li>
              <li>Threat intelligence feeds</li>
              <li>Cloud service APIs</li>
              <li>Social media and OSINT APIs</li>
            </ul>
          </li>
          <li><strong>Database and Storage:</strong>
            <ul>
              <li>SQLite for local data storage</li>
              <li>PostgreSQL for complex queries</li>
              <li>Redis for caching and queues</li>
              <li>File-based storage and CSV export</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Custom Tool Development",content:`
        <h2>Custom Security Tool Development</h2>
        <p>Developing custom tools allows bug bounty hunters to address specific needs, implement unique methodologies, and gain competitive advantages.</p>
        
        <h3>Reconnaissance Tool Development</h3>
        <ul>
          <li><strong>Subdomain Discovery Tools:</strong>
            <ul>
              <li>DNS brute forcing algorithms</li>
              <li>Certificate transparency parsing</li>
              <li>Search engine scraping</li>
              <li>Passive DNS integration</li>
            </ul>
          </li>
          <li><strong>Port Scanning Utilities:</strong>
            <ul>
              <li>Custom TCP/UDP scanners</li>
              <li>Service fingerprinting</li>
              <li>Banner grabbing automation</li>
              <li>Stealth scanning techniques</li>
            </ul>
          </li>
          <li><strong>Web Technology Detection:</strong>
            <ul>
              <li>Framework and CMS identification</li>
              <li>JavaScript library detection</li>
              <li>Server technology fingerprinting</li>
              <li>Version detection algorithms</li>
            </ul>
          </li>
        </ul>
        
        <h3>Vulnerability Detection Scripts</h3>
        <ul>
          <li><strong>Web Application Scanners:</strong>
            <ul>
              <li>SQL injection detection</li>
              <li>XSS payload automation</li>
              <li>SSRF testing frameworks</li>
              <li>Authentication bypass scripts</li>
            </ul>
          </li>
          <li><strong>API Security Testing:</strong>
            <ul>
              <li>REST API endpoint discovery</li>
              <li>GraphQL introspection</li>
              <li>API authentication testing</li>
              <li>Rate limiting bypass</li>
            </ul>
          </li>
          <li><strong>Mobile Application Testing:</strong>
            <ul>
              <li>APK analysis automation</li>
              <li>iOS application testing</li>
              <li>Mobile API interaction</li>
              <li>Certificate pinning bypass</li>
            </ul>
          </li>
        </ul>
        
        <h3>Reporting and Documentation Automation</h3>
        <ul>
          <li><strong>Report Generation:</strong>
            <ul>
              <li>Automated vulnerability reports</li>
              <li>Template-based documentation</li>
              <li>Screenshot and evidence capture</li>
              <li>Proof-of-concept generation</li>
            </ul>
          </li>
          <li><strong>Data Visualization:</strong>
            <ul>
              <li>Attack surface mapping</li>
              <li>Vulnerability trend analysis</li>
              <li>Program statistics dashboards</li>
              <li>Progress tracking charts</li>
            </ul>
          </li>
          <li><strong>Communication Automation:</strong>
            <ul>
              <li>Slack and Discord notifications</li>
              <li>Email alert systems</li>
              <li>Platform submission automation</li>
              <li>Status update broadcasting</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Scaling and Optimization",content:`
        <h2>Scaling Bug Bounty Operations</h2>
        <p>Effective scaling requires optimized workflows, efficient resource utilization, and intelligent prioritization of testing efforts.</p>
        
        <h3>Workflow Optimization</h3>
        <ul>
          <li><strong>Pipeline Architecture:</strong>
            <ul>
              <li>Modular workflow design</li>
              <li>Input/output standardization</li>
              <li>Error handling and recovery</li>
              <li>Progress tracking and logging</li>
            </ul>
          </li>
          <li><strong>Parallel Processing:</strong>
            <ul>
              <li>Multi-threading implementation</li>
              <li>Asynchronous programming</li>
              <li>Distributed computing</li>
              <li>Queue-based task management</li>
            </ul>
          </li>
          <li><strong>Resource Management:</strong>
            <ul>
              <li>Rate limiting and throttling</li>
              <li>Memory and CPU optimization</li>
              <li>Network bandwidth management</li>
              <li>Cloud resource scaling</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intelligent Prioritization</h3>
        <ul>
          <li><strong>Target Prioritization:</strong>
            <ul>
              <li>Asset criticality scoring</li>
              <li>Attack surface analysis</li>
              <li>Historical vulnerability data</li>
              <li>Program reward analysis</li>
            </ul>
          </li>
          <li><strong>Vulnerability Scoring:</strong>
            <ul>
              <li>CVSS score calculation</li>
              <li>Exploitability assessment</li>
              <li>Business impact evaluation</li>
              <li>Duplicate detection</li>
            </ul>
          </li>
          <li><strong>Testing Strategy Optimization:</strong>
            <ul>
              <li>Methodology effectiveness tracking</li>
              <li>Success rate analysis</li>
              <li>Time investment optimization</li>
              <li>ROI calculation and improvement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Continuous Integration and Deployment</h3>
        <ul>
          <li><strong>CI/CD for Security Tools:</strong>
            <ul>
              <li>Automated testing and validation</li>
              <li>Version control and deployment</li>
              <li>Configuration management</li>
              <li>Environment provisioning</li>
            </ul>
          </li>
          <li><strong>Monitoring and Alerting:</strong>
            <ul>
              <li>Tool performance monitoring</li>
              <li>Error detection and alerting</li>
              <li>Resource utilization tracking</li>
              <li>Success rate monitoring</li>
            </ul>
          </li>
          <li><strong>Maintenance and Updates:</strong>
            <ul>
              <li>Automated dependency updates</li>
              <li>Security patch management</li>
              <li>Tool version management</li>
              <li>Configuration drift detection</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which Python library is most commonly used for HTTP automation in bug bounty scripts?",options:["urllib","requests","http.client","selenium"],correctAnswer:1,explanation:"The requests library is most commonly used for HTTP automation in bug bounty scripts due to its simple API, built-in session management, and comprehensive feature set for web interactions."},{question:"What is the primary benefit of implementing parallel processing in reconnaissance tools?",options:["Reduced memory usage","Better accuracy","Increased speed and efficiency","Simplified code structure"],correctAnswer:2,explanation:"Parallel processing primarily provides increased speed and efficiency by allowing multiple operations to run simultaneously, significantly reducing the time required for large-scale reconnaissance tasks."},{question:"Which factor is most important when prioritizing targets for automated testing?",options:["Alphabetical order","Domain age","Attack surface size and criticality","Geographic location"],correctAnswer:2,explanation:"Attack surface size and criticality are most important for target prioritization, as they indicate the potential for finding vulnerabilities and the impact of any discoveries on the organization."}]},type:"quiz"}]},a={id:"bb-8",pathId:"bug-bounty",title:"Mobile Application Security Testing",description:"Master mobile application security testing for iOS and Android platforms, including static and dynamic analysis, reverse engineering, and mobile-specific vulnerability discovery.",objectives:["Understand mobile application security fundamentals","Learn static and dynamic analysis techniques","Master mobile application reverse engineering","Develop skills in mobile-specific vulnerability testing","Learn iOS and Android security testing methodologies","Create comprehensive mobile security assessments"],difficulty:"Advanced",estimatedTime:150,sections:[{title:"Mobile Security Fundamentals",content:`
        <h2>Mobile Application Security Testing</h2>
        <p>Mobile application security testing requires specialized knowledge of mobile platforms, architectures, and unique attack vectors specific to mobile environments.</p>
        
        <h3>Mobile Security Landscape</h3>
        <ul>
          <li><strong>Mobile Platform Architecture:</strong>
            <ul>
              <li>iOS security model and sandboxing</li>
              <li>Android security architecture</li>
              <li>Application lifecycle and permissions</li>
              <li>Inter-process communication (IPC)</li>
            </ul>
          </li>
          <li><strong>Mobile Attack Surface:</strong>
            <ul>
              <li>Application layer vulnerabilities</li>
              <li>Network communication security</li>
              <li>Local data storage issues</li>
              <li>Platform-specific vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Mobile Threat Model:</strong>
            <ul>
              <li>Malicious applications</li>
              <li>Network-based attacks</li>
              <li>Physical device access</li>
              <li>Social engineering attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>OWASP Mobile Top 10</h3>
        <ul>
          <li><strong>M1: Improper Platform Usage:</strong>
            <ul>
              <li>Misuse of platform features</li>
              <li>Violation of published guidelines</li>
              <li>Insecure platform permissions</li>
              <li>TouchID/FaceID bypass</li>
            </ul>
          </li>
          <li><strong>M2: Insecure Data Storage:</strong>
            <ul>
              <li>Unencrypted local databases</li>
              <li>Insecure shared preferences</li>
              <li>Keychain/Keystore vulnerabilities</li>
              <li>Log file data leakage</li>
            </ul>
          </li>
          <li><strong>M3: Insecure Communication:</strong>
            <ul>
              <li>Weak SSL/TLS implementation</li>
              <li>Certificate pinning bypass</li>
              <li>Man-in-the-middle vulnerabilities</li>
              <li>Insecure protocols usage</li>
            </ul>
          </li>
          <li><strong>M4: Insecure Authentication:</strong>
            <ul>
              <li>Weak authentication schemes</li>
              <li>Session management flaws</li>
              <li>Biometric authentication bypass</li>
              <li>OAuth implementation issues</li>
            </ul>
          </li>
          <li><strong>M5: Insufficient Cryptography:</strong>
            <ul>
              <li>Weak encryption algorithms</li>
              <li>Poor key management</li>
              <li>Hardcoded cryptographic keys</li>
              <li>Custom cryptographic implementations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remaining Mobile Top 10</h3>
        <ul>
          <li><strong>M6: Insecure Authorization:</strong>
            <ul>
              <li>Privilege escalation</li>
              <li>Authorization bypass</li>
              <li>Insecure direct object references</li>
              <li>Function-level access control</li>
            </ul>
          </li>
          <li><strong>M7: Client Code Quality:</strong>
            <ul>
              <li>Buffer overflows</li>
              <li>Format string vulnerabilities</li>
              <li>Memory corruption issues</li>
              <li>Code injection vulnerabilities</li>
            </ul>
          </li>
          <li><strong>M8: Code Tampering:</strong>
            <ul>
              <li>Binary patching</li>
              <li>Runtime manipulation</li>
              <li>Anti-tampering bypass</li>
              <li>Repackaging attacks</li>
            </ul>
          </li>
          <li><strong>M9: Reverse Engineering:</strong>
            <ul>
              <li>Source code recovery</li>
              <li>Algorithm analysis</li>
              <li>Intellectual property theft</li>
              <li>Anti-reverse engineering bypass</li>
            </ul>
          </li>
          <li><strong>M10: Extraneous Functionality:</strong>
            <ul>
              <li>Hidden backdoors</li>
              <li>Debug functionality</li>
              <li>Test code in production</li>
              <li>Administrative interfaces</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Android Security Testing",content:`
        <h2>Android Application Security Testing</h2>
        <p>Android security testing involves analyzing APK files, runtime behavior, and platform-specific security mechanisms to identify vulnerabilities.</p>
        
        <h3>Android Static Analysis</h3>
        <ul>
          <li><strong>APK Analysis:</strong>
            <ul>
              <li>APK structure and components</li>
              <li>AndroidManifest.xml analysis</li>
              <li>Permissions and intent filters</li>
              <li>Resource and asset examination</li>
            </ul>
          </li>
          <li><strong>Code Analysis:</strong>
            <ul>
              <li>Java/Kotlin source code review</li>
              <li>Native library analysis (JNI)</li>
              <li>Smali code examination</li>
              <li>Hardcoded secrets detection</li>
            </ul>
          </li>
          <li><strong>Static Analysis Tools:</strong>
            <ul>
              <li>JADX for APK decompilation</li>
              <li>APKTool for resource extraction</li>
              <li>MobSF for automated analysis</li>
              <li>QARK for vulnerability scanning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Android Dynamic Analysis</h3>
        <ul>
          <li><strong>Runtime Analysis Setup:</strong>
            <ul>
              <li>Android emulator configuration</li>
              <li>Physical device rooting</li>
              <li>Frida instrumentation framework</li>
              <li>Xposed framework usage</li>
            </ul>
          </li>
          <li><strong>Runtime Manipulation:</strong>
            <ul>
              <li>Method hooking and interception</li>
              <li>SSL pinning bypass</li>
              <li>Root detection bypass</li>
              <li>Anti-debugging bypass</li>
            </ul>
          </li>
          <li><strong>Network Traffic Analysis:</strong>
            <ul>
              <li>HTTP/HTTPS proxy setup</li>
              <li>Certificate installation</li>
              <li>Traffic interception and modification</li>
              <li>API endpoint discovery</li>
            </ul>
          </li>
        </ul>
        
        <h3>Android-Specific Vulnerabilities</h3>
        <ul>
          <li><strong>Intent-Based Attacks:</strong>
            <ul>
              <li>Intent spoofing and hijacking</li>
              <li>Exported component exploitation</li>
              <li>Broadcast receiver vulnerabilities</li>
              <li>Deep link manipulation</li>
            </ul>
          </li>
          <li><strong>Storage Vulnerabilities:</strong>
            <ul>
              <li>Shared preferences exposure</li>
              <li>SQLite database security</li>
              <li>External storage access</li>
              <li>Backup flag vulnerabilities</li>
            </ul>
          </li>
          <li><strong>WebView Security:</strong>
            <ul>
              <li>JavaScript interface exposure</li>
              <li>File access vulnerabilities</li>
              <li>Universal XSS in WebView</li>
              <li>WebView debugging enabled</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"iOS Security Testing",content:`
        <h2>iOS Application Security Testing</h2>
        <p>iOS security testing requires understanding of the iOS security model, code signing, and specialized tools for analyzing iOS applications.</p>
        
        <h3>iOS Static Analysis</h3>
        <ul>
          <li><strong>IPA Analysis:</strong>
            <ul>
              <li>IPA file structure and contents</li>
              <li>Info.plist configuration analysis</li>
              <li>Entitlements and capabilities</li>
              <li>Binary and resource examination</li>
            </ul>
          </li>
          <li><strong>Binary Analysis:</strong>
            <ul>
              <li>Mach-O binary format</li>
              <li>Objective-C/Swift code analysis</li>
              <li>Class-dump for header extraction</li>
              <li>Strings and symbol analysis</li>
            </ul>
          </li>
          <li><strong>iOS Static Analysis Tools:</strong>
            <ul>
              <li>Hopper Disassembler</li>
              <li>IDA Pro for iOS analysis</li>
              <li>MobSF for iOS applications</li>
              <li>iMazing for IPA extraction</li>
            </ul>
          </li>
        </ul>
        
        <h3>iOS Dynamic Analysis</h3>
        <ul>
          <li><strong>iOS Testing Environment:</strong>
            <ul>
              <li>Jailbroken device setup</li>
              <li>iOS Simulator limitations</li>
              <li>Cydia and package management</li>
              <li>SSH access configuration</li>
            </ul>
          </li>
          <li><strong>Runtime Manipulation:</strong>
            <ul>
              <li>Frida for iOS instrumentation</li>
              <li>Cycript for runtime exploration</li>
              <li>LLDB debugging techniques</li>
              <li>Method swizzling and hooking</li>
            </ul>
          </li>
          <li><strong>iOS-Specific Bypasses:</strong>
            <ul>
              <li>Jailbreak detection bypass</li>
              <li>SSL pinning circumvention</li>
              <li>TouchID/FaceID bypass</li>
              <li>Anti-debugging evasion</li>
            </ul>
          </li>
        </ul>
        
        <h3>iOS-Specific Vulnerabilities</h3>
        <ul>
          <li><strong>Keychain Vulnerabilities:</strong>
            <ul>
              <li>Keychain access control</li>
              <li>Shared keychain groups</li>
              <li>Keychain dumping techniques</li>
              <li>Backup and restore issues</li>
            </ul>
          </li>
          <li><strong>URL Scheme Attacks:</strong>
            <ul>
              <li>Custom URL scheme hijacking</li>
              <li>Universal link vulnerabilities</li>
              <li>Deep link parameter injection</li>
              <li>Cross-app communication flaws</li>
            </ul>
          </li>
          <li><strong>Data Protection Issues:</strong>
            <ul>
              <li>File protection class bypass</li>
              <li>Backup encryption weaknesses</li>
              <li>Pasteboard data leakage</li>
              <li>Screenshot security issues</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which OWASP Mobile Top 10 category covers SSL pinning bypass vulnerabilities?",options:["M1: Improper Platform Usage","M3: Insecure Communication","M5: Insufficient Cryptography","M8: Code Tampering"],correctAnswer:1,explanation:"SSL pinning bypass vulnerabilities fall under M3: Insecure Communication, as they involve weaknesses in the secure communication between mobile applications and backend servers."},{question:"What is the primary tool used for runtime manipulation in both Android and iOS applications?",options:["Burp Suite","JADX","Frida","APKTool"],correctAnswer:2,explanation:"Frida is the primary tool used for runtime manipulation in both Android and iOS applications, providing dynamic instrumentation capabilities for method hooking and runtime analysis."},{question:"Which Android component is most commonly exploited through intent-based attacks?",options:["Services","Exported activities and broadcast receivers","Content providers","Application class"],correctAnswer:1,explanation:"Exported activities and broadcast receivers are most commonly exploited through intent-based attacks because they can receive intents from other applications, potentially leading to unauthorized access or data exposure."}]},type:"quiz"}]},r={id:"bb-9",pathId:"bug-bounty",title:"Business Logic Vulnerabilities",description:"Master the identification and exploitation of business logic flaws, including workflow bypasses, race conditions, and application-specific vulnerabilities that traditional scanners miss.",objectives:["Understand business logic vulnerability fundamentals","Learn to identify workflow and process flaws","Master race condition and timing attack techniques","Develop skills in application flow analysis","Learn to exploit payment and transaction logic","Create comprehensive business logic test cases"],difficulty:"Advanced",estimatedTime:140,sections:[{title:"Business Logic Fundamentals",content:`
        <h2>Business Logic Vulnerability Fundamentals</h2>
        <p>Business logic vulnerabilities arise from flaws in application design and implementation that allow attackers to abuse legitimate functionality in unintended ways.</p>
        
        <h3>Understanding Business Logic</h3>
        <ul>
          <li><strong>Business Logic Definition:</strong>
            <ul>
              <li>Application-specific rules and workflows</li>
              <li>Business process implementation</li>
              <li>User interaction patterns</li>
              <li>Data validation and processing logic</li>
            </ul>
          </li>
          <li><strong>Common Business Logic Flaws:</strong>
            <ul>
              <li>Workflow bypass vulnerabilities</li>
              <li>Insufficient process validation</li>
              <li>Race condition vulnerabilities</li>
              <li>Price manipulation attacks</li>
            </ul>
          </li>
          <li><strong>Why Logic Flaws are Missed:</strong>
            <ul>
              <li>Application-specific nature</li>
              <li>Require understanding of business context</li>
              <li>Not detectable by automated scanners</li>
              <li>Need manual testing and analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Business Logic Attack Categories</h3>
        <ul>
          <li><strong>Authentication and Authorization Bypass:</strong>
            <ul>
              <li>Multi-step authentication bypass</li>
              <li>Role-based access control flaws</li>
              <li>Session management vulnerabilities</li>
              <li>Password reset logic flaws</li>
            </ul>
          </li>
          <li><strong>Transaction and Payment Logic:</strong>
            <ul>
              <li>Price manipulation attacks</li>
              <li>Discount and coupon abuse</li>
              <li>Currency conversion flaws</li>
              <li>Refund and chargeback logic</li>
            </ul>
          </li>
          <li><strong>Workflow and Process Flaws:</strong>
            <ul>
              <li>Step skipping in multi-step processes</li>
              <li>State manipulation attacks</li>
              <li>Approval process bypass</li>
              <li>Data validation bypass</li>
            </ul>
          </li>
          <li><strong>Resource and Rate Limiting:</strong>
            <ul>
              <li>Resource exhaustion attacks</li>
              <li>Rate limiting bypass</li>
              <li>Quota and limit manipulation</li>
              <li>Time-based restriction bypass</li>
            </ul>
          </li>
        </ul>
        
        <h3>Business Logic Testing Methodology</h3>
        <ul>
          <li><strong>Application Understanding:</strong>
            <ul>
              <li>Business model analysis</li>
              <li>User role identification</li>
              <li>Workflow mapping</li>
              <li>Critical function identification</li>
            </ul>
          </li>
          <li><strong>Test Case Development:</strong>
            <ul>
              <li>Negative test case creation</li>
              <li>Edge case identification</li>
              <li>Boundary value testing</li>
              <li>State transition testing</li>
            </ul>
          </li>
          <li><strong>Manual Testing Approach:</strong>
            <ul>
              <li>Step-by-step process analysis</li>
              <li>Parameter manipulation testing</li>
              <li>Timing and sequence testing</li>
              <li>Multi-user scenario testing</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Race Conditions and Timing Attacks",content:`
        <h2>Race Conditions and Timing Attack Exploitation</h2>
        <p>Race conditions occur when application behavior depends on timing or sequence of events, allowing attackers to exploit timing windows for unauthorized access or actions.</p>
        
        <h3>Race Condition Fundamentals</h3>
        <ul>
          <li><strong>Race Condition Types:</strong>
            <ul>
              <li>Time-of-check to time-of-use (TOCTOU)</li>
              <li>Concurrent request processing</li>
              <li>Database transaction races</li>
              <li>File system race conditions</li>
            </ul>
          </li>
          <li><strong>Common Scenarios:</strong>
            <ul>
              <li>Account balance manipulation</li>
              <li>Coupon and discount abuse</li>
              <li>Resource allocation races</li>
              <li>Authentication bypass</li>
            </ul>
          </li>
          <li><strong>Detection Techniques:</strong>
            <ul>
              <li>Concurrent request analysis</li>
              <li>Timing measurement</li>
              <li>State observation</li>
              <li>Resource monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Exploitation Techniques</h3>
        <ul>
          <li><strong>Concurrent Request Generation:</strong>
            <ul>
              <li>Multi-threaded request tools</li>
              <li>Burp Suite Intruder</li>
              <li>Custom Python scripts</li>
              <li>Browser developer tools</li>
            </ul>
          </li>
          <li><strong>Timing Optimization:</strong>
            <ul>
              <li>Request synchronization</li>
              <li>Network latency consideration</li>
              <li>Server processing time analysis</li>
              <li>Connection pooling effects</li>
            </ul>
          </li>
          <li><strong>State Manipulation:</strong>
            <ul>
              <li>Session state races</li>
              <li>Database state manipulation</li>
              <li>Cache invalidation races</li>
              <li>File lock bypasses</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Race Condition Scenarios</h3>
        <ul>
          <li><strong>E-commerce Vulnerabilities:</strong>
            <ul>
              <li>Inventory manipulation</li>
              <li>Price change exploitation</li>
              <li>Cart modification races</li>
              <li>Payment processing races</li>
            </ul>
          </li>
          <li><strong>Financial Application Races:</strong>
            <ul>
              <li>Double spending attacks</li>
              <li>Balance manipulation</li>
              <li>Transfer limit bypass</li>
              <li>Interest calculation races</li>
            </ul>
          </li>
          <li><strong>Social Media and Content:</strong>
            <ul>
              <li>Like/vote manipulation</li>
              <li>Comment posting races</li>
              <li>Follow/unfollow races</li>
              <li>Content moderation bypass</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Workflow and Process Analysis",content:`
        <h2>Workflow and Process Vulnerability Analysis</h2>
        <p>Analyzing application workflows and business processes helps identify logic flaws that allow bypassing intended security controls and business rules.</p>
        
        <h3>Workflow Analysis Methodology</h3>
        <ul>
          <li><strong>Process Mapping:</strong>
            <ul>
              <li>User journey documentation</li>
              <li>State transition diagrams</li>
              <li>Decision point identification</li>
              <li>Validation checkpoint mapping</li>
            </ul>
          </li>
          <li><strong>Critical Path Analysis:</strong>
            <ul>
              <li>Security-critical steps</li>
              <li>Authorization checkpoints</li>
              <li>Data validation points</li>
              <li>Business rule enforcement</li>
            </ul>
          </li>
          <li><strong>Bypass Opportunity Identification:</strong>
            <ul>
              <li>Step skipping possibilities</li>
              <li>Alternative path discovery</li>
              <li>State manipulation opportunities</li>
              <li>Validation bypass techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Workflow Vulnerabilities</h3>
        <ul>
          <li><strong>Multi-Step Process Flaws:</strong>
            <ul>
              <li>Step sequence manipulation</li>
              <li>Incomplete process completion</li>
              <li>State persistence issues</li>
              <li>Session management flaws</li>
            </ul>
          </li>
          <li><strong>Approval and Review Processes:</strong>
            <ul>
              <li>Approval bypass techniques</li>
              <li>Review process manipulation</li>
              <li>Escalation path abuse</li>
              <li>Delegation mechanism flaws</li>
            </ul>
          </li>
          <li><strong>Registration and Onboarding:</strong>
            <ul>
              <li>Account creation bypass</li>
              <li>Verification process flaws</li>
              <li>Identity validation bypass</li>
              <li>Terms acceptance manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Testing Techniques and Tools</h3>
        <ul>
          <li><strong>Manual Testing Approaches:</strong>
            <ul>
              <li>Process flow manipulation</li>
              <li>Parameter tampering</li>
              <li>Request replay attacks</li>
              <li>State modification testing</li>
            </ul>
          </li>
          <li><strong>Automated Testing Tools:</strong>
            <ul>
              <li>Burp Suite workflow analysis</li>
              <li>Custom automation scripts</li>
              <li>State machine testing tools</li>
              <li>Process monitoring utilities</li>
            </ul>
          </li>
          <li><strong>Documentation and Reporting:</strong>
            <ul>
              <li>Workflow diagram creation</li>
              <li>Vulnerability impact assessment</li>
              <li>Business risk evaluation</li>
              <li>Remediation recommendations</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What makes business logic vulnerabilities particularly challenging to detect?",options:["They require expensive tools","They are application-specific and require understanding of business context","They only affect legacy applications","They are always encrypted"],correctAnswer:1,explanation:"Business logic vulnerabilities are challenging to detect because they are application-specific and require understanding of the business context, making them undetectable by automated scanners that focus on generic vulnerabilities."},{question:"Which type of race condition involves exploiting the time gap between checking and using a resource?",options:["Concurrent request processing","Database transaction races","Time-of-check to time-of-use (TOCTOU)","File system race conditions"],correctAnswer:2,explanation:"Time-of-check to time-of-use (TOCTOU) race conditions exploit the time gap between when a resource is checked (validated) and when it is actually used, allowing attackers to modify the resource in between."},{question:"What is the most effective approach for identifying workflow bypass vulnerabilities?",options:["Automated vulnerability scanning","Process mapping and manual step manipulation testing","Code review only","Network traffic analysis"],correctAnswer:1,explanation:"Process mapping and manual step manipulation testing is most effective for identifying workflow bypass vulnerabilities because it requires understanding the intended business process and manually testing deviations from the expected flow."}]},type:"quiz"}]},c={id:"bb-10",pathId:"bug-bounty",title:"Advanced Exploitation Techniques",description:"Master advanced exploitation techniques for complex vulnerabilities, including chaining attacks, bypassing modern security controls, and developing sophisticated proof-of-concepts.",objectives:["Understand advanced exploitation methodologies","Learn vulnerability chaining and combination attacks","Master modern security control bypass techniques","Develop sophisticated proof-of-concept creation skills","Learn advanced payload development and delivery","Create comprehensive exploitation strategies"],difficulty:"Expert",estimatedTime:160,sections:[{title:"Advanced Exploitation Fundamentals",content:`
        <h2>Advanced Exploitation Techniques</h2>
        <p>Advanced exploitation involves combining multiple vulnerabilities, bypassing modern security controls, and developing sophisticated attack chains to achieve maximum impact.</p>
        
        <h3>Exploitation Complexity Levels</h3>
        <ul>
          <li><strong>Basic Exploitation:</strong>
            <ul>
              <li>Single vulnerability exploitation</li>
              <li>Direct attack vectors</li>
              <li>Standard payloads and techniques</li>
              <li>Minimal security control bypass</li>
            </ul>
          </li>
          <li><strong>Intermediate Exploitation:</strong>
            <ul>
              <li>Multi-step attack sequences</li>
              <li>Basic security control bypass</li>
              <li>Custom payload development</li>
              <li>Context-specific exploitation</li>
            </ul>
          </li>
          <li><strong>Advanced Exploitation:</strong>
            <ul>
              <li>Complex vulnerability chaining</li>
              <li>Modern security control bypass</li>
              <li>Zero-day exploitation techniques</li>
              <li>Advanced persistent access</li>
            </ul>
          </li>
        </ul>
        
        <h3>Modern Security Controls</h3>
        <ul>
          <li><strong>Web Application Firewalls (WAF):</strong>
            <ul>
              <li>Signature-based detection</li>
              <li>Behavioral analysis engines</li>
              <li>Rate limiting and throttling</li>
              <li>IP reputation filtering</li>
            </ul>
          </li>
          <li><strong>Content Security Policy (CSP):</strong>
            <ul>
              <li>Script source restrictions</li>
              <li>Inline script blocking</li>
              <li>Resource loading controls</li>
              <li>Reporting mechanisms</li>
            </ul>
          </li>
          <li><strong>Same-Site Cookies:</strong>
            <ul>
              <li>CSRF protection mechanisms</li>
              <li>Cross-site request restrictions</li>
              <li>Cookie scope limitations</li>
              <li>Browser enforcement variations</li>
            </ul>
          </li>
          <li><strong>Modern Browser Security:</strong>
            <ul>
              <li>CORS (Cross-Origin Resource Sharing)</li>
              <li>Subresource Integrity (SRI)</li>
              <li>Feature Policy controls</li>
              <li>Trusted Types enforcement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Exploitation Planning and Strategy</h3>
        <ul>
          <li><strong>Target Analysis:</strong>
            <ul>
              <li>Security control identification</li>
              <li>Technology stack analysis</li>
              <li>Attack surface mapping</li>
              <li>Vulnerability correlation</li>
            </ul>
          </li>
          <li><strong>Attack Vector Selection:</strong>
            <ul>
              <li>Bypass technique evaluation</li>
              <li>Payload delivery methods</li>
              <li>Persistence mechanisms</li>
              <li>Impact maximization strategies</li>
            </ul>
          </li>
          <li><strong>Risk Assessment:</strong>
            <ul>
              <li>Detection probability analysis</li>
              <li>Legal and ethical considerations</li>
              <li>Collateral damage assessment</li>
              <li>Disclosure timeline planning</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Vulnerability Chaining and Combination Attacks",content:`
        <h2>Vulnerability Chaining and Combination Attacks</h2>
        <p>Chaining multiple vulnerabilities creates powerful attack scenarios that can bypass layered security controls and achieve significant impact.</p>
        
        <h3>Chaining Methodologies</h3>
        <ul>
          <li><strong>Sequential Chaining:</strong>
            <ul>
              <li>Step-by-step vulnerability exploitation</li>
              <li>Each vulnerability enables the next</li>
              <li>Progressive privilege escalation</li>
              <li>Cumulative impact building</li>
            </ul>
          </li>
          <li><strong>Parallel Chaining:</strong>
            <ul>
              <li>Simultaneous vulnerability exploitation</li>
              <li>Multiple attack vectors</li>
              <li>Redundant access paths</li>
              <li>Increased success probability</li>
            </ul>
          </li>
          <li><strong>Conditional Chaining:</strong>
            <ul>
              <li>Context-dependent exploitation</li>
              <li>Environmental condition requirements</li>
              <li>User interaction dependencies</li>
              <li>Timing-sensitive combinations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Chaining Patterns</h3>
        <ul>
          <li><strong>Information Disclosure + Injection:</strong>
            <ul>
              <li>Directory traversal revealing configuration</li>
              <li>Error messages exposing database structure</li>
              <li>Source code disclosure enabling targeted attacks</li>
              <li>API documentation leakage</li>
            </ul>
          </li>
          <li><strong>CSRF + XSS Combinations:</strong>
            <ul>
              <li>XSS bypassing CSRF tokens</li>
              <li>CSRF enabling XSS payload delivery</li>
              <li>Session fixation + CSRF</li>
              <li>Clickjacking + CSRF</li>
            </ul>
          </li>
          <li><strong>Authentication Bypass Chains:</strong>
            <ul>
              <li>Password reset + account enumeration</li>
              <li>Session management + privilege escalation</li>
              <li>OAuth flaws + account takeover</li>
              <li>2FA bypass + session hijacking</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Chaining Examples</h3>
        <ul>
          <li><strong>SSRF + Cloud Metadata:</strong>
            <ul>
              <li>SSRF accessing cloud metadata services</li>
              <li>IAM credential extraction</li>
              <li>Cloud resource enumeration</li>
              <li>Lateral movement in cloud environments</li>
            </ul>
          </li>
          <li><strong>Deserialization + RCE:</strong>
            <ul>
              <li>Insecure deserialization discovery</li>
              <li>Gadget chain development</li>
              <li>Remote code execution achievement</li>
              <li>System compromise and persistence</li>
            </ul>
          </li>
          <li><strong>XXE + File Disclosure:</strong>
            <ul>
              <li>XML external entity injection</li>
              <li>Local file inclusion</li>
              <li>Internal network scanning</li>
              <li>Sensitive data extraction</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Security Control Bypass Techniques",content:`
        <h2>Modern Security Control Bypass Techniques</h2>
        <p>Bypassing modern security controls requires understanding their implementation details and developing creative techniques to circumvent protection mechanisms.</p>
        
        <h3>WAF Bypass Techniques</h3>
        <ul>
          <li><strong>Payload Obfuscation:</strong>
            <ul>
              <li>Encoding and character manipulation</li>
              <li>Case variation and mixed encoding</li>
              <li>Comment insertion and whitespace</li>
              <li>Unicode normalization bypass</li>
            </ul>
          </li>
          <li><strong>Protocol-Level Bypasses:</strong>
            <ul>
              <li>HTTP parameter pollution</li>
              <li>Request method manipulation</li>
              <li>Header injection techniques</li>
              <li>Content-Type confusion</li>
            </ul>
          </li>
          <li><strong>Timing and Rate Bypasses:</strong>
            <ul>
              <li>Distributed attack sources</li>
              <li>Slow and low techniques</li>
              <li>Request spacing optimization</li>
              <li>Session rotation strategies</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSP Bypass Techniques</h3>
        <ul>
          <li><strong>Policy Weakness Exploitation:</strong>
            <ul>
              <li>Unsafe-inline and unsafe-eval</li>
              <li>Wildcard domain abuse</li>
              <li>JSONP endpoint exploitation</li>
              <li>Angular template injection</li>
            </ul>
          </li>
          <li><strong>Browser-Specific Bypasses:</strong>
            <ul>
              <li>Browser parsing differences</li>
              <li>Legacy browser vulnerabilities</li>
              <li>Extension and plugin abuse</li>
              <li>Developer tool manipulation</li>
            </ul>
          </li>
          <li><strong>Third-Party Service Abuse:</strong>
            <ul>
              <li>CDN and hosting service exploitation</li>
              <li>Whitelisted domain compromise</li>
              <li>Service worker manipulation</li>
              <li>Iframe sandbox escape</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Payload Development</h3>
        <ul>
          <li><strong>Polyglot Payloads:</strong>
            <ul>
              <li>Multi-context exploitation</li>
              <li>Cross-language payload development</li>
              <li>Format-agnostic attacks</li>
              <li>Universal bypass techniques</li>
            </ul>
          </li>
          <li><strong>Steganographic Payloads:</strong>
            <ul>
              <li>Hidden payload embedding</li>
              <li>Image and media file abuse</li>
              <li>DNS and network covert channels</li>
              <li>Social media platform abuse</li>
            </ul>
          </li>
          <li><strong>Living-off-the-Land Techniques:</strong>
            <ul>
              <li>Legitimate tool abuse</li>
              <li>Built-in functionality exploitation</li>
              <li>Administrative tool misuse</li>
              <li>System feature weaponization</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary advantage of vulnerability chaining over single vulnerability exploitation?",options:["Faster exploitation speed","Lower detection probability","Bypassing layered security controls and achieving greater impact","Simpler proof-of-concept development"],correctAnswer:2,explanation:"Vulnerability chaining's primary advantage is bypassing layered security controls and achieving greater impact by combining multiple vulnerabilities to overcome individual security measures that might block single-vulnerability attacks."},{question:"Which technique is most effective for bypassing signature-based WAF detection?",options:["Increasing request frequency","Using only GET requests","Payload obfuscation and encoding","Disabling JavaScript"],correctAnswer:2,explanation:"Payload obfuscation and encoding is most effective for bypassing signature-based WAF detection because it modifies the attack payload to avoid matching known attack signatures while maintaining functionality."},{question:"What makes CSP bypass particularly challenging in modern applications?",options:["Limited browser support","High computational requirements","Strict policy enforcement and limited unsafe directives","Lack of testing tools"],correctAnswer:2,explanation:"CSP bypass is challenging because modern applications implement strict policies with limited unsafe directives, requiring attackers to find creative ways to execute code within the policy constraints or exploit policy weaknesses."}]},type:"quiz"}]},u={id:"bb-11",pathId:"bug-bounty",title:"Professional Report Writing",description:"Master the art of writing professional vulnerability reports that maximize impact, ensure clear communication, and increase acceptance rates in bug bounty programs.",objectives:["Understand professional report writing standards","Learn to structure clear and comprehensive reports","Master technical writing and communication skills","Develop effective proof-of-concept creation","Learn impact assessment and risk communication","Create reports that maximize bounty rewards"],difficulty:"Intermediate",estimatedTime:110,sections:[{title:"Report Writing Fundamentals",content:`
        <h2>Professional Vulnerability Report Writing</h2>
        <p>Professional report writing is crucial for bug bounty success, ensuring vulnerabilities are clearly communicated, properly understood, and efficiently remediated.</p>
        
        <h3>Report Quality Importance</h3>
        <ul>
          <li><strong>Impact on Bounty Success:</strong>
            <ul>
              <li>Higher acceptance rates</li>
              <li>Increased bounty rewards</li>
              <li>Faster triage and resolution</li>
              <li>Improved researcher reputation</li>
            </ul>
          </li>
          <li><strong>Communication Benefits:</strong>
            <ul>
              <li>Clear vulnerability understanding</li>
              <li>Reduced back-and-forth clarification</li>
              <li>Efficient remediation guidance</li>
              <li>Professional relationship building</li>
            </ul>
          </li>
          <li><strong>Common Report Failures:</strong>
            <ul>
              <li>Insufficient technical detail</li>
              <li>Poor impact explanation</li>
              <li>Missing reproduction steps</li>
              <li>Unclear proof-of-concept</li>
            </ul>
          </li>
        </ul>
        
        <h3>Report Structure Standards</h3>
        <ul>
          <li><strong>Executive Summary:</strong>
            <ul>
              <li>Vulnerability overview</li>
              <li>Business impact summary</li>
              <li>Risk level assessment</li>
              <li>Recommended priority</li>
            </ul>
          </li>
          <li><strong>Technical Details:</strong>
            <ul>
              <li>Vulnerability description</li>
              <li>Affected components</li>
              <li>Root cause analysis</li>
              <li>Technical impact explanation</li>
            </ul>
          </li>
          <li><strong>Reproduction Information:</strong>
            <ul>
              <li>Step-by-step instructions</li>
              <li>Required tools and setup</li>
              <li>Expected vs. actual results</li>
              <li>Environmental considerations</li>
            </ul>
          </li>
          <li><strong>Supporting Evidence:</strong>
            <ul>
              <li>Screenshots and videos</li>
              <li>Code snippets and payloads</li>
              <li>Network traffic captures</li>
              <li>Log file excerpts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Writing Style and Tone</h3>
        <ul>
          <li><strong>Professional Communication:</strong>
            <ul>
              <li>Clear and concise language</li>
              <li>Technical accuracy</li>
              <li>Respectful and constructive tone</li>
              <li>Objective and factual presentation</li>
            </ul>
          </li>
          <li><strong>Audience Considerations:</strong>
            <ul>
              <li>Technical vs. business stakeholders</li>
              <li>Security team expertise levels</li>
              <li>Developer background knowledge</li>
              <li>Management decision-making needs</li>
            </ul>
          </li>
          <li><strong>Clarity and Precision:</strong>
            <ul>
              <li>Specific terminology usage</li>
              <li>Unambiguous instructions</li>
              <li>Logical information flow</li>
              <li>Consistent formatting</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Technical Documentation and Evidence",content:`
        <h2>Technical Documentation and Evidence Collection</h2>
        <p>Comprehensive technical documentation and evidence collection are essential for demonstrating vulnerability validity and facilitating effective remediation.</p>
        
        <h3>Reproduction Steps Documentation</h3>
        <ul>
          <li><strong>Step-by-Step Instructions:</strong>
            <ul>
              <li>Numbered sequential steps</li>
              <li>Specific parameter values</li>
              <li>Required user actions</li>
              <li>Expected system responses</li>
            </ul>
          </li>
          <li><strong>Environmental Setup:</strong>
            <ul>
              <li>Browser and version requirements</li>
              <li>Tool configuration details</li>
              <li>Account and permission needs</li>
              <li>Network and proxy settings</li>
            </ul>
          </li>
          <li><strong>Payload Documentation:</strong>
            <ul>
              <li>Complete payload strings</li>
              <li>Encoding and formatting notes</li>
              <li>Customization instructions</li>
              <li>Alternative payload options</li>
            </ul>
          </li>
        </ul>
        
        <h3>Visual Evidence Creation</h3>
        <ul>
          <li><strong>Screenshot Best Practices:</strong>
            <ul>
              <li>High-resolution captures</li>
              <li>Relevant information highlighting</li>
              <li>Multiple perspective views</li>
              <li>Annotation and callouts</li>
            </ul>
          </li>
          <li><strong>Video Demonstrations:</strong>
            <ul>
              <li>Screen recording setup</li>
              <li>Clear narration or captions</li>
              <li>Focused content presentation</li>
              <li>Appropriate length and pacing</li>
            </ul>
          </li>
          <li><strong>Network Traffic Evidence:</strong>
            <ul>
              <li>HTTP request/response captures</li>
              <li>Burp Suite history exports</li>
              <li>Wireshark packet captures</li>
              <li>cURL command examples</li>
            </ul>
          </li>
        </ul>
        
        <h3>Code and Log Analysis</h3>
        <ul>
          <li><strong>Source Code References:</strong>
            <ul>
              <li>Vulnerable code snippets</li>
              <li>Line number references</li>
              <li>Function and method identification</li>
              <li>Code flow explanation</li>
            </ul>
          </li>
          <li><strong>Log File Analysis:</strong>
            <ul>
              <li>Relevant log entries</li>
              <li>Error message documentation</li>
              <li>Timestamp correlation</li>
              <li>Pattern identification</li>
            </ul>
          </li>
          <li><strong>Database Evidence:</strong>
            <ul>
              <li>Query result screenshots</li>
              <li>Schema information</li>
              <li>Data modification proof</li>
              <li>Injection payload effects</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Impact Assessment and Risk Communication",content:`
        <h2>Impact Assessment and Risk Communication</h2>
        <p>Effective impact assessment and risk communication help stakeholders understand vulnerability severity and prioritize remediation efforts appropriately.</p>
        
        <h3>Impact Assessment Framework</h3>
        <ul>
          <li><strong>Technical Impact:</strong>
            <ul>
              <li>System compromise level</li>
              <li>Data access and modification</li>
              <li>Service availability effects</li>
              <li>Privilege escalation potential</li>
            </ul>
          </li>
          <li><strong>Business Impact:</strong>
            <ul>
              <li>Financial loss potential</li>
              <li>Reputation damage risk</li>
              <li>Regulatory compliance issues</li>
              <li>Customer trust implications</li>
            </ul>
          </li>
          <li><strong>Operational Impact:</strong>
            <ul>
              <li>Service disruption potential</li>
              <li>Recovery time requirements</li>
              <li>Resource allocation needs</li>
              <li>Incident response activation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Risk Scoring and Prioritization</h3>
        <ul>
          <li><strong>CVSS Scoring:</strong>
            <ul>
              <li>Base score calculation</li>
              <li>Temporal score factors</li>
              <li>Environmental score adjustments</li>
              <li>Score justification explanation</li>
            </ul>
          </li>
          <li><strong>Custom Risk Metrics:</strong>
            <ul>
              <li>Organization-specific factors</li>
              <li>Asset criticality weighting</li>
              <li>Threat landscape considerations</li>
              <li>Exploitability assessment</li>
            </ul>
          </li>
          <li><strong>Comparative Analysis:</strong>
            <ul>
              <li>Similar vulnerability references</li>
              <li>Industry standard comparisons</li>
              <li>Historical incident examples</li>
              <li>Peer organization impacts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remediation Guidance</h3>
        <ul>
          <li><strong>Fix Recommendations:</strong>
            <ul>
              <li>Specific remediation steps</li>
              <li>Code change suggestions</li>
              <li>Configuration modifications</li>
              <li>Architecture improvements</li>
            </ul>
          </li>
          <li><strong>Temporary Mitigations:</strong>
            <ul>
              <li>Immediate protection measures</li>
              <li>WAF rule suggestions</li>
              <li>Access control adjustments</li>
              <li>Monitoring enhancements</li>
            </ul>
          </li>
          <li><strong>Verification Methods:</strong>
            <ul>
              <li>Fix validation procedures</li>
              <li>Testing methodologies</li>
              <li>Regression test suggestions</li>
              <li>Ongoing monitoring recommendations</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the most important element of a vulnerability report for ensuring quick triage?",options:["Detailed technical analysis","Clear reproduction steps","Comprehensive impact assessment","Professional formatting"],correctAnswer:1,explanation:"Clear reproduction steps are most important for quick triage because they allow security teams to immediately verify the vulnerability's existence and understand its scope without extensive investigation."},{question:"Which type of evidence is most effective for demonstrating the real-world impact of a vulnerability?",options:["Source code snippets","Network traffic captures","Video demonstrations showing exploitation","Log file excerpts"],correctAnswer:2,explanation:"Video demonstrations showing exploitation are most effective for demonstrating real-world impact because they provide visual proof of the vulnerability being exploited and its actual effects on the system."},{question:"What should be the primary focus when writing impact assessments for business stakeholders?",options:["Technical implementation details","Business consequences and financial implications","Code-level vulnerabilities","Network architecture issues"],correctAnswer:1,explanation:"Business consequences and financial implications should be the primary focus for business stakeholders because they need to understand the potential business impact to make informed decisions about prioritization and resource allocation."}]},type:"quiz"}]},d={id:"bb-12",pathId:"bug-bounty",title:"Legal and Compliance Considerations",description:"Master the legal and ethical aspects of bug bounty hunting, including program terms, responsible disclosure, international law, and compliance requirements.",objectives:["Understand legal frameworks governing bug bounty activities","Learn responsible disclosure principles and practices","Master program terms and scope compliance","Develop skills in international law considerations","Learn to navigate legal risks and protections","Create compliant testing methodologies"],difficulty:"Intermediate",estimatedTime:100,sections:[{title:"Legal Framework and Foundations",content:`
        <h2>Legal Framework for Bug Bounty Activities</h2>
        <p>Understanding the legal landscape is crucial for bug bounty hunters to operate within legal boundaries while maximizing their research effectiveness.</p>
        
        <h3>Legal Foundations</h3>
        <ul>
          <li><strong>Computer Crime Laws:</strong>
            <ul>
              <li>Computer Fraud and Abuse Act (CFAA) - United States</li>
              <li>Computer Misuse Act - United Kingdom</li>
              <li>Criminal Code provisions - Various countries</li>
              <li>Cybercrime legislation worldwide</li>
            </ul>
          </li>
          <li><strong>Authorized vs. Unauthorized Access:</strong>
            <ul>
              <li>Explicit permission requirements</li>
              <li>Scope and boundary definitions</li>
              <li>Terms of service implications</li>
              <li>Legal safe harbor provisions</li>
            </ul>
          </li>
          <li><strong>Intellectual Property Considerations:</strong>
            <ul>
              <li>Copyright and software licensing</li>
              <li>Trade secret protection</li>
              <li>Patent considerations</li>
              <li>Reverse engineering legality</li>
            </ul>
          </li>
        </ul>
        
        <h3>Bug Bounty Program Legal Protections</h3>
        <ul>
          <li><strong>Safe Harbor Provisions:</strong>
            <ul>
              <li>Legal protection for authorized testing</li>
              <li>Good faith research exemptions</li>
              <li>Compliance with program terms</li>
              <li>Responsible disclosure requirements</li>
            </ul>
          </li>
          <li><strong>Program Terms and Conditions:</strong>
            <ul>
              <li>Scope definition and limitations</li>
              <li>Prohibited activities and methods</li>
              <li>Data handling requirements</li>
              <li>Disclosure timeline obligations</li>
            </ul>
          </li>
          <li><strong>Legal Documentation:</strong>
            <ul>
              <li>Participation agreements</li>
              <li>Non-disclosure agreements (NDAs)</li>
              <li>Liability limitation clauses</li>
              <li>Dispute resolution mechanisms</li>
            </ul>
          </li>
        </ul>
        
        <h3>International Legal Considerations</h3>
        <ul>
          <li><strong>Cross-Border Research:</strong>
            <ul>
              <li>Jurisdictional complexities</li>
              <li>Conflicting legal frameworks</li>
              <li>Data protection regulations</li>
              <li>Extradition and enforcement</li>
            </ul>
          </li>
          <li><strong>Regional Variations:</strong>
            <ul>
              <li>European Union regulations</li>
              <li>Asia-Pacific legal frameworks</li>
              <li>Developing country considerations</li>
              <li>Cultural and legal differences</li>
            </ul>
          </li>
          <li><strong>Compliance Strategies:</strong>
            <ul>
              <li>Multi-jurisdictional compliance</li>
              <li>Legal counsel consultation</li>
              <li>Risk assessment and mitigation</li>
              <li>Documentation and record keeping</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Responsible Disclosure and Ethics",content:`
        <h2>Responsible Disclosure and Ethical Practices</h2>
        <p>Responsible disclosure is the cornerstone of ethical bug bounty hunting, balancing security research benefits with organizational and public safety.</p>
        
        <h3>Responsible Disclosure Principles</h3>
        <ul>
          <li><strong>Core Principles:</strong>
            <ul>
              <li>Minimize harm and disruption</li>
              <li>Respect privacy and confidentiality</li>
              <li>Provide reasonable disclosure timelines</li>
              <li>Collaborate with affected organizations</li>
            </ul>
          </li>
          <li><strong>Disclosure Timeline:</strong>
            <ul>
              <li>Initial vulnerability notification</li>
              <li>Vendor acknowledgment period</li>
              <li>Remediation development time</li>
              <li>Public disclosure coordination</li>
            </ul>
          </li>
          <li><strong>Communication Standards:</strong>
            <ul>
              <li>Clear and professional reporting</li>
              <li>Technical accuracy and completeness</li>
              <li>Constructive remediation guidance</li>
              <li>Ongoing collaboration and support</li>
            </ul>
          </li>
        </ul>
        
        <h3>Ethical Testing Boundaries</h3>
        <ul>
          <li><strong>Scope Compliance:</strong>
            <ul>
              <li>In-scope asset identification</li>
              <li>Out-of-scope restriction adherence</li>
              <li>Testing method limitations</li>
              <li>Data access restrictions</li>
            </ul>
          </li>
          <li><strong>Impact Minimization:</strong>
            <ul>
              <li>Non-destructive testing methods</li>
              <li>Service availability protection</li>
              <li>Data integrity preservation</li>
              <li>User privacy protection</li>
            </ul>
          </li>
          <li><strong>Professional Conduct:</strong>
            <ul>
              <li>Honest and transparent communication</li>
              <li>Conflict of interest disclosure</li>
              <li>Respectful stakeholder interaction</li>
              <li>Community reputation building</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Protection and Privacy</h3>
        <ul>
          <li><strong>Personal Data Handling:</strong>
            <ul>
              <li>GDPR compliance requirements</li>
              <li>Data minimization principles</li>
              <li>Consent and lawful basis</li>
              <li>Data subject rights protection</li>
            </ul>
          </li>
          <li><strong>Sensitive Information Management:</strong>
            <ul>
              <li>PII (Personally Identifiable Information) protection</li>
              <li>Financial data safeguarding</li>
              <li>Healthcare information security</li>
              <li>Trade secret confidentiality</li>
            </ul>
          </li>
          <li><strong>Evidence Collection Ethics:</strong>
            <ul>
              <li>Minimal data collection</li>
              <li>Secure storage and transmission</li>
              <li>Timely data deletion</li>
              <li>Access control and encryption</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Risk Management and Legal Protection",content:`
        <h2>Risk Management and Legal Protection Strategies</h2>
        <p>Effective risk management and legal protection strategies help bug bounty hunters minimize legal exposure while maintaining research effectiveness.</p>
        
        <h3>Legal Risk Assessment</h3>
        <ul>
          <li><strong>Risk Identification:</strong>
            <ul>
              <li>Criminal liability exposure</li>
              <li>Civil lawsuit potential</li>
              <li>Regulatory violation risks</li>
              <li>Professional reputation damage</li>
            </ul>
          </li>
          <li><strong>Risk Evaluation:</strong>
            <ul>
              <li>Probability assessment</li>
              <li>Impact severity analysis</li>
              <li>Legal precedent review</li>
              <li>Jurisdiction-specific factors</li>
            </ul>
          </li>
          <li><strong>Risk Mitigation:</strong>
            <ul>
              <li>Legal counsel consultation</li>
              <li>Insurance coverage evaluation</li>
              <li>Documentation and evidence</li>
              <li>Professional association membership</li>
            </ul>
          </li>
        </ul>
        
        <h3>Documentation and Record Keeping</h3>
        <ul>
          <li><strong>Research Documentation:</strong>
            <ul>
              <li>Methodology and approach records</li>
              <li>Timeline and activity logs</li>
              <li>Communication records</li>
              <li>Evidence collection documentation</li>
            </ul>
          </li>
          <li><strong>Legal Compliance Records:</strong>
            <ul>
              <li>Program terms acceptance</li>
              <li>Scope verification documentation</li>
              <li>Permission and authorization</li>
              <li>Disclosure timeline compliance</li>
            </ul>
          </li>
          <li><strong>Professional Development:</strong>
            <ul>
              <li>Training and certification records</li>
              <li>Professional association membership</li>
              <li>Continuing education documentation</li>
              <li>Ethical guideline adherence</li>
            </ul>
          </li>
        </ul>
        
        <h3>Incident Response and Legal Support</h3>
        <ul>
          <li><strong>Legal Incident Preparation:</strong>
            <ul>
              <li>Legal counsel identification</li>
              <li>Emergency contact procedures</li>
              <li>Documentation preservation</li>
              <li>Communication protocols</li>
            </ul>
          </li>
          <li><strong>Response Strategies:</strong>
            <ul>
              <li>Immediate action procedures</li>
              <li>Stakeholder communication</li>
              <li>Evidence preservation</li>
              <li>Legal representation coordination</li>
            </ul>
          </li>
          <li><strong>Professional Support Networks:</strong>
            <ul>
              <li>Security researcher communities</li>
              <li>Legal aid organizations</li>
              <li>Professional associations</li>
              <li>Industry mentorship programs</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the most important factor in determining whether bug bounty research is legally protected?",options:["The severity of vulnerabilities found","The researcher's professional credentials","Compliance with program terms and authorized scope","The financial value of bounty rewards"],correctAnswer:2,explanation:"Compliance with program terms and authorized scope is most important for legal protection because it establishes explicit permission for security research activities within defined boundaries."},{question:"Which principle is fundamental to responsible disclosure practices?",options:["Immediate public disclosure","Maximizing financial rewards","Minimizing harm while allowing reasonable remediation time","Keeping vulnerabilities secret indefinitely"],correctAnswer:2,explanation:"Minimizing harm while allowing reasonable remediation time is fundamental to responsible disclosure, balancing public safety with giving organizations time to fix vulnerabilities before public disclosure."},{question:"What is the primary legal risk when conducting cross-border bug bounty research?",options:["Currency exchange complications","Language barriers","Conflicting legal frameworks and jurisdictional issues","Time zone differences"],correctAnswer:2,explanation:"Conflicting legal frameworks and jurisdictional issues present the primary legal risk in cross-border research because different countries have varying laws regarding computer security research and unauthorized access."}]},type:"quiz"}]},g={id:"bb-13",pathId:"bug-bounty",title:"Community and Networking",description:"Master the art of building professional networks, engaging with the security community, and leveraging relationships for career advancement and knowledge sharing in bug bounty hunting.",objectives:["Understand the importance of community engagement","Learn to build professional security networks","Master online and offline networking strategies","Develop mentorship and knowledge sharing skills","Learn to leverage community resources effectively","Create lasting professional relationships"],difficulty:"Beginner",estimatedTime:90,sections:[{title:"Security Community Landscape",content:`
        <h2>Security Community and Professional Networking</h2>
        <p>The cybersecurity community is a valuable resource for learning, collaboration, and career advancement. Building strong professional networks is essential for long-term success in bug bounty hunting.</p>
        
        <h3>Community Importance</h3>
        <ul>
          <li><strong>Knowledge Sharing Benefits:</strong>
            <ul>
              <li>Learning from experienced researchers</li>
              <li>Staying updated on latest techniques</li>
              <li>Discovering new tools and methodologies</li>
              <li>Understanding industry trends</li>
            </ul>
          </li>
          <li><strong>Professional Development:</strong>
            <ul>
              <li>Career advancement opportunities</li>
              <li>Skill development and mentorship</li>
              <li>Industry recognition and reputation</li>
              <li>Job referrals and recommendations</li>
            </ul>
          </li>
          <li><strong>Collaboration Opportunities:</strong>
            <ul>
              <li>Joint research projects</li>
              <li>Team-based bug bounty hunting</li>
              <li>Tool development partnerships</li>
              <li>Conference speaking opportunities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Community Platforms and Channels</h3>
        <ul>
          <li><strong>Online Communities:</strong>
            <ul>
              <li>Twitter security community (#InfoSec, #BugBounty)</li>
              <li>Discord servers and Slack workspaces</li>
              <li>Reddit communities (r/netsec, r/bugbounty)</li>
              <li>Specialized forums and platforms</li>
            </ul>
          </li>
          <li><strong>Professional Platforms:</strong>
            <ul>
              <li>LinkedIn security groups</li>
              <li>GitHub open source projects</li>
              <li>Medium and blog platforms</li>
              <li>YouTube and educational content</li>
            </ul>
          </li>
          <li><strong>Bug Bounty Platforms:</strong>
            <ul>
              <li>HackerOne community features</li>
              <li>Bugcrowd researcher networks</li>
              <li>Intigriti community programs</li>
              <li>Platform-specific forums and chats</li>
            </ul>
          </li>
        </ul>
        
        <h3>Community Etiquette and Best Practices</h3>
        <ul>
          <li><strong>Professional Behavior:</strong>
            <ul>
              <li>Respectful and constructive communication</li>
              <li>Helpful and supportive attitude</li>
              <li>Ethical conduct and integrity</li>
              <li>Inclusive and welcoming approach</li>
            </ul>
          </li>
          <li><strong>Knowledge Sharing Ethics:</strong>
            <ul>
              <li>Responsible disclosure practices</li>
              <li>Proper attribution and credit</li>
              <li>Avoiding sensitive information sharing</li>
              <li>Respecting intellectual property</li>
            </ul>
          </li>
          <li><strong>Community Contribution:</strong>
            <ul>
              <li>Sharing knowledge and experiences</li>
              <li>Helping newcomers and beginners</li>
              <li>Contributing to open source projects</li>
              <li>Organizing and participating in events</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Building Professional Networks",content:`
        <h2>Building and Maintaining Professional Networks</h2>
        <p>Effective networking requires strategic relationship building, consistent engagement, and mutual value creation within the security community.</p>
        
        <h3>Networking Strategies</h3>
        <ul>
          <li><strong>Online Networking:</strong>
            <ul>
              <li>Social media engagement and interaction</li>
              <li>Content creation and sharing</li>
              <li>Community participation and contribution</li>
              <li>Virtual event attendance</li>
            </ul>
          </li>
          <li><strong>Conference and Event Networking:</strong>
            <ul>
              <li>Security conference attendance</li>
              <li>Workshop and training participation</li>
              <li>Meetup and local group involvement</li>
              <li>Speaking and presentation opportunities</li>
            </ul>
          </li>
          <li><strong>Professional Development:</strong>
            <ul>
              <li>Certification and training programs</li>
              <li>Industry association membership</li>
              <li>Volunteer work and community service</li>
              <li>Research collaboration projects</li>
            </ul>
          </li>
        </ul>
        
        <h3>Relationship Building Techniques</h3>
        <ul>
          <li><strong>Value-First Approach:</strong>
            <ul>
              <li>Offering help before asking for assistance</li>
              <li>Sharing valuable resources and insights</li>
              <li>Providing constructive feedback</li>
              <li>Making meaningful introductions</li>
            </ul>
          </li>
          <li><strong>Consistent Engagement:</strong>
            <ul>
              <li>Regular communication and interaction</li>
              <li>Following up on conversations</li>
              <li>Remembering personal details and interests</li>
              <li>Celebrating others' achievements</li>
            </ul>
          </li>
          <li><strong>Authentic Communication:</strong>
            <ul>
              <li>Genuine interest in others' work</li>
              <li>Honest and transparent interactions</li>
              <li>Respectful disagreement and debate</li>
              <li>Personal story and experience sharing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Mentorship and Knowledge Transfer</h3>
        <ul>
          <li><strong>Finding Mentors:</strong>
            <ul>
              <li>Identifying experienced professionals</li>
              <li>Approaching potential mentors respectfully</li>
              <li>Defining mentorship goals and expectations</li>
              <li>Maintaining mentor relationships</li>
            </ul>
          </li>
          <li><strong>Becoming a Mentor:</strong>
            <ul>
              <li>Sharing knowledge with newcomers</li>
              <li>Providing guidance and support</li>
              <li>Creating educational content</li>
              <li>Facilitating learning opportunities</li>
            </ul>
          </li>
          <li><strong>Peer Learning:</strong>
            <ul>
              <li>Study groups and learning circles</li>
              <li>Collaborative research projects</li>
              <li>Skill exchange and teaching</li>
              <li>Challenge and competition participation</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Career Development and Opportunities",content:`
        <h2>Career Development and Professional Opportunities</h2>
        <p>Leveraging community connections and professional networks can open doors to career advancement, job opportunities, and professional growth in cybersecurity.</p>
        
        <h3>Career Pathways</h3>
        <ul>
          <li><strong>Bug Bounty Career Progression:</strong>
            <ul>
              <li>Part-time to full-time hunting</li>
              <li>Specialization in specific domains</li>
              <li>Platform ambassador and community roles</li>
              <li>Independent security consulting</li>
            </ul>
          </li>
          <li><strong>Traditional Security Roles:</strong>
            <ul>
              <li>Penetration testing and red teaming</li>
              <li>Application security engineering</li>
              <li>Security research and development</li>
              <li>Security consulting and advisory</li>
            </ul>
          </li>
          <li><strong>Leadership and Management:</strong>
            <ul>
              <li>Security team leadership</li>
              <li>Program management roles</li>
              <li>Executive security positions</li>
              <li>Entrepreneurship and startup founding</li>
            </ul>
          </li>
        </ul>
        
        <h3>Professional Branding and Reputation</h3>
        <ul>
          <li><strong>Online Presence Development:</strong>
            <ul>
              <li>Professional social media profiles</li>
              <li>Personal website and portfolio</li>
              <li>Blog writing and content creation</li>
              <li>Speaking and presentation portfolio</li>
            </ul>
          </li>
          <li><strong>Thought Leadership:</strong>
            <ul>
              <li>Research publication and sharing</li>
              <li>Industry trend analysis</li>
              <li>Tool and methodology development</li>
              <li>Community education and training</li>
            </ul>
          </li>
          <li><strong>Recognition and Awards:</strong>
            <ul>
              <li>Bug bounty platform rankings</li>
              <li>Industry award nominations</li>
              <li>Conference speaking invitations</li>
              <li>Media interviews and features</li>
            </ul>
          </li>
        </ul>
        
        <h3>Continuous Learning and Development</h3>
        <ul>
          <li><strong>Skill Development:</strong>
            <ul>
              <li>Technical skill advancement</li>
              <li>Soft skill development</li>
              <li>Business and management skills</li>
              <li>Communication and presentation abilities</li>
            </ul>
          </li>
          <li><strong>Industry Engagement:</strong>
            <ul>
              <li>Conference and event participation</li>
              <li>Professional association involvement</li>
              <li>Standards and policy contribution</li>
              <li>Research and academic collaboration</li>
            </ul>
          </li>
          <li><strong>Knowledge Sharing:</strong>
            <ul>
              <li>Teaching and training others</li>
              <li>Writing and publication</li>
              <li>Open source contribution</li>
              <li>Community leadership roles</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the most effective approach for building professional relationships in the security community?",options:["Asking for help immediately upon meeting someone","Focusing only on high-profile individuals","Offering value and help before asking for assistance","Attending events without engaging with others"],correctAnswer:2,explanation:"Offering value and help before asking for assistance is most effective because it builds trust, demonstrates expertise, and creates reciprocal relationships based on mutual benefit rather than one-sided requests."},{question:"Which platform is most important for building a professional security research reputation?",options:["Only LinkedIn","Only Twitter","A combination of multiple platforms including social media, blogs, and conferences","Only bug bounty platforms"],correctAnswer:2,explanation:"A combination of multiple platforms is most important because it provides broader reach, diverse audiences, and multiple touchpoints for professional engagement, allowing for comprehensive reputation building across the security community."},{question:"What is the primary benefit of mentoring others in the security community?",options:["Immediate financial rewards","Reducing competition","Reinforcing your own knowledge while building relationships and reputation","Gaining access to exclusive programs"],correctAnswer:2,explanation:"Mentoring others reinforces your own knowledge through teaching, builds strong professional relationships, and enhances your reputation as a knowledgeable and helpful community member, creating long-term career benefits."}]},type:"quiz"}]},p={id:"bb-14",pathId:"bug-bounty",title:"SSRF (Server-Side Request Forgery) Exploitation",description:"Master Server-Side Request Forgery (SSRF) vulnerabilities, including detection techniques, exploitation methods, and advanced bypass strategies for modern applications.",objectives:["Understand SSRF vulnerability fundamentals and types","Learn SSRF detection and identification techniques","Master basic and advanced SSRF exploitation methods","Develop skills in SSRF filter and protection bypass","Learn cloud-specific SSRF attack vectors","Create comprehensive SSRF testing methodologies"],difficulty:"Advanced",estimatedTime:130,sections:[{title:"SSRF Fundamentals and Types",content:`
        <h2>Server-Side Request Forgery (SSRF) Fundamentals</h2>
        <p>SSRF vulnerabilities allow attackers to make requests from the server to internal or external resources, potentially accessing sensitive data or services not directly accessible.</p>
        
        <h3>SSRF Vulnerability Types</h3>
        <ul>
          <li><strong>Basic SSRF:</strong>
            <ul>
              <li>Direct server-side request manipulation</li>
              <li>URL parameter exploitation</li>
              <li>File upload and processing abuse</li>
              <li>Webhook and callback manipulation</li>
            </ul>
          </li>
          <li><strong>Blind SSRF:</strong>
            <ul>
              <li>No direct response visibility</li>
              <li>Time-based detection methods</li>
              <li>Out-of-band confirmation techniques</li>
              <li>DNS and HTTP log analysis</li>
            </ul>
          </li>
          <li><strong>Semi-Blind SSRF:</strong>
            <ul>
              <li>Partial response information</li>
              <li>Error message analysis</li>
              <li>Response time variations</li>
              <li>Status code differences</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common SSRF Attack Vectors</h3>
        <ul>
          <li><strong>URL-Based Parameters:</strong>
            <ul>
              <li>Direct URL manipulation</li>
              <li>Redirect and fetch functionality</li>
              <li>Image and media processing</li>
              <li>PDF generation and conversion</li>
            </ul>
          </li>
          <li><strong>File Processing Functions:</strong>
            <ul>
              <li>XML external entity (XXE) exploitation</li>
              <li>SVG file processing</li>
              <li>Office document conversion</li>
              <li>Image metadata processing</li>
            </ul>
          </li>
          <li><strong>API and Integration Points:</strong>
            <ul>
              <li>Webhook configuration</li>
              <li>Third-party service integration</li>
              <li>Proxy and gateway functions</li>
              <li>Health check and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSRF Impact and Consequences</h3>
        <ul>
          <li><strong>Internal Network Access:</strong>
            <ul>
              <li>Internal service enumeration</li>
              <li>Database and cache access</li>
              <li>Administrative interface exposure</li>
              <li>Network topology discovery</li>
            </ul>
          </li>
          <li><strong>Cloud Metadata Exploitation:</strong>
            <ul>
              <li>AWS EC2 metadata service</li>
              <li>Azure instance metadata</li>
              <li>Google Cloud metadata API</li>
              <li>Credential and token extraction</li>
            </ul>
          </li>
          <li><strong>External Service Abuse:</strong>
            <ul>
              <li>Port scanning and reconnaissance</li>
              <li>Service fingerprinting</li>
              <li>Denial of service attacks</li>
              <li>Protocol smuggling</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"SSRF Detection and Identification",content:`
        <h2>SSRF Detection and Identification Techniques</h2>
        <p>Effective SSRF detection requires understanding application functionality and systematically testing various input vectors for server-side request capabilities.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Parameter Analysis:</strong>
            <ul>
              <li>URL parameter identification</li>
              <li>Hidden form field discovery</li>
              <li>JSON and XML payload analysis</li>
              <li>HTTP header manipulation</li>
            </ul>
          </li>
          <li><strong>Functionality Mapping:</strong>
            <ul>
              <li>File upload and processing features</li>
              <li>Image and media handling</li>
              <li>Webhook and callback configuration</li>
              <li>Import and export functions</li>
            </ul>
          </li>
          <li><strong>Response Analysis:</strong>
            <ul>
              <li>Response time measurement</li>
              <li>Error message examination</li>
              <li>Content length variations</li>
              <li>HTTP status code analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Testing Techniques</h3>
        <ul>
          <li><strong>Basic SSRF Testing:</strong>
            <ul>
              <li>Localhost and loopback testing</li>
              <li>Internal IP range scanning</li>
              <li>External service requests</li>
              <li>Protocol scheme manipulation</li>
            </ul>
          </li>
          <li><strong>Blind SSRF Detection:</strong>
            <ul>
              <li>DNS interaction testing</li>
              <li>HTTP callback services</li>
              <li>Time-based confirmation</li>
              <li>Out-of-band data exfiltration</li>
            </ul>
          </li>
          <li><strong>Advanced Detection Methods:</strong>
            <ul>
              <li>Protocol smuggling attempts</li>
              <li>Encoding and obfuscation testing</li>
              <li>Redirect chain exploitation</li>
              <li>DNS rebinding techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Detection Tools and Automation</h3>
        <ul>
          <li><strong>Manual Testing Tools:</strong>
            <ul>
              <li>Burp Suite SSRF detection</li>
              <li>OWASP ZAP SSRF scanner</li>
              <li>Custom Python scripts</li>
              <li>Browser developer tools</li>
            </ul>
          </li>
          <li><strong>Automated Scanners:</strong>
            <ul>
              <li>SSRFmap for comprehensive testing</li>
              <li>Nuclei SSRF templates</li>
              <li>Custom automation frameworks</li>
              <li>CI/CD integration tools</li>
            </ul>
          </li>
          <li><strong>Callback and Interaction Services:</strong>
            <ul>
              <li>Burp Collaborator</li>
              <li>Interactsh for out-of-band testing</li>
              <li>Custom callback servers</li>
              <li>DNS interaction monitoring</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Advanced SSRF Exploitation and Bypass",content:`
        <h2>Advanced SSRF Exploitation and Bypass Techniques</h2>
        <p>Advanced SSRF exploitation involves bypassing filters, exploiting cloud services, and chaining SSRF with other vulnerabilities for maximum impact.</p>
        
        <h3>Filter and Protection Bypass</h3>
        <ul>
          <li><strong>URL Encoding and Obfuscation:</strong>
            <ul>
              <li>Double URL encoding</li>
              <li>Unicode and UTF-8 encoding</li>
              <li>Mixed case and character variations</li>
              <li>Decimal and hexadecimal IP notation</li>
            </ul>
          </li>
          <li><strong>DNS and Redirect Bypasses:</strong>
            <ul>
              <li>DNS rebinding attacks</li>
              <li>Subdomain takeover exploitation</li>
              <li>HTTP redirect chain abuse</li>
              <li>Short URL service manipulation</li>
            </ul>
          </li>
          <li><strong>Protocol and Scheme Manipulation:</strong>
            <ul>
              <li>Alternative protocol schemes</li>
              <li>Protocol smuggling techniques</li>
              <li>Port specification bypass</li>
              <li>IPv6 address exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud-Specific SSRF Attacks</h3>
        <ul>
          <li><strong>AWS Metadata Exploitation:</strong>
            <ul>
              <li>EC2 metadata service access</li>
              <li>IAM role credential extraction</li>
              <li>Security group enumeration</li>
              <li>User data and instance information</li>
            </ul>
          </li>
          <li><strong>Azure Metadata Service:</strong>
            <ul>
              <li>Instance metadata API access</li>
              <li>Managed identity token extraction</li>
              <li>Subscription and resource information</li>
              <li>Network configuration details</li>
            </ul>
          </li>
          <li><strong>Google Cloud Metadata:</strong>
            <ul>
              <li>Compute metadata server access</li>
              <li>Service account token retrieval</li>
              <li>Project and instance information</li>
              <li>SSH key and configuration data</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSRF Chaining and Advanced Techniques</h3>
        <ul>
          <li><strong>SSRF + RCE Combinations:</strong>
            <ul>
              <li>Internal service exploitation</li>
              <li>Redis and database attacks</li>
              <li>Memcached exploitation</li>
              <li>Docker API abuse</li>
            </ul>
          </li>
          <li><strong>SSRF + Information Disclosure:</strong>
            <ul>
              <li>Internal file system access</li>
              <li>Configuration file retrieval</li>
              <li>Source code disclosure</li>
              <li>Database connection strings</li>
            </ul>
          </li>
          <li><strong>SSRF + Privilege Escalation:</strong>
            <ul>
              <li>Administrative interface access</li>
              <li>Internal API exploitation</li>
              <li>Service account abuse</li>
              <li>Network segmentation bypass</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary difference between basic SSRF and blind SSRF?",options:["Basic SSRF is more dangerous","Blind SSRF provides no direct response visibility","Basic SSRF only works on internal networks","Blind SSRF requires authentication"],correctAnswer:1,explanation:"Blind SSRF provides no direct response visibility, requiring attackers to use out-of-band techniques like DNS interactions or time-based methods to confirm the vulnerability's existence."},{question:"Which cloud service is most commonly targeted in SSRF attacks for credential extraction?",options:["Load balancers","Content delivery networks","Instance metadata services","DNS services"],correctAnswer:2,explanation:"Instance metadata services (like AWS EC2 metadata at ***************) are most commonly targeted because they provide access to sensitive information including IAM credentials, instance details, and configuration data."},{question:"What is the most effective technique for bypassing IP-based SSRF filters?",options:["Using only HTTPS requests","DNS rebinding and redirect chain exploitation","Increasing request frequency","Using only POST requests"],correctAnswer:1,explanation:"DNS rebinding and redirect chain exploitation are most effective for bypassing IP-based filters because they can circumvent blacklist restrictions by using legitimate domains that resolve to internal IPs or redirect to blocked addresses."}]},type:"quiz"}]},m={id:"bb-15",pathId:"bug-bounty",title:"Deserialization Vulnerabilities",description:"Master insecure deserialization vulnerabilities across multiple programming languages, including detection, exploitation, and gadget chain development for achieving remote code execution.",objectives:["Understand deserialization fundamentals and security risks","Learn language-specific deserialization vulnerabilities","Master gadget chain identification and exploitation","Develop skills in payload generation and delivery","Learn detection and prevention techniques","Create comprehensive deserialization testing strategies"],difficulty:"Expert",estimatedTime:150,sections:[{title:"Deserialization Fundamentals",content:`
        <h2>Insecure Deserialization Vulnerabilities</h2>
        <p>Deserialization vulnerabilities occur when applications deserialize untrusted data without proper validation, potentially leading to remote code execution and complete system compromise.</p>
        
        <h3>Serialization and Deserialization Concepts</h3>
        <ul>
          <li><strong>Serialization Process:</strong>
            <ul>
              <li>Object state conversion to byte stream</li>
              <li>Data structure preservation</li>
              <li>Cross-platform data exchange</li>
              <li>Persistent storage mechanisms</li>
            </ul>
          </li>
          <li><strong>Deserialization Process:</strong>
            <ul>
              <li>Byte stream reconstruction to objects</li>
              <li>Object state restoration</li>
              <li>Method and constructor invocation</li>
              <li>Memory allocation and initialization</li>
            </ul>
          </li>
          <li><strong>Security Implications:</strong>
            <ul>
              <li>Untrusted data processing</li>
              <li>Object injection attacks</li>
              <li>Code execution during deserialization</li>
              <li>Application logic manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Deserialization Formats</h3>
        <ul>
          <li><strong>Binary Formats:</strong>
            <ul>
              <li>Java serialization (ObjectInputStream)</li>
              <li>.NET BinaryFormatter</li>
              <li>Python pickle protocol</li>
              <li>PHP serialize/unserialize</li>
            </ul>
          </li>
          <li><strong>Text-Based Formats:</strong>
            <ul>
              <li>JSON deserialization</li>
              <li>XML serialization</li>
              <li>YAML parsing</li>
              <li>MessagePack and Protocol Buffers</li>
            </ul>
          </li>
          <li><strong>Framework-Specific:</strong>
            <ul>
              <li>Spring Framework serialization</li>
              <li>Ruby Marshal format</li>
              <li>Node.js serialization libraries</li>
              <li>Custom application formats</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attack Vectors and Entry Points</h3>
        <ul>
          <li><strong>Direct Input Vectors:</strong>
            <ul>
              <li>HTTP request parameters</li>
              <li>File upload functionality</li>
              <li>API endpoint payloads</li>
              <li>Cookie and session data</li>
            </ul>
          </li>
          <li><strong>Indirect Input Vectors:</strong>
            <ul>
              <li>Database stored objects</li>
              <li>Cache and temporary storage</li>
              <li>Message queue systems</li>
              <li>Configuration file processing</li>
            </ul>
          </li>
          <li><strong>Network Protocol Exploitation:</strong>
            <ul>
              <li>RMI (Remote Method Invocation)</li>
              <li>JMX (Java Management Extensions)</li>
              <li>LDAP directory services</li>
              <li>Custom network protocols</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Language-Specific Exploitation",content:`
        <h2>Language-Specific Deserialization Exploitation</h2>
        <p>Different programming languages have unique deserialization mechanisms and vulnerability patterns that require specialized exploitation techniques.</p>
        
        <h3>Java Deserialization</h3>
        <ul>
          <li><strong>Java Serialization Mechanism:</strong>
            <ul>
              <li>ObjectInputStream and ObjectOutputStream</li>
              <li>Serializable interface implementation</li>
              <li>readObject() method execution</li>
              <li>Class loading and instantiation</li>
            </ul>
          </li>
          <li><strong>Common Java Gadget Chains:</strong>
            <ul>
              <li>Apache Commons Collections</li>
              <li>Spring Framework gadgets</li>
              <li>Apache Commons BeanUtils</li>
              <li>Hibernate ORM chains</li>
            </ul>
          </li>
          <li><strong>Exploitation Tools:</strong>
            <ul>
              <li>ysoserial payload generator</li>
              <li>Java Deserialization Scanner</li>
              <li>SerialKiller detection tool</li>
              <li>Custom payload development</li>
            </ul>
          </li>
        </ul>
        
        <h3>.NET Deserialization</h3>
        <ul>
          <li><strong>.NET Serialization Types:</strong>
            <ul>
              <li>BinaryFormatter serialization</li>
              <li>DataContractSerializer</li>
              <li>JavaScriptSerializer</li>
              <li>Json.NET deserialization</li>
            </ul>
          </li>
          <li><strong>.NET Gadget Chains:</strong>
            <ul>
              <li>ObjectDataProvider chains</li>
              <li>WindowsIdentity gadgets</li>
              <li>TypeConfuseDelegate chains</li>
              <li>ActivitySurrogateSelector exploitation</li>
            </ul>
          </li>
          <li><strong>Exploitation Tools:</strong>
            <ul>
              <li>ysoserial.net payload generator</li>
              <li>ViewState decoder tools</li>
              <li>Custom .NET payload creation</li>
              <li>PowerShell-based exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Python and PHP Exploitation</h3>
        <ul>
          <li><strong>Python Pickle Exploitation:</strong>
            <ul>
              <li>Pickle protocol vulnerabilities</li>
              <li>__reduce__ method abuse</li>
              <li>Custom pickle payload creation</li>
              <li>Django and Flask framework exploitation</li>
            </ul>
          </li>
          <li><strong>PHP Serialization Attacks:</strong>
            <ul>
              <li>PHP serialize/unserialize functions</li>
              <li>Magic method exploitation (__wakeup, __destruct)</li>
              <li>Property-oriented programming (POP)</li>
              <li>WordPress and framework-specific chains</li>
            </ul>
          </li>
          <li><strong>Other Languages:</strong>
            <ul>
              <li>Ruby Marshal deserialization</li>
              <li>Node.js serialization libraries</li>
              <li>Go language serialization</li>
              <li>Rust serialization frameworks</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Gadget Chain Development and Advanced Techniques",content:`
        <h2>Gadget Chain Development and Advanced Exploitation</h2>
        <p>Advanced deserialization exploitation requires understanding gadget chain construction, payload optimization, and bypass techniques for modern protections.</p>
        
        <h3>Gadget Chain Construction</h3>
        <ul>
          <li><strong>Gadget Chain Fundamentals:</strong>
            <ul>
              <li>Chain component identification</li>
              <li>Method invocation sequences</li>
              <li>Object property manipulation</li>
              <li>Execution flow control</li>
            </ul>
          </li>
          <li><strong>Chain Discovery Techniques:</strong>
            <ul>
              <li>Static code analysis</li>
              <li>Dynamic analysis and debugging</li>
              <li>Automated gadget discovery tools</li>
              <li>Manual chain construction</li>
            </ul>
          </li>
          <li><strong>Payload Optimization:</strong>
            <ul>
              <li>Chain length minimization</li>
              <li>Reliability improvement</li>
              <li>Environment-specific adaptation</li>
              <li>Stealth and evasion techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Exploitation Techniques</h3>
        <ul>
          <li><strong>Blind Deserialization Exploitation:</strong>
            <ul>
              <li>Time-based confirmation</li>
              <li>DNS interaction techniques</li>
              <li>Error-based information gathering</li>
              <li>Out-of-band data exfiltration</li>
            </ul>
          </li>
          <li><strong>Filter and Protection Bypass:</strong>
            <ul>
              <li>Blacklist evasion techniques</li>
              <li>Class loading manipulation</li>
              <li>Serialization format confusion</li>
              <li>Encoding and obfuscation</li>
            </ul>
          </li>
          <li><strong>Memory Corruption Exploitation:</strong>
            <ul>
              <li>Buffer overflow during deserialization</li>
              <li>Heap manipulation techniques</li>
              <li>Type confusion attacks</li>
              <li>Use-after-free exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Detection and Prevention</h3>
        <ul>
          <li><strong>Vulnerability Detection:</strong>
            <ul>
              <li>Static analysis tools</li>
              <li>Dynamic testing frameworks</li>
              <li>Code review methodologies</li>
              <li>Automated scanning techniques</li>
            </ul>
          </li>
          <li><strong>Protection Mechanisms:</strong>
            <ul>
              <li>Input validation and sanitization</li>
              <li>Whitelist-based class filtering</li>
              <li>Serialization format restrictions</li>
              <li>Runtime monitoring and detection</li>
            </ul>
          </li>
          <li><strong>Secure Development Practices:</strong>
            <ul>
              <li>Avoiding untrusted deserialization</li>
              <li>Using safe serialization formats</li>
              <li>Implementing integrity checks</li>
              <li>Regular security assessments</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary security risk associated with insecure deserialization?",options:["Data corruption","Memory leaks","Remote code execution","Performance degradation"],correctAnswer:2,explanation:"Remote code execution is the primary security risk because deserialization can trigger arbitrary code execution during the object reconstruction process, potentially allowing attackers to gain complete control of the system."},{question:"Which Java library is most commonly exploited in deserialization attacks?",options:["Apache Commons Lang","Apache Commons Collections","Google Guava","Apache Commons IO"],correctAnswer:1,explanation:"Apache Commons Collections is most commonly exploited because it contains gadget chains that can be chained together to achieve remote code execution through the InvokerTransformer and other transformer classes."},{question:"What is a 'gadget chain' in the context of deserialization exploitation?",options:["A series of encrypted payloads","A sequence of existing code that can be chained together to achieve code execution","A type of serialization format","A debugging technique"],correctAnswer:1,explanation:"A gadget chain is a sequence of existing code (gadgets) in the application or its dependencies that can be chained together during deserialization to achieve arbitrary code execution without introducing new code."}]},type:"quiz"}]},h={id:"bb-16",pathId:"bug-bounty",title:"XXE (XML External Entity) Exploitation",description:"Master XML External Entity (XXE) vulnerabilities, including detection techniques, exploitation methods, and advanced attack vectors for data exfiltration and system compromise.",objectives:["Understand XXE vulnerability fundamentals and XML processing","Learn XXE detection and identification techniques","Master basic and advanced XXE exploitation methods","Develop skills in blind XXE and out-of-band techniques","Learn XXE prevention and mitigation strategies","Create comprehensive XXE testing methodologies"],difficulty:"Advanced",estimatedTime:120,sections:[{title:"XXE Fundamentals and XML Processing",content:`
        <h2>XML External Entity (XXE) Fundamentals</h2>
        <p>XXE vulnerabilities occur when XML parsers process external entity references without proper validation, allowing attackers to access local files, internal networks, and sensitive data.</p>
        
        <h3>XML and Entity Concepts</h3>
        <ul>
          <li><strong>XML Structure and Components:</strong>
            <ul>
              <li>XML document structure and syntax</li>
              <li>Document Type Definition (DTD)</li>
              <li>Entity declarations and references</li>
              <li>XML namespaces and schemas</li>
            </ul>
          </li>
          <li><strong>Entity Types:</strong>
            <ul>
              <li>Internal entities (defined within DTD)</li>
              <li>External entities (referenced from external sources)</li>
              <li>Parameter entities (used within DTD)</li>
              <li>General entities (used in document content)</li>
            </ul>
          </li>
          <li><strong>XML Parser Behavior:</strong>
            <ul>
              <li>Entity resolution and expansion</li>
              <li>External resource loading</li>
              <li>DTD processing and validation</li>
              <li>Error handling and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>XXE Vulnerability Types</h3>
        <ul>
          <li><strong>Classic XXE:</strong>
            <ul>
              <li>Direct file disclosure</li>
              <li>Internal network scanning</li>
              <li>Service enumeration</li>
              <li>Configuration file access</li>
            </ul>
          </li>
          <li><strong>Blind XXE:</strong>
            <ul>
              <li>No direct output visibility</li>
              <li>Out-of-band data exfiltration</li>
              <li>Error-based information gathering</li>
              <li>Time-based confirmation</li>
            </ul>
          </li>
          <li><strong>XXE via File Upload:</strong>
            <ul>
              <li>Document processing vulnerabilities</li>
              <li>Image metadata exploitation</li>
              <li>Office document attacks</li>
              <li>SVG file processing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common XXE Attack Vectors</h3>
        <ul>
          <li><strong>Direct XML Input:</strong>
            <ul>
              <li>API endpoint XML processing</li>
              <li>SOAP web service exploitation</li>
              <li>XML-based configuration</li>
              <li>Data import functionality</li>
            </ul>
          </li>
          <li><strong>Indirect XML Processing:</strong>
            <ul>
              <li>File upload and conversion</li>
              <li>Document generation services</li>
              <li>RSS and feed processing</li>
              <li>XML-based authentication</li>
            </ul>
          </li>
          <li><strong>Content-Type Manipulation:</strong>
            <ul>
              <li>JSON to XML conversion</li>
              <li>Form data to XML processing</li>
              <li>Content-Type header modification</li>
              <li>Accept header manipulation</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"XXE Detection and Exploitation",content:`
        <h2>XXE Detection and Basic Exploitation</h2>
        <p>Effective XXE detection requires understanding XML processing points and systematically testing for external entity resolution capabilities.</p>
        
        <h3>Detection Techniques</h3>
        <ul>
          <li><strong>Input Vector Identification:</strong>
            <ul>
              <li>XML parameter discovery</li>
              <li>Content-Type analysis</li>
              <li>File upload functionality</li>
              <li>API endpoint enumeration</li>
            </ul>
          </li>
          <li><strong>Basic XXE Testing:</strong>
            <ul>
              <li>Simple entity declaration</li>
              <li>Local file inclusion attempts</li>
              <li>External DTD references</li>
              <li>Error message analysis</li>
            </ul>
          </li>
          <li><strong>Response Analysis:</strong>
            <ul>
              <li>Entity expansion in output</li>
              <li>Error message examination</li>
              <li>Response time variations</li>
              <li>Content length changes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Basic XXE Exploitation</h3>
        <ul>
          <li><strong>File Disclosure Attacks:</strong>
            <ul>
              <li>Local file system access</li>
              <li>Configuration file retrieval</li>
              <li>Source code disclosure</li>
              <li>Log file examination</li>
            </ul>
          </li>
          <li><strong>Internal Network Scanning:</strong>
            <ul>
              <li>Port scanning via XXE</li>
              <li>Service enumeration</li>
              <li>Internal host discovery</li>
              <li>Network topology mapping</li>
            </ul>
          </li>
          <li><strong>Denial of Service:</strong>
            <ul>
              <li>Billion laughs attack</li>
              <li>Quadratic blowup</li>
              <li>External entity recursion</li>
              <li>Resource exhaustion</li>
            </ul>
          </li>
        </ul>
        
        <h3>XXE Payload Construction</h3>
        <ul>
          <li><strong>Basic Payload Structure:</strong>
            <ul>
              <li>DTD declaration syntax</li>
              <li>Entity definition format</li>
              <li>External reference construction</li>
              <li>Parameter entity usage</li>
            </ul>
          </li>
          <li><strong>File Access Payloads:</strong>
            <ul>
              <li>Unix/Linux file system paths</li>
              <li>Windows file system access</li>
              <li>URL-based external references</li>
              <li>Protocol scheme exploitation</li>
            </ul>
          </li>
          <li><strong>Network Scanning Payloads:</strong>
            <ul>
              <li>HTTP-based internal requests</li>
              <li>FTP and other protocol abuse</li>
              <li>Port enumeration techniques</li>
              <li>Service fingerprinting</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Advanced XXE Techniques and Blind Exploitation",content:`
        <h2>Advanced XXE Techniques and Blind Exploitation</h2>
        <p>Advanced XXE exploitation involves blind techniques, out-of-band data exfiltration, and sophisticated attack chains for maximum impact.</p>
        
        <h3>Blind XXE Exploitation</h3>
        <ul>
          <li><strong>Out-of-Band Techniques:</strong>
            <ul>
              <li>External DTD hosting</li>
              <li>HTTP callback methods</li>
              <li>DNS interaction testing</li>
              <li>FTP data exfiltration</li>
            </ul>
          </li>
          <li><strong>Error-Based Exploitation:</strong>
            <ul>
              <li>Induced error messages</li>
              <li>File existence confirmation</li>
              <li>Directory enumeration</li>
              <li>Content-based error analysis</li>
            </ul>
          </li>
          <li><strong>Time-Based Confirmation:</strong>
            <ul>
              <li>Response delay analysis</li>
              <li>Resource loading timing</li>
              <li>Network timeout exploitation</li>
              <li>Processing time variations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Exfiltration Techniques</h3>
        <ul>
          <li><strong>Direct Data Extraction:</strong>
            <ul>
              <li>File content in XML output</li>
              <li>Base64 encoding methods</li>
              <li>Character-by-character extraction</li>
              <li>Chunked data retrieval</li>
            </ul>
          </li>
          <li><strong>Out-of-Band Exfiltration:</strong>
            <ul>
              <li>HTTP parameter injection</li>
              <li>DNS subdomain encoding</li>
              <li>FTP filename manipulation</li>
              <li>Email-based data transfer</li>
            </ul>
          </li>
          <li><strong>Advanced Exfiltration:</strong>
            <ul>
              <li>Multi-stage data extraction</li>
              <li>Encrypted data channels</li>
              <li>Steganographic techniques</li>
              <li>Social media platform abuse</li>
            </ul>
          </li>
        </ul>
        
        <h3>XXE Prevention and Mitigation</h3>
        <ul>
          <li><strong>Parser Configuration:</strong>
            <ul>
              <li>Disable external entity processing</li>
              <li>Disable DTD processing</li>
              <li>Use secure parser settings</li>
              <li>Implement entity resolution restrictions</li>
            </ul>
          </li>
          <li><strong>Input Validation:</strong>
            <ul>
              <li>XML schema validation</li>
              <li>Content filtering and sanitization</li>
              <li>Whitelist-based validation</li>
              <li>Input size limitations</li>
            </ul>
          </li>
          <li><strong>Security Controls:</strong>
            <ul>
              <li>Network segmentation</li>
              <li>File system access restrictions</li>
              <li>Monitoring and logging</li>
              <li>Web application firewalls</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary difference between internal and external XML entities?",options:["Internal entities are more dangerous","External entities reference resources outside the XML document","Internal entities cannot be exploited","External entities are always encrypted"],correctAnswer:1,explanation:"External entities reference resources outside the XML document (like files or URLs), while internal entities are defined within the DTD itself. External entities enable XXE attacks by allowing access to external resources."},{question:"Which technique is most effective for exploiting blind XXE vulnerabilities?",options:["Direct file inclusion","Out-of-band data exfiltration","Error message analysis only","Response time measurement only"],correctAnswer:1,explanation:"Out-of-band data exfiltration is most effective for blind XXE because it allows data extraction even when the application doesn't display the XML processing results directly, using external servers to receive the data."},{question:"What is the 'billion laughs' attack in XXE exploitation?",options:["A data exfiltration technique","A file disclosure method","A denial of service attack using entity expansion","A network scanning technique"],correctAnswer:2,explanation:"The 'billion laughs' attack is a denial of service attack that uses recursive entity expansion to consume excessive memory and CPU resources, potentially crashing the XML parser or application."}]},type:"quiz"}]},y={id:"bb-17",pathId:"bug-bounty",title:"CSRF (Cross-Site Request Forgery) Exploitation",description:"Master Cross-Site Request Forgery (CSRF) vulnerabilities, including detection techniques, exploitation methods, and modern bypass strategies for CSRF protection mechanisms.",objectives:["Understand CSRF vulnerability fundamentals and attack mechanics","Learn CSRF detection and identification techniques","Master basic and advanced CSRF exploitation methods","Develop skills in CSRF protection bypass techniques","Learn modern CSRF attack vectors and SameSite bypass","Create comprehensive CSRF testing methodologies"],difficulty:"Intermediate",estimatedTime:110,sections:[{title:"CSRF Fundamentals and Attack Mechanics",content:`
        <h2>Cross-Site Request Forgery (CSRF) Fundamentals</h2>
        <p>CSRF vulnerabilities allow attackers to perform unauthorized actions on behalf of authenticated users by exploiting the trust that web applications have in user browsers.</p>
        
        <h3>CSRF Attack Mechanics</h3>
        <ul>
          <li><strong>Attack Prerequisites:</strong>
            <ul>
              <li>User authentication to target application</li>
              <li>Predictable request structure</li>
              <li>Lack of proper CSRF protection</li>
              <li>Social engineering or malicious website</li>
            </ul>
          </li>
          <li><strong>Attack Flow:</strong>
            <ul>
              <li>User authenticates to vulnerable application</li>
              <li>Attacker crafts malicious request</li>
              <li>User visits attacker-controlled page</li>
              <li>Browser automatically sends authenticated request</li>
            </ul>
          </li>
          <li><strong>Browser Behavior:</strong>
            <ul>
              <li>Automatic cookie transmission</li>
              <li>Same-origin policy limitations</li>
              <li>Cross-origin request handling</li>
              <li>Preflight request requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSRF Vulnerability Types</h3>
        <ul>
          <li><strong>GET-based CSRF:</strong>
            <ul>
              <li>Simple URL-based attacks</li>
              <li>Image tag exploitation</li>
              <li>Link-based social engineering</li>
              <li>Iframe and embed attacks</li>
            </ul>
          </li>
          <li><strong>POST-based CSRF:</strong>
            <ul>
              <li>Form-based attacks</li>
              <li>JavaScript-driven requests</li>
              <li>XMLHttpRequest exploitation</li>
              <li>Fetch API abuse</li>
            </ul>
          </li>
          <li><strong>JSON and API CSRF:</strong>
            <ul>
              <li>Content-Type manipulation</li>
              <li>Simple request exploitation</li>
              <li>CORS misconfiguration abuse</li>
              <li>API endpoint targeting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common CSRF Targets</h3>
        <ul>
          <li><strong>Account Management:</strong>
            <ul>
              <li>Password change functionality</li>
              <li>Email address modification</li>
              <li>Profile information updates</li>
              <li>Security settings changes</li>
            </ul>
          </li>
          <li><strong>Financial Operations:</strong>
            <ul>
              <li>Money transfers and payments</li>
              <li>Account balance modifications</li>
              <li>Investment transactions</li>
              <li>Billing and subscription changes</li>
            </ul>
          </li>
          <li><strong>Administrative Functions:</strong>
            <ul>
              <li>User privilege modifications</li>
              <li>System configuration changes</li>
              <li>Content management operations</li>
              <li>Access control modifications</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"CSRF Detection and Basic Exploitation",content:`
        <h2>CSRF Detection and Basic Exploitation Techniques</h2>
        <p>Effective CSRF detection requires systematic analysis of application functionality and testing for proper cross-site request protection mechanisms.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Functionality Analysis:</strong>
            <ul>
              <li>State-changing operation identification</li>
              <li>Authentication requirement mapping</li>
              <li>Request structure analysis</li>
              <li>Protection mechanism evaluation</li>
            </ul>
          </li>
          <li><strong>Request Analysis:</strong>
            <ul>
              <li>HTTP method examination</li>
              <li>Parameter structure review</li>
              <li>Header requirement analysis</li>
              <li>Content-Type dependency</li>
            </ul>
          </li>
          <li><strong>Protection Testing:</strong>
            <ul>
              <li>CSRF token presence</li>
              <li>Referer header validation</li>
              <li>Origin header checking</li>
              <li>SameSite cookie attributes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Basic CSRF Exploitation</h3>
        <ul>
          <li><strong>GET Request Exploitation:</strong>
            <ul>
              <li>Image tag attacks (&lt;img src="..."&gt;)</li>
              <li>Link-based exploitation</li>
              <li>CSS background-image abuse</li>
              <li>Iframe source manipulation</li>
            </ul>
          </li>
          <li><strong>POST Request Exploitation:</strong>
            <ul>
              <li>Auto-submitting forms</li>
              <li>JavaScript-driven requests</li>
              <li>Hidden iframe techniques</li>
              <li>XMLHttpRequest exploitation</li>
            </ul>
          </li>
          <li><strong>Payload Construction:</strong>
            <ul>
              <li>HTML form creation</li>
              <li>JavaScript payload development</li>
              <li>Social engineering integration</li>
              <li>Multi-step attack chains</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSRF Testing Tools and Techniques</h3>
        <ul>
          <li><strong>Manual Testing:</strong>
            <ul>
              <li>Browser developer tools</li>
              <li>Request modification and replay</li>
              <li>Cross-origin testing</li>
              <li>Protection bypass attempts</li>
            </ul>
          </li>
          <li><strong>Automated Tools:</strong>
            <ul>
              <li>Burp Suite CSRF scanner</li>
              <li>OWASP ZAP CSRF testing</li>
              <li>Custom automation scripts</li>
              <li>Browser extension tools</li>
            </ul>
          </li>
          <li><strong>Proof-of-Concept Development:</strong>
            <ul>
              <li>Standalone HTML pages</li>
              <li>JavaScript-based exploits</li>
              <li>Social engineering scenarios</li>
              <li>Multi-browser compatibility</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Advanced CSRF Techniques and Modern Bypasses",content:`
        <h2>Advanced CSRF Techniques and Modern Protection Bypasses</h2>
        <p>Advanced CSRF exploitation involves bypassing modern protection mechanisms, exploiting complex scenarios, and chaining CSRF with other vulnerabilities.</p>
        
        <h3>CSRF Token Bypass Techniques</h3>
        <ul>
          <li><strong>Token Validation Flaws:</strong>
            <ul>
              <li>Missing token validation</li>
              <li>Empty token acceptance</li>
              <li>Token reuse vulnerabilities</li>
              <li>Predictable token generation</li>
            </ul>
          </li>
          <li><strong>Token Extraction Methods:</strong>
            <ul>
              <li>XSS-based token theft</li>
              <li>Subdomain token leakage</li>
              <li>Referer header exploitation</li>
              <li>Error page token disclosure</li>
            </ul>
          </li>
          <li><strong>Token Bypass Strategies:</strong>
            <ul>
              <li>HTTP method override</li>
              <li>Content-Type manipulation</li>
              <li>Parameter pollution</li>
              <li>Encoding and case variations</li>
            </ul>
          </li>
        </ul>
        
        <h3>SameSite Cookie Bypass</h3>
        <ul>
          <li><strong>SameSite Attribute Understanding:</strong>
            <ul>
              <li>Strict, Lax, and None values</li>
              <li>Browser implementation differences</li>
              <li>Default behavior variations</li>
              <li>Cross-site context definitions</li>
            </ul>
          </li>
          <li><strong>SameSite Bypass Techniques:</strong>
            <ul>
              <li>Top-level navigation exploitation</li>
              <li>Subdomain-based attacks</li>
              <li>Browser quirk exploitation</li>
              <li>Legacy browser targeting</li>
            </ul>
          </li>
          <li><strong>Modern Attack Vectors:</strong>
            <ul>
              <li>WebSocket CSRF attacks</li>
              <li>PostMessage exploitation</li>
              <li>Service Worker abuse</li>
              <li>Progressive Web App attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSRF Chaining and Advanced Scenarios</h3>
        <ul>
          <li><strong>CSRF + XSS Combinations:</strong>
            <ul>
              <li>XSS-enabled CSRF token extraction</li>
              <li>DOM-based CSRF attacks</li>
              <li>Stored XSS for persistent CSRF</li>
              <li>Reflected XSS CSRF chains</li>
            </ul>
          </li>
          <li><strong>CSRF + Clickjacking:</strong>
            <ul>
              <li>Invisible iframe overlays</li>
              <li>UI redressing attacks</li>
              <li>Drag-and-drop exploitation</li>
              <li>Touch-based mobile attacks</li>
            </ul>
          </li>
          <li><strong>Advanced Attack Scenarios:</strong>
            <ul>
              <li>Multi-step CSRF attacks</li>
              <li>Time-delayed exploitation</li>
              <li>Conditional CSRF attacks</li>
              <li>State-dependent exploitation</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the fundamental requirement for a successful CSRF attack?",options:["The victim must have administrative privileges","The victim must be authenticated to the target application","The attack must use HTTPS","The application must use cookies"],correctAnswer:1,explanation:"The victim must be authenticated to the target application because CSRF attacks rely on the browser automatically including authentication credentials (like session cookies) with cross-site requests."},{question:"Which SameSite cookie attribute provides the strongest CSRF protection?",options:["SameSite=None","SameSite=Lax","SameSite=Strict","SameSite=Secure"],correctAnswer:2,explanation:"SameSite=Strict provides the strongest CSRF protection because it prevents cookies from being sent with any cross-site requests, including top-level navigation, effectively blocking most CSRF attacks."},{question:"What is the most effective way to bypass CSRF token protection?",options:["Brute forcing the token","Using XSS to extract the token","Disabling JavaScript","Using only GET requests"],correctAnswer:1,explanation:"Using XSS to extract the token is most effective because it allows the attacker to read the CSRF token from the same origin and include it in the malicious request, bypassing the protection mechanism."}]},type:"quiz"}]},f={id:"bb-18",pathId:"bug-bounty",title:"File Upload Vulnerabilities",description:"Master file upload vulnerability exploitation, including bypass techniques, malicious file creation, and achieving remote code execution through file upload mechanisms.",objectives:["Understand file upload vulnerability fundamentals","Learn file upload restriction bypass techniques","Master malicious file creation and payload development","Develop skills in achieving RCE through file uploads","Learn file upload security testing methodologies","Create comprehensive file upload attack strategies"],difficulty:"Advanced",estimatedTime:125,sections:[{title:"File Upload Security Fundamentals",content:`
        <h2>File Upload Vulnerability Fundamentals</h2>
        <p>File upload vulnerabilities occur when applications fail to properly validate, sanitize, or restrict uploaded files, potentially leading to remote code execution and system compromise.</p>
        
        <h3>File Upload Attack Vectors</h3>
        <ul>
          <li><strong>Remote Code Execution:</strong>
            <ul>
              <li>Web shell upload and execution</li>
              <li>Server-side script execution</li>
              <li>Binary executable upload</li>
              <li>Library and module injection</li>
            </ul>
          </li>
          <li><strong>Client-Side Attacks:</strong>
            <ul>
              <li>Malicious JavaScript execution</li>
              <li>HTML injection and XSS</li>
              <li>SVG-based attacks</li>
              <li>PDF and document exploits</li>
            </ul>
          </li>
          <li><strong>Information Disclosure:</strong>
            <ul>
              <li>Path traversal via filenames</li>
              <li>Configuration file overwrite</li>
              <li>Source code disclosure</li>
              <li>Database file replacement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common File Upload Restrictions</h3>
        <ul>
          <li><strong>File Type Restrictions:</strong>
            <ul>
              <li>File extension blacklisting</li>
              <li>MIME type validation</li>
              <li>Magic number checking</li>
              <li>Content-based validation</li>
            </ul>
          </li>
          <li><strong>File Size and Content Limits:</strong>
            <ul>
              <li>Maximum file size restrictions</li>
              <li>Content length validation</li>
              <li>Upload rate limiting</li>
              <li>Storage quota enforcement</li>
            </ul>
          </li>
          <li><strong>Filename and Path Restrictions:</strong>
            <ul>
              <li>Filename character filtering</li>
              <li>Path traversal prevention</li>
              <li>Reserved name blocking</li>
              <li>Length limitations</li>
            </ul>
          </li>
        </ul>
        
        <h3>File Upload Processing Flow</h3>
        <ul>
          <li><strong>Upload Reception:</strong>
            <ul>
              <li>HTTP multipart parsing</li>
              <li>Temporary file creation</li>
              <li>Memory vs. disk storage</li>
              <li>Upload progress tracking</li>
            </ul>
          </li>
          <li><strong>Validation and Processing:</strong>
            <ul>
              <li>File type detection</li>
              <li>Content scanning and analysis</li>
              <li>Virus and malware scanning</li>
              <li>Metadata extraction</li>
            </ul>
          </li>
          <li><strong>Storage and Access:</strong>
            <ul>
              <li>File system storage</li>
              <li>Database blob storage</li>
              <li>Cloud storage integration</li>
              <li>Access control implementation</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Bypass Techniques and Evasion Methods",content:`
        <h2>File Upload Restriction Bypass Techniques</h2>
        <p>Bypassing file upload restrictions requires understanding validation mechanisms and developing creative techniques to circumvent security controls.</p>
        
        <h3>File Extension Bypass</h3>
        <ul>
          <li><strong>Extension Manipulation:</strong>
            <ul>
              <li>Double extension attacks (.php.jpg)</li>
              <li>Null byte injection (.php%00.jpg)</li>
              <li>Case variation bypasses (.PHP, .Php)</li>
              <li>Alternative extensions (.phtml, .php5)</li>
            </ul>
          </li>
          <li><strong>Blacklist Evasion:</strong>
            <ul>
              <li>Uncommon executable extensions</li>
              <li>Server-specific extensions</li>
              <li>Configuration-dependent execution</li>
              <li>Polyglot file creation</li>
            </ul>
          </li>
          <li><strong>Whitelist Bypass:</strong>
            <ul>
              <li>Content-Type spoofing</li>
              <li>File signature manipulation</li>
              <li>Embedded payload techniques</li>
              <li>Archive file exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>MIME Type and Content Bypass</h3>
        <ul>
          <li><strong>MIME Type Manipulation:</strong>
            <ul>
              <li>Content-Type header spoofing</li>
              <li>Multipart boundary manipulation</li>
              <li>MIME type confusion</li>
              <li>Browser MIME sniffing abuse</li>
            </ul>
          </li>
          <li><strong>File Signature Bypass:</strong>
            <ul>
              <li>Magic number spoofing</li>
              <li>File header manipulation</li>
              <li>Polyglot file construction</li>
              <li>Steganographic techniques</li>
            </ul>
          </li>
          <li><strong>Content Validation Evasion:</strong>
            <ul>
              <li>Image-based payload embedding</li>
              <li>Document macro exploitation</li>
              <li>Archive file abuse</li>
              <li>Metadata injection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Path and Filename Manipulation</h3>
        <ul>
          <li><strong>Path Traversal Attacks:</strong>
            <ul>
              <li>Directory traversal sequences (../)</li>
              <li>Absolute path specification</li>
              <li>URL encoding bypass</li>
              <li>Unicode normalization abuse</li>
            </ul>
          </li>
          <li><strong>Filename Injection:</strong>
            <ul>
              <li>Special character injection</li>
              <li>Command injection via filenames</li>
              <li>SQL injection in filename processing</li>
              <li>XSS via filename display</li>
            </ul>
          </li>
          <li><strong>Overwrite Attacks:</strong>
            <ul>
              <li>Configuration file replacement</li>
              <li>Application file overwrite</li>
              <li>System file modification</li>
              <li>Database file corruption</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Malicious File Creation and RCE Achievement",content:`
        <h2>Malicious File Creation and Remote Code Execution</h2>
        <p>Achieving remote code execution through file uploads requires crafting effective payloads and understanding server-side execution contexts.</p>
        
        <h3>Web Shell Development</h3>
        <ul>
          <li><strong>Basic Web Shells:</strong>
            <ul>
              <li>PHP command execution shells</li>
              <li>ASP.NET web shell creation</li>
              <li>JSP-based command execution</li>
              <li>Python and Ruby web shells</li>
            </ul>
          </li>
          <li><strong>Advanced Web Shell Features:</strong>
            <ul>
              <li>File management capabilities</li>
              <li>Database interaction</li>
              <li>Network connectivity testing</li>
              <li>Privilege escalation helpers</li>
            </ul>
          </li>
          <li><strong>Stealth and Evasion:</strong>
            <ul>
              <li>Obfuscated code techniques</li>
              <li>Encrypted communication</li>
              <li>Log evasion methods</li>
              <li>Anti-forensics features</li>
            </ul>
          </li>
        </ul>
        
        <h3>Polyglot File Techniques</h3>
        <ul>
          <li><strong>Image-Script Polyglots:</strong>
            <ul>
              <li>JPEG with embedded PHP</li>
              <li>PNG with script payload</li>
              <li>GIF with executable code</li>
              <li>SVG with JavaScript/PHP</li>
            </ul>
          </li>
          <li><strong>Document-Based Payloads:</strong>
            <ul>
              <li>PDF with embedded scripts</li>
              <li>Office document macros</li>
              <li>ZIP archive exploitation</li>
              <li>XML-based payload delivery</li>
            </ul>
          </li>
          <li><strong>Multi-Format Exploitation:</strong>
            <ul>
              <li>Cross-format compatibility</li>
              <li>Parser confusion attacks</li>
              <li>Format-specific triggers</li>
              <li>Conditional payload execution</li>
            </ul>
          </li>
        </ul>
        
        <h3>Post-Upload Exploitation</h3>
        <ul>
          <li><strong>File Access and Execution:</strong>
            <ul>
              <li>Direct URL access attempts</li>
              <li>Include/require exploitation</li>
              <li>Server-side processing triggers</li>
              <li>Scheduled task exploitation</li>
            </ul>
          </li>
          <li><strong>Privilege Escalation:</strong>
            <ul>
              <li>Local file inclusion chaining</li>
              <li>Configuration file modification</li>
              <li>Service account abuse</li>
              <li>System command execution</li>
            </ul>
          </li>
          <li><strong>Persistence and Lateral Movement:</strong>
            <ul>
              <li>Backdoor installation</li>
              <li>User account creation</li>
              <li>Network reconnaissance</li>
              <li>Additional system compromise</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which technique is most effective for bypassing file extension blacklists?",options:["Using only uppercase extensions","Double extension attacks (.php.jpg)","Compressing the file","Changing the file size"],correctAnswer:1,explanation:"Double extension attacks (.php.jpg) are most effective because they can trick validation that only checks the final extension while still allowing server execution based on the first extension, depending on server configuration."},{question:"What is a polyglot file in the context of file upload attacks?",options:["A file written in multiple programming languages","A file that is valid in multiple formats simultaneously","A compressed file containing multiple files","A file with multiple extensions"],correctAnswer:1,explanation:"A polyglot file is valid in multiple formats simultaneously, such as a file that appears as a valid image to validation checks but also contains executable code that can be processed by the server."},{question:"Which file upload restriction is most difficult to bypass?",options:["File extension blacklisting","MIME type validation","Content-based validation with sandboxed execution","File size limitations"],correctAnswer:2,explanation:"Content-based validation with sandboxed execution is most difficult to bypass because it analyzes the actual file content and executes it in a controlled environment to detect malicious behavior, rather than relying on easily spoofed metadata."}]},type:"quiz"}]},b={id:"bb-19",pathId:"bug-bounty",title:"Authentication Bypass Techniques",description:"Master authentication bypass vulnerabilities, including weak authentication mechanisms, session management flaws, and advanced bypass techniques for modern authentication systems.",objectives:["Understand authentication system vulnerabilities","Learn password-based authentication bypass techniques","Master session management and token-based attacks","Develop skills in multi-factor authentication bypass","Learn OAuth and SSO vulnerability exploitation","Create comprehensive authentication testing strategies"],difficulty:"Advanced",estimatedTime:140,sections:[{title:"Authentication System Fundamentals",content:`
        <h2>Authentication System Vulnerabilities</h2>
        <p>Authentication bypass vulnerabilities allow attackers to gain unauthorized access to user accounts or administrative functions without providing valid credentials.</p>
        
        <h3>Authentication Mechanisms</h3>
        <ul>
          <li><strong>Password-Based Authentication:</strong>
            <ul>
              <li>Username and password combinations</li>
              <li>Password complexity requirements</li>
              <li>Account lockout mechanisms</li>
              <li>Password reset functionality</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication (MFA):</strong>
            <ul>
              <li>SMS and email-based OTP</li>
              <li>Time-based OTP (TOTP)</li>
              <li>Hardware security keys</li>
              <li>Biometric authentication</li>
            </ul>
          </li>
          <li><strong>Token-Based Authentication:</strong>
            <ul>
              <li>Session tokens and cookies</li>
              <li>JSON Web Tokens (JWT)</li>
              <li>API keys and bearer tokens</li>
              <li>OAuth access tokens</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Authentication Vulnerabilities</h3>
        <ul>
          <li><strong>Weak Authentication Logic:</strong>
            <ul>
              <li>Insufficient password verification</li>
              <li>Predictable credential patterns</li>
              <li>Default or hardcoded credentials</li>
              <li>Weak password policies</li>
            </ul>
          </li>
          <li><strong>Session Management Flaws:</strong>
            <ul>
              <li>Predictable session identifiers</li>
              <li>Session fixation vulnerabilities</li>
              <li>Insufficient session timeout</li>
              <li>Session hijacking opportunities</li>
            </ul>
          </li>
          <li><strong>Implementation Weaknesses:</strong>
            <ul>
              <li>Race conditions in authentication</li>
              <li>Time-based authentication bypass</li>
              <li>Response manipulation attacks</li>
              <li>Client-side authentication reliance</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attack Surface Analysis</h3>
        <ul>
          <li><strong>Login Functionality:</strong>
            <ul>
              <li>Primary login endpoints</li>
              <li>Alternative login methods</li>
              <li>Administrative interfaces</li>
              <li>API authentication endpoints</li>
            </ul>
          </li>
          <li><strong>Password Recovery:</strong>
            <ul>
              <li>Password reset mechanisms</li>
              <li>Security question systems</li>
              <li>Account recovery processes</li>
              <li>Email-based verification</li>
            </ul>
          </li>
          <li><strong>Account Management:</strong>
            <ul>
              <li>Registration processes</li>
              <li>Profile update functionality</li>
              <li>Account linking features</li>
              <li>Social media integration</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Password and Credential-Based Attacks",content:`
        <h2>Password and Credential-Based Authentication Bypass</h2>
        <p>Password-based authentication systems are vulnerable to various attack techniques that exploit weak implementations and credential management flaws.</p>
        
        <h3>Brute Force and Dictionary Attacks</h3>
        <ul>
          <li><strong>Traditional Brute Force:</strong>
            <ul>
              <li>Systematic password enumeration</li>
              <li>Common password lists</li>
              <li>Credential stuffing attacks</li>
              <li>Rate limiting bypass techniques</li>
            </ul>
          </li>
          <li><strong>Smart Brute Force Techniques:</strong>
            <ul>
              <li>Username enumeration first</li>
              <li>Password spraying attacks</li>
              <li>Seasonal and contextual passwords</li>
              <li>Organization-specific patterns</li>
            </ul>
          </li>
          <li><strong>Evasion Techniques:</strong>
            <ul>
              <li>Distributed attack sources</li>
              <li>User-Agent rotation</li>
              <li>Session management manipulation</li>
              <li>CAPTCHA bypass methods</li>
            </ul>
          </li>
        </ul>
        
        <h3>Logic Flaw Exploitation</h3>
        <ul>
          <li><strong>Authentication Logic Bypass:</strong>
            <ul>
              <li>Response manipulation attacks</li>
              <li>Status code modification</li>
              <li>Parameter tampering</li>
              <li>Request method manipulation</li>
            </ul>
          </li>
          <li><strong>Race Condition Attacks:</strong>
            <ul>
              <li>Concurrent login attempts</li>
              <li>Password reset race conditions</li>
              <li>Account lockout bypass</li>
              <li>Token validation races</li>
            </ul>
          </li>
          <li><strong>Time-Based Attacks:</strong>
            <ul>
              <li>Timing attack exploitation</li>
              <li>Password reset token timing</li>
              <li>Session timeout manipulation</li>
              <li>OTP timing windows</li>
            </ul>
          </li>
        </ul>
        
        <h3>Password Reset Vulnerabilities</h3>
        <ul>
          <li><strong>Token-Based Reset Flaws:</strong>
            <ul>
              <li>Predictable reset tokens</li>
              <li>Token reuse vulnerabilities</li>
              <li>Insufficient token expiration</li>
              <li>Token leakage in referrer</li>
            </ul>
          </li>
          <li><strong>Email-Based Reset Attacks:</strong>
            <ul>
              <li>Email enumeration techniques</li>
              <li>Host header injection</li>
              <li>Password reset poisoning</li>
              <li>Email interception methods</li>
            </ul>
          </li>
          <li><strong>Security Question Bypass:</strong>
            <ul>
              <li>Weak security questions</li>
              <li>Social engineering answers</li>
              <li>OSINT-based answer discovery</li>
              <li>Question enumeration attacks</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Advanced Authentication Bypass Techniques",content:`
        <h2>Advanced Authentication Bypass and Modern System Exploitation</h2>
        <p>Modern authentication systems require sophisticated bypass techniques targeting JWT tokens, OAuth flows, and multi-factor authentication mechanisms.</p>
        
        <h3>JWT and Token-Based Attacks</h3>
        <ul>
          <li><strong>JWT Vulnerabilities:</strong>
            <ul>
              <li>Algorithm confusion attacks (RS256 to HS256)</li>
              <li>None algorithm exploitation</li>
              <li>Weak signing key attacks</li>
              <li>JWT header manipulation</li>
            </ul>
          </li>
          <li><strong>Token Manipulation:</strong>
            <ul>
              <li>Payload modification attacks</li>
              <li>Signature bypass techniques</li>
              <li>Token replay attacks</li>
              <li>Cross-service token abuse</li>
            </ul>
          </li>
          <li><strong>Session Token Attacks:</strong>
            <ul>
              <li>Session fixation attacks</li>
              <li>Session hijacking techniques</li>
              <li>Cross-subdomain token theft</li>
              <li>Token prediction attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>OAuth and SSO Exploitation</h3>
        <ul>
          <li><strong>OAuth Flow Attacks:</strong>
            <ul>
              <li>Authorization code interception</li>
              <li>State parameter manipulation</li>
              <li>Redirect URI validation bypass</li>
              <li>Scope elevation attacks</li>
            </ul>
          </li>
          <li><strong>SSO Implementation Flaws:</strong>
            <ul>
              <li>SAML assertion manipulation</li>
              <li>Identity provider spoofing</li>
              <li>Attribute injection attacks</li>
              <li>Signature validation bypass</li>
            </ul>
          </li>
          <li><strong>Social Login Exploitation:</strong>
            <ul>
              <li>Account linking vulnerabilities</li>
              <li>Email verification bypass</li>
              <li>Profile information manipulation</li>
              <li>Cross-platform account takeover</li>
            </ul>
          </li>
        </ul>
        
        <h3>Multi-Factor Authentication Bypass</h3>
        <ul>
          <li><strong>OTP-Based Attacks:</strong>
            <ul>
              <li>OTP brute force attacks</li>
              <li>SMS interception techniques</li>
              <li>Email OTP manipulation</li>
              <li>Backup code exploitation</li>
            </ul>
          </li>
          <li><strong>Implementation Bypass:</strong>
            <ul>
              <li>MFA enrollment bypass</li>
              <li>Step skipping attacks</li>
              <li>Response manipulation</li>
              <li>Rate limiting bypass</li>
            </ul>
          </li>
          <li><strong>Social Engineering MFA:</strong>
            <ul>
              <li>SIM swapping attacks</li>
              <li>Social engineering OTP</li>
              <li>Phishing MFA tokens</li>
              <li>Real-time phishing attacks</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which JWT attack involves changing the algorithm from RS256 to HS256?",options:["None algorithm attack","Algorithm confusion attack","Weak key attack","Payload manipulation attack"],correctAnswer:1,explanation:"Algorithm confusion attack involves changing the algorithm from RS256 (asymmetric) to HS256 (symmetric), allowing attackers to forge tokens using the public key as the HMAC secret."},{question:"What is the primary vulnerability in password spraying attacks?",options:["Weak passwords","Lack of account lockout or insufficient rate limiting","Predictable usernames","Weak encryption"],correctAnswer:1,explanation:"Password spraying exploits the lack of account lockout or insufficient rate limiting by trying a few common passwords against many accounts, staying below lockout thresholds while maximizing attack coverage."},{question:"Which OAuth vulnerability allows attackers to intercept authorization codes?",options:["State parameter manipulation","Redirect URI validation bypass","Scope elevation","PKCE bypass"],correctAnswer:1,explanation:"Redirect URI validation bypass allows attackers to redirect the authorization code to their controlled domain, enabling them to intercept the code and complete the OAuth flow to gain unauthorized access."}]},type:"quiz"}]},v={id:"bb-20",pathId:"bug-bounty",title:"IDOR (Insecure Direct Object Reference) Exploitation",description:"Master Insecure Direct Object Reference (IDOR) vulnerabilities, including detection techniques, exploitation methods, and advanced enumeration strategies for unauthorized data access.",objectives:["Understand IDOR vulnerability fundamentals and access control flaws","Learn IDOR detection and identification techniques","Master basic and advanced IDOR exploitation methods","Develop skills in automated IDOR enumeration","Learn blind IDOR and indirect reference attacks","Create comprehensive IDOR testing methodologies"],difficulty:"Intermediate",estimatedTime:115,sections:[{title:"IDOR Fundamentals and Access Control",content:`
        <h2>Insecure Direct Object Reference (IDOR) Fundamentals</h2>
        <p>IDOR vulnerabilities occur when applications expose direct references to internal objects without proper access control, allowing attackers to access unauthorized data or functionality.</p>
        
        <h3>IDOR Vulnerability Concepts</h3>
        <ul>
          <li><strong>Direct Object References:</strong>
            <ul>
              <li>Database record identifiers</li>
              <li>File system path references</li>
              <li>URL parameter values</li>
              <li>API endpoint identifiers</li>
            </ul>
          </li>
          <li><strong>Access Control Failures:</strong>
            <ul>
              <li>Missing authorization checks</li>
              <li>Insufficient permission validation</li>
              <li>Horizontal privilege escalation</li>
              <li>Vertical privilege escalation</li>
            </ul>
          </li>
          <li><strong>Common IDOR Scenarios:</strong>
            <ul>
              <li>User profile access</li>
              <li>Document and file retrieval</li>
              <li>Financial transaction data</li>
              <li>Administrative functionality</li>
            </ul>
          </li>
        </ul>
        
        <h3>IDOR Types and Classifications</h3>
        <ul>
          <li><strong>Horizontal IDOR:</strong>
            <ul>
              <li>Same privilege level access</li>
              <li>User-to-user data access</li>
              <li>Peer resource enumeration</li>
              <li>Cross-account information disclosure</li>
            </ul>
          </li>
          <li><strong>Vertical IDOR:</strong>
            <ul>
              <li>Privilege escalation attacks</li>
              <li>Administrative function access</li>
              <li>Higher privilege data access</li>
              <li>System configuration exposure</li>
            </ul>
          </li>
          <li><strong>Blind IDOR:</strong>
            <ul>
              <li>No direct response visibility</li>
              <li>Side-channel confirmation</li>
              <li>Time-based detection</li>
              <li>Error message analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common IDOR Locations</h3>
        <ul>
          <li><strong>URL Parameters:</strong>
            <ul>
              <li>GET request parameters</li>
              <li>RESTful API endpoints</li>
              <li>Query string values</li>
              <li>Path parameter references</li>
            </ul>
          </li>
          <li><strong>Form Data and POST Bodies:</strong>
            <ul>
              <li>Hidden form fields</li>
              <li>JSON payload values</li>
              <li>XML data references</li>
              <li>Multipart form data</li>
            </ul>
          </li>
          <li><strong>HTTP Headers:</strong>
            <ul>
              <li>Custom header values</li>
              <li>Authorization header data</li>
              <li>Cookie parameter values</li>
              <li>Referer header references</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"IDOR Detection and Enumeration",content:`
        <h2>IDOR Detection and Systematic Enumeration</h2>
        <p>Effective IDOR detection requires systematic analysis of application functionality and automated enumeration of object references to identify access control gaps.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Functionality Mapping:</strong>
            <ul>
              <li>User account feature analysis</li>
              <li>Data access point identification</li>
              <li>Administrative function discovery</li>
              <li>API endpoint enumeration</li>
            </ul>
          </li>
          <li><strong>Parameter Analysis:</strong>
            <ul>
              <li>Numeric identifier patterns</li>
              <li>UUID and GUID references</li>
              <li>Encoded parameter values</li>
              <li>Hash-based identifiers</li>
            </ul>
          </li>
          <li><strong>Access Control Testing:</strong>
            <ul>
              <li>Multi-user account testing</li>
              <li>Privilege level comparison</li>
              <li>Session context switching</li>
              <li>Authorization bypass attempts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Manual IDOR Testing</h3>
        <ul>
          <li><strong>Basic Enumeration:</strong>
            <ul>
              <li>Sequential ID manipulation</li>
              <li>Random ID generation</li>
              <li>Pattern-based guessing</li>
              <li>Boundary value testing</li>
            </ul>
          </li>
          <li><strong>Multi-User Testing:</strong>
            <ul>
              <li>Cross-account access attempts</li>
              <li>Role-based testing scenarios</li>
              <li>Privilege escalation testing</li>
              <li>Session sharing analysis</li>
            </ul>
          </li>
          <li><strong>Response Analysis:</strong>
            <ul>
              <li>Data disclosure examination</li>
              <li>Error message analysis</li>
              <li>Response time variations</li>
              <li>Content length differences</li>
            </ul>
          </li>
        </ul>
        
        <h3>Automated IDOR Discovery</h3>
        <ul>
          <li><strong>Burp Suite Extensions:</strong>
            <ul>
              <li>Autorize extension usage</li>
              <li>AuthMatrix for role testing</li>
              <li>Custom Intruder payloads</li>
              <li>Session handling rules</li>
            </ul>
          </li>
          <li><strong>Custom Automation Scripts:</strong>
            <ul>
              <li>Python-based enumeration</li>
              <li>Multi-threaded testing</li>
              <li>Response comparison logic</li>
              <li>Pattern recognition algorithms</li>
            </ul>
          </li>
          <li><strong>API Testing Tools:</strong>
            <ul>
              <li>Postman collection testing</li>
              <li>REST API enumeration</li>
              <li>GraphQL introspection</li>
              <li>OpenAPI specification abuse</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Advanced IDOR Exploitation and Bypass Techniques",content:`
        <h2>Advanced IDOR Exploitation and Protection Bypass</h2>
        <p>Advanced IDOR exploitation involves bypassing protection mechanisms, exploiting complex reference systems, and chaining IDOR with other vulnerabilities.</p>
        
        <h3>Protection Bypass Techniques</h3>
        <ul>
          <li><strong>Encoding and Obfuscation:</strong>
            <ul>
              <li>Base64 encoded identifiers</li>
              <li>URL encoding manipulation</li>
              <li>Hash collision attacks</li>
              <li>Custom encoding schemes</li>
            </ul>
          </li>
          <li><strong>Parameter Manipulation:</strong>
            <ul>
              <li>HTTP method override</li>
              <li>Content-Type manipulation</li>
              <li>Parameter pollution attacks</li>
              <li>Case sensitivity bypass</li>
            </ul>
          </li>
          <li><strong>Indirect Reference Attacks:</strong>
            <ul>
              <li>Reference chain exploitation</li>
              <li>Nested object access</li>
              <li>Relationship-based enumeration</li>
              <li>Foreign key exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Complex IDOR Scenarios</h3>
        <ul>
          <li><strong>Multi-Step IDOR:</strong>
            <ul>
              <li>Chained reference exploitation</li>
              <li>Progressive privilege escalation</li>
              <li>State-dependent access</li>
              <li>Workflow-based enumeration</li>
            </ul>
          </li>
          <li><strong>Time-Based IDOR:</strong>
            <ul>
              <li>Temporal access control bypass</li>
              <li>Race condition exploitation</li>
              <li>Session timing attacks</li>
              <li>Cache-based enumeration</li>
            </ul>
          </li>
          <li><strong>Blind IDOR Exploitation:</strong>
            <ul>
              <li>Boolean-based enumeration</li>
              <li>Error message analysis</li>
              <li>Side-channel information</li>
              <li>Timing-based confirmation</li>
            </ul>
          </li>
        </ul>
        
        <h3>IDOR Impact Maximization</h3>
        <ul>
          <li><strong>Data Exfiltration:</strong>
            <ul>
              <li>Bulk data extraction</li>
              <li>Sensitive information harvesting</li>
              <li>Personal data enumeration</li>
              <li>Financial record access</li>
            </ul>
          </li>
          <li><strong>Privilege Escalation:</strong>
            <ul>
              <li>Administrative function access</li>
              <li>System configuration exposure</li>
              <li>User management capabilities</li>
              <li>Security control bypass</li>
            </ul>
          </li>
          <li><strong>Chaining with Other Vulnerabilities:</strong>
            <ul>
              <li>IDOR + XSS combinations</li>
              <li>IDOR + CSRF attacks</li>
              <li>IDOR + SQL injection</li>
              <li>IDOR + file upload abuse</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary difference between horizontal and vertical IDOR?",options:["Horizontal affects more users","Vertical IDOR involves privilege escalation while horizontal involves same-level access","Horizontal is more dangerous","Vertical only affects administrators"],correctAnswer:1,explanation:"Vertical IDOR involves privilege escalation (accessing higher privilege data/functions), while horizontal IDOR involves accessing data at the same privilege level (like accessing another user's profile)."},{question:"Which technique is most effective for detecting blind IDOR vulnerabilities?",options:["Direct response analysis","Error message examination and timing analysis","Source code review","Network traffic analysis"],correctAnswer:1,explanation:"Error message examination and timing analysis are most effective for blind IDOR because there's no direct response showing the data, requiring indirect methods to confirm unauthorized access."},{question:"What is the most common location for IDOR vulnerabilities in modern web applications?",options:["HTTP headers only","Cookie values","URL parameters and API endpoints","Form hidden fields only"],correctAnswer:2,explanation:"URL parameters and API endpoints are the most common locations for IDOR vulnerabilities because they frequently contain direct object references like user IDs, document IDs, and resource identifiers."}]},type:"quiz"}]},S={id:"bb-program-selection",title:"Bug Bounty Program Selection",description:"Learn how to evaluate and select the right bug bounty programs to maximize your success and earnings.",difficulty:"Intermediate",estimatedTime:90,objectives:["Understand different types of bug bounty programs","Learn to evaluate program quality and potential","Master program research and reconnaissance","Develop a strategic approach to program selection"],sections:[{title:"Types of Bug Bounty Programs",content:`
        <h2>Bug Bounty Program Types</h2>
        <p>Understanding different types of bug bounty programs helps you choose the right targets for your skills and goals.</p>

        <h3>Public Programs</h3>
        <ul>
          <li><strong>Open to all researchers</strong></li>
          <li>Higher competition but more opportunities</li>
          <li>Transparent rules and scope</li>
          <li>Public leaderboards and statistics</li>
        </ul>

        <h3>Private Programs</h3>
        <ul>
          <li><strong>Invitation-only access</strong></li>
          <li>Lower competition, higher payouts</li>
          <li>Requires proven track record</li>
          <li>Often more sensitive targets</li>
        </ul>

        <h3>Continuous vs. Time-Limited</h3>
        <ul>
          <li><strong>Continuous:</strong> Always accepting submissions</li>
          <li><strong>Time-Limited:</strong> Specific duration (contests, events)</li>
          <li><strong>Live Hacking Events:</strong> Real-time collaborative testing</li>
        </ul>

        <h3>Platform-Based vs. Direct</h3>
        <ul>
          <li><strong>Platform-Based:</strong> HackerOne, Bugcrowd, Synack</li>
          <li><strong>Direct Programs:</strong> Company-hosted programs</li>
          <li><strong>Hybrid:</strong> Combination of both approaches</li>
        </ul>
      `,type:"text"},{title:"Program Evaluation Criteria",content:`
        <h2>Evaluating Program Quality</h2>
        <p>Not all bug bounty programs are created equal. Use these criteria to assess program potential.</p>

        <h3>Financial Factors</h3>
        <ul>
          <li><strong>Payout Ranges:</strong> Minimum and maximum rewards</li>
          <li><strong>Average Payouts:</strong> Historical payment data</li>
          <li><strong>Payment Speed:</strong> Time from submission to payment</li>
          <li><strong>Bonus Opportunities:</strong> Special rewards and incentives</li>
        </ul>

        <h3>Scope and Assets</h3>
        <ul>
          <li><strong>Asset Diversity:</strong> Web apps, mobile apps, APIs, infrastructure</li>
          <li><strong>Scope Clarity:</strong> Well-defined in-scope and out-of-scope items</li>
          <li><strong>Asset Complexity:</strong> Modern technologies and frameworks</li>
          <li><strong>Update Frequency:</strong> Regular scope additions and updates</li>
        </ul>

        <h3>Program Maturity</h3>
        <ul>
          <li><strong>Response Time:</strong> How quickly team responds to reports</li>
          <li><strong>Triage Quality:</strong> Accuracy of initial assessments</li>
          <li><strong>Communication:</strong> Clear and professional interactions</li>
          <li><strong>Reputation:</strong> Community feedback and ratings</li>
        </ul>

        <h3>Competition Level</h3>
        <ul>
          <li><strong>Researcher Count:</strong> Number of active participants</li>
          <li><strong>Submission Volume:</strong> Reports submitted per month</li>
          <li><strong>Duplicate Rates:</strong> Frequency of duplicate findings</li>
          <li><strong>Skill Level:</strong> Expertise of competing researchers</li>
        </ul>
      `,type:"text"},{title:"Program Research Techniques",content:`
        <h2>Researching Target Programs</h2>
        <p>Thorough research before engaging with a program can significantly improve your success rate.</p>

        <h3>Platform Research</h3>
        <ul>
          <li><strong>Program Statistics:</strong> Payout data, response times, ratings</li>
          <li><strong>Leaderboards:</strong> Top researchers and their earnings</li>
          <li><strong>Recent Activity:</strong> New programs and scope changes</li>
          <li><strong>Community Discussions:</strong> Forums and chat channels</li>
        </ul>

        <h3>Technical Reconnaissance</h3>
        <ul>
          <li><strong>Technology Stack:</strong> Frameworks, languages, infrastructure</li>
          <li><strong>Asset Mapping:</strong> Subdomains, endpoints, services</li>
          <li><strong>Previous Vulnerabilities:</strong> Public disclosures and CVEs</li>
          <li><strong>Security Posture:</strong> Existing security measures</li>
        </ul>

        <h3>Historical Analysis</h3>
        <ul>
          <li><strong>Past Reports:</strong> Types of vulnerabilities found</li>
          <li><strong>Payout Trends:</strong> Changes in reward amounts over time</li>
          <li><strong>Researcher Success:</strong> Who finds what types of bugs</li>
          <li><strong>Program Evolution:</strong> How scope and rules have changed</li>
        </ul>

        <h3>Tools for Research</h3>
        <ul>
          <li><strong>Subdomain Enumeration:</strong> Subfinder, Amass, Assetfinder</li>
          <li><strong>Technology Detection:</strong> Wappalyzer, BuiltWith, Shodan</li>
          <li><strong>Historical Data:</strong> Wayback Machine, SecurityTrails</li>
          <li><strong>Social Intelligence:</strong> LinkedIn, GitHub, job postings</li>
        </ul>
      `,type:"text"},{title:"Strategic Program Selection",content:`
        <h2>Developing a Selection Strategy</h2>
        <p>Create a systematic approach to choosing programs that align with your skills and goals.</p>

        <h3>Skill-Based Selection</h3>
        <ul>
          <li><strong>Web Application Security:</strong> Focus on web app programs</li>
          <li><strong>Mobile Security:</strong> Target mobile app programs</li>
          <li><strong>Network Security:</strong> Infrastructure-focused programs</li>
          <li><strong>API Security:</strong> Programs with extensive API scope</li>
        </ul>

        <h3>Experience Level Matching</h3>
        <ul>
          <li><strong>Beginner:</strong> New programs with lower competition</li>
          <li><strong>Intermediate:</strong> Established programs with good payouts</li>
          <li><strong>Advanced:</strong> High-value private programs</li>
          <li><strong>Expert:</strong> Complex enterprise applications</li>
        </ul>

        <h3>Portfolio Approach</h3>
        <ul>
          <li><strong>High-Volume Programs:</strong> Consistent smaller payouts</li>
          <li><strong>High-Value Programs:</strong> Potential for large rewards</li>
          <li><strong>Learning Programs:</strong> Skill development opportunities</li>
          <li><strong>Relationship Programs:</strong> Long-term engagement focus</li>
        </ul>

        <h3>Time Management</h3>
        <ul>
          <li><strong>Quick Wins:</strong> Programs with fast response times</li>
          <li><strong>Deep Dives:</strong> Complex applications requiring time investment</li>
          <li><strong>Maintenance:</strong> Regular testing of familiar programs</li>
          <li><strong>Exploration:</strong> New programs and technologies</li>
        </ul>
      `,type:"text"},{title:"Red Flags and Warning Signs",content:`
        <h2>Programs to Avoid</h2>
        <p>Recognize warning signs that indicate a program may not be worth your time and effort.</p>

        <h3>Financial Red Flags</h3>
        <ul>
          <li><strong>Extremely Low Payouts:</strong> Below market rates</li>
          <li><strong>Payment Delays:</strong> Consistent late payments</li>
          <li><strong>Unclear Reward Structure:</strong> Vague payout criteria</li>
          <li><strong>Frequent Downgrades:</strong> Reducing severity ratings</li>
        </ul>

        <h3>Communication Issues</h3>
        <ul>
          <li><strong>Poor Response Times:</strong> Weeks without acknowledgment</li>
          <li><strong>Unprofessional Communication:</strong> Rude or dismissive responses</li>
          <li><strong>Inconsistent Decisions:</strong> Contradictory rulings</li>
          <li><strong>Lack of Transparency:</strong> No explanation for decisions</li>
        </ul>

        <h3>Scope Problems</h3>
        <ul>
          <li><strong>Overly Restrictive Scope:</strong> Very limited testing areas</li>
          <li><strong>Unclear Boundaries:</strong> Ambiguous scope definitions</li>
          <li><strong>Frequent Scope Changes:</strong> Constant rule modifications</li>
          <li><strong>Unrealistic Expectations:</strong> Impossible testing requirements</li>
        </ul>

        <h3>Community Feedback</h3>
        <ul>
          <li><strong>Negative Reviews:</strong> Consistent poor ratings</li>
          <li><strong>Researcher Complaints:</strong> Public criticism</li>
          <li><strong>High Dropout Rates:</strong> Researchers leaving program</li>
          <li><strong>Platform Warnings:</strong> Official platform concerns</li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is a key advantage of private bug bounty programs?",options:["They are open to all researchers","They have lower competition and higher payouts","They require no experience","They have unlimited scope"],correctAnswer:1,explanation:"Private bug bounty programs typically have lower competition due to invitation-only access and often offer higher payouts as a result."},{question:"Which factor is most important when evaluating program competition level?",options:["Program age","Company size","Number of active researchers","Technology stack"],correctAnswer:2,explanation:"The number of active researchers is a key indicator of competition level, as more researchers mean higher competition for finding vulnerabilities."}]},type:"quiz"}]},k={id:"bb-recon-methodology",title:"Reconnaissance Methodology",description:"Master systematic reconnaissance techniques for effective bug bounty hunting.",difficulty:"Intermediate",estimatedTime:120,objectives:["Understand reconnaissance fundamentals","Learn passive and active recon techniques","Master subdomain enumeration","Develop systematic recon workflows"],sections:[{title:"Reconnaissance Fundamentals",content:`
        <h2>Bug Bounty Reconnaissance</h2>
        <p>Learn systematic approaches to reconnaissance for bug bounty hunting success.</p>
        <h3>Reconnaissance Types</h3>
        <ul>
          <li>Passive reconnaissance (OSINT)</li>
          <li>Active reconnaissance</li>
          <li>Subdomain enumeration</li>
          <li>Technology stack identification</li>
        </ul>
      `,type:"text"}]},A={id:"bb-4",pathId:"bug-bounty-hunting",title:"Web Application Testing",description:"Master web application security testing techniques for bug bounty hunting, including OWASP Top 10 vulnerabilities and advanced attack vectors.",objectives:["Understand web application architecture and attack surface","Master OWASP Top 10 vulnerability identification and exploitation","Learn advanced web application testing techniques","Develop efficient testing workflows and automation","Understand business logic flaws and application-specific vulnerabilities"],difficulty:"Intermediate",estimatedTime:180,sections:[{title:"Web Application Security Fundamentals",content:`
        <h2>Web Application Security Fundamentals</h2>
        <p>Understanding web application architecture and common vulnerabilities is essential for effective bug bounty hunting.</p>

        <h3>Web Application Architecture</h3>
        <ul>
          <li><strong>Frontend Components:</strong>
            <ul>
              <li>HTML, CSS, and JavaScript frameworks</li>
              <li>Single Page Applications (SPAs)</li>
              <li>Client-side routing and state management</li>
              <li>Browser security features and limitations</li>
            </ul>
          </li>
          <li><strong>Backend Components:</strong>
            <ul>
              <li>Web servers and application frameworks</li>
              <li>Database systems and data storage</li>
              <li>Authentication and session management</li>
              <li>API endpoints and microservices</li>
            </ul>
          </li>
        </ul>

        <h3>Common Attack Vectors</h3>
        <ul>
          <li>Injection attacks (SQL, NoSQL, Command, LDAP)</li>
          <li>Cross-Site Scripting (XSS) vulnerabilities</li>
          <li>Cross-Site Request Forgery (CSRF) attacks</li>
          <li>Authentication and authorization bypasses</li>
          <li>Business logic flaws and race conditions</li>
        </ul>
      `,type:"text"},{title:"OWASP Top 10 Vulnerabilities",content:`
        <h2>OWASP Top 10 Vulnerabilities</h2>
        <p>The OWASP Top 10 represents the most critical web application security risks.</p>

        <h3>A01: Broken Access Control</h3>
        <ul>
          <li>Vertical privilege escalation</li>
          <li>Horizontal privilege escalation</li>
          <li>Insecure Direct Object References (IDOR)</li>
          <li>Missing function-level access controls</li>
        </ul>

        <h3>A02: Cryptographic Failures</h3>
        <ul>
          <li>Weak encryption algorithms</li>
          <li>Improper key management</li>
          <li>Data transmission without encryption</li>
          <li>Weak random number generation</li>
        </ul>

        <h3>A03: Injection</h3>
        <ul>
          <li>SQL injection vulnerabilities</li>
          <li>NoSQL injection attacks</li>
          <li>Command injection flaws</li>
          <li>LDAP and XML injection</li>
        </ul>
      `,type:"text"}],quiz:{questions:[{question:"What is the most effective way to test for SQL injection vulnerabilities?",options:["Manual testing with payloads","Automated scanning tools only","Combination of manual and automated testing","Code review only"],correct:2,explanation:"A combination of manual and automated testing provides the most comprehensive coverage for SQL injection detection."}]},practicalExercises:[{title:"OWASP Top 10 Vulnerability Assessment",description:"Identify and exploit OWASP Top 10 vulnerabilities in a test application",difficulty:"Intermediate",estimatedTime:120}]},w={title:"Mobile Application Security Testing",description:"Comprehensive guide to testing and securing mobile applications (Android/iOS) against modern threats.",concepts:["Mobile app architecture","Platform-specific vulnerabilities","Reverse engineering","Data storage and privacy","Network communication security"],labs:[{title:"Android App Security Lab",description:"Analyze and test Android applications for security flaws",difficulty:"Intermediate",duration:"2 hours",objectives:["Decompile APKs","Analyze app permissions","Test for insecure data storage","Identify network vulnerabilities"],tools:["MobSF","APKTool","Burp Suite"],prerequisites:["Android basics","APK structure knowledge"]},{title:"iOS App Security Lab",description:"Test iOS applications for security issues",difficulty:"Advanced",duration:"2 hours",objectives:["Analyze IPA files","Test for insecure data storage","Analyze network traffic","Identify platform-specific vulnerabilities"],tools:["MobSF","Frida","Burp Suite"],prerequisites:["iOS basics","IPA structure knowledge"]}],useCases:[{title:"Insecure Data Storage",description:"Detect and remediate insecure data storage in mobile apps",scenario:"Analyze app storage mechanisms and permissions",mitreTactics:["Persistence","Collection"],tools:["MobSF","APKTool","Frida"],steps:["Analyze app data storage","Test for unencrypted data","Check for improper permissions","Remediate vulnerabilities"]},{title:"Reverse Engineering and Tampering",description:"Test mobile apps for reverse engineering and tampering risks",scenario:"Decompile and analyze app code and logic",mitreTactics:["Defense Evasion","Execution"],tools:["APKTool","Frida","MobSF"],steps:["Decompile app binaries","Analyze code for security flaws","Test for runtime manipulation","Implement anti-tampering controls"]}],mitreMapping:[{tactic:"Persistence",techniques:[{name:"Insecure Data Storage",description:"Detect unencrypted or improperly stored data",detection:"Analyze app storage and permissions"},{name:"Improper Platform Usage",description:"Detect misuse of platform features",detection:"Analyze app permissions and APIs"}]},{tactic:"Defense Evasion",techniques:[{name:"Obfuscated Files or Information",description:"Detect obfuscation and anti-analysis techniques",detection:"Analyze app binaries and runtime behavior"},{name:"Reverse Engineering",description:"Test for reverse engineering and tampering",detection:"Monitor for code changes and runtime manipulation"}]}],tools:[{name:"Mobile App Testing Tools",description:"Tools for testing mobile application security",useCases:["Static analysis","Dynamic analysis","Reverse engineering"],examples:["MobSF","APKTool","Frida"]},{name:"Network Analysis Tools",description:"Tools for analyzing mobile app network traffic",useCases:["Traffic interception","SSL pinning bypass","API testing"],examples:["Burp Suite","Charles Proxy","Wireshark"]}],prerequisites:["Understanding of mobile app architecture","Basic programming skills","Familiarity with Android/iOS platforms","Awareness of mobile-specific vulnerabilities"],resources:[{type:"Guide",title:"OWASP Mobile Security Testing Guide",url:"https://owasp.org/www-project-mobile-security-testing-guide/"},{type:"Toolkit",title:"Mobile App Security Tools",url:"https://github.com/OWASP/owasp-mstg"}]},C={title:"API Security Testing",description:"Comprehensive guide to testing and securing REST and GraphQL APIs.",concepts:["API architecture and design","Authentication and authorization","Input validation and sanitization","Rate limiting and DoS protection","API security best practices"],labs:[{title:"REST API Security Testing",description:"Learn to test and secure REST APIs",difficulty:"Intermediate",duration:"2 hours",objectives:["Identify API endpoints","Test authentication mechanisms","Analyze request/response patterns","Implement security controls"],tools:["Postman","Burp Suite","OWASP ZAP"],prerequisites:["Basic API knowledge","Understanding of HTTP protocols"]},{title:"GraphQL Security Testing",description:"Advanced testing of GraphQL APIs",difficulty:"Advanced",duration:"2.5 hours",objectives:["Analyze GraphQL schema","Test query complexity","Identify introspection vulnerabilities","Implement rate limiting"],tools:["GraphQL Playground","Insomnia","Burp Suite"],prerequisites:["GraphQL basics","API security concepts"]}],useCases:[{title:"Authentication Bypass",description:"Test and identify authentication vulnerabilities",scenario:"Analyze and bypass API authentication mechanisms",mitreTactics:["Initial Access","Persistence"],tools:["Burp Suite","Postman","Custom Scripts"],steps:["Analyze authentication flow","Test token handling","Identify weak implementations","Document vulnerabilities"]},{title:"Data Exposure",description:"Identify and prevent data leakage",scenario:"Test API endpoints for sensitive data exposure",mitreTactics:["Collection","Exfiltration"],tools:["OWASP ZAP","Burp Suite","Custom Proxies"],steps:["Map API endpoints","Analyze response data","Test access controls","Implement data protection"]}],mitreMapping:[{tactic:"Initial Access",techniques:[{name:"Valid Accounts",description:"Test authentication mechanisms",detection:"Monitor authentication attempts and failures"},{name:"Exploit Public-Facing Application",description:"Test API vulnerabilities",detection:"Monitor API access patterns and errors"}]},{tactic:"Persistence",techniques:[{name:"Access Token Manipulation",description:"Test token handling",detection:"Monitor token usage and validation"},{name:"Account Manipulation",description:"Test account management APIs",detection:"Monitor account modification requests"}]}],tools:[{name:"API Testing Tools",description:"Tools for testing API security",useCases:["Request manipulation","Response analysis","Vulnerability scanning"],examples:["Postman","Burp Suite","OWASP ZAP"]},{name:"API Development Tools",description:"Tools for API development and testing",useCases:["API design","Testing","Documentation"],examples:["Swagger","Insomnia","GraphQL Playground"]}],prerequisites:["Understanding of API concepts","Knowledge of HTTP protocols","Familiarity with security testing","Basic programming skills"],resources:[{type:"Guide",title:"API Security Testing Guide",url:"https://example.com/api-security-guide"},{type:"Cheat Sheet",title:"API Testing Commands",url:"https://example.com/api-testing-cheatsheet"}]},P={modules:[i,e,t,n,l,o,s,a,r,c,u,d,g,p,m,h,y,f,b,v,S,k,A,w,C]},x=()=>P.modules;export{P as bugBountyLearningPath,x as getAllBugBountyModules};
