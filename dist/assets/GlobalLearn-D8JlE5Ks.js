import{j as e,m as $,D as B,L as be,aR as ae,q as D,l as A,a3 as fe,G as pe,W as ye,aH as je,r as c,a4 as K,t as re,ai as ve,u as Ne,w as I,c1 as we,c as J,Q as Z,d as ke,e as O,A as Ce,a7 as Se,H as Me,b as le,bX as w,bv as $e,c2 as R,M as L,ax as Fe,x as q}from"./index-CVvVjHWF.js";import{U}from"./UpgradeBanner-BQfQqcqw.js";import{n as m,b as Ee,l as ee}from"./learning-paths-structure-YI8Fv9cD.js";function Te({children:t,className:o="",hover:g=!0,animate:r=!0,glow:y=!1,accent:i=!1,accentColor:p="green",...u}){const j={green:"bg-[#88cc14]",blue:"bg-blue-500",red:"bg-red-500",yellow:"bg-yellow-500",purple:"bg-purple-500",orange:"bg-orange-500"},b=`
    bg-[#1A1F35]
    border border-gray-800
    rounded-xl overflow-hidden
    ${g?"hover:border-[#88cc14]/50 transition-colors duration-300":""}
    ${y?"shadow-lg shadow-[#88cc14]/10":""}
    ${o}
  `,N=e.jsxs(e.Fragment,{children:[i&&e.jsx("div",{className:`h-1 ${j[p]}`}),e.jsx("div",{className:"p-6",children:t})]});return r?e.jsx($.div,{className:b,whileHover:g?{y:-5}:{},transition:{duration:.3},...u,children:N}):e.jsx("div",{className:b,...u,children:N})}const Le=({title:t,description:o,image:g,type:r="subscription",feature:y,requiredTier:i="premium",difficulty:p,to:u})=>{const{requiresCoins:j,getItemCost:b}=B();return e.jsx($.div,{whileHover:{y:-5},transition:{duration:.3},children:e.jsx(be,{to:u||"/pricing",children:e.jsxs(Te,{className:"h-full relative overflow-hidden group",hover:!1,animate:!1,children:[e.jsx("div",{className:"absolute inset-0 bg-black/70 backdrop-blur-sm flex flex-col items-center justify-center z-10 p-4",children:r==="subscription"?e.jsxs(e.Fragment,{children:[e.jsx(ae,{className:"text-[#88cc14] text-4xl mb-3"}),e.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:t}),e.jsx("p",{className:"text-gray-300 text-sm mb-3 text-center",children:o}),e.jsxs("span",{className:"bg-[#88cc14] text-black px-3 py-1 rounded-full text-sm font-bold",children:[i.charAt(0).toUpperCase()+i.slice(1)," Required"]})]}):e.jsxs(e.Fragment,{children:[e.jsx(D,{className:"text-yellow-500 text-4xl mb-3"}),e.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:t}),e.jsx("p",{className:"text-gray-300 text-sm mb-3 text-center",children:o}),e.jsxs("span",{className:"bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center gap-1",children:[e.jsx(D,{className:"text-sm"}),b("challenge",p)," Coins"]})]})}),e.jsxs("div",{className:"relative h-40 overflow-hidden",children:[g?e.jsx("img",{src:g,alt:t,className:"w-full h-full object-cover filter blur-sm opacity-30"}):e.jsx("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 filter blur-sm opacity-30"}),e.jsx("div",{className:"absolute top-2 right-2 bg-black/50 p-1 rounded-full",children:e.jsx(A,{className:"text-white"})})]}),e.jsxs("div",{className:"p-4 filter blur-sm opacity-30",children:[e.jsx("h3",{className:"font-bold text-lg mb-1 text-white",children:t}),e.jsx("p",{className:"text-gray-400 text-sm line-clamp-2",children:o})]})]})})})},se={"nf-1":m["nf-1"],"nf-2":m["nf-2"],"nf-3":m["nf-3"],"nf-4":m["nf-4"],"nf-5":m["nf-5"],"nf-6":m["nf-6"],"nf-7":m["nf-7"],"nf-8":m["nf-8"],"nf-9":m["nf-9"],"nf-10":m["nf-10"],"nf-11":m["nf-11"],"nf-12":m["nf-12"],"nf-13":m["nf-13"],"nf-14":m["nf-14"],"nf-15":m["nf-15"],"nf-16":m["nf-16"],"nf-17":m["nf-17"],"nf-18":m["nf-18"],"nf-19":m["nf-19"],"nf-20":m["nf-20"],"nf-21":m["nf-21"],"nf-22":m["nf-22"],"nf-23":m["nf-23"],"nf-24":m["nf-24"],"nf-25":m["nf-25"]},Ae=Ee(),z={};Ae.forEach(t=>{z[t.id]=t});const te={"os-introduction":{title:"Introduction to Operating Systems",sections:[{title:"What is an Operating System?",content:[{type:"text",value:"An operating system (OS) is system software that manages computer hardware, software resources, and provides common services for computer programs. It acts as an intermediary between users and the computer hardware."},{type:"text",value:"The operating system is a critical component in the functioning of a computer system. Without an operating system, a user cannot run an application program on their computer."}]},{title:"Core Functions of an Operating System",content:[{type:"text",value:"Operating systems perform several key functions:"},{type:"text",value:"<ul><li><strong>Process Management:</strong> Creating, scheduling, and terminating processes</li><li><strong>Memory Management:</strong> Allocating and deallocating memory space</li><li><strong>File System Management:</strong> Creating, deleting, and organizing files</li><li><strong>Device Management:</strong> Managing device communication</li><li><strong>Security:</strong> Protecting system resources and user data</li></ul>"}]}]}},Pe=t=>se[t]?se[t]:z[t]?z[t]:te[t]?te[t]:null,Re=({data:t})=>e.jsx("div",{className:"space-y-4 p-4 bg-gray-900 rounded-lg",children:t.layers.map((o,g)=>e.jsxs($.div,{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},transition:{delay:g*.2},className:"p-4 rounded-lg flex items-center gap-3",style:{backgroundColor:`${o.color}20`,border:`1px solid ${o.color}`},children:[e.jsx("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center",style:{backgroundColor:`${o.color}40`},children:e.jsx("span",{className:"text-white text-xl",children:o.icon})}),e.jsx("span",{className:"text-white font-medium",children:o.name})]},g))}),Oe={FaUser:je,FaUsers:ye,FaClock:pe,FaNetwork:fe},Ue=({data:t})=>e.jsx("div",{className:"grid grid-cols-2 gap-4 p-4 bg-gray-900 rounded-lg",children:t.types.map((o,g)=>{const r=Oe[o.icon];return e.jsxs($.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},transition:{delay:g*.1},className:"bg-gray-800 p-4 rounded-lg flex items-center gap-3",children:[r&&e.jsx(r,{className:"text-[#88cc14] text-xl"}),e.jsx("span",{className:"text-white font-medium",children:o.name})]},g)})});function ze({commands:t=[],onCommandComplete:o}){const[g,r]=c.useState(""),[y,i]=c.useState([]),[p,u]=c.useState(0),[j,b]=c.useState(!1),N=c.useRef(null),v=c.useRef(null);c.useEffect(()=>{var d;(d=v.current)==null||d.scrollIntoView({behavior:"smooth"})},[y]),c.useEffect(()=>{i([{type:"system",text:"=== XCerberus OS Lab Terminal ==="},{type:"system",text:'Type "help" for available commands'},{type:"system",text:""}]),t.length>0&&i(d=>[...d,{type:"system",text:`Current task: ${t[0].description}`}])},[t]);const k=d=>{if(d.preventDefault(),!g.trim()||j)return;const h=g.trim();i(n=>[...n,{type:"input",text:`$ ${h}`}]),r(""),b(!0),setTimeout(()=>{if(h==="help")i(n=>[...n,{type:"system",text:"Available commands:"},{type:"system",text:"  help     - Show this help message"},{type:"system",text:"  clear    - Clear terminal screen"},{type:"system",text:""},{type:"system",text:"Task-specific commands:"},...t.map(x=>({type:"system",text:`  ${x.command}  - ${x.description}`}))]);else if(h==="clear")i([]);else{const n=t[p];n&&h===n.command?(i(x=>[...x,{type:"success",text:"✓ Command successful!"},{type:"output",text:n.expectedOutput||"Command executed successfully."}]),o==null||o(n),p<t.length-1?setTimeout(()=>{u(x=>x+1),i(x=>[...x,{type:"system",text:""},{type:"system",text:`Next task: ${t[p+1].description}`}])},1e3):i(x=>[...x,{type:"success",text:"🎉 All tasks completed! Congratulations!"}])):(i(x=>[...x,{type:"error",text:"Command not recognized or incorrect for current task."}]),n!=null&&n.hint&&i(x=>[...x,{type:"hint",text:`Hint: ${n.hint}`}]))}b(!1)},500)};return e.jsxs("div",{className:"bg-black rounded-lg overflow-hidden border border-gray-800",children:[e.jsx("div",{className:"p-3 border-b border-gray-800 bg-gray-900",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500"}),e.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500"}),e.jsx("span",{className:"ml-2 text-gray-400 text-sm",children:"XCerberus@terminal:~"})]})}),e.jsxs("div",{ref:N,className:"h-96 p-4 font-mono text-sm overflow-y-auto bg-black text-gray-300",children:[y.map((d,h)=>e.jsx("div",{className:`mb-2 ${d.type==="input"||d.type==="success"?"text-green-400":d.type==="error"?"text-red-400":d.type==="hint"?"text-yellow-400":d.type==="output"?"text-blue-300":"text-gray-300"}`,children:d.text},h)),j&&e.jsx("div",{className:"text-green-400 animate-pulse",children:"_"}),e.jsx("div",{ref:v}),e.jsxs("form",{onSubmit:k,className:"mt-2 flex items-center",children:[e.jsx("span",{className:"text-green-400 mr-2",children:"$"}),e.jsx("input",{type:"text",value:g,onChange:d=>r(d.target.value),className:"flex-1 bg-transparent border-none outline-none text-green-400",disabled:j,autoFocus:!0})]})]})]})}const Be=({questions:t})=>{const[o,g]=c.useState(0),[r,y]=c.useState({}),[i,p]=c.useState(!1),[u,j]=c.useState(0);if(!t||t.length===0)return null;const b=(h,n)=>{y(x=>({...x,[h]:n}))},N=()=>{let h=0;return Object.entries(r).forEach(([n,x])=>{t[n].correct===x&&h++}),h},v=()=>{const h=N();j(h),p(!0)},k=()=>{y({}),p(!1),j(0),g(0)},d=Object.keys(r).length===t.length;return e.jsxs($.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"Knowledge Check"}),i?e.jsxs($.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-4xl font-bold mb-2",children:[u," / ",t.length]}),e.jsx("p",{className:"text-gray-600",children:u===t.length?"Perfect score! You've mastered this topic!":u>t.length/2?"Good job! Keep practicing to improve further.":"Keep studying! You'll get better with practice."})]}),e.jsx("div",{className:"space-y-6",children:t.map((h,n)=>{const x=r[n]===h.correct;return e.jsx("div",{className:`p-4 rounded-lg ${x?"bg-green-50":"bg-red-50"}`,children:e.jsxs("div",{className:"flex items-start gap-3",children:[x?e.jsx(K,{className:"text-green-500 mt-1"}):e.jsx(re,{className:"text-red-500 mt-1"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900 mb-2",children:h.question}),e.jsx("p",{className:"text-sm text-gray-600",children:h.explanation})]})]})},n)})}),e.jsxs("button",{onClick:k,className:"w-full flex items-center justify-center gap-2 bg-gray-900 text-white py-3 px-4 rounded-lg font-bold hover:bg-gray-800 transition-all",children:[e.jsx(ve,{}),"Try Again"]})]}):e.jsxs("div",{className:"space-y-8",children:[t.map((h,n)=>e.jsxs($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:n*.1},className:"space-y-4",children:[e.jsxs("p",{className:"font-medium text-gray-900",children:[n+1,". ",h.question]}),e.jsx("div",{className:"space-y-2",children:h.options.map((x,C)=>e.jsx("button",{onClick:()=>b(n,C),className:`w-full text-left p-4 rounded-lg transition-all ${r[n]===C?"bg-[#88cc14]/10 border-[#88cc14] border":"bg-gray-50 hover:bg-[#88cc14]/5 border border-gray-200"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-5 h-5 rounded-full border ${r[n]===C?"border-[#88cc14] bg-[#88cc14]":"border-gray-300"} flex items-center justify-center`,children:r[n]===C&&e.jsx(K,{className:"text-white text-xs"})}),e.jsx("span",{className:"text-gray-700",children:x})]})},C))})]},n)),e.jsx("button",{onClick:v,disabled:!d,className:`w-full py-3 px-4 rounded-lg font-bold transition-all ${d?"bg-[#88cc14] text-black hover:bg-[#7ab811]":"bg-gray-100 text-gray-400 cursor-not-allowed"}`,children:"Submit Answers"})]})]})},Ie=({moduleId:t,topicId:o,isAccessible:g})=>{const{darkMode:r}=Ne(),{user:y}=I(),{getModuleProgress:i,isModuleCompleted:p}=we(),[u,j]=c.useState(!1),[b,N]=c.useState(0),[v,k]=c.useState(!1);return c.useEffect(()=>{if(t){const d=i(t);N(d),k(p(t))}},[t,i,p]),!g||!y?null:e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg shadow-sm mb-6`,children:[e.jsxs("div",{className:`p-4 flex items-center justify-between cursor-pointer ${r?"hover:bg-gray-700":"hover:bg-gray-50"}`,onClick:()=>j(!u),children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3",children:e.jsx(J,{className:"text-blue-500"})}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-bold ${r?"text-white":"text-gray-800"}`,children:"Your Progress"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mr-2",children:e.jsx("div",{className:`h-full ${v?"bg-green-500":"bg-blue-500"}`,style:{width:`${b}%`}})}),e.jsxs("span",{className:`text-xs ${r?"text-gray-400":"text-gray-600"}`,children:[b,"% Complete"]})]})]})]}),e.jsxs("div",{className:"flex items-center",children:[v&&e.jsxs("div",{className:"flex items-center mr-4",children:[e.jsx(Z,{className:"text-green-500 mr-1"}),e.jsx("span",{className:`font-medium ${r?"text-white":"text-gray-800"}`,children:"Completed"})]}),u?e.jsx(ke,{}):e.jsx(O,{})]})]}),e.jsx(Ce,{children:u&&e.jsx($.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"overflow-hidden",children:e.jsxs("div",{className:"p-4 pt-0 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:`text-sm font-medium mb-2 ${r?"text-gray-300":"text-gray-700"}`,children:"Module Progress"}),e.jsx(Se,{showLevel:!1})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:`text-sm font-medium mb-2 ${r?"text-gray-300":"text-gray-700"}`,children:"Module Rewards"}),e.jsx("div",{className:`p-3 rounded-lg ${r?"bg-gray-700":"bg-gray-100"}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center mr-2",children:e.jsx(J,{className:"text-yellow-500"})}),e.jsx("span",{className:r?"text-white":"text-gray-800",children:"Complete Module"})]}),e.jsx("span",{className:"font-medium text-yellow-500",children:"Achievement"})]})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:`text-sm font-medium mb-2 ${r?"text-gray-300":"text-gray-700"}`,children:"Topic Rewards"}),e.jsx("div",{className:`p-3 rounded-lg ${r?"bg-gray-700":"bg-gray-100"}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-2",children:e.jsx(Z,{className:"text-blue-500"})}),e.jsx("span",{className:r?"text-white":"text-gray-800",children:"Complete Topic"})]}),e.jsx("span",{className:"font-medium text-blue-500",children:"Progress"})]})})]}),e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-orange-500/20 flex items-center justify-center mr-2",children:e.jsx(Me,{className:"text-orange-500"})}),e.jsxs("div",{children:[e.jsx("h4",{className:`text-sm font-medium ${r?"text-gray-300":"text-gray-700"}`,children:"Learning Streak"}),e.jsx("p",{className:`text-xs ${r?"text-gray-400":"text-gray-600"}`,children:"Complete at least one topic daily to maintain your streak!"})]})]})})]})})})]})},We={OSLayersVisualization:Re,OSTypesVisualization:Ue},_e=()=>{const t=le(),{user:o}=I(),{subscriptionLevel:g,hasAccess:r,getRemainingContent:y}=B(),[i,p]=c.useState("fundamentals"),[u,j]=c.useState("os-concepts"),[b,N]=c.useState("os-introduction"),[v,k]=c.useState(["fundamentals"]),[d,h]=c.useState(["os-concepts"]),[n,x]=c.useState(!0),[C,W]=c.useState(!1),[ne,_]=c.useState(!1),E=!!o,P=Pe(b),F=ee.find(a=>a.id===i),f=F==null?void 0:F.modules.find(a=>a.id===u),G=f==null?void 0:f.topics.find(a=>a.id===b),[V,Y]=c.useState(!0),[ie,H]=c.useState(0);c.useEffect(()=>{if(f)if(!E)Y(f.tier===w.FREE),H(0);else{const a=r("learnModules",f.tier||"free");Y(a);const{remaining:l}=y("learnModules");H(l)}},[f,g,r,y,E]);const ce=a=>{k(l=>l.includes(a)?l.filter(s=>s!==a):[...l,a])},oe=a=>{h(l=>l.includes(a)?l.filter(s=>s!==a):[...l,a])},T=(a,l,s)=>{p(a),j(l),N(s),v.includes(a)||k([...v,a]),d.includes(l)||h([...d,l]),C&&W(!1)},de=()=>{x(!n)},xe=()=>{W(!C)},me=a=>{switch(a){case w.PREMIUM:return e.jsx(ae,{className:"text-yellow-400"});case w.BUSINESS:return e.jsx(Fe,{className:"text-blue-400"});default:return null}},Q=a=>{if(!a||a===w.FREE)return null;const l=me(a),s=r("learnModules",a);return e.jsxs("span",{className:`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${s?a===w.PREMIUM?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}`,children:[l,e.jsx("span",{className:"ml-1",children:a.charAt(0).toUpperCase()+a.slice(1)}),!s&&e.jsx(A,{className:"ml-1 text-xs"})]})},X=()=>e.jsx("div",{className:`bg-gray-100 dark:bg-gray-800 overflow-y-auto ${n?"w-64 flex-shrink-0":"w-0"} transition-all duration-300 ease-in-out`,children:n&&e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"text-xl font-bold mb-4 text-gray-800 dark:text-white",children:"Learning Paths"}),e.jsx("div",{className:"space-y-2",children:ee.map(a=>e.jsxs("div",{className:"rounded-lg overflow-hidden",children:[e.jsxs("button",{className:`w-full flex items-center justify-between p-3 text-left font-medium rounded-lg ${i===a.id?"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>ce(a.id),children:[e.jsx("span",{className:"truncate",children:a.title}),v.includes(a.id)?e.jsx(O,{className:"flex-shrink-0"}):e.jsx(L,{className:"flex-shrink-0"})]}),v.includes(a.id)&&e.jsx("div",{className:"mt-1 ml-2 pl-2 border-l-2 border-gray-300 dark:border-gray-600",children:a.modules.map(l=>e.jsxs("div",{className:"mt-1",children:[e.jsxs("button",{className:`w-full flex items-center justify-between p-2 text-left text-sm font-medium rounded-lg ${u===l.id?"bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-200":"text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700/50"}`,onClick:()=>oe(l.id),children:[e.jsxs("span",{className:"flex items-center truncate",children:[l.title,Q(l.tier)]}),d.includes(l.id)?e.jsx(O,{className:"flex-shrink-0"}):e.jsx(L,{className:"flex-shrink-0"})]}),d.includes(l.id)&&e.jsx("div",{className:"mt-1 ml-2 pl-2 border-l-2 border-gray-200 dark:border-gray-700",children:l.topics.map(s=>e.jsx("button",{className:`w-full flex items-center justify-between p-2 text-left text-xs rounded-lg ${b===s.id?"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-200":"text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-700/30"}`,onClick:()=>T(a.id,l.id,s.id),children:e.jsxs("span",{className:"flex items-center truncate",children:[s.title,Q(s.tier)]})},s.id))})]},l.id))})]},a.id))})]})}),ue=()=>V?P?e.jsxs("div",{className:"prose prose-lg dark:prose-invert max-w-none",children:[e.jsx("h1",{children:P.title}),e.jsx(Ie,{moduleId:u,topicId:b,isAccessible:V}),P.sections.map((a,l)=>e.jsxs("div",{className:"mb-8",children:[a.title&&e.jsx("h2",{children:a.title}),a.content.map((s,S)=>{if(s.type==="text")return e.jsx("p",{dangerouslySetInnerHTML:{__html:s.value}},S);if(s.type==="code")return e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto",children:e.jsx("code",{children:s.value})},S);if(s.type==="image")return e.jsxs("div",{className:"my-4",children:[e.jsx("img",{src:s.src,alt:s.alt||"Learning content image",className:"rounded-lg max-w-full h-auto"}),s.caption&&e.jsx("p",{className:"text-sm text-center text-gray-500 dark:text-gray-400 mt-2",children:s.caption})]},S);if(s.type==="visualization"){const M=We[s.component];return M?e.jsx("div",{className:"my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:e.jsx(M,{...s.props})},S):null}else{if(s.type==="lab")return e.jsx("div",{className:"my-6",children:ne?e.jsxs("div",{className:"border rounded-lg overflow-hidden",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 px-4 py-2 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-lg font-medium",children:["Lab: ",s.title]}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",onClick:()=>_(!1),children:"Close Lab"})]}),e.jsx("div",{className:"p-4",children:e.jsx(ze,{commands:s.commands})})]}):e.jsx("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",onClick:()=>_(!0),children:"Start Lab Exercise"})},S);if(s.type==="quiz")return e.jsx("div",{className:"my-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:e.jsx(Be,{questions:s.questions})},S)}return null})]},l)),e.jsxs("div",{className:"flex justify-between mt-8 pt-4 border-t border-gray-200 dark:border-gray-700",children:[ge(),he()]})]}):e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Select a topic to start learning"})}):e.jsx(Le,{title:(f==null?void 0:f.title)||"Premium Module",description:E?"This module is only available with a higher subscription tier.":"Sign in to access this premium module.",tier:(f==null?void 0:f.tier)||w.PREMIUM,actionText:E?"Upgrade to Access":"Sign In",onAction:()=>t(E?"/pricing":"/login")}),ge=()=>{if(!f||!G)return e.jsx("div",{});const a=f.topics.findIndex(s=>s.id===b);if(a>0){const s=f.topics[a-1];return e.jsxs("button",{className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",onClick:()=>T(i,u,s.id),children:[e.jsx(R,{className:"mr-2"}),"Previous: ",s.title]})}const l=F.modules.findIndex(s=>s.id===u);if(l>0){const s=F.modules[l-1],S=s.topics[s.topics.length-1],M=r("learnModules",s.tier||"free");return e.jsxs("button",{className:`flex items-center ${M?"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300":"text-gray-400 dark:text-gray-600 cursor-not-allowed"}`,onClick:()=>{M&&T(i,s.id,S.id)},children:[e.jsx(R,{className:"mr-2"}),"Previous: ",s.title,!M&&e.jsx(A,{className:"ml-2"})]})}return e.jsx("div",{})},he=()=>{if(!f||!G)return e.jsx("div",{});const a=f.topics.findIndex(s=>s.id===b);if(a<f.topics.length-1){const s=f.topics[a+1];return e.jsxs("button",{className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",onClick:()=>T(i,u,s.id),children:["Next: ",s.title,e.jsx(L,{className:"ml-2"})]})}const l=F.modules.findIndex(s=>s.id===u);if(l<F.modules.length-1){const s=F.modules[l+1],S=s.topics[0],M=r("learnModules",s.tier||"free");return e.jsxs("button",{className:`flex items-center ${M?"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300":"text-gray-400 dark:text-gray-600 cursor-not-allowed"}`,onClick:()=>{M&&T(i,s.id,S.id)},children:["Next: ",s.title,e.jsx(L,{className:"ml-2"}),!M&&e.jsx(A,{className:"ml-2"})]})}return e.jsx("div",{})};return e.jsxs("div",{className:"flex flex-col h-full bg-white dark:bg-gray-900",children:[e.jsxs("div",{className:"md:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h1",{className:"text-xl font-bold text-gray-800 dark:text-white",children:"Learning"}),e.jsx("button",{className:"p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700",onClick:xe,children:C?e.jsx(re,{}):e.jsx($e,{})})]}),C&&e.jsx($.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"mt-4 overflow-hidden",children:X()})]}),e.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[e.jsx("div",{className:"hidden md:block",children:X()}),e.jsx("button",{className:"hidden md:flex items-center justify-center w-6 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors",onClick:de,children:n?e.jsx(R,{}):e.jsx(L,{})}),e.jsxs("div",{className:"flex-1 overflow-auto p-4 md:p-8",children:[g===w.FREE&&e.jsx("div",{className:"mb-6",children:e.jsx(U,{title:"Upgrade to Access All Learning Modules",description:`You have access to ${ie} more modules with your current plan.`,buttonText:"Upgrade Now",onButtonClick:()=>t("/pricing")})}),ue()]})]})]})},He=()=>{const t=le(),{user:o,loading:g}=I(),{subscriptionLevel:r,loading:y,getRemainingContent:i}=B(),[p,u]=c.useState(0),[j,b]=c.useState(!1);c.useEffect(()=>{if(localStorage.getItem("has_visited_learn")||(b(!0),localStorage.setItem("has_visited_learn","true")),!y&&o){const{remaining:k}=i("learnModules");u(k)}},[y,i,o]);const N=()=>j?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs($.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 p-3 rounded-full",children:e.jsx(q,{className:"text-blue-600 dark:text-blue-300 text-2xl"})})}),e.jsx("h2",{className:"text-2xl font-bold text-center mb-4 text-gray-800 dark:text-white",children:"Welcome to XCerberus Learning"}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-300 mb-6 text-center",children:["Explore our comprehensive cybersecurity curriculum designed for all skill levels.",r===w.FREE&&e.jsxs("span",{className:"block mt-2 text-sm",children:["You have access to ",e.jsx("span",{className:"font-bold",children:p})," modules with your free account."]})]}),e.jsxs("div",{className:"flex flex-col space-y-3",children:[e.jsx("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors",onClick:()=>b(!1),children:"Start Learning"}),r===w.FREE&&e.jsx("button",{className:"w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-lg transition-colors",onClick:()=>t("/pricing"),children:"Explore Premium"})]})]})}):null;return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[e.jsx(q,{className:"mr-2 text-blue-600 dark:text-blue-400"}),"Learning Center"]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Master cybersecurity skills with our comprehensive curriculum"})]}),r===w.FREE&&e.jsx("div",{className:"hidden sm:block",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>t("/pricing"),children:"Upgrade for Full Access"})})]})})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[!o&&e.jsx("div",{className:"mb-6",children:e.jsx(U,{title:"Sign In to Track Your Progress",description:"Create an account or sign in to save your progress and access more learning content.",buttonText:"Sign In",onButtonClick:()=>t("/login"),variant:"prominent"})}),o&&r===w.FREE&&e.jsx("div",{className:"mb-6",children:e.jsx(U,{title:"Unlock All Learning Modules",description:`You have access to ${p} more modules with your free account. Upgrade to unlock all content.`,buttonText:"View Plans",onButtonClick:()=>t("/pricing")})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden h-[calc(100vh-12rem)]",children:e.jsx(_e,{})})]}),N()]})};export{He as default};
