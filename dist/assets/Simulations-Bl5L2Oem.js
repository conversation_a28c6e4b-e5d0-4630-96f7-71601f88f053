import{u,r as h,j as e,m as a,L as i,aQ as o,N as l,x as b,b4 as g,z as d,M as p}from"./index-CVvVjHWF.js";import{C as f}from"./CyberForceSEO-C79kd8_3.js";import"./index-Cc2BstNK.js";const N=()=>{const{darkMode:s}=u(),[c,n]=h.useState(null),x=[{id:"offensive",title:"Offensive Simulations",description:"Practice ethical hacking techniques in realistic environments",icon:e.jsx(o,{className:"text-red-500",size:36}),path:"/simulations/offensive",color:"from-red-500/20 to-red-600/5",borderColor:"border-red-500/20",hoverBorderColor:"hover:border-red-500",features:["Web Application Penetration Testing","Network Exploitation","Social Engineering Scenarios","Privilege Escalation Challenges"]},{id:"defensive",title:"Defensive Simulations",description:"Develop blue team skills to detect and respond to cyber threats",icon:e.jsx(l,{className:"text-blue-500",size:36}),path:"/simulations/defensive",color:"from-blue-500/20 to-blue-600/5",borderColor:"border-blue-500/20",hoverBorderColor:"hover:border-blue-500",features:["Security Operations Center (SOC)","Incident Response Scenarios","Threat Hunting Exercises","Log Analysis Challenges"]}];return e.jsxs("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-20`,children:[e.jsx(f,{title:"Cybersecurity Simulations",description:"Experience realistic cybersecurity simulations with CyberForce. Practice offensive and defensive security techniques in safe, controlled environments.",keywords:["cybersecurity simulations","offensive security","defensive security","SOC training","penetration testing"],canonicalUrl:"https://cyberforce.om/simulations"}),e.jsxs("div",{className:"relative overflow-hidden bg-gradient-to-b from-[#0B1120] to-[#1A1F35] py-16",children:[e.jsxs("div",{className:"absolute inset-0 opacity-20",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-red-500/20"}),e.jsx("div",{className:"grid grid-cols-10 grid-rows-10 h-full w-full",children:Array.from({length:100}).map((t,r)=>e.jsx("div",{className:"border-[0.5px] border-white/5"},r))})]}),e.jsx("div",{className:"container mx-auto px-4 relative z-10",children:e.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[e.jsxs(a.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-4xl md:text-5xl font-bold mb-6 text-white",children:["Realistic Cybersecurity ",e.jsx("span",{className:"text-[#F5B93F]",children:"Simulations"})]}),e.jsx(a.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"text-xl text-gray-300 mb-8",children:"Develop practical skills through immersive offensive and defensive security scenarios"}),e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"flex flex-wrap justify-center gap-4",children:[e.jsxs(i,{to:"/simulations/offensive",className:"px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:[e.jsx(o,{})," Offensive Simulations"]}),e.jsxs(i,{to:"/simulations/defensive",className:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:[e.jsx(l,{})," Defensive Simulations"]})]})]})})]}),e.jsxs("div",{className:"container mx-auto px-4 py-16",children:[e.jsxs("div",{className:"max-w-3xl mx-auto text-center mb-16",children:[e.jsx("h2",{className:`text-3xl font-bold mb-6 ${s?"text-white":"text-gray-900"}`,children:"Why Train with CyberForce Simulations?"}),e.jsx("p",{className:`text-lg mb-8 ${s?"text-gray-300":"text-gray-600"}`,children:"Our simulations provide hands-on experience in realistic environments, allowing you to develop practical skills that are directly applicable to real-world cybersecurity challenges."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:`p-6 rounded-lg ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border`,children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-4",children:e.jsx(b,{className:"text-blue-500 text-xl"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Learn by Doing"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Practical experience is the most effective way to develop cybersecurity skills"})]}),e.jsxs("div",{className:`p-6 rounded-lg ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border`,children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center mx-auto mb-4",children:e.jsx(g,{className:"text-purple-500 text-xl"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Real-World Scenarios"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Based on actual cyber attacks and defense strategies used in the industry"})]}),e.jsxs("div",{className:`p-6 rounded-lg ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border`,children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center mx-auto mb-4",children:e.jsx(l,{className:"text-green-500 text-xl"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Safe Environment"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Practice offensive and defensive techniques in controlled, legal environments"})]})]})]}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16",children:x.map(t=>e.jsxs(i,{to:t.path,className:`relative overflow-hidden rounded-xl border ${t.borderColor} ${t.hoverBorderColor} transition-all duration-300 ${s?"bg-[#1A1F35]":"bg-white"}`,onMouseEnter:()=>n(t.id),onMouseLeave:()=>n(null),children:[e.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${t.color} opacity-50`}),e.jsxs("div",{className:"relative z-10 p-8",children:[e.jsxs("div",{className:"flex items-start justify-between mb-6",children:[e.jsx("div",{className:"w-16 h-16 rounded-lg bg-white/10 backdrop-blur-sm flex items-center justify-center",children:t.icon}),e.jsx(d,{className:`text-xl transition-transform duration-300 ${c===t.id?"translate-x-0 opacity-100":"-translate-x-4 opacity-0"}`})]}),e.jsx("h3",{className:"text-2xl font-bold mb-3",children:t.title}),e.jsx("p",{className:`mb-6 ${s?"text-gray-300":"text-gray-600"}`,children:t.description}),e.jsx("div",{className:"space-y-2",children:t.features.map((r,m)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(p,{className:"text-xs flex-shrink-0"}),e.jsx("span",{className:`${s?"text-gray-300":"text-gray-700"}`,children:r})]},m))})]})]},t.id))}),e.jsxs("div",{className:`rounded-xl overflow-hidden relative ${s?"bg-[#1A1F35]":"bg-white"} border ${s?"border-gray-800":"border-gray-200"}`,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10"}),e.jsxs("div",{className:"relative z-10 p-8 md:p-12 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Ready to Test Your Skills?"}),e.jsx("p",{className:`text-lg mb-8 max-w-2xl mx-auto ${s?"text-gray-300":"text-gray-600"}`,children:"Join CyberForce today and gain access to our full range of cybersecurity simulations, challenges, and learning resources."}),e.jsxs(i,{to:"/pricing",className:"px-8 py-3 bg-[#4A5CBA] hover:bg-[#3A4CAA] text-white rounded-lg font-medium inline-flex items-center gap-2 transition-colors",children:["Get Started ",e.jsx(d,{})]})]})]})]})]})};export{N as default};
