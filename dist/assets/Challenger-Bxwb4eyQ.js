import{u as l,a as r,j as e}from"./index-CVvVjHWF.js";import{C as t}from"./ChallengerHub-DvrHIatL.js";const d=()=>{const{darkMode:s}=l(),{challengeId:a}=r();return e.jsx("div",{className:`${s?"text-white":"text-gray-900"}`,children:e.jsxs("div",{className:"px-4 py-4",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Challenger"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Compete in cybersecurity challenges and climb the leaderboards"})]}),e.jsx(t,{challengeId:a})]})})};export{d as default};
