import{v as u,b as y,u as w,r as m,j as e,L as x,ae as v,aY as j,l as N,bD as S,bE as E}from"./index-CVvVjHWF.js";const $=async(a,r)=>{try{if(!a||!r)throw new Error("Email and password are required");if(!k(a))throw new Error("Please enter a valid email address");if(r.length<6)throw new Error("Password must be at least 6 characters long");const{data:s,error:o}=await u.auth.signInWithPassword({email:a.toLowerCase().trim(),password:r});if(o)switch(o.message){case"Invalid login credentials":throw new Error("Invalid email or password. Please check your credentials and try again.");case"Email not confirmed":throw new Error("Please verify your email address before signing in.");case"Too many requests":throw new Error("Too many login attempts. Please try again later.");default:throw new Error(o.message||"Authentication failed")}if(!s.session||!s.user)throw new Error("Authentication failed. Please try again.");const{data:d,error:i}=await u.from("profiles").select("*").eq("id",s.user.id).single();i&&i.code!=="PGRST116"&&console.error("Profile fetch error:",i);const{data:n,error:l}=await u.rpc("get_user_subscription",{p_user_id:s.user.id});return l&&console.error("Subscription fetch error:",l),{success:!0,session:s.session,user:s.user,profile:d||null,subscription:n||{tier:"free",active:!0}}}catch(s){return console.error("Authentication validation error:",s),{success:!1,error:s.message||"Authentication failed"}}},k=a=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a),h=new Map,P=a=>{const r=Date.now(),s=h.get(a)||{count:0,lastAttempt:0};if(r-s.lastAttempt>15*60*1e3&&(s.count=0),s.count>=5){const o=Math.ceil((9e5-(r-s.lastAttempt))/1e3/60);throw new Error(`Too many login attempts. Please try again in ${o} minutes.`)}return s.count++,s.lastAttempt=r,h.set(a,s),!0},A=a=>{h.delete(a)},L=()=>{const a=y(),{darkMode:r}=w(),[s,o]=m.useState(""),[d,i]=m.useState(""),[n,l]=m.useState(!1),[b,g]=m.useState(null),p=async c=>{var f;c.preventDefault(),l(!0),g(null);try{P(s);const t=await $(s,d);if(!t.success){g(t.error);return}A(s),t.session&&(localStorage.setItem("supabase.auth.token",t.session.access_token),localStorage.setItem("supabase.auth.refreshToken",t.session.refresh_token),localStorage.setItem("supabase.auth.user",JSON.stringify(t.user)),localStorage.setItem("supabase.auth.expires_at",(f=t.session.expires_at)==null?void 0:f.toString())),t.subscription&&localStorage.setItem("user_subscription",JSON.stringify(t.subscription)),t.profile&&localStorage.setItem("user_profile",JSON.stringify(t.profile)),a("/enhanced-dashboard")}catch(t){console.error("Login error:",t),g(t.message||"Login failed. Please try again.")}finally{l(!1)}};return e.jsx("div",{className:`min-h-screen ${r?"bg-[#0B1120]":"bg-gray-50"} flex items-center justify-center p-4`,children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx("div",{className:"mb-8",children:e.jsxs(x,{to:"/",className:`${r?"text-gray-400 hover:text-white":"text-gray-600 hover:text-gray-900"} flex items-center gap-2`,children:[e.jsx(v,{}),e.jsx("span",{children:"Back to Home"})]})}),e.jsx("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-xl border overflow-hidden`,children:e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Sign in to your account"})}),b&&e.jsx("div",{className:`${r?"bg-red-500/20 text-red-400":"bg-red-100 text-red-800"} px-4 py-3 rounded mb-6`,children:b}),e.jsxs("form",{onSubmit:p,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"email",className:`block ${r?"text-gray-400":"text-gray-700"} mb-2`,children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(j,{className:"text-gray-500"})}),e.jsx("input",{type:"email",id:"email",className:`${r?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"<EMAIL>",value:s,onChange:c=>o(c.target.value),required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("label",{htmlFor:"password",className:`${r?"text-gray-400":"text-gray-700"}`,children:"Password"}),e.jsx(x,{to:"/forgot-password",className:"text-[#88cc14] hover:underline text-sm",children:"Forgot Password?"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(N,{className:"text-gray-500"})}),e.jsx("input",{type:"password",id:"password",className:`${r?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"••••••••",value:d,onChange:c=>i(c.target.value),required:!0})]})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900 dark:text-gray-300",children:"Remember me"})]})}),e.jsx("button",{type:"submit",className:`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${n?"opacity-70 cursor-not-allowed":""}`,disabled:n,children:n?"Signing In...":"Sign In"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:`${r?"text-gray-400":"text-gray-600"}`,children:["Don't have an account?"," ",e.jsx(x,{to:"/signup",className:"text-[#88cc14] hover:underline",children:"Sign Up"})]})}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:`w-full border-t ${r?"border-gray-800":"border-gray-300"}`})}),e.jsx("div",{className:"relative flex justify-center",children:e.jsx("span",{className:`${r?"bg-[#1A1F35]":"bg-white"} px-4 text-sm ${r?"text-gray-400":"text-gray-600"}`,children:"Or continue with"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 ${r?"bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800":"bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300"} py-3 px-4 rounded-lg border transition-colors`,children:[e.jsx(S,{}),e.jsx("span",{children:"Google"})]}),e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 ${r?"bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800":"bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300"} py-3 px-4 rounded-lg border transition-colors`,children:[e.jsx(E,{}),e.jsx("span",{children:"GitHub"})]})]})]})]})})]})})};export{L as default};
