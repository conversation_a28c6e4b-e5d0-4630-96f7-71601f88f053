import{b as i,u as c,j as s}from"./index-CVvVjHWF.js";const l=t=>{console.log(`Attempting to login with ${t} account...`);const r={free:{email:"<EMAIL>",password:"FreeUser123!",user_metadata:{username:"free_user",full_name:"Free User",subscription_tier:"free",coins:100}},premium:{email:"<EMAIL>",password:"PremiumUser123!",user_metadata:{username:"premium_user",full_name:"Premium User",subscription_tier:"premium",coins:500}},business:{email:"<EMAIL>",password:"BusinessUser123!",user_metadata:{username:"business_user",full_name:"Business User",subscription_tier:"business",coins:1e3}},chitti:{email:"chitti.gouth<PERSON><PERSON>@gmail.com",password:"Chitti123!",user_metadata:{username:"chitti",full_name:"<PERSON><PERSON><PERSON>",subscription_tier:"free",coins:100}},admin:{email:"<EMAIL>",password:"Admin123!",user_metadata:{username:"admin",full_name:"Super Admin",subscription_tier:"business",coins:9999,is_admin:!0,is_super_admin:!0,role:"super_admin"}}};console.log(`Looking for account with tier: ${t}`),console.log(`Available tiers: ${Object.keys(r).join(", ")}`);const e=r[t]||r.free;console.log(`Selected account: ${e.email} (${e.user_metadata.subscription_tier} tier)`);const a={access_token:`mock_token_${t}_${Date.now()}`,refresh_token:`mock_refresh_${t}_${Date.now()}`,expires_at:Date.now()+3600*1e3},o={id:`user_${t}_${Date.now()}`,email:e.email,user_metadata:e.user_metadata,created_at:new Date().toISOString()},n={tier:e.user_metadata.subscription_tier,coins:e.user_metadata.coins,start_date:new Date().toISOString(),end_date:e.user_metadata.subscription_tier==="free"?null:new Date(Date.now()+30*24*60*60*1e3).toISOString()};return localStorage.setItem("supabase.auth.token",JSON.stringify(a)),localStorage.setItem("supabase.auth.user",JSON.stringify(o)),localStorage.setItem("user_subscription",JSON.stringify(n)),console.log(`Logged in as ${e.email} (${t} tier)`),{user:o,session:{access_token:a.access_token}}},m=()=>{const t=i(),{darkMode:r}=c(),e=a=>{try{console.log(`Logging in with ${a} account...`);const o=l(a);console.log("Login result:",o),a==="admin"?(console.log("Redirecting to super admin dashboard..."),t("/super-admin",{replace:!0})):(console.log("Redirecting to premium dashboard..."),t("/enhanced-dashboard",{replace:!0}))}catch(o){console.error("Error during test login:",o),alert(`Login failed: ${o.message||"Unknown error"}`)}};return s.jsx("div",{className:`min-h-screen ${r?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:s.jsx("div",{className:"container mx-auto px-4",children:s.jsxs("div",{className:"max-w-md mx-auto",children:[s.jsx("h1",{className:"text-3xl font-bold mb-6 text-center",children:"Test Login"}),s.jsxs("div",{className:`${r?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[s.jsx("p",{className:"mb-4 text-center",children:"Select a test account to login:"}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("button",{onClick:()=>e("free"),className:"w-full py-3 px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors",children:"Free Tier Account"}),s.jsx("button",{onClick:()=>e("premium"),className:"w-full py-3 px-4 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors",children:"Premium Tier Account"}),s.jsx("button",{onClick:()=>e("business"),className:"w-full py-3 px-4 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors",children:"Business Tier Account"}),s.jsx("button",{onClick:()=>e("chitti"),className:"w-full py-3 px-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors",children:"Chitti's Account"}),s.jsx("button",{onClick:()=>e("admin"),className:"w-full py-3 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors",children:"Super Admin Account"})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsx("p",{className:`text-sm ${r?"text-gray-400":"text-gray-600"}`,children:"These are test accounts for demonstration purposes."})})]})]})})})};export{m as default};
