import{b as g,u as h,r as u,j as e,bQ as b,C as i,W as a,aD as n,aN as r,bR as c,ax as d,bx as j}from"./index-CVvVjHWF.js";const y=()=>{const o=g(),{darkMode:s}=h(),[l,t]=u.useState("overview"),x=()=>{localStorage.removeItem("supabase.auth.token"),localStorage.removeItem("supabase.auth.user"),localStorage.removeItem("user_subscription"),o("/login",{replace:!0})},m=()=>{switch(l){case"users":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"User Management"}),e.jsx("p",{children:"Manage all users, roles, and permissions."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"View all registered users"}),e.jsx("li",{children:"Edit user details and subscription tiers"}),e.jsx("li",{children:"Manage user roles and permissions"}),e.jsx("li",{children:"Ban or suspend accounts"}),e.jsx("li",{children:"Reset passwords"})]})]})]});case"content":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Content Management"}),e.jsx("p",{children:"Manage all learning content, challenges, and posts."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"Create and edit learning modules"}),e.jsx("li",{children:"Manage challenges and their solutions"}),e.jsx("li",{children:"Review and approve user-submitted content"}),e.jsx("li",{children:"Organize content categories"}),e.jsx("li",{children:"Schedule content publication"})]})]})]});case"notifications":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Notification System"}),e.jsx("p",{children:"Manage system notifications and announcements."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"Create system-wide announcements"}),e.jsx("li",{children:"Send targeted notifications to specific user groups"}),e.jsx("li",{children:"Schedule notification delivery"}),e.jsx("li",{children:"View notification delivery statistics"}),e.jsx("li",{children:"Manage notification templates"})]})]})]});case"customization":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Page Customization"}),e.jsx("p",{children:"Customize the appearance and content of various pages."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"Customize the landing page layout"}),e.jsx("li",{children:"Edit featured content sections"}),e.jsx("li",{children:"Manage navigation menus"}),e.jsx("li",{children:"Customize dashboard layouts for different user tiers"}),e.jsx("li",{children:"Edit system-wide text and messaging"})]})]})]});case"business":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Business Setup"}),e.jsx("p",{children:"Manage business settings, pricing, and subscription plans."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"Configure subscription tiers and pricing"}),e.jsx("li",{children:"Manage payment gateway settings"}),e.jsx("li",{children:"View and export financial reports"}),e.jsx("li",{children:"Configure business information"}),e.jsx("li",{children:"Manage tax settings"})]})]})]});case"statistics":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Statistics & Tracking"}),e.jsx("p",{children:"View detailed analytics and user activity tracking."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"View user engagement metrics"}),e.jsx("li",{children:"Track content popularity and completion rates"}),e.jsx("li",{children:"Monitor subscription conversions and churn"}),e.jsx("li",{children:"Analyze user learning patterns"}),e.jsx("li",{children:"Export detailed reports"})]})]})]});case"settings":return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"System Settings"}),e.jsx("p",{children:"Configure system-wide settings and preferences."}),e.jsxs("div",{className:"mt-4 p-4 bg-yellow-100 text-yellow-800 rounded-lg",children:["This section will allow you to:",e.jsxs("ul",{className:"list-disc ml-5 mt-2",children:[e.jsx("li",{children:"Configure email settings"}),e.jsx("li",{children:"Manage API integrations"}),e.jsx("li",{children:"Set up backup and maintenance schedules"}),e.jsx("li",{children:"Configure security settings"}),e.jsx("li",{children:"Manage system logs"})]})]})]});case"overview":default:return e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Super Admin Dashboard"}),e.jsx("p",{className:"mb-6",children:"Welcome to the Super Admin Dashboard. From here, you can manage all aspects of the platform."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-3 rounded-full bg-blue-500 bg-opacity-10",children:e.jsx(a,{className:"text-blue-500"})}),e.jsx("h3",{className:"ml-3 text-lg font-semibold",children:"User Management"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Manage users, roles, and permissions."}),e.jsx("button",{onClick:()=>t("users"),className:"text-blue-500 hover:text-blue-700 font-medium",children:"Manage Users →"})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-3 rounded-full bg-green-500 bg-opacity-10",children:e.jsx(n,{className:"text-green-500"})}),e.jsx("h3",{className:"ml-3 text-lg font-semibold",children:"Content Management"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Manage learning content and challenges."}),e.jsx("button",{onClick:()=>t("content"),className:"text-green-500 hover:text-green-700 font-medium",children:"Manage Content →"})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-3 rounded-full bg-purple-500 bg-opacity-10",children:e.jsx(r,{className:"text-purple-500"})}),e.jsx("h3",{className:"ml-3 text-lg font-semibold",children:"Notifications"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Manage system notifications and announcements."}),e.jsx("button",{onClick:()=>t("notifications"),className:"text-purple-500 hover:text-purple-700 font-medium",children:"Manage Notifications →"})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-3 rounded-full bg-yellow-500 bg-opacity-10",children:e.jsx(c,{className:"text-yellow-500"})}),e.jsx("h3",{className:"ml-3 text-lg font-semibold",children:"Page Customization"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Customize the appearance and content of pages."}),e.jsx("button",{onClick:()=>t("customization"),className:"text-yellow-500 hover:text-yellow-700 font-medium",children:"Customize Pages →"})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-3 rounded-full bg-red-500 bg-opacity-10",children:e.jsx(d,{className:"text-red-500"})}),e.jsx("h3",{className:"ml-3 text-lg font-semibold",children:"Business Setup"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Manage business settings and subscription plans."}),e.jsx("button",{onClick:()=>t("business"),className:"text-red-500 hover:text-red-700 font-medium",children:"Configure Business →"})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-lg border p-6`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-3 rounded-full bg-indigo-500 bg-opacity-10",children:e.jsx(i,{className:"text-indigo-500"})}),e.jsx("h3",{className:"ml-3 text-lg font-semibold",children:"Statistics & Tracking"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"View analytics and user activity tracking."}),e.jsx("button",{onClick:()=>t("statistics"),className:"text-indigo-500 hover:text-indigo-700 font-medium",children:"View Statistics →"})]})]})]})}};return e.jsxs("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"}`,children:[e.jsx("header",{className:`fixed top-0 left-0 right-0 z-10 ${s?"bg-[#0B1120] border-gray-800":"bg-white border-gray-200"} border-b`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex items-center justify-between h-16",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("h1",{className:"text-xl font-bold",children:"Super Admin Dashboard"})}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("button",{onClick:x,className:`flex items-center px-3 py-2 rounded-md ${s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(b,{className:"mr-2"}),"Logout"]})})]})})}),e.jsxs("div",{className:"flex pt-16",children:[e.jsx("aside",{className:`fixed left-0 top-16 bottom-0 w-64 ${s?"bg-[#0B1120] border-gray-800":"bg-white border-gray-200"} border-r overflow-y-auto`,children:e.jsx("nav",{className:"p-4",children:e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("overview"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="overview"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(i,{className:"mr-2"})," Overview"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("users"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="users"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(a,{className:"mr-2"})," User Management"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("content"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="content"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(n,{className:"mr-2"})," Content Management"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("notifications"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="notifications"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(r,{className:"mr-2"})," Notifications"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("customization"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="customization"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(c,{className:"mr-2"})," Page Customization"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("business"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="business"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(d,{className:"mr-2"})," Business Setup"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("statistics"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="statistics"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(i,{className:"mr-2"})," Statistics & Tracking"]})}),e.jsx("li",{children:e.jsxs("button",{onClick:()=>t("settings"),className:`w-full text-left px-4 py-2 rounded-md flex items-center ${l==="settings"?"bg-[#88cc14] text-black":s?"hover:bg-gray-800":"hover:bg-gray-100"}`,children:[e.jsx(j,{className:"mr-2"})," System Settings"]})})]})})}),e.jsx("main",{className:"flex-1 ml-64 p-6",children:m()})]})]})};export{y as default};
