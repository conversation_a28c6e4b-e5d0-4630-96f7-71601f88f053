import{w as j,bw as p,r as s,j as e,bx as N,by as v,C as w,G as F,m as c,bz as C,U as $,b4 as S,bA as q}from"./index-CVvVjHWF.js";const A=()=>{var o,m;const{user:P}=j(),{preferences:r,updatePreference:t,generateStudySchedule:x}=p(),[d,l]=s.useState("content-format"),[b,g]=s.useState([]),[n,y]=s.useState(((o=r.studyTimePreference)==null?void 0:o.weekday)||60),[i,h]=s.useState(((m=r.studyTimePreference)==null?void 0:m.weekend)||120);s.useEffect(()=>{const a=x();g(a)},[r.studyTimePreference]);const f=()=>{t("studyTimePreference",{weekday:n,weekend:i})};return e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden",children:[e.jsxs("div",{className:"border-b border-gray-200 dark:border-gray-700 px-6 py-4",children:[e.jsxs("h2",{className:"text-xl font-bold text-gray-800 dark:text-white flex items-center",children:[e.jsx(N,{className:"mr-2 text-blue-500"}),"Learning Preferences"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"Customize your learning experience"})]}),e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsxs("nav",{className:"flex overflow-x-auto",children:[e.jsxs("button",{onClick:()=>l("content-format"),className:`py-3 px-6 text-sm font-medium flex items-center whitespace-nowrap ${d==="content-format"?"border-b-2 border-blue-500 text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"}`,children:[e.jsx(v,{className:"mr-2"}),"Content Format"]}),e.jsxs("button",{onClick:()=>l("difficulty"),className:`py-3 px-6 text-sm font-medium flex items-center whitespace-nowrap ${d==="difficulty"?"border-b-2 border-blue-500 text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"}`,children:[e.jsx(w,{className:"mr-2"}),"Difficulty Level"]}),e.jsxs("button",{onClick:()=>l("study-schedule"),className:`py-3 px-6 text-sm font-medium flex items-center whitespace-nowrap ${d==="study-schedule"?"border-b-2 border-blue-500 text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"}`,children:[e.jsx(F,{className:"mr-2"}),"Study Schedule"]})]})}),e.jsxs("div",{className:"p-6",children:[d==="content-format"&&e.jsxs(c.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-4",children:"Preferred Content Format"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("button",{onClick:()=>t("contentFormat","visual"),className:`p-4 rounded-lg border flex items-start transition-colors ${r.contentFormat==="visual"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-4 text-blue-600 dark:text-blue-400",children:e.jsx(C,{})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:`font-medium mb-1 ${r.contentFormat==="visual"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Visual Learner"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Prioritize videos, diagrams, and visual demonstrations"})]})]}),e.jsxs("button",{onClick:()=>t("contentFormat","text"),className:`p-4 rounded-lg border flex items-start transition-colors ${r.contentFormat==="text"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center mr-4 text-purple-600 dark:text-purple-400",children:e.jsx($,{})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:`font-medium mb-1 ${r.contentFormat==="text"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Text Learner"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Prefer reading detailed explanations and documentation"})]})]}),e.jsxs("button",{onClick:()=>t("contentFormat","interactive"),className:`p-4 rounded-lg border flex items-start transition-colors ${r.contentFormat==="interactive"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-4 text-green-600 dark:text-green-400",children:e.jsx(S,{})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:`font-medium mb-1 ${r.contentFormat==="interactive"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Hands-on Learner"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Focus on interactive exercises, labs, and practical challenges"})]})]}),e.jsxs("button",{onClick:()=>t("contentFormat","balanced"),className:`p-4 rounded-lg border flex items-start transition-colors ${r.contentFormat==="balanced"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-100 dark:bg-orange-900/50 flex items-center justify-center mr-4 text-orange-600 dark:text-orange-400",children:e.jsx(q,{})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:`font-medium mb-1 ${r.contentFormat==="balanced"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Balanced Approach"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Mix of all learning styles (default)"})]})]})]}),e.jsxs("div",{className:"mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("h4",{className:"font-medium text-gray-800 dark:text-white mb-2",children:"What this means"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Based on your selection, we'll prioritize content in your preferred format when available. Multiple formats will still be accessible, but your preferred format will be shown first."})]})]}),d==="difficulty"&&e.jsxs(c.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-4",children:"Content Difficulty"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:`p-4 rounded-lg border ${r.difficulty==="easier"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.difficulty==="easier",onChange:()=>t("difficulty","easier"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.difficulty==="easier"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.difficulty==="easier"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${r.difficulty==="easier"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Easier"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"More guidance, simplified explanations, and additional help resources"})]})]})}),e.jsx("div",{className:`p-4 rounded-lg border ${r.difficulty==="standard"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.difficulty==="standard",onChange:()=>t("difficulty","standard"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.difficulty==="standard"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.difficulty==="standard"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${r.difficulty==="standard"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Standard"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"Balanced difficulty suitable for most learners"})]})]})}),e.jsx("div",{className:`p-4 rounded-lg border ${r.difficulty==="challenging"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.difficulty==="challenging",onChange:()=>t("difficulty","challenging"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.difficulty==="challenging"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.difficulty==="challenging"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${r.difficulty==="challenging"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Challenging"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"More complex scenarios, fewer hints, and advanced content"})]})]})}),e.jsx("div",{className:`p-4 rounded-lg border ${r.difficulty==="adaptive"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.difficulty==="adaptive",onChange:()=>t("difficulty","adaptive"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.difficulty==="adaptive"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.difficulty==="adaptive"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${r.difficulty==="adaptive"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Adaptive (Recommended)"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"Automatically adjusts difficulty based on your performance"})]})]})})]}),e.jsxs("div",{className:"mt-6 flex items-center",children:[e.jsx("input",{type:"checkbox",id:"showAdvancedContent",checked:r.showAdvancedContent,onChange:a=>t("showAdvancedContent",a.target.checked),className:"w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"}),e.jsx("label",{htmlFor:"showAdvancedContent",className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Show advanced content and optional challenges"})]})]}),d==="study-schedule"&&e.jsxs(c.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-4",children:"Study Schedule"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Minutes per weekday"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"range",min:"15",max:"240",step:"15",value:n,onChange:a=>y(parseInt(a.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"}),e.jsxs("span",{className:"ml-3 w-16 text-sm font-medium text-gray-700 dark:text-gray-300",children:[n," min"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Minutes per weekend day"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"range",min:"30",max:"360",step:"30",value:i,onChange:a=>h(parseInt(a.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"}),e.jsxs("span",{className:"ml-3 w-16 text-sm font-medium text-gray-700 dark:text-gray-300",children:[i," min"]})]})]})]}),e.jsx("button",{onClick:f,className:"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Update Study Time"}),e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"font-medium text-gray-800 dark:text-white mb-3",children:"Recommended Schedule"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:e.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:b.map(a=>e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"font-medium text-gray-800 dark:text-white",children:a.day}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:[a.recommendedMinutes," minutes"]})]}),a.sessions.length>1?e.jsxs("div",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300",children:["Recommended: ",a.sessions.map((k,u)=>e.jsxs("span",{children:[u>0&&" + ",k.duration," min session"]},u))]}):e.jsxs("div",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300",children:["Recommended: One ",a.sessions[0].duration," min session"]})]},a.day))})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"font-medium text-gray-800 dark:text-white mb-3",children:"Reminder Preferences"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:`p-3 rounded-lg border ${r.reminderFrequency==="daily"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.reminderFrequency==="daily",onChange:()=>t("reminderFrequency","daily"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.reminderFrequency==="daily"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.reminderFrequency==="daily"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsx("span",{className:`${r.reminderFrequency==="daily"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Daily Reminders"})]})}),e.jsx("div",{className:`p-3 rounded-lg border ${r.reminderFrequency==="weekly"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.reminderFrequency==="weekly",onChange:()=>t("reminderFrequency","weekly"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.reminderFrequency==="weekly"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.reminderFrequency==="weekly"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsx("span",{className:`${r.reminderFrequency==="weekly"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"Weekly Reminders"})]})}),e.jsx("div",{className:`p-3 rounded-lg border ${r.reminderFrequency==="none"?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 dark:border-blue-400":"border-gray-200 dark:border-gray-700"}`,children:e.jsxs("label",{className:"flex items-center cursor-pointer",children:[e.jsx("input",{type:"radio",checked:r.reminderFrequency==="none",onChange:()=>t("reminderFrequency","none"),className:"sr-only"}),e.jsx("div",{className:`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${r.reminderFrequency==="none"?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"}`,children:r.reminderFrequency==="none"&&e.jsx("div",{className:"w-3 h-3 rounded-full bg-white"})}),e.jsx("span",{className:`${r.reminderFrequency==="none"?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-white"}`,children:"No Reminders"})]})})]})]})]})]})]})]})};export{A as default};
