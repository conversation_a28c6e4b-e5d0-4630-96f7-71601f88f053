import{w as K,r as i,v as p,j as e,br as Q,l as U,a2 as Z,bC as ee,ai as te,ak as se,at as re,bd as ne,bA as ae}from"./index-CVvVjHWF.js";const oe=({labId:u,onComplete:h})=>{const{user:m}=K(),f=i.useRef(null),[I,b]=i.useState(!0),[y,N]=i.useState(!1),[n,M]=i.useState(null),[a,k]=i.useState(null),[_,w]=i.useState([]),[v,E]=i.useState(""),[x,z]=i.useState([]),[c,C]=i.useState({}),[S,$]=i.useState(!1),[d,T]=i.useState(null),[L,R]=i.useState(null);i.useEffect(()=>(A(),()=>{a&&j()}),[u]),i.useEffect(()=>{if(a&&n){const t=setInterval(Y,1e4),r=setInterval(G,1e3);return()=>{clearInterval(t),clearInterval(r)}}},[a,n]),i.useEffect(()=>{x.length>0&&x.every(r=>c[r.id])&&!S&&($(!0),m&&B())},[c,x]),i.useEffect(()=>{f.current&&(f.current.scrollTop=f.current.scrollHeight)},[_]);const A=async()=>{try{b(!0);const{data:t,error:r}=await p.from("virtual_labs").select(`
          id,
          title,
          description,
          difficulty,
          estimated_time,
          environment_type,
          environment_config,
          objectives,
          hints,
          is_premium
        `).eq("id",u).single();if(r)throw r;if(t.is_premium){const{data:l}=await p.from("profiles").select("subscription_tier").eq("id",m.id).single();if(!l||l.subscription_tier==="free"){R("This lab requires a premium subscription"),b(!1);return}}if(M(t),z(t.objectives||[]),m){const{data:l}=await p.from("user_lab_progress").select("completed_objectives, is_completed").eq("user_id",m.id).eq("lab_id",u).single();l&&(C(l.completed_objectives||{}),l.is_completed&&$(!0))}b(!1)}catch(t){console.error("Error fetching lab data:",t),R("Failed to load lab data"),b(!1)}},D=async()=>{try{N(!0),s("Initializing virtual environment..."),await new Promise(r=>setTimeout(r,3e3));const t=n.environment_config;s("> Starting environment..."),s("> Configuring network interfaces..."),s("> Setting up virtual services..."),s("> Environment ready!"),s(""),s("Welcome to CyberForce Virtual Lab"),s(`Lab: ${n.title}`),s('Type "help" for available commands'),s(""),k({id:`lab-${u}-${Date.now()}`,startTime:new Date,expiresAt:new Date(Date.now()+(t.time_limit||3600)*1e3),ip:"********",ports:t.exposed_ports||[80,22,443]}),T((t.time_limit||3600)*1e3),N(!1)}catch(t){console.error("Error initializing environment:",t),s("> ERROR: Failed to initialize environment"),N(!1)}},j=async()=>{try{s("> Stopping environment..."),await new Promise(t=>setTimeout(t,1e3)),s("> Environment stopped"),k(null)}catch(t){console.error("Error stopping environment:",t),s("> ERROR: Failed to stop environment")}},O=async()=>{try{s("> Resetting environment..."),await j(),await D()}catch(t){console.error("Error resetting environment:",t),s("> ERROR: Failed to reset environment")}},s=t=>{w(r=>[...r,t])},q=t=>{t.preventDefault(),v.trim()&&(s(`$ ${v}`),V(v),E(""))},V=t=>{var l,P;const r=t.trim().toLowerCase();switch(r){case"help":s("Available commands:"),s("  help       - Show this help message"),s("  objectives - List lab objectives"),s("  reset      - Reset the environment"),s("  ip         - Show environment IP address"),s("  ports      - List exposed ports"),s("  time       - Show remaining time"),s("  clear      - Clear terminal"),(l=n==null?void 0:n.environment_config)!=null&&l.commands&&(s(""),s("Lab-specific commands:"),n.environment_config.commands.forEach(o=>{s(`  ${o.name.padEnd(10)} - ${o.description}`)}));break;case"objectives":s("Lab Objectives:"),x.forEach((o,g)=>{const X=c[o.id]?"[COMPLETED]":"[PENDING]";s(`${g+1}. ${o.description} ${X}`)});break;case"reset":O();break;case"ip":s(a?`Environment IP: ${a.ip}`:"No active environment");break;case"ports":a&&a.ports?(s("Exposed ports:"),a.ports.forEach(o=>{s(`  ${o}/tcp - open`)})):s("No active environment or no ports exposed");break;case"time":if(d){const o=Math.floor(d/6e4),g=Math.floor(d%6e4/1e3);s(`Remaining time: ${o}m ${g}s`)}else s("No active session");break;case"clear":w([]);break;default:if((P=n==null?void 0:n.environment_config)!=null&&P.commands){const o=n.environment_config.commands.find(g=>g.name===r);if(o){s(o.response||"Command executed successfully"),o.completes_objective&&F(o.completes_objective);return}}s(`Command not found: ${r}`),s('Type "help" for available commands')}},Y=()=>{if(Math.random()<.1){const t=x.filter(r=>!c[r.id]);if(t.length>0){const r=t[Math.floor(Math.random()*t.length)];F(r.id)}}},F=t=>{const r=x.find(l=>l.id===t);r&&!c[t]&&(s(""),s("🎉 OBJECTIVE COMPLETED! 🎉"),s(`Completed: ${r.description}`),s(""),C(l=>({...l,[t]:!0})),H(t))},H=async t=>{if(m)try{const r={...c,[t]:!0};await p.from("user_lab_progress").upsert({user_id:m.id,lab_id:u,completed_objectives:r,updated_at:new Date},{onConflict:"user_id, lab_id"})}catch(r){console.error("Error saving objective progress:",r)}},B=async()=>{try{await p.from("user_lab_progress").upsert({user_id:m.id,lab_id:u,is_completed:!0,completed_at:new Date,updated_at:new Date},{onConflict:"user_id, lab_id"}),h&&h()}catch(t){console.error("Error saving learning progress:",t)}},G=()=>{a&&d&&(d<=0?(j(),s(""),s("⚠️ SESSION EXPIRED ⚠️"),s("Your lab session has ended due to time limit"),s("You can start a new session to continue"),s("")):T(t=>t-1e3))},J=()=>{if(!d)return"00:00";const t=Math.floor(d/6e4),r=Math.floor(d%6e4/1e3);return`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`},W=()=>{console.log("Downloading instructions for lab:",n==null?void 0:n.title)};return I?e.jsx(Q,{text:"Loading lab environment..."}):L?e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 text-center bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:[e.jsx(U,{className:"text-red-500 text-4xl mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-white mb-2",children:L}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Please upgrade your subscription to access premium labs."}),e.jsx("button",{onClick:()=>window.location.href="/pricing",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors",children:"View subscription options"})]}):e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden",children:[e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-bold text-gray-800 dark:text-white flex items-center",children:[e.jsx(Z,{className:"mr-2 text-blue-500"}),n==null?void 0:n.title]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:n==null?void 0:n.description})]}),e.jsx("div",{className:"mt-4 md:mt-0 flex items-center space-x-3",children:a?e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 text-xs px-2 py-1 rounded-full flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-1"}),"Active"]}),e.jsx("span",{className:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full font-mono",children:J()}),e.jsx("button",{onClick:j,className:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Stop environment",children:e.jsx(ee,{})}),e.jsx("button",{onClick:O,className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",title:"Reset environment",children:e.jsx(te,{})})]}):e.jsx("button",{onClick:D,disabled:y,className:`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${y?"opacity-70 cursor-not-allowed":""}`,children:y?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"animate-spin mr-2",children:"⟳"}),"Starting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(se,{className:"mr-2"}),"Start Lab"]})})})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-0",children:[e.jsx("div",{className:"lg:col-span-2 border-r border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"h-[500px] flex flex-col",children:[e.jsxs("div",{className:"bg-gray-800 text-white p-2 flex justify-between items-center text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"mr-2"}),"Terminal"]}),e.jsx("button",{onClick:()=>w([]),className:"text-gray-400 hover:text-white",title:"Clear terminal",children:"Clear"})]}),e.jsx("div",{ref:f,className:"flex-1 bg-black text-green-400 p-3 font-mono text-sm overflow-y-auto",children:_.map((t,r)=>e.jsx("div",{className:"mb-1 whitespace-pre-wrap",children:t},r))}),e.jsx("form",{onSubmit:q,className:"bg-gray-900 p-2",children:e.jsxs("div",{className:"flex items-center text-green-400 font-mono text-sm",children:[e.jsx("span",{className:"mr-2",children:"$"}),e.jsx("input",{type:"text",value:v,onChange:t=>E(t.target.value),className:"flex-1 bg-transparent outline-none",placeholder:a?"Type a command... (try 'help')":"Start the environment to use terminal",disabled:!a})]})})]})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-3",children:"Lab Information"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Difficulty:"}),e.jsx("span",{className:"font-medium text-gray-800 dark:text-white capitalize",children:n==null?void 0:n.difficulty})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Estimated time:"}),e.jsxs("span",{className:"font-medium text-gray-800 dark:text-white",children:[n==null?void 0:n.estimated_time," minutes"]})]}),a&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Environment IP:"}),e.jsx("span",{className:"font-medium text-gray-800 dark:text-white font-mono",children:a.ip})]})]}),e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsxs("button",{onClick:W,className:"text-sm flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(ne,{className:"mr-1"}),"Download instructions"]}),e.jsxs("button",{onClick:()=>{a&&navigator.clipboard.writeText(a.ip)},disabled:!a,className:`text-sm flex items-center ${a?"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300":"text-gray-400 dark:text-gray-600 cursor-not-allowed"}`,children:[e.jsx(ae,{className:"mr-1"}),"Copy IP"]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-3",children:"Lab Objectives"}),e.jsx("div",{className:"space-y-3",children:x.map((t,r)=>e.jsx("div",{className:`p-3 rounded-lg border ${c[t.id]?"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800":"bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"}`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 ${c[t.id]?"bg-green-500 text-white":"bg-gray-200 dark:bg-gray-700"}`,children:c[t.id]?e.jsx(FaCheckCircle,{className:"text-xs"}):e.jsx("span",{className:"text-xs",children:r+1})}),e.jsxs("div",{children:[e.jsx("p",{className:`text-sm ${c[t.id]?"text-green-800 dark:text-green-300 font-medium":"text-gray-800 dark:text-white"}`,children:t.description}),t.hint&&!c[t.id]&&e.jsx("button",{className:"text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mt-1",onClick:()=>{s(""),s(`HINT: ${t.hint}`),s("")},children:"Show hint"})]})]})},t.id))}),S&&e.jsxs("div",{className:"mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-center",children:[e.jsx("h4",{className:"text-green-800 dark:text-green-300 font-semibold mb-2",children:"Congratulations! 🎉"}),e.jsx("p",{className:"text-green-700 dark:text-green-400 text-sm mb-3",children:"You've completed all the objectives for this lab!"}),e.jsx("button",{onClick:()=>h&&h(),className:"bg-green-600 hover:bg-green-700 text-white text-sm px-4 py-2 rounded-lg",children:"Continue to next module"})]})]})]})]})]})};export{oe as default};
