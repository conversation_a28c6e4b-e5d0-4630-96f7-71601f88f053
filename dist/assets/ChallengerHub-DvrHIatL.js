import{D as ce,j as e,m as oe,l as te,b0 as ee,L as pe,aR as ge,u as ae,r as o,a0 as he,b1 as re,b2 as de,a4 as X,E as le,J as Z,c as G,at as me,v as C,a3 as xe,b3 as ie,b as ue,a as fe,ae as ye,C as be,$ as je,G as ve,W as Ne,H as we}from"./index-CVvVjHWF.js";const ne=({requiredLevel:g=ee.BASIC,feature:u,children:p,showUpgrade:N=!0})=>{const{subscriptionLevel:M,loading:f}=ce(),_={[ee.NONE]:0,[ee.BASIC]:1,[ee.PREMIUM]:2,[ee.BUSINESS]:3},b=_[M]>=_[g];return f?e.jsx("div",{className:"p-6 flex justify-center items-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#88cc14]"})}):b?p:e.jsxs(oe.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-black/5 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/10 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(te,{className:"text-[#88cc14] text-2xl"})}),e.jsxs("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:[g," Subscription Required"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:u?`Access to ${u} requires a ${g} subscription or higher.`:`This content requires a ${g} subscription or higher.`}),N&&e.jsxs(pe,{to:"/pricing",className:"inline-flex items-center gap-2 bg-[#88cc14] text-black px-6 py-2 rounded-lg font-bold hover:bg-[#7ab811] transition-colors",children:[e.jsx(ge,{}),e.jsx("span",{children:"Upgrade Now"})]})]})},Se=({onComplete:g,challengeId:u})=>{const{darkMode:p}=ae(),[N,M]=o.useState("http://vulnerable-webapp.example.com"),[f,_]=o.useState(""),[b,V]=o.useState(null),[S,A]=o.useState(0),[I,w]=o.useState(!1),[B]=o.useState(new Date),[x,j]=o.useState(0),[P,$]=o.useState([{id:1,description:"Identify vulnerabilities in the web application",completed:!1},{id:2,description:"Exploit the vulnerabilities to gain access",completed:!1},{id:3,description:"Extract the secret flag",completed:!1}]),[T,z]=o.useState([{type:"system",text:"Web Exploitation Challenge initialized..."},{type:"system",text:"Target: Vulnerable Web Application"},{type:"system",text:'Type "help" for available commands.'}]),[D,W]=o.useState(null),[H,Y]=o.useState(""),[L,J]=o.useState(!1),F=o.useRef(null);o.useEffect(()=>(F.current=setInterval(()=>{const c=Math.floor((new Date-B)/1e3);j(c)},1e3),()=>{F.current&&clearInterval(F.current)}),[B]),o.useEffect(()=>{(async()=>{if(u)try{const{data:c,error:n}=await C.from("challenger_challenges").select("*").eq("id",u).single();if(n)throw n;c&&(W(c),c.objectives&&$(c.objectives.map(t=>({...t,completed:!1}))))}catch(c){console.error("Error fetching challenge data:",c)}})()},[u]);const s=(a,c)=>{z(n=>[...n,{type:a,text:c}]),setTimeout(()=>{const n=document.getElementById("web-exploit-console");n&&(n.scrollTop=n.scrollHeight)},100)},q=a=>{a.preventDefault(),f.trim()&&(s("command",`$ ${f}`),m(f),_(""))},m=a=>{var n,t;const c=a.toLowerCase().trim();if(A(r=>r+1),c==="help")s("system","Available commands:"),s("system","  help - Show this help message"),s("system","  scan <url> - Scan a URL for vulnerabilities"),s("system","  exploit <vulnerability> - Exploit a specific vulnerability"),s("system","  extract - Extract data from the compromised system"),s("system","  submit <flag> - Submit a flag"),s("system","  clear - Clear the console");else if(c.startsWith("scan")){const r=c.split(" ")[1]||N;s("system",`Scanning ${r} for vulnerabilities...`),setTimeout(()=>{s("success","Scan complete. Vulnerabilities found:"),s("info","1. SQL Injection vulnerability in login form"),s("info","2. Cross-Site Scripting (XSS) vulnerability in search function"),s("info","3. Insecure Direct Object Reference (IDOR) in user profile"),$(h=>h.map(l=>l.id===1?{...l,completed:!0}:l))},2e3)}else if(c.startsWith("exploit")){const r=c.split(" ")[1];if(!r){s("error","Please specify a vulnerability to exploit."),s("system","Usage: exploit <vulnerability>"),s("system","Example: exploit sql");return}if(!((n=P.find(h=>h.id===1))!=null&&n.completed)){s("error","You need to scan for vulnerabilities first.");return}r==="sql"||r==="sqli"||r==="sql_injection"?(s("system","Exploiting SQL Injection vulnerability..."),setTimeout(()=>{s("success","SQL Injection successful!"),s("info","Dumping database contents..."),s("data","admin:5f4dcc3b5aa765d61d8327deb882cf99"),s("data","user1:e10adc3949ba59abbe56e057f20f883e"),s("data","user2:d8578edf8458ce06fbc5bb76a58c5ca4"),$(h=>h.map(l=>l.id===2?{...l,completed:!0}:l))},2e3)):r==="xss"||r==="cross_site_scripting"?(s("system","Exploiting Cross-Site Scripting vulnerability..."),setTimeout(()=>{s("success","XSS exploitation successful!"),s("info","Captured user session cookies."),s("data","session=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."),$(h=>h.map(l=>l.id===2?{...l,completed:!0}:l))},2e3)):r==="idor"||r==="insecure_direct_object_reference"?(s("system","Exploiting Insecure Direct Object Reference vulnerability..."),setTimeout(()=>{s("success","IDOR exploitation successful!"),s("info","Accessed admin profile."),s("data","Admin Name: Administrator"),s("data","Admin Email: <EMAIL>"),$(h=>h.map(l=>l.id===2?{...l,completed:!0}:l))},2e3)):(s("error",`Unknown vulnerability: ${r}`),s("system","Available vulnerabilities: sql, xss, idor"))}else if(c==="extract"){if(!((t=P.find(r=>r.id===2))!=null&&t.completed)){s("error","You need to exploit a vulnerability first.");return}s("system","Extracting sensitive data from the system..."),setTimeout(()=>{s("success","Data extraction successful!"),s("info","Found secret flag in database:"),s("flag","flag{w3b_3xpl01t_m4st3r_2023}"),$(r=>r.map(h=>h.id===3?{...h,completed:!0}:h)),J(!0)},2e3)}else if(c.startsWith("submit")){const r=a.split(" ").slice(1).join(" ");if(!r){s("error","Please specify a flag to submit."),s("system","Usage: submit <flag>");return}r==="flag{w3b_3xpl01t_m4st3r_2023}"?(s("success","Flag is correct! Challenge completed!"),clearInterval(F.current),w(!0),V({type:"success",text:"Challenge completed successfully!"}),g&&g({success:!0,flag:r,attempts:S,timeSpent:x,approachScore:E(),totalScore:U(x)})):s("error","Incorrect flag. Try again.")}else c==="clear"?z([{type:"system",text:"Console cleared."},{type:"system",text:'Type "help" for available commands.'}]):(s("error",`Unknown command: ${a}`),s("system",'Type "help" for available commands.'))},K=a=>{a.preventDefault(),m(`submit ${H}`),Y("")},E=()=>{const a=P.filter(r=>r.completed).length,c=P.length,n=a/c*50,t=Math.max(0,50-(S>10?(S-10)*2:0));return Math.round(n+t)},U=a=>{var r;const c=E(),n=((r=D==null?void 0:D.completion_criteria)==null?void 0:r.time_limit)||3600,t=Math.max(0,50-Math.floor(a/n*50));return c+t},k=a=>{const c=Math.floor(a/60),n=a%60;return`${c.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`};return e.jsxs("div",{className:`${p?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${p?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(he,{className:"text-blue-500 mr-2"}),"Web Exploitation Challenge"]}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs("div",{className:`px-3 py-1 rounded-lg ${p?"bg-gray-800":"bg-gray-100"} flex items-center`,children:[e.jsx(re,{className:"mr-1 text-blue-500"}),e.jsx("span",{className:"text-sm",children:k(x)})]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:`p-6 rounded-lg ${p?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(de,{className:"mr-2 text-red-500"}),"Objectives"]}),b&&e.jsx("div",{className:`p-3 rounded-lg mb-4 ${b.type==="success"?"bg-green-100 text-green-800 border-green-200":b.type==="warning"?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-red-100 text-red-800 border-red-200"} border`,children:e.jsxs("div",{className:"flex items-center",children:[b.type==="success"?e.jsx(X,{className:"mr-2"}):b.type==="warning"?e.jsx(le,{className:"mr-2"}):e.jsx(Z,{className:"mr-2"}),e.jsx("span",{children:b.text})]})}),e.jsx("ul",{className:"space-y-4",children:P.map(a=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${a.completed?"bg-green-500 text-white":p?"bg-gray-700 text-gray-400":"bg-gray-200 text-gray-500"}`,children:a.completed?e.jsx(X,{className:"text-xs"}):e.jsx("span",{className:"text-xs",children:a.id})}),e.jsx("span",{className:a.completed?"line-through opacity-70":"",children:a.description})]},a.id))}),L&&!I&&e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"font-bold mb-2",children:"Submit Flag:"}),e.jsxs("form",{onSubmit:K,className:"flex",children:[e.jsx("input",{type:"text",value:H,onChange:a=>Y(a.target.value),placeholder:"flag{...}",className:`flex-grow p-2 rounded-l-lg ${p?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`}),e.jsx("button",{type:"submit",className:"px-4 py-2 rounded-r-lg bg-blue-600 hover:bg-blue-700 text-white",children:"Submit"})]})]}),I&&e.jsxs("div",{className:"mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(G,{className:"mr-2 text-yellow-500"}),e.jsx("span",{className:"font-bold",children:"Challenge Completed!"})]}),e.jsx("p",{children:"You successfully completed the Web Exploitation Challenge."}),e.jsxs("div",{className:"mt-2 grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Time:"}),e.jsx("span",{className:"ml-2",children:k(x)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Attempts:"}),e.jsx("span",{className:"ml-2",children:S})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Approach Score:"}),e.jsxs("span",{className:"ml-2",children:[E(),"/100"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Total Score:"}),e.jsxs("span",{className:"ml-2",children:[U(x),"/100"]})]})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:`p-6 rounded-lg ${p?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(me,{className:"mr-2 text-green-500"}),"Terminal"]}),e.jsx("div",{id:"web-exploit-console",className:`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${p?"bg-gray-900 text-gray-300":"bg-gray-800 text-gray-200"}`,children:T.map((a,c)=>e.jsx("div",{className:"mb-1",children:e.jsx("span",{className:a.type==="system"?"text-blue-400":a.type==="command"?"text-green-400":a.type==="error"?"text-red-400":a.type==="success"?"text-green-400":a.type==="info"?"text-yellow-400":a.type==="data"?"text-purple-400":a.type==="flag"?"text-red-400 font-bold":"text-gray-400",children:a.text})},c))}),e.jsxs("form",{onSubmit:q,className:"mt-4 flex",children:[e.jsx("div",{className:`px-3 py-2 ${p?"bg-gray-900 text-green-400":"bg-gray-800 text-green-400"} rounded-l-lg`,children:"$"}),e.jsx("input",{type:"text",value:f,onChange:a=>_(a.target.value),className:`flex-grow p-2 ${p?"bg-gray-900 border-gray-700 text-gray-300":"bg-gray-800 border-gray-700 text-gray-200"} border-y border-r rounded-r-lg font-mono`,placeholder:"Type a command...",disabled:I})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:`font-bold mb-2 ${p?"text-gray-300":"text-gray-700"}`,children:"Challenge Description:"}),e.jsx("p",{className:`text-sm ${p?"text-gray-400":"text-gray-600"}`,children:'Your task is to exploit vulnerabilities in a web application to gain unauthorized access and extract the secret flag. Use the terminal to interact with the target system. Type "help" to see available commands.'})]})]})})]})})]})},Ce=({onComplete:g,challengeId:u})=>{const{darkMode:p}=ae(),[N,M]=o.useState(""),[f,_]=o.useState(null),[b,V]=o.useState(0),[S,A]=o.useState(!1),[I]=o.useState(new Date),[w,B]=o.useState(0),[x,j]=o.useState([{id:1,description:"Perform network reconnaissance",completed:!1},{id:2,description:"Identify vulnerable services",completed:!1},{id:3,description:"Exploit vulnerabilities to gain access",completed:!1},{id:4,description:"Escalate privileges",completed:!1},{id:5,description:"Extract the secret flag",completed:!1}]),[P,$]=o.useState([{type:"system",text:"Network Penetration Challenge initialized..."},{type:"system",text:"Target Network: ***********/24"},{type:"system",text:'Type "help" for available commands.'}]),[T,z]=o.useState(null),[D,W]=o.useState(""),[H,Y]=o.useState(!1),[L,J]=o.useState(null),F=o.useRef(null);o.useEffect(()=>(F.current=setInterval(()=>{const c=Math.floor((new Date-I)/1e3);B(c)},1e3),()=>{F.current&&clearInterval(F.current)}),[I]),o.useEffect(()=>{(async()=>{if(u)try{const{data:c,error:n}=await C.from("challenger_challenges").select("*").eq("id",u).single();if(n)throw n;c&&(z(c),c.objectives&&j(c.objectives.map(t=>({...t,completed:!1}))))}catch(c){console.error("Error fetching challenge data:",c)}})()},[u]);const s=(a,c)=>{$(n=>[...n,{type:a,text:c}]),setTimeout(()=>{const n=document.getElementById("network-pentest-console");n&&(n.scrollTop=n.scrollHeight)},100)},q=a=>{a.preventDefault(),N.trim()&&(s("command",`$ ${N}`),m(N),M(""))},m=a=>{var n,t,r,h;const c=a.toLowerCase().trim();if(V(l=>l+1),c==="help")s("system","Available commands:"),s("system","  help - Show this help message"),s("system","  scan <target> - Scan a target for open ports and services"),s("system","  enum <target> <service> - Enumerate a specific service on a target"),s("system","  exploit <target> <service> - Exploit a vulnerable service"),s("system","  privesc - Attempt to escalate privileges"),s("system","  extract - Extract data from the compromised system"),s("system","  submit <flag> - Submit a flag"),s("system","  clear - Clear the console");else if(c.startsWith("scan")){const l=c.split(" ")[1]||"***********/24";s("system",`Scanning ${l} for open ports and services...`),setTimeout(()=>{l==="***********/24"||l==="network"?(s("success","Network scan complete. Hosts discovered:"),s("info","*********** - Router/Gateway"),s("info","************ - Web Server"),s("info","************ - Database Server"),s("info","************ - File Server"),J({hosts:[{ip:"***********",type:"router",services:["ssh:22","http:80"]},{ip:"************",type:"webserver",services:["http:80","https:443","ssh:22"]},{ip:"************",type:"database",services:["mysql:3306","ssh:22"]},{ip:"************",type:"fileserver",services:["ftp:21","smb:445","ssh:22"]}]})):l==="***********"?(s("success","Host scan complete for ***********:"),s("info","Port 22: SSH - OpenSSH 7.9"),s("info","Port 80: HTTP - Router Admin Interface")):l==="************"?(s("success","Host scan complete for ************:"),s("info","Port 22: SSH - OpenSSH 7.4"),s("info","Port 80: HTTP - Apache 2.4.6"),s("info","Port 443: HTTPS - Apache 2.4.6")):l==="************"?(s("success","Host scan complete for ************:"),s("info","Port 22: SSH - OpenSSH 7.4"),s("info","Port 3306: MySQL 5.7.30")):l==="************"?(s("success","Host scan complete for ************:"),s("info","Port 21: FTP - vsftpd 3.0.2"),s("info","Port 22: SSH - OpenSSH 7.4"),s("info","Port 445: SMB - Samba 4.9.1")):s("error",`No hosts found at ${l}`),j(d=>d.map(i=>i.id===1?{...i,completed:!0}:i))},2e3)}else if(c.startsWith("enum")){const l=c.split(" "),d=l[1],i=l[2];if(!d||!i){s("error","Please specify a target and service to enumerate."),s("system","Usage: enum <target> <service>"),s("system","Example: enum ************ ftp");return}if(!((n=x.find(y=>y.id===1))!=null&&n.completed)){s("error","You need to scan the network first.");return}s("system",`Enumerating ${i} service on ${d}...`),setTimeout(()=>{if(d==="************"&&(i==="http"||i==="web"||i==="80"))s("success","Web server enumeration complete:"),s("info","Web application: Custom CMS"),s("info","Potential vulnerabilities:"),s("info","- SQL Injection in login form"),s("info","- Outdated Apache version with known CVEs");else if(d==="************"&&(i==="mysql"||i==="db"||i==="3306"))s("success","Database enumeration complete:"),s("info","MySQL 5.7.30 with weak configuration"),s("info","Anonymous access enabled"),s("info","Default credentials may be in use");else if(d==="************"&&(i==="ftp"||i==="21"))s("success","FTP enumeration complete:"),s("info","Anonymous FTP access enabled"),s("info","Writable directories found"),s("info","Outdated vsftpd 3.0.2 with known vulnerabilities");else if(d==="************"&&(i==="smb"||i==="445"))s("success","SMB enumeration complete:"),s("info","Samba 4.9.1 with misconfigured shares"),s("info","Readable shares without authentication"),s("info","Potential for SMB relay attacks");else{s("error",`Could not enumerate ${i} on ${d}`);return}j(y=>y.map(v=>v.id===2?{...v,completed:!0}:v))},2e3)}else if(c.startsWith("exploit")){const l=c.split(" "),d=l[1],i=l[2];if(!d||!i){s("error","Please specify a target and service to exploit."),s("system","Usage: exploit <target> <service>"),s("system","Example: exploit ************ ftp");return}if(!((t=x.find(y=>y.id===2))!=null&&t.completed)){s("error","You need to enumerate services first.");return}s("system",`Exploiting ${i} service on ${d}...`),setTimeout(()=>{if(d==="************"&&(i==="http"||i==="web"||i==="80"))s("success","SQL Injection successful!"),s("info","Dumped user credentials from database"),s("data","admin:5f4dcc3b5aa765d61d8327deb882cf99"),s("data","user1:e10adc3949ba59abbe56e057f20f883e"),s("info","Gained access to web admin panel");else if(d==="************"&&(i==="mysql"||i==="db"||i==="3306"))s("success","MySQL exploitation successful!"),s("info","Connected to database with default credentials"),s("data","Connected to: information_schema"),s("data","Available databases: information_schema, mysql, performance_schema, sys, webapp_db");else if(d==="************"&&(i==="ftp"||i==="21"))s("success","FTP exploitation successful!"),s("info","Logged in with anonymous access"),s("data","Directory listing:"),s("data","- backup/"),s("data","- config/"),s("data","- logs/");else if(d==="************"&&(i==="smb"||i==="445"))s("success","SMB exploitation successful!"),s("info","Accessed shares without authentication"),s("data","Available shares:"),s("data","- public"),s("data","- backups"),s("data","- admin$");else{s("error",`Could not exploit ${i} on ${d}`);return}j(y=>y.map(v=>v.id===3?{...v,completed:!0}:v))},2e3)}else if(c==="privesc"){if(!((r=x.find(l=>l.id===3))!=null&&r.completed)){s("error","You need to exploit a service first.");return}s("system","Attempting privilege escalation..."),setTimeout(()=>{s("success","Privilege escalation successful!"),s("info","Exploited kernel vulnerability CVE-2021-4034"),s("info","Gained root access to the system"),s("data","uid=0(root) gid=0(root) groups=0(root)"),j(l=>l.map(d=>d.id===4?{...d,completed:!0}:d))},2e3)}else if(c==="extract"){if(!((h=x.find(l=>l.id===4))!=null&&h.completed)){s("error","You need to escalate privileges first.");return}s("system","Extracting sensitive data from the system..."),setTimeout(()=>{s("success","Data extraction successful!"),s("info","Found secret flag in /root/flag.txt:"),s("flag","flag{n3tw0rk_p3n3tr4t10n_m4st3r_2023}"),j(l=>l.map(d=>d.id===5?{...d,completed:!0}:d)),Y(!0)},2e3)}else if(c.startsWith("submit")){const l=a.split(" ").slice(1).join(" ");if(!l){s("error","Please specify a flag to submit."),s("system","Usage: submit <flag>");return}l==="flag{n3tw0rk_p3n3tr4t10n_m4st3r_2023}"?(s("success","Flag is correct! Challenge completed!"),clearInterval(F.current),A(!0),_({type:"success",text:"Challenge completed successfully!"}),g&&g({success:!0,flag:l,attempts:b,timeSpent:w,approachScore:E(),totalScore:U(w)})):s("error","Incorrect flag. Try again.")}else c==="clear"?$([{type:"system",text:"Console cleared."},{type:"system",text:'Type "help" for available commands.'}]):(s("error",`Unknown command: ${a}`),s("system",'Type "help" for available commands.'))},K=a=>{a.preventDefault(),m(`submit ${D}`),W("")},E=()=>{const a=x.filter(r=>r.completed).length,c=x.length,n=a/c*50,t=Math.max(0,50-(b>15?(b-15)*2:0));return Math.round(n+t)},U=a=>{var r;const c=E(),n=((r=T==null?void 0:T.completion_criteria)==null?void 0:r.time_limit)||7200,t=Math.max(0,50-Math.floor(a/n*50));return c+t},k=a=>{const c=Math.floor(a/60),n=a%60;return`${c.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`};return e.jsxs("div",{className:`${p?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${p?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(xe,{className:"text-blue-500 mr-2"}),"Network Penetration Challenge"]}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs("div",{className:`px-3 py-1 rounded-lg ${p?"bg-gray-800":"bg-gray-100"} flex items-center`,children:[e.jsx(re,{className:"mr-1 text-blue-500"}),e.jsx("span",{className:"text-sm",children:k(w)})]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:`p-6 rounded-lg ${p?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(de,{className:"mr-2 text-red-500"}),"Objectives"]}),f&&e.jsx("div",{className:`p-3 rounded-lg mb-4 ${f.type==="success"?"bg-green-100 text-green-800 border-green-200":f.type==="warning"?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-red-100 text-red-800 border-red-200"} border`,children:e.jsxs("div",{className:"flex items-center",children:[f.type==="success"?e.jsx(X,{className:"mr-2"}):f.type==="warning"?e.jsx(le,{className:"mr-2"}):e.jsx(Z,{className:"mr-2"}),e.jsx("span",{children:f.text})]})}),e.jsx("ul",{className:"space-y-4",children:x.map(a=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${a.completed?"bg-green-500 text-white":p?"bg-gray-700 text-gray-400":"bg-gray-200 text-gray-500"}`,children:a.completed?e.jsx(X,{className:"text-xs"}):e.jsx("span",{className:"text-xs",children:a.id})}),e.jsx("span",{className:a.completed?"line-through opacity-70":"",children:a.description})]},a.id))}),H&&!S&&e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"font-bold mb-2",children:"Submit Flag:"}),e.jsxs("form",{onSubmit:K,className:"flex",children:[e.jsx("input",{type:"text",value:D,onChange:a=>W(a.target.value),placeholder:"flag{...}",className:`flex-grow p-2 rounded-l-lg ${p?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`}),e.jsx("button",{type:"submit",className:"px-4 py-2 rounded-r-lg bg-blue-600 hover:bg-blue-700 text-white",children:"Submit"})]})]}),S&&e.jsxs("div",{className:"mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(G,{className:"mr-2 text-yellow-500"}),e.jsx("span",{className:"font-bold",children:"Challenge Completed!"})]}),e.jsx("p",{children:"You successfully completed the Network Penetration Challenge."}),e.jsxs("div",{className:"mt-2 grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Time:"}),e.jsx("span",{className:"ml-2",children:k(w)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Attempts:"}),e.jsx("span",{className:"ml-2",children:b})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Approach Score:"}),e.jsxs("span",{className:"ml-2",children:[E(),"/100"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Total Score:"}),e.jsxs("span",{className:"ml-2",children:[U(w),"/100"]})]})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:`p-6 rounded-lg ${p?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(me,{className:"mr-2 text-green-500"}),"Terminal"]}),e.jsx("div",{id:"network-pentest-console",className:`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${p?"bg-gray-900 text-gray-300":"bg-gray-800 text-gray-200"}`,children:P.map((a,c)=>e.jsx("div",{className:"mb-1",children:e.jsx("span",{className:a.type==="system"?"text-blue-400":a.type==="command"?"text-green-400":a.type==="error"?"text-red-400":a.type==="success"?"text-green-400":a.type==="info"?"text-yellow-400":a.type==="data"?"text-purple-400":a.type==="flag"?"text-red-400 font-bold":"text-gray-400",children:a.text})},c))}),e.jsxs("form",{onSubmit:q,className:"mt-4 flex",children:[e.jsx("div",{className:`px-3 py-2 ${p?"bg-gray-900 text-green-400":"bg-gray-800 text-green-400"} rounded-l-lg`,children:"$"}),e.jsx("input",{type:"text",value:N,onChange:a=>M(a.target.value),className:`flex-grow p-2 ${p?"bg-gray-900 border-gray-700 text-gray-300":"bg-gray-800 border-gray-700 text-gray-200"} border-y border-r rounded-r-lg font-mono`,placeholder:"Type a command...",disabled:S})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:`font-bold mb-2 ${p?"text-gray-300":"text-gray-700"}`,children:"Challenge Description:"}),e.jsx("p",{className:`text-sm ${p?"text-gray-400":"text-gray-600"}`,children:'Your task is to perform a penetration test on a simulated network. Identify vulnerabilities, exploit them to gain access, escalate privileges, and extract the secret flag. Use the terminal to interact with the target network. Type "help" to see available commands.'})]})]})})]})})]})},_e=({onComplete:g,challengeId:u})=>{const{darkMode:p}=ae(),[N,M]=o.useState(""),[f,_]=o.useState(null),[b,V]=o.useState(0),[S,A]=o.useState(!1),[I]=o.useState(new Date),[w,B]=o.useState(0),[x,j]=o.useState([{id:1,description:"Analyze the password hash",completed:!1},{id:2,description:"Identify the hash type",completed:!1},{id:3,description:"Crack the password hash",completed:!1},{id:4,description:"Submit the cracked password",completed:!1}]),[P,$]=o.useState([{type:"system",text:"Password Cracking Challenge initialized..."},{type:"system",text:"Target: Password Hash"},{type:"system",text:'Type "help" for available commands.'}]),[T,z]=o.useState(null),[D,W]=o.useState(""),[H,Y]=o.useState(!1),[L,J]=o.useState("5f4dcc3b5aa765d61d8327deb882cf99"),[F,s]=o.useState("MD5"),q=o.useRef(null);o.useEffect(()=>(q.current=setInterval(()=>{const t=Math.floor((new Date-I)/1e3);B(t)},1e3),()=>{q.current&&clearInterval(q.current)}),[I]),o.useEffect(()=>{(async()=>{if(u)try{const{data:t,error:r}=await C.from("challenger_challenges").select("*").eq("id",u).single();if(r)throw r;t&&(z(t),t.objectives&&j(t.objectives.map(h=>({...h,completed:!1}))))}catch(t){console.error("Error fetching challenge data:",t)}})()},[u]);const m=(n,t)=>{$(r=>[...r,{type:n,text:t}]),setTimeout(()=>{const r=document.getElementById("password-cracking-console");r&&(r.scrollTop=r.scrollHeight)},100)},K=n=>{n.preventDefault(),N.trim()&&(m("command",`$ ${N}`),E(N),M(""))},E=n=>{var r,h;const t=n.toLowerCase().trim();if(V(l=>l+1),t==="help")m("system","Available commands:"),m("system","  help - Show this help message"),m("system","  analyze <hash> - Analyze a password hash"),m("system","  identify <hash> - Identify the hash type"),m("system","  crack <hash> - Attempt to crack the password hash"),m("system","  submit <password> - Submit the cracked password"),m("system","  clear - Clear the console");else if(t.startsWith("analyze")){const l=t.split(" ")[1]||L;m("system",`Analyzing hash: ${l}`),setTimeout(()=>{m("success","Hash analysis complete:"),m("info",`Hash: ${l}`),m("info",`Length: ${l.length} characters`),m("info",`Character set: ${/^[0-9a-f]+$/.test(l)?"Hexadecimal (0-9, a-f)":"Mixed"}`),j(d=>d.map(i=>i.id===1?{...i,completed:!0}:i))},1500)}else if(t.startsWith("identify")){const l=t.split(" ")[1]||L;if(!((r=x.find(d=>d.id===1))!=null&&r.completed)){m("error","You need to analyze the hash first.");return}m("system",`Identifying hash type for: ${l}`),setTimeout(()=>{l.length===32?(m("success","Hash type identified:"),m("info","Hash type: MD5"),m("info","Confidence: High"),j(d=>d.map(i=>i.id===2?{...i,completed:!0}:i))):l.length===40?(m("success","Hash type identified:"),m("info","Hash type: SHA-1"),m("info","Confidence: High"),j(d=>d.map(i=>i.id===2?{...i,completed:!0}:i))):l.length===64?(m("success","Hash type identified:"),m("info","Hash type: SHA-256"),m("info","Confidence: High"),j(d=>d.map(i=>i.id===2?{...i,completed:!0}:i))):(m("error","Could not identify hash type with confidence."),m("info","Try analyzing the hash first or provide a valid hash."))},2e3)}else if(t.startsWith("crack")){const l=t.split(" ")[1]||L;if(!((h=x.find(y=>y.id===2))!=null&&h.completed)){m("error","You need to identify the hash type first.");return}m("system",`Attempting to crack hash: ${l}`),m("system","Using dictionary attack with common passwords...");let d=0;const i=setInterval(()=>{d+=10,d<=100&&m("info",`Progress: ${d}%`),d>=100&&(clearInterval(i),m("success","Password cracked successfully!"),m("info","The password is: password"),j(y=>y.map(v=>v.id===3?{...v,completed:!0}:v)),Y(!0))},1e3)}else if(t.startsWith("submit")){const l=n.split(" ").slice(1).join(" ");if(!l){m("error","Please specify a password to submit."),m("system","Usage: submit <password>");return}l.toLowerCase()==="password"?(m("success","Correct password! Challenge completed!"),clearInterval(q.current),A(!0),j(d=>d.map(i=>i.id===4?{...i,completed:!0}:i)),_({type:"success",text:"Challenge completed successfully!"}),g&&g({success:!0,password:l,attempts:b,timeSpent:w,approachScore:k(),totalScore:a(w)})):m("error","Incorrect password. Try again.")}else t==="clear"?$([{type:"system",text:"Console cleared."},{type:"system",text:'Type "help" for available commands.'}]):(m("error",`Unknown command: ${n}`),m("system",'Type "help" for available commands.'))},U=n=>{n.preventDefault(),E(`submit ${D}`),W("")},k=()=>{const n=x.filter(l=>l.completed).length,t=x.length,r=n/t*50,h=Math.max(0,50-(b>10?(b-10)*2:0));return Math.round(r+h)},a=n=>{var l;const t=k(),r=((l=T==null?void 0:T.completion_criteria)==null?void 0:l.time_limit)||3600,h=Math.max(0,50-Math.floor(n/r*50));return t+h},c=n=>{const t=Math.floor(n/60),r=n%60;return`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`};return e.jsxs("div",{className:`${p?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${p?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(ie,{className:"text-yellow-500 mr-2"}),"Password Cracking Challenge"]}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs("div",{className:`px-3 py-1 rounded-lg ${p?"bg-gray-800":"bg-gray-100"} flex items-center`,children:[e.jsx(re,{className:"mr-1 text-blue-500"}),e.jsx("span",{className:"text-sm",children:c(w)})]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:`p-6 rounded-lg ${p?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(te,{className:"mr-2 text-red-500"}),"Objectives"]}),f&&e.jsx("div",{className:`p-3 rounded-lg mb-4 ${f.type==="success"?"bg-green-100 text-green-800 border-green-200":f.type==="warning"?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-red-100 text-red-800 border-red-200"} border`,children:e.jsxs("div",{className:"flex items-center",children:[f.type==="success"?e.jsx(X,{className:"mr-2"}):f.type==="warning"?e.jsx(le,{className:"mr-2"}):e.jsx(Z,{className:"mr-2"}),e.jsx("span",{children:f.text})]})}),e.jsx("ul",{className:"space-y-4",children:x.map(n=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("div",{className:`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${n.completed?"bg-green-500 text-white":p?"bg-gray-700 text-gray-400":"bg-gray-200 text-gray-500"}`,children:n.completed?e.jsx(X,{className:"text-xs"}):e.jsx("span",{className:"text-xs",children:n.id})}),e.jsx("span",{className:n.completed?"line-through opacity-70":"",children:n.description})]},n.id))}),H&&!S&&e.jsxs("div",{className:"mt-6",children:[e.jsx("h4",{className:"font-bold mb-2",children:"Submit Password:"}),e.jsxs("form",{onSubmit:U,className:"flex",children:[e.jsx("input",{type:"text",value:D,onChange:n=>W(n.target.value),placeholder:"Enter password",className:`flex-grow p-2 rounded-l-lg ${p?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`}),e.jsx("button",{type:"submit",className:"px-4 py-2 rounded-r-lg bg-blue-600 hover:bg-blue-700 text-white",children:"Submit"})]})]}),S&&e.jsxs("div",{className:"mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(G,{className:"mr-2 text-yellow-500"}),e.jsx("span",{className:"font-bold",children:"Challenge Completed!"})]}),e.jsx("p",{children:"You successfully completed the Password Cracking Challenge."}),e.jsxs("div",{className:"mt-2 grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Time:"}),e.jsx("span",{className:"ml-2",children:c(w)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Attempts:"}),e.jsx("span",{className:"ml-2",children:b})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Approach Score:"}),e.jsxs("span",{className:"ml-2",children:[k(),"/100"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold",children:"Total Score:"}),e.jsxs("span",{className:"ml-2",children:[a(w),"/100"]})]})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:`p-6 rounded-lg ${p?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border`,children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx(ie,{className:"mr-2 text-yellow-500"}),"Password Cracker"]}),e.jsx("div",{id:"password-cracking-console",className:`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${p?"bg-gray-900 text-gray-300":"bg-gray-800 text-gray-200"}`,children:P.map((n,t)=>e.jsx("div",{className:"mb-1",children:e.jsx("span",{className:n.type==="system"?"text-blue-400":n.type==="command"?"text-green-400":n.type==="error"?"text-red-400":n.type==="success"?"text-green-400":n.type==="info"?"text-yellow-400":"text-gray-400",children:n.text})},t))}),e.jsxs("form",{onSubmit:K,className:"mt-4 flex",children:[e.jsx("div",{className:`px-3 py-2 ${p?"bg-gray-900 text-green-400":"bg-gray-800 text-green-400"} rounded-l-lg`,children:"$"}),e.jsx("input",{type:"text",value:N,onChange:n=>M(n.target.value),className:`flex-grow p-2 ${p?"bg-gray-900 border-gray-700 text-gray-300":"bg-gray-800 border-gray-700 text-gray-200"} border-y border-r rounded-r-lg font-mono`,placeholder:"Type a command...",disabled:S})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:`font-bold mb-2 ${p?"text-gray-300":"text-gray-700"}`,children:"Challenge Description:"}),e.jsx("p",{className:`text-sm ${p?"text-gray-400":"text-gray-600"}`,children:'Your task is to crack a password hash using password cracking techniques. Analyze the hash, identify its type, and use appropriate methods to recover the original password. Type "help" to see available commands.'}),e.jsx("div",{className:"mt-2 p-3 rounded-lg bg-blue-500/10 border border-blue-500/30",children:e.jsxs("p",{className:"text-sm flex items-start",children:[e.jsx(Z,{className:"text-blue-500 mr-2 mt-0.5 flex-shrink-0"}),e.jsxs("span",{children:["Target Hash: ",e.jsx("span",{className:"font-mono",children:L})]})]})})]})]})})]})})]})},Ee=()=>{const{darkMode:g}=ae(),{subscriptionLevel:u}=ce(),p=ue(),{challengeId:N}=fe(),[M,f]=o.useState([]),[_,b]=o.useState([]),[V,S]=o.useState(!0),[A,I]=o.useState("all"),[w,B]=o.useState(""),[x,j]=o.useState(null),[P,$]=o.useState([]),[T,z]=o.useState("all"),[D,W]=o.useState([]),[H,Y]=o.useState("all"),[L,J]=o.useState([]),[F,s]=o.useState(!1),[q,m]=o.useState(null),[K,E]=o.useState(!1);o.useEffect(()=>{(async()=>{try{const{data:{user:r}}=await C.auth.getUser();m(r)}catch(r){console.error("Error getting current user:",r)}finally{E(!0)}})()},[]),o.useEffect(()=>{(async()=>{try{S(!0);const{data:r,error:h}=await C.from("practice_simulation_categories").select("*").order("display_order");if(h)throw h;$(r||[]);const{data:l,error:d}=await C.from("practice_simulation_difficulty_levels").select("*").order("display_order");if(d)throw d;W(l||[]);const{data:i,error:y}=await C.from("challenger_challenges").select(`
            *,
            category:category_id(id, name),
            difficulty:difficulty_id(id, name)
          `).eq("is_active",!0).order("created_at");if(y)throw y;const v=(i==null?void 0:i.length)>0?i:Te;f(v);const{data:R}=await C.auth.getUser();if(R&&R.id){const{data:Q,error:O}=await C.from("challenger_challenge_attempts").select("*").eq("user_id",R.id);if(O)throw O;b(Q||[])}if(N){const Q=v.find(O=>O.id===N||O.slug===N);if(Q){j(Q);const{data:O,error:se}=await C.from("leaderboards").select(`
                *,
                user:user_id(id, email, user_metadata)
              `).eq("challenge_id",Q.id).order("completion_time",{ascending:!0}).limit(10);if(se)throw se;J(O||[])}}S(!1)}catch(r){console.error("Error fetching challenges:",r),S(!1)}})()},[N]);const U=M.filter(t=>{const r=t.title.toLowerCase().includes(w.toLowerCase())||t.description.toLowerCase().includes(w.toLowerCase()),h=T==="all"||t.category&&t.category.id===T,l=H==="all"||t.difficulty&&t.difficulty.id===H,d=A==="all"||A==="completed"&&_.some(i=>i.challenge_id===t.id&&i.is_completed)||A==="in-progress"&&_.some(i=>i.challenge_id===t.id&&!i.is_completed)||A==="not-started"&&!_.some(i=>i.challenge_id===t.id);return r&&h&&l&&d}),k=async t=>{if(console.log("Challenge completed:",t),x&&t.success)try{const{data:r}=await C.auth.getUser();if(r&&r.id){const{data:h,error:l}=await C.from("challenger_challenge_attempts").upsert({user_id:r.id,challenge_id:x.id,is_completed:!0,completion_time:t.timeSpent,approach_score:t.approachScore||80,total_score:t.totalScore||t.approachScore||80,completed_at:new Date().toISOString()}).select();if(l)throw l;const{error:d}=await C.from("leaderboards").upsert({challenge_id:x.id,user_id:r.id,completion_time:t.timeSpent,approach_score:t.approachScore||80,total_score:t.totalScore||t.approachScore||80});if(d)throw d;const{data:i}=await C.from("leaderboards").select(`
              *,
              user:user_id(id, email, user_metadata)
            `).eq("challenge_id",x.id).order("completion_time",{ascending:!0}).limit(10);J(i||[]),s(!0);const{data:y}=await C.from("challenger_challenge_attempts").select("*").eq("user_id",r.id);b(y||[])}}catch(r){console.error("Error saving challenge completion:",r)}},a=t=>{switch(t.challenge_type){case"web_exploit":return e.jsx(Se,{onComplete:k,challengeId:t.id});case"network_penetration":return e.jsx(Ce,{onComplete:k,challengeId:t.id});case"password_cracking":return e.jsx(_e,{onComplete:k,challengeId:t.id});default:return e.jsxs("div",{className:`p-6 rounded-lg ${g?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Challenge Not Available"}),e.jsx("p",{className:"mb-6",children:"This challenge type is not yet implemented."})]})}},c=(x==null?void 0:x.is_premium)&&u==="free",n=(x==null?void 0:x.is_business)&&u!=="business";return x?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("button",{onClick:()=>{j(null),s(!1),p("/dashboard/challenger")},className:`mb-4 flex items-center ${g?"text-gray-300 hover:text-white":"text-gray-700 hover:text-gray-900"}`,children:[e.jsx(ye,{className:"mr-2"})," Back to Challenges"]}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-6",children:[e.jsxs("div",{className:"lg:w-3/4",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:x.title}),c?e.jsx(ne,{feature:"premium challenges",message:"This challenge requires a premium subscription."}):n?e.jsx(ne,{feature:"business challenges",message:"This challenge requires a business subscription.",requiredTier:"business"}):a(x)]}),e.jsx("div",{className:"lg:w-1/4",children:e.jsxs("div",{className:`rounded-lg border ${g?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"}`,children:[e.jsx("div",{className:`p-4 border-b ${g?"border-gray-800":"border-gray-200"}`,children:e.jsxs("h3",{className:"text-lg font-bold flex items-center",children:[e.jsx(G,{className:"text-yellow-500 mr-2"}),"Leaderboard"]})}),e.jsx("div",{className:"p-4",children:L.length===0?e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:`${g?"text-gray-400":"text-gray-600"}`,children:"No entries yet. Be the first to complete this challenge!"})}):e.jsxs("div",{children:[e.jsxs("div",{className:`grid grid-cols-12 gap-2 py-2 font-bold ${g?"text-gray-300":"text-gray-700"}`,children:[e.jsx("div",{className:"col-span-1",children:"#"}),e.jsx("div",{className:"col-span-5",children:"User"}),e.jsx("div",{className:"col-span-3",children:"Time"}),e.jsx("div",{className:"col-span-3",children:"Score"})]}),L.map((t,r)=>{var h,l,d,i,y,v,R;return e.jsxs("div",{className:`grid grid-cols-12 gap-2 py-2 ${r%2===0?g?"bg-gray-800":"bg-gray-50":""} ${((h=t.user)==null?void 0:h.id)===((i=(d=(l=C.auth.getUser())==null?void 0:l.data)==null?void 0:d.user)==null?void 0:i.id)?g?"bg-blue-900":"bg-blue-50":""}`,children:[e.jsx("div",{className:"col-span-1",children:r===0?e.jsx(G,{className:"text-yellow-500"}):r===1?e.jsx(G,{className:"text-gray-400"}):r===2?e.jsx(G,{className:"text-yellow-700"}):r+1}),e.jsx("div",{className:"col-span-5 truncate",children:((v=(y=t.user)==null?void 0:y.user_metadata)==null?void 0:v.full_name)||((R=t.user)==null?void 0:R.email)||"Anonymous"}),e.jsxs("div",{className:"col-span-3 flex items-center",children:[e.jsx(re,{className:"mr-1 text-xs"}),$e(t.completion_time)]}),e.jsxs("div",{className:"col-span-3 flex items-center",children:[e.jsx(be,{className:"mr-1 text-xs"}),t.total_score]})]},t.id)})]})})]})})]})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Challenger"}),e.jsx("p",{className:`mb-6 ${g?"text-gray-400":"text-gray-600"}`,children:"Compete in cybersecurity challenges and climb the leaderboards"}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(je,{className:`${g?"text-gray-400":"text-gray-500"}`})}),e.jsx("input",{type:"text",placeholder:"Search challenges...",value:w,onChange:t=>B(t.target.value),className:`pl-10 pr-4 py-2 w-full rounded-lg ${g?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("select",{value:T,onChange:t=>z(t.target.value),className:`px-4 py-2 rounded-lg ${g?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,children:[e.jsx("option",{value:"all",children:"All Categories"}),P.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),e.jsxs("select",{value:H,onChange:t=>Y(t.target.value),className:`px-4 py-2 rounded-lg ${g?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,children:[e.jsx("option",{value:"all",children:"All Difficulties"}),D.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),e.jsxs("select",{value:A,onChange:t=>I(t.target.value),className:`px-4 py-2 rounded-lg ${g?"bg-gray-800 border-gray-700 text-white":"bg-white border-gray-300 text-gray-900"} border`,children:[e.jsx("option",{value:"all",children:"All Challenges"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"in-progress",children:"In Progress"}),e.jsx("option",{value:"not-started",children:"Not Started"})]})]})]}),V?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):U.length===0?e.jsxs("div",{className:`text-center py-12 ${g?"text-gray-400":"text-gray-600"}`,children:[e.jsx(Z,{className:"mx-auto text-4xl mb-4"}),e.jsx("p",{className:"text-xl",children:"No challenges found matching your filters."}),e.jsx("p",{className:"mt-2",children:"Try adjusting your search criteria or filters."})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:U.map(t=>{var y,v,R,Q,O;const r=_.find(se=>se.challenge_id===t.id),h=r==null?void 0:r.is_completed,l=r&&!h,d=t.is_premium,i=t.is_business;return e.jsxs(oe.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:`rounded-lg overflow-hidden border ${g?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"}`,children:[e.jsxs("div",{className:`p-4 border-b ${g?"border-gray-800":"border-gray-200"}`,children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("h3",{className:"text-lg font-bold",children:t.title}),e.jsxs("div",{className:"flex items-center",children:[d&&e.jsx("span",{className:"ml-2 px-2 py-0.5 text-xs rounded bg-yellow-500 text-white",children:"Premium"}),i&&e.jsx("span",{className:"ml-2 px-2 py-0.5 text-xs rounded bg-purple-500 text-white",children:"Business"})]})]}),e.jsxs("div",{className:"flex items-center mt-2 text-sm",children:[e.jsx("span",{className:`px-2 py-0.5 rounded ${((y=t.difficulty)==null?void 0:y.name)==="Beginner"?"bg-green-100 text-green-800":((v=t.difficulty)==null?void 0:v.name)==="Intermediate"?"bg-blue-100 text-blue-800":((R=t.difficulty)==null?void 0:R.name)==="Advanced"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:((Q=t.difficulty)==null?void 0:Q.name)||"Unknown"}),e.jsx("span",{className:`ml-2 px-2 py-0.5 rounded ${g?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-800"}`,children:((O=t.category)==null?void 0:O.name)||"Uncategorized"}),t.estimated_time&&e.jsxs("span",{className:`ml-2 flex items-center ${g?"text-gray-400":"text-gray-600"}`,children:[e.jsx(ve,{className:"mr-1"})," ",t.estimated_time," min"]})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("p",{className:`mb-4 ${g?"text-gray-400":"text-gray-600"}`,children:t.description}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(G,{className:"text-yellow-500 mr-1"}),e.jsxs("span",{className:"font-bold",children:[t.points," pts"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ne,{className:"text-blue-500 mr-1"}),e.jsxs("span",{children:[Math.floor(Math.random()*100)+10," participants"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(we,{className:"text-red-500 mr-1"}),e.jsxs("span",{children:[Math.floor(Math.random()*5)+1,"/5"]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[h&&e.jsxs("span",{className:"flex items-center text-green-500",children:[e.jsx(X,{className:"mr-1"})," Completed"]}),l&&e.jsxs("span",{className:"flex items-center text-blue-500",children:[e.jsx(Z,{className:"mr-1"})," In Progress"]})]}),e.jsx("button",{onClick:()=>{j(t),p(`/dashboard/challenger/${t.slug||t.id}`)},className:`px-4 py-2 rounded-lg ${d&&u==="free"?`${g?"bg-gray-700":"bg-gray-300"} cursor-not-allowed`:i&&u!=="business"?`${g?"bg-gray-700":"bg-gray-300"} cursor-not-allowed`:`${g?"bg-blue-600":"bg-blue-500"} text-white hover:bg-blue-700`}`,disabled:d&&u==="free"||i&&u!=="business",children:d&&u==="free"?e.jsxs("span",{className:"flex items-center",children:[e.jsx(te,{className:"mr-1"})," Premium"]}):i&&u!=="business"?e.jsxs("span",{className:"flex items-center",children:[e.jsx(te,{className:"mr-1"})," Business"]}):h?"Try Again":l?"Continue":"Start Challenge"})]})]})]},t.id)})})]})},$e=g=>{if(!g)return"00:00";const u=Math.floor(g/60),p=g%60;return`${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}`},Te=[{id:"web-exploit-challenge",slug:"web-exploit-challenge",title:"Web Application Exploitation",description:"Exploit vulnerabilities in a web application to gain unauthorized access.",challenge_type:"web_exploit",category:{id:"web-security",name:"Web Security"},difficulty:{id:"intermediate",name:"Intermediate"},points:250,estimated_time:45,is_premium:!1,is_business:!1,objectives:[{id:1,description:"Identify vulnerabilities in the web application"},{id:2,description:"Exploit the vulnerabilities to gain access"},{id:3,description:"Extract the secret flag"}],completion_criteria:{flag_format:"flag{...}",time_limit:3600}},{id:"password-cracking-challenge",slug:"password-cracking-challenge",title:"Password Cracking Challenge",description:"Crack password hashes using various techniques to demonstrate the importance of strong password security.",challenge_type:"password_cracking",category:{id:"cryptography",name:"Cryptography"},difficulty:{id:"beginner",name:"Beginner"},points:150,estimated_time:30,is_premium:!1,is_business:!1,objectives:[{id:1,description:"Analyze the password hash"},{id:2,description:"Identify the hash type"},{id:3,description:"Crack the password hash"},{id:4,description:"Submit the cracked password"}],completion_criteria:{time_limit:1800}},{id:"network-penetration-challenge",slug:"network-penetration-challenge",title:"Network Penetration Testing",description:"Perform a penetration test on a simulated network to identify and exploit vulnerabilities.",challenge_type:"network_penetration",category:{id:"network-security",name:"Network Security"},difficulty:{id:"advanced",name:"Advanced"},points:500,estimated_time:60,is_premium:!0,is_business:!1,objectives:[{id:1,description:"Perform network reconnaissance"},{id:2,description:"Identify vulnerable services"},{id:3,description:"Exploit vulnerabilities to gain access"},{id:4,description:"Escalate privileges"},{id:5,description:"Extract the secret flag"}],completion_criteria:{flag_format:"flag{...}",time_limit:7200}}];export{Ee as C};
