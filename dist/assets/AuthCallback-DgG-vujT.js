import{bG as o,b as i,w as d,r as c,j as e}from"./index-CVvVjHWF.js";const h="https://your-project.supabase.co",u="your-anon-key-here",x=o(h,u,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),p=()=>{const s=i(),{setUser:a}=d(),[r,l]=c.useState(null);return c.useEffect(()=>{(async()=>{try{const{data:{session:t},error:n}=await x.auth.getSession();if(n)throw n;t?(a(t.user),s("/dashboard")):window.location.hash.includes("type=recovery")?s("/reset-password"):s("/login")}catch(t){console.error("Error handling auth callback:",t),l(t.message),setTimeout(()=>s("/login"),3e3)}})()},[s,a]),r?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:e.jsx("div",{className:"max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-red-600 dark:text-red-400",children:"Error"}),e.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:r}),e.jsx("p",{className:"mt-4 text-sm text-gray-500 dark:text-gray-400",children:"Redirecting to login..."})]})})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:e.jsx("div",{className:"max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Processing..."}),e.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Please wait while we complete the authentication process."})]})})})};export{p as default};
