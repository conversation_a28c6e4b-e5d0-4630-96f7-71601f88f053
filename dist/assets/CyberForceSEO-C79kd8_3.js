import{bO as U,r as Z,ah as _,j as T}from"./index-CVvVjHWF.js";import{P as y}from"./index-Cc2BstNK.js";function ee(t){return t&&typeof t=="object"&&"default"in t?t.default:t}var X=Z,te=ee(X);function q(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function re(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var ne=!!(typeof window<"u"&&window.document&&window.document.createElement);function oe(t,e,n){if(typeof t!="function")throw new Error("Expected reducePropsToState to be a function.");if(typeof e!="function")throw new Error("Expected handleStateChangeOnClient to be a function.");if(typeof n<"u"&&typeof n!="function")throw new Error("Expected mapStateOnServer to either be undefined or a function.");function r(o){return o.displayName||o.name||"Component"}return function(a){if(typeof a!="function")throw new Error("Expected WrappedComponent to be a React component.");var u=[],i;function c(){i=t(u.map(function(f){return f.props})),s.canUseDOM?e(i):n&&(i=n(i))}var s=function(f){re(l,f);function l(){return f.apply(this,arguments)||this}l.peek=function(){return i},l.rewind=function(){if(l.canUseDOM)throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");var h=i;return i=void 0,u=[],h};var d=l.prototype;return d.UNSAFE_componentWillMount=function(){u.push(this),c()},d.componentDidUpdate=function(){c()},d.componentWillUnmount=function(){var h=u.indexOf(this);u.splice(h,1),c()},d.render=function(){return te.createElement(a,this.props)},l}(X.PureComponent);return q(s,"displayName","SideEffect("+r(a)+")"),q(s,"canUseDOM",ne),s}}var ae=oe;const ie=U(ae);var ce=typeof Element<"u",se=typeof Map=="function",ue=typeof Set=="function",fe=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function L(t,e){if(t===e)return!0;if(t&&e&&typeof t=="object"&&typeof e=="object"){if(t.constructor!==e.constructor)return!1;var n,r,o;if(Array.isArray(t)){if(n=t.length,n!=e.length)return!1;for(r=n;r--!==0;)if(!L(t[r],e[r]))return!1;return!0}var a;if(se&&t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(a=t.entries();!(r=a.next()).done;)if(!e.has(r.value[0]))return!1;for(a=t.entries();!(r=a.next()).done;)if(!L(r.value[1],e.get(r.value[0])))return!1;return!0}if(ue&&t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(a=t.entries();!(r=a.next()).done;)if(!e.has(r.value[0]))return!1;return!0}if(fe&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(e)){if(n=t.length,n!=e.length)return!1;for(r=n;r--!==0;)if(t[r]!==e[r])return!1;return!0}if(t.constructor===RegExp)return t.source===e.source&&t.flags===e.flags;if(t.valueOf!==Object.prototype.valueOf&&typeof t.valueOf=="function"&&typeof e.valueOf=="function")return t.valueOf()===e.valueOf();if(t.toString!==Object.prototype.toString&&typeof t.toString=="function"&&typeof e.toString=="function")return t.toString()===e.toString();if(o=Object.keys(t),n=o.length,n!==Object.keys(e).length)return!1;for(r=n;r--!==0;)if(!Object.prototype.hasOwnProperty.call(e,o[r]))return!1;if(ce&&t instanceof Element)return!1;for(r=n;r--!==0;)if(!((o[r]==="_owner"||o[r]==="__v"||o[r]==="__o")&&t.$$typeof)&&!L(t[o[r]],e[o[r]]))return!1;return!0}return t!==t&&e!==e}var le=function(e,n){try{return L(e,n)}catch(r){if((r.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw r}};const pe=U(le);/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var Y=Object.getOwnPropertySymbols,de=Object.prototype.hasOwnProperty,me=Object.prototype.propertyIsEnumerable;function Te(t){if(t==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}function ye(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de",Object.getOwnPropertyNames(t)[0]==="5")return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;var r=Object.getOwnPropertyNames(e).map(function(a){return e[a]});if(r.join("")!=="**********")return!1;var o={};return"abcdefghijklmnopqrst".split("").forEach(function(a){o[a]=a}),Object.keys(Object.assign({},o)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}var ve=ye()?Object.assign:function(t,e){for(var n,r=Te(t),o,a=1;a<arguments.length;a++){n=Object(arguments[a]);for(var u in n)de.call(n,u)&&(r[u]=n[u]);if(Y){o=Y(n);for(var i=0;i<o.length;i++)me.call(n,o[i])&&(r[o[i]]=n[o[i]])}}return r};const he=U(ve);var S={BODY:"bodyAttributes",HTML:"htmlAttributes",TITLE:"titleAttributes"},p={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title"};Object.keys(p).map(function(t){return p[t]});var v={CHARSET:"charset",CSS_TEXT:"cssText",HREF:"href",HTTPEQUIV:"http-equiv",INNER_HTML:"innerHTML",ITEM_PROP:"itemprop",NAME:"name",PROPERTY:"property",REL:"rel",SRC:"src",TARGET:"target"},M={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},I={DEFAULT_TITLE:"defaultTitle",DEFER:"defer",ENCODE_SPECIAL_CHARACTERS:"encodeSpecialCharacters",ON_CHANGE_CLIENT_STATE:"onChangeClientState",TITLE_TEMPLATE:"titleTemplate"},ge=Object.keys(M).reduce(function(t,e){return t[M[e]]=e,t},{}),Ee=[p.NOSCRIPT,p.SCRIPT,p.STYLE],b="data-react-helmet",be=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ae=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},Oe=function(){function t(e,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),g=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ce=function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},$=function(t,e){var n={};for(var r in t)e.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n},Se=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t},H=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return n===!1?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},we=function(e){var n=P(e,p.TITLE),r=P(e,I.TITLE_TEMPLATE);if(r&&n)return r.replace(/%s/g,function(){return Array.isArray(n)?n.join(""):n});var o=P(e,I.DEFAULT_TITLE);return n||o||void 0},Pe=function(e){return P(e,I.ON_CHANGE_CLIENT_STATE)||function(){}},N=function(e,n){return n.filter(function(r){return typeof r[e]<"u"}).map(function(r){return r[e]}).reduce(function(r,o){return g({},r,o)},{})},je=function(e,n){return n.filter(function(r){return typeof r[p.BASE]<"u"}).map(function(r){return r[p.BASE]}).reverse().reduce(function(r,o){if(!r.length)for(var a=Object.keys(o),u=0;u<a.length;u++){var i=a[u],c=i.toLowerCase();if(e.indexOf(c)!==-1&&o[c])return r.concat(o)}return r},[])},R=function(e,n,r){var o={};return r.filter(function(a){return Array.isArray(a[e])?!0:(typeof a[e]<"u"&&Ie("Helmet: "+e+' should be of type "Array". Instead found type "'+be(a[e])+'"'),!1)}).map(function(a){return a[e]}).reverse().reduce(function(a,u){var i={};u.filter(function(d){for(var m=void 0,h=Object.keys(d),A=0;A<h.length;A++){var E=h[A],O=E.toLowerCase();n.indexOf(O)!==-1&&!(m===v.REL&&d[m].toLowerCase()==="canonical")&&!(O===v.REL&&d[O].toLowerCase()==="stylesheet")&&(m=O),n.indexOf(E)!==-1&&(E===v.INNER_HTML||E===v.CSS_TEXT||E===v.ITEM_PROP)&&(m=E)}if(!m||!d[m])return!1;var j=d[m].toLowerCase();return o[m]||(o[m]={}),i[m]||(i[m]={}),o[m][j]?!1:(i[m][j]=!0,!0)}).reverse().forEach(function(d){return a.push(d)});for(var c=Object.keys(i),s=0;s<c.length;s++){var f=c[s],l=he({},o[f],i[f]);o[f]=l}return a},[]).reverse()},P=function(e,n){for(var r=e.length-1;r>=0;r--){var o=e[r];if(o.hasOwnProperty(n))return o[n]}return null},Re=function(e){return{baseTag:je([v.HREF,v.TARGET],e),bodyAttributes:N(S.BODY,e),defer:P(e,I.DEFER),encode:P(e,I.ENCODE_SPECIAL_CHARACTERS),htmlAttributes:N(S.HTML,e),linkTags:R(p.LINK,[v.REL,v.HREF],e),metaTags:R(p.META,[v.NAME,v.CHARSET,v.HTTPEQUIV,v.PROPERTY,v.ITEM_PROP],e),noscriptTags:R(p.NOSCRIPT,[v.INNER_HTML],e),onChangeClientState:Pe(e),scriptTags:R(p.SCRIPT,[v.SRC,v.INNER_HTML],e),styleTags:R(p.STYLE,[v.CSS_TEXT],e),title:we(e),titleAttributes:N(S.TITLE,e)}},F=function(){var t=Date.now();return function(e){var n=Date.now();n-t>16?(t=n,e(n)):setTimeout(function(){F(e)},0)}}(),G=function(e){return clearTimeout(e)},xe=typeof window<"u"?window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||F:global.requestAnimationFrame||F,_e=typeof window<"u"?window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||G:global.cancelAnimationFrame||G,Ie=function(e){return console&&typeof console.warn=="function"&&console.warn(e)},x=null,Le=function(e){x&&_e(x),e.defer?x=xe(function(){z(e,function(){x=null})}):(z(e),x=null)},z=function(e,n){var r=e.baseTag,o=e.bodyAttributes,a=e.htmlAttributes,u=e.linkTags,i=e.metaTags,c=e.noscriptTags,s=e.onChangeClientState,f=e.scriptTags,l=e.styleTags,d=e.title,m=e.titleAttributes;D(p.BODY,o),D(p.HTML,a),Me(d,m);var h={baseTag:w(p.BASE,r),linkTags:w(p.LINK,u),metaTags:w(p.META,i),noscriptTags:w(p.NOSCRIPT,c),scriptTags:w(p.SCRIPT,f),styleTags:w(p.STYLE,l)},A={},E={};Object.keys(h).forEach(function(O){var j=h[O],B=j.newTags,K=j.oldTags;B.length&&(A[O]=B),K.length&&(E[O]=h[O].oldTags)}),n&&n(),s(e,A,E)},V=function(e){return Array.isArray(e)?e.join(""):e},Me=function(e,n){typeof e<"u"&&document.title!==e&&(document.title=V(e)),D(p.TITLE,n)},D=function(e,n){var r=document.getElementsByTagName(e)[0];if(r){for(var o=r.getAttribute(b),a=o?o.split(","):[],u=[].concat(a),i=Object.keys(n),c=0;c<i.length;c++){var s=i[c],f=n[s]||"";r.getAttribute(s)!==f&&r.setAttribute(s,f),a.indexOf(s)===-1&&a.push(s);var l=u.indexOf(s);l!==-1&&u.splice(l,1)}for(var d=u.length-1;d>=0;d--)r.removeAttribute(u[d]);a.length===u.length?r.removeAttribute(b):r.getAttribute(b)!==i.join(",")&&r.setAttribute(b,i.join(","))}},w=function(e,n){var r=document.head||document.querySelector(p.HEAD),o=r.querySelectorAll(e+"["+b+"]"),a=Array.prototype.slice.call(o),u=[],i=void 0;return n&&n.length&&n.forEach(function(c){var s=document.createElement(e);for(var f in c)if(c.hasOwnProperty(f))if(f===v.INNER_HTML)s.innerHTML=c.innerHTML;else if(f===v.CSS_TEXT)s.styleSheet?s.styleSheet.cssText=c.cssText:s.appendChild(document.createTextNode(c.cssText));else{var l=typeof c[f]>"u"?"":c[f];s.setAttribute(f,l)}s.setAttribute(b,"true"),a.some(function(d,m){return i=m,s.isEqualNode(d)})?a.splice(i,1):u.push(s)}),a.forEach(function(c){return c.parentNode.removeChild(c)}),u.forEach(function(c){return r.appendChild(c)}),{oldTags:a,newTags:u}},W=function(e){return Object.keys(e).reduce(function(n,r){var o=typeof e[r]<"u"?r+'="'+e[r]+'"':""+r;return n?n+" "+o:o},"")},Ne=function(e,n,r,o){var a=W(r),u=V(n);return a?"<"+e+" "+b+'="true" '+a+">"+H(u,o)+"</"+e+">":"<"+e+" "+b+'="true">'+H(u,o)+"</"+e+">"},He=function(e,n,r){return n.reduce(function(o,a){var u=Object.keys(a).filter(function(s){return!(s===v.INNER_HTML||s===v.CSS_TEXT)}).reduce(function(s,f){var l=typeof a[f]>"u"?f:f+'="'+H(a[f],r)+'"';return s?s+" "+l:l},""),i=a.innerHTML||a.cssText||"",c=Ee.indexOf(e)===-1;return o+"<"+e+" "+b+'="true" '+u+(c?"/>":">"+i+"</"+e+">")},"")},Q=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return Object.keys(e).reduce(function(r,o){return r[M[o]||o]=e[o],r},n)},Fe=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return Object.keys(e).reduce(function(r,o){return r[ge[o]||o]=e[o],r},n)},De=function(e,n,r){var o,a=(o={key:n},o[b]=!0,o),u=Q(r,a);return[_.createElement(p.TITLE,u,n)]},ke=function(e,n){return n.map(function(r,o){var a,u=(a={key:o},a[b]=!0,a);return Object.keys(r).forEach(function(i){var c=M[i]||i;if(c===v.INNER_HTML||c===v.CSS_TEXT){var s=r.innerHTML||r.cssText;u.dangerouslySetInnerHTML={__html:s}}else u[c]=r[i]}),_.createElement(e,u)})},C=function(e,n,r){switch(e){case p.TITLE:return{toComponent:function(){return De(e,n.title,n.titleAttributes)},toString:function(){return Ne(e,n.title,n.titleAttributes,r)}};case S.BODY:case S.HTML:return{toComponent:function(){return Q(n)},toString:function(){return W(n)}};default:return{toComponent:function(){return ke(e,n)},toString:function(){return He(e,n,r)}}}},J=function(e){var n=e.baseTag,r=e.bodyAttributes,o=e.encode,a=e.htmlAttributes,u=e.linkTags,i=e.metaTags,c=e.noscriptTags,s=e.scriptTags,f=e.styleTags,l=e.title,d=l===void 0?"":l,m=e.titleAttributes;return{base:C(p.BASE,n,o),bodyAttributes:C(S.BODY,r,o),htmlAttributes:C(S.HTML,a,o),link:C(p.LINK,u,o),meta:C(p.META,i,o),noscript:C(p.NOSCRIPT,c,o),script:C(p.SCRIPT,s,o),style:C(p.STYLE,f,o),title:C(p.TITLE,{title:d,titleAttributes:m},o)}},Ue=function(e){var n,r;return r=n=function(o){Ce(a,o);function a(){return Ae(this,a),Se(this,o.apply(this,arguments))}return a.prototype.shouldComponentUpdate=function(i){return!pe(this.props,i)},a.prototype.mapNestedChildrenToProps=function(i,c){if(!c)return null;switch(i.type){case p.SCRIPT:case p.NOSCRIPT:return{innerHTML:c};case p.STYLE:return{cssText:c}}throw new Error("<"+i.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")},a.prototype.flattenArrayTypeChildren=function(i){var c,s=i.child,f=i.arrayTypeChildren,l=i.newChildProps,d=i.nestedChildren;return g({},f,(c={},c[s.type]=[].concat(f[s.type]||[],[g({},l,this.mapNestedChildrenToProps(s,d))]),c))},a.prototype.mapObjectTypeChildren=function(i){var c,s,f=i.child,l=i.newProps,d=i.newChildProps,m=i.nestedChildren;switch(f.type){case p.TITLE:return g({},l,(c={},c[f.type]=m,c.titleAttributes=g({},d),c));case p.BODY:return g({},l,{bodyAttributes:g({},d)});case p.HTML:return g({},l,{htmlAttributes:g({},d)})}return g({},l,(s={},s[f.type]=g({},d),s))},a.prototype.mapArrayTypeChildrenToProps=function(i,c){var s=g({},c);return Object.keys(i).forEach(function(f){var l;s=g({},s,(l={},l[f]=i[f],l))}),s},a.prototype.warnOnInvalidChildren=function(i,c){return!0},a.prototype.mapChildrenToProps=function(i,c){var s=this,f={};return _.Children.forEach(i,function(l){if(!(!l||!l.props)){var d=l.props,m=d.children,h=$(d,["children"]),A=Fe(h);switch(s.warnOnInvalidChildren(l,m),l.type){case p.LINK:case p.META:case p.NOSCRIPT:case p.SCRIPT:case p.STYLE:f=s.flattenArrayTypeChildren({child:l,arrayTypeChildren:f,newChildProps:A,nestedChildren:m});break;default:c=s.mapObjectTypeChildren({child:l,newProps:c,newChildProps:A,nestedChildren:m});break}}}),c=this.mapArrayTypeChildrenToProps(f,c),c},a.prototype.render=function(){var i=this.props,c=i.children,s=$(i,["children"]),f=g({},s);return c&&(f=this.mapChildrenToProps(c,f)),_.createElement(e,f)},Oe(a,null,[{key:"canUseDOM",set:function(i){e.canUseDOM=i}}]),a}(_.Component),n.propTypes={base:y.object,bodyAttributes:y.object,children:y.oneOfType([y.arrayOf(y.node),y.node]),defaultTitle:y.string,defer:y.bool,encodeSpecialCharacters:y.bool,htmlAttributes:y.object,link:y.arrayOf(y.object),meta:y.arrayOf(y.object),noscript:y.arrayOf(y.object),onChangeClientState:y.func,script:y.arrayOf(y.object),style:y.arrayOf(y.object),title:y.string,titleAttributes:y.object,titleTemplate:y.string},n.defaultProps={defer:!0,encodeSpecialCharacters:!0},n.peek=e.peek,n.rewind=function(){var o=e.rewind();return o||(o=J({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}})),o},r},Be=function(){return null},qe=ie(Re,Le,J)(Be),k=Ue(qe);k.renderStatic=k.rewind;const Ge=({title:t,description:e,keywords:n=[],ogImage:r="/images/CyberForce.png",ogType:o="website",twitterCard:a="summary_large_image",canonicalUrl:u,structuredData:i})=>{const c="CyberForce - Advanced Cybersecurity Training Platform",s=t?`${t} | CyberForce`:c,l=e||"CyberForce is a premier destination for comprehensive cyber security capacity building and training, empowering individuals and organizations to effectively navigate the complex world of cyber security.",m=[...["cybersecurity","cyber security training","security challenges","CTF","cyber defense","security training","Oman cybersecurity","CyberForce Oman","cybersecurity learning platform"],...n].join(", "),h=u||(typeof window<"u"?window.location.href:"https://cyberforce.om"),E=i||{"@context":"https://schema.org","@type":"Organization",name:"CyberForce",url:"https://cyberforce.om",logo:"/logo/Artboard <EMAIL>",sameAs:["https://www.linkedin.com/company/cyberforceoman/","https://www.instagram.com/cyberforce_om/","https://x.com/cyberforce_om"],description:l,address:{"@type":"PostalAddress",addressCountry:"Oman",addressLocality:"Muscat"},contactPoint:{"@type":"ContactPoint",telephone:"+96871104475",contactType:"customer service",email:"<EMAIL>"}};return T.jsxs(k,{children:[T.jsx("title",{children:s}),T.jsx("meta",{name:"description",content:l}),T.jsx("meta",{name:"keywords",content:m}),T.jsx("link",{rel:"canonical",href:h}),T.jsx("meta",{property:"og:title",content:s}),T.jsx("meta",{property:"og:description",content:l}),T.jsx("meta",{property:"og:image",content:r}),T.jsx("meta",{property:"og:url",content:h}),T.jsx("meta",{property:"og:type",content:o}),T.jsx("meta",{property:"og:site_name",content:c}),T.jsx("meta",{name:"twitter:card",content:a}),T.jsx("meta",{name:"twitter:site",content:"@cyberforce_om"}),T.jsx("meta",{name:"twitter:title",content:s}),T.jsx("meta",{name:"twitter:description",content:l}),T.jsx("meta",{name:"twitter:image",content:r}),T.jsx("meta",{name:"author",content:"CyberForce"}),T.jsx("meta",{name:"language",content:"English"}),T.jsx("meta",{name:"robots",content:"index, follow"}),T.jsx("meta",{name:"googlebot",content:"index, follow"}),T.jsx("meta",{name:"revisit-after",content:"7 days"}),T.jsx("meta",{name:"distribution",content:"global"}),T.jsx("meta",{name:"rating",content:"general"}),T.jsx("meta",{name:"geo.region",content:"OM"}),T.jsx("meta",{name:"geo.placename",content:"Muscat"}),T.jsx("script",{type:"application/ld+json",children:JSON.stringify(E)})]})};export{Ge as C};
