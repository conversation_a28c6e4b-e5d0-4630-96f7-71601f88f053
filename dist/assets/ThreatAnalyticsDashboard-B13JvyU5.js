import{bg as q,u as J,r as x,j as e,E as K,$ as Z}from"./index-CVvVjHWF.js";class ee{constructor(){this.initialized=!1,this.services={}}async initialize(){this.initialized||(this.services=q.initialize(),this.initialized=!0)}async correlateIPData(h){this.initialized||await this.initialize();const s={ip:h,sources:[],riskScore:0,riskFactors:[],geoData:null,relatedIndicators:[],analysisTimestamp:new Date().toISOString()};try{if(this.services.abuseIPDB){const n=await this.services.abuseIPDB.checkIP(h);if(n){s.sources.push("AbuseIPDB"),s.geoData={countryCode:n.countryCode,countryName:n.countryName,isp:n.isp,domain:n.domain,usageType:n.usageType};const r=n.abuseConfidenceScore||0;s.riskScore+=r*.7,r>80?s.riskFactors.push("High abuse confidence score"):r>50&&s.riskFactors.push("Medium abuse confidence score"),n.totalReports>10&&(s.riskFactors.push("Multiple abuse reports"),s.riskScore+=10)}}if(this.services.otx){const n=await this.services.otx.getIPReputation(h);if(n){if(s.sources.push("AlienVault OTX"),n.pulse_info&&n.pulse_info.count>0){const r=n.pulse_info.count;s.riskScore+=Math.min(r*5,30),r>5&&s.riskFactors.push("Associated with multiple threat intelligence reports"),n.pulse_info.pulses&&n.pulse_info.pulses.forEach(t=>{t.indicators&&t.indicators.forEach(l=>{l.type!=="IPv4"&&l.type!=="IPv6"&&s.relatedIndicators.push({type:l.type,indicator:l.indicator,title:t.name,created:t.created})})})}if(n.reputation&&n.reputation.reputation_score){const r=n.reputation.reputation_score;s.riskScore+=(100-r)*.3,r<20&&s.riskFactors.push("Poor reputation score")}}}return s.riskScore=Math.min(Math.round(s.riskScore),100),s.riskScore>=75?s.riskLevel="Critical":s.riskScore>=50?s.riskLevel="High":s.riskScore>=25?s.riskLevel="Medium":s.riskLevel="Low",s}catch(n){throw console.error(`Error correlating data for IP ${h}:`,n),n}}async analyzeThreatCluster(h){this.initialized||await this.initialize();const s={analyzedCount:h.length,highRiskCount:0,countryClusters:{},ispClusters:{},highRiskIPs:[],analysisTimestamp:new Date().toISOString()};try{const n=h.map(t=>this.correlateIPData(t));return(await Promise.all(n)).forEach(t=>{var l;if(t.riskScore>=50&&(s.highRiskCount++,s.highRiskIPs.push({ip:t.ip,riskScore:t.riskScore,riskLevel:t.riskLevel,countryName:((l=t.geoData)==null?void 0:l.countryName)||"Unknown"})),t.geoData&&t.geoData.countryName){const c=t.geoData.countryName;s.countryClusters[c]||(s.countryClusters[c]={count:0,avgRiskScore:0,ips:[]}),s.countryClusters[c].count++,s.countryClusters[c].avgRiskScore=(s.countryClusters[c].avgRiskScore*(s.countryClusters[c].count-1)+t.riskScore)/s.countryClusters[c].count,s.countryClusters[c].ips.push(t.ip)}if(t.geoData&&t.geoData.isp){const c=t.geoData.isp;s.ispClusters[c]||(s.ispClusters[c]={count:0,avgRiskScore:0,ips:[]}),s.ispClusters[c].count++,s.ispClusters[c].avgRiskScore=(s.ispClusters[c].avgRiskScore*(s.ispClusters[c].count-1)+t.riskScore)/s.ispClusters[c].count,s.ispClusters[c].ips.push(t.ip)}}),s.topCountries=Object.entries(s.countryClusters).sort((t,l)=>l[1].count-t[1].count).slice(0,5).map(([t,l])=>({country:t,count:l.count,avgRiskScore:Math.round(l.avgRiskScore)})),s.topISPs=Object.entries(s.ispClusters).sort((t,l)=>l[1].count-t[1].count).slice(0,5).map(([t,l])=>({isp:t,count:l.count,avgRiskScore:Math.round(l.avgRiskScore)})),s}catch(n){throw console.error("Error analyzing threat cluster:",n),n}}async generateThreatReport(h={}){this.initialized||await this.initialize();const s={title:h.title||"Threat Intelligence Report",generatedAt:new Date().toISOString(),summary:{totalThreats:0,criticalThreats:0,highThreats:0,mediumThreats:0,lowThreats:0},topThreats:[],geographicDistribution:{},attackVectors:{},recommendations:[]};try{if(this.services.abuseIPDB){const n=await this.services.abuseIPDB.getBlacklist(70,50);if(n&&n.length>0){const r=await this.analyzeThreatCluster(n.map(t=>t.ipAddress));s.summary.totalThreats=n.length,s.summary.criticalThreats=r.highRiskIPs.filter(t=>t.riskLevel==="Critical").length,s.summary.highThreats=r.highRiskIPs.filter(t=>t.riskLevel==="High").length,s.topThreats=r.highRiskIPs.sort((t,l)=>l.riskScore-t.riskScore).slice(0,10),s.geographicDistribution=r.topCountries.reduce((t,l)=>(t[l.country]=l.count,t),{})}}if(this.services.otx){const n=await this.services.otx.getPulses(20);if(n&&n.length>0){const r={};n.forEach(l=>{l.tags&&l.tags.forEach(c=>{let d=c.toLowerCase();if(d.includes("ransomware"))d="Ransomware";else if(d.includes("phish"))d="Phishing";else if(d.includes("malware"))d="Malware";else if(d.includes("ddos"))d="DDoS";else if(d.includes("exploit"))d="Exploit";else if(d.includes("backdoor"))d="Backdoor";else if(d.includes("trojan"))d="Trojan";else if(d.includes("botnet"))d="Botnet";else return;r[d]=(r[d]||0)+1})}),s.attackVectors=Object.entries(r).sort((l,c)=>c[1]-l[1]).reduce((l,[c,d])=>(l[c]=d,l),{});const t=Object.keys(s.attackVectors).slice(0,3);t.includes("Ransomware")&&s.recommendations.push("Implement regular backup procedures and test recovery processes","Deploy endpoint protection with anti-ransomware capabilities","Segment networks to limit lateral movement"),t.includes("Phishing")&&s.recommendations.push("Conduct regular phishing awareness training","Implement email filtering solutions","Deploy multi-factor authentication"),t.includes("Exploit")&&s.recommendations.push("Establish a robust patch management process","Conduct regular vulnerability scanning","Implement a web application firewall"),(t.includes("Malware")||t.includes("Trojan"))&&s.recommendations.push("Deploy advanced endpoint protection","Implement application whitelisting","Conduct regular system scans"),(t.includes("DDoS")||t.includes("Botnet"))&&s.recommendations.push("Implement DDoS protection services","Configure network rate limiting","Develop a DDoS response plan")}}return s}catch(n){throw console.error("Error generating threat report:",n),n}}async analyzeDomain(h){var n;this.initialized||await this.initialize();const s={domain:h,sources:[],riskScore:0,riskFactors:[],relatedIndicators:[],analysisTimestamp:new Date().toISOString()};try{if(this.services.otx){const r=await this.services.otx.getDomainReputation(h);if(r){if(s.sources.push("AlienVault OTX"),r.pulse_info&&r.pulse_info.count>0){const t=r.pulse_info.count;s.riskScore+=Math.min(t*5,30),t>5&&s.riskFactors.push("Associated with multiple threat intelligence reports"),r.pulse_info.pulses&&r.pulse_info.pulses.forEach(l=>{l.indicators&&l.indicators.forEach(c=>{c.type!=="domain"&&c.indicator!==h&&s.relatedIndicators.push({type:c.type,indicator:c.indicator,title:l.name,created:l.created})})})}if(r.passive_dns&&r.passive_dns.length>0&&(s.passiveDNS=r.passive_dns.map(l=>({address:l.address,firstSeen:l.first,lastSeen:l.last})),r.passive_dns.filter(l=>{const c=new Date(l.first);return(new Date-c)/(1e3*60*60*24)<30}).length>5&&(s.riskFactors.push("Multiple recent DNS records (potential DGA)"),s.riskScore+=15)),r.whois&&(s.whois={creationDate:r.whois.creation_date,expirationDate:r.whois.expiration_date,updatedDate:r.whois.updated_date,registrar:r.whois.registrar,nameServers:r.whois.nameservers},r.whois.creation_date)){const t=new Date(r.whois.creation_date);(new Date-t)/(1e3*60*60*24)<30&&(s.riskFactors.push("Recently registered domain"),s.riskScore+=20)}r.malware&&r.malware.count>0&&(s.riskFactors.push("Associated with malware"),s.riskScore+=25,s.malware={count:r.malware.count,samples:((n=r.malware.samples)==null?void 0:n.map(t=>({hash:t.hash,date:t.date,name:t.name})))||[]}),r.url_list&&r.url_list.url_list&&r.url_list.url_list.length>0&&(s.urls=r.url_list.url_list.map(t=>({url:t.url,date:t.date,domain:t.domain})))}}return s.riskScore=Math.min(Math.round(s.riskScore),100),s.riskScore>=75?s.riskLevel="Critical":s.riskScore>=50?s.riskLevel="High":s.riskScore>=25?s.riskLevel="Medium":s.riskLevel="Low",s}catch(r){throw console.error(`Error analyzing domain ${h}:`,r),r}}}const P=new ee,te=()=>{const{darkMode:j}=J(),[h,s]=x.useState(!0),[n,r]=x.useState(null),[t,l]=x.useState(null),[c,d]=x.useState(null),[O,M]=x.useState(null),[N,B]=x.useState(""),[o,L]=x.useState(null),[A,U]=x.useState("ip"),E=x.useRef(null),_=x.useRef(null);x.useEffect(()=>{const i=async()=>{try{s(!0),r(null),await P.initialize();const a=await P.generateThreatReport({title:"Current Threat Landscape"});l(a)}catch(a){console.error("Error loading threat analytics data:",a),a.message&&a.message.includes("Network Error")?r("Network error connecting to threat intelligence APIs. Using proxy to retry connection..."):a.response&&a.response.status===403?r("API access forbidden. Please check your API keys and try again."):a.response&&a.response.status===429?r("API rate limit exceeded. Please wait a moment and try again."):r("Unable to connect to threat analytics API. Retrying connection..."),setTimeout(()=>{t||(r("Retrying connection to threat intelligence APIs..."),i())},5e3)}finally{s(!1)}};i()},[]),x.useEffect(()=>{t&&E.current&&V(),t&&_.current&&H()},[t,j]);const V=()=>{const i=E.current,a=i.getContext("2d");a.clearRect(0,0,i.width,i.height);const D=i.width,b=i.height,w=40,I=20,T=b-60,k=Object.entries(t.attackVectors).sort((y,g)=>g[1]-y[1]).slice(0,5);if(k.length===0)return;const C=Math.max(...k.map(([y,g])=>g));k.forEach(([y,g],R)=>{const v=60+R*(w+I),S=g/C*T,u=b-40-S,p=a.createLinearGradient(v,u,v,b-40);let m,f;switch(y){case"Ransomware":m="#ff0066",f="#990033";break;case"Phishing":m="#3366ff",f="#003399";break;case"Malware":m="#ff9900",f="#cc6600";break;case"DDoS":m="#00cc99",f="#006633";break;case"Exploit":m="#cc33ff",f="#660099";break;default:m="#999999",f="#666666"}p.addColorStop(0,m),p.addColorStop(1,f),a.fillStyle=p,a.beginPath(),a.roundRect(v,u,w,S,5),a.fill(),a.fillStyle=j?"#ffffff":"#000000",a.font="12px Arial",a.textAlign="center",a.fillText(g,v+w/2,u-5),a.fillStyle=j?"#cccccc":"#333333",a.font="10px Arial",a.fillText(y,v+w/2,b-20)}),a.fillStyle=j?"#ffffff":"#000000",a.font="bold 14px Arial",a.textAlign="center",a.fillText("Top Attack Vectors",D/2,20)},H=()=>{const i=_.current,a=i.getContext("2d");a.clearRect(0,0,i.width,i.height);const D=i.width,b=i.height,w=Math.min(D,b)/2-40,I=D/2,T=b/2,k=Object.entries(t.geographicDistribution).sort((u,p)=>p[1]-u[1]);if(k.length===0)return;const C=k.reduce((u,[p,m])=>u+m,0),y=["#ff3366","#3366ff","#33cc33","#ff9900","#9966ff","#ff6666","#66ccff","#99cc00","#ffcc00","#cc66ff"];let g=0;const R=[];k.forEach(([u,p],m)=>{const f=p/C*2*Math.PI,z=g+f,F=g+f/2;if(a.fillStyle=y[m%y.length],a.beginPath(),a.moveTo(I,T),a.arc(I,T,w,g,z),a.closePath(),a.fill(),f>.2){const Y=Math.round(p/C*100),$=w*.7,Q=I+Math.cos(F)*$,W=T+Math.sin(F)*$;a.fillStyle="#ffffff",a.font="bold 12px Arial",a.textAlign="center",a.textBaseline="middle",a.fillText(`${Y}%`,Q,W)}R.push({country:u,value:p,percent:Math.round(p/C*100),color:y[m%y.length]}),g=z});const v=20;let S=b-20-R.length*20;R.forEach(u=>{a.fillStyle=u.color,a.fillRect(v,S,15,15),a.fillStyle=j?"#ffffff":"#000000",a.font="12px Arial",a.textAlign="left",a.textBaseline="middle",a.fillText(`${u.country} (${u.percent}%)`,v+20,S+7),S+=20}),a.fillStyle=j?"#ffffff":"#000000",a.font="bold 14px Arial",a.textAlign="center",a.fillText("Geographic Distribution",D/2,20)},G=async i=>{try{d(i),s(!0),r(null);const a=await P.correlateIPData(i);M(a)}catch(a){console.error(`Error analyzing IP ${i}:`,a),r(`Failed to analyze IP ${i}.`),M(null)}finally{s(!1)}},X=async i=>{if(i.preventDefault(),!!N.trim())try{if(s(!0),r(null),A==="ip"){const a=await P.correlateIPData(N);L({type:"ip",data:a})}else{const a=await P.analyzeDomain(N);L({type:"domain",data:a})}}catch(a){console.error(`Error searching for "${N}":`,a),r(`Failed to analyze ${A==="ip"?"IP":"domain"} "${N}".`),L(null)}finally{s(!1)}};return h&&!t?e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):n&&!t?e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Advanced Threat Analytics"}),e.jsx("p",{className:"text-gray-400 mb-4",children:"In-depth analysis of threat data from multiple intelligence sources."}),e.jsxs("div",{className:"bg-blue-900 bg-opacity-20 border border-blue-800 rounded-lg p-4 mb-4 text-blue-400",children:[e.jsx(K,{className:"inline-block mr-2"}),n]}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsxs("div",{className:"animate-pulse flex space-x-4 items-center",children:[e.jsx("div",{className:"rounded-full bg-blue-700 h-3 w-3"}),e.jsx("div",{className:"rounded-full bg-blue-700 h-3 w-3 animate-animation-delay-200"}),e.jsx("div",{className:"rounded-full bg-blue-700 h-3 w-3 animate-animation-delay-400"})]})})]}):e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-bold",children:"Threat Analytics Dashboard"}),e.jsxs("form",{onSubmit:X,className:"flex",children:[e.jsx("div",{className:"mr-2",children:e.jsxs("select",{className:"bg-gray-700 border border-gray-600 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",value:A,onChange:i=>U(i.target.value),children:[e.jsx("option",{value:"ip",children:"IP"}),e.jsx("option",{value:"domain",children:"Domain"})]})}),e.jsx("input",{type:"text",placeholder:`Search ${A==="ip"?"IP address":"domain"}...`,className:"bg-gray-700 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",value:N,onChange:i=>B(i.target.value)}),e.jsxs("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center",disabled:h,children:[e.jsx(Z,{className:"mr-2"}),"Analyze"]})]})]}),o&&e.jsxs("div",{className:"mb-6 bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"text-lg font-semibold mb-3",children:["Analysis Results: ",o.type==="ip"?"IP Address":"Domain"," ",N]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Risk Score"}),e.jsxs("div",{className:"text-2xl font-bold",children:[o.data.riskScore,"/100"]}),e.jsxs("div",{className:`text-sm ${o.data.riskLevel==="Critical"?"text-red-400":o.data.riskLevel==="High"?"text-orange-400":o.data.riskLevel==="Medium"?"text-yellow-400":"text-green-400"}`,children:[o.data.riskLevel," Risk"]})]}),e.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Data Sources"}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:o.data.sources.map((i,a)=>e.jsx("span",{className:"bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded",children:i},a))})]}),e.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Analysis Time"}),e.jsx("div",{children:new Date(o.data.analysisTimestamp).toLocaleString()})]})]}),o.data.riskFactors.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Risk Factors"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 pl-2",children:o.data.riskFactors.map((i,a)=>e.jsx("li",{className:"text-yellow-300",children:i},a))})]}),o.type==="ip"&&o.data.geoData&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Geolocation Data"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Country"}),e.jsx("div",{children:o.data.geoData.countryName||"Unknown"})]}),e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"ISP"}),e.jsx("div",{children:o.data.geoData.isp||"Unknown"})]}),e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Domain"}),e.jsx("div",{children:o.data.geoData.domain||"Unknown"})]}),e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Usage Type"}),e.jsx("div",{children:o.data.geoData.usageType||"Unknown"})]})]})]}),o.type==="domain"&&o.data.whois&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"WHOIS Data"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:[e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Creation Date"}),e.jsx("div",{children:o.data.whois.creationDate?new Date(o.data.whois.creationDate).toLocaleDateString():"Unknown"})]}),e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Expiration Date"}),e.jsx("div",{children:o.data.whois.expirationDate?new Date(o.data.whois.expirationDate).toLocaleDateString():"Unknown"})]}),e.jsxs("div",{className:"bg-gray-800 p-2 rounded",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Registrar"}),e.jsx("div",{children:o.data.whois.registrar||"Unknown"})]})]})]}),o.data.relatedIndicators&&o.data.relatedIndicators.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Related Indicators"}),e.jsx("div",{className:"max-h-40 overflow-y-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-2 py-1 text-left text-xs",children:"Type"}),e.jsx("th",{className:"px-2 py-1 text-left text-xs",children:"Indicator"}),e.jsx("th",{className:"px-2 py-1 text-left text-xs",children:"Source"})]})}),e.jsx("tbody",{children:o.data.relatedIndicators.slice(0,10).map((i,a)=>e.jsxs("tr",{className:"border-t border-gray-700",children:[e.jsx("td",{className:"px-2 py-1 text-xs",children:i.type}),e.jsx("td",{className:"px-2 py-1 text-xs font-mono",children:i.indicator}),e.jsx("td",{className:"px-2 py-1 text-xs",children:i.title})]},a))})]})})]})]}),t&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:t.title}),e.jsxs("p",{className:"text-sm text-gray-400 mb-4",children:["Generated on ",new Date(t.generatedAt).toLocaleString()]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Total Threats"}),e.jsx("div",{className:"text-2xl font-bold",children:t.summary.totalThreats})]}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Critical"}),e.jsx("div",{className:"text-2xl font-bold text-red-400",children:t.summary.criticalThreats})]}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"High"}),e.jsx("div",{className:"text-2xl font-bold text-orange-400",children:t.summary.highThreats})]}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Medium"}),e.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:t.summary.mediumThreats})]}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Low"}),e.jsx("div",{className:"text-2xl font-bold text-green-400",children:t.summary.lowThreats})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsx("div",{className:"bg-gray-700 p-4 rounded-lg",children:e.jsx("canvas",{ref:E,width:"400",height:"250",className:"w-full"})}),e.jsx("div",{className:"bg-gray-700 p-4 rounded-lg",children:e.jsx("canvas",{ref:_,width:"400",height:"250",className:"w-full"})})]})]}),t&&t.topThreats&&t.topThreats.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Top Threats"}),e.jsx("div",{className:"bg-gray-700 rounded-lg overflow-hidden",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"IP Address"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Risk Score"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Risk Level"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Country"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Actions"})]})}),e.jsx("tbody",{children:t.topThreats.map((i,a)=>e.jsxs("tr",{className:`border-t border-gray-600 hover:bg-gray-600 cursor-pointer ${c===i.ip?"bg-gray-600":""}`,children:[e.jsx("td",{className:"px-4 py-2 font-mono",children:i.ip}),e.jsxs("td",{className:"px-4 py-2",children:[i.riskScore,"/100"]}),e.jsx("td",{className:"px-4 py-2",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs ${i.riskLevel==="Critical"?"bg-red-900 text-red-200":i.riskLevel==="High"?"bg-orange-900 text-orange-200":i.riskLevel==="Medium"?"bg-yellow-900 text-yellow-200":"bg-green-900 text-green-200"}`,children:i.riskLevel})}),e.jsx("td",{className:"px-4 py-2",children:i.countryName}),e.jsx("td",{className:"px-4 py-2",children:e.jsx("button",{onClick:()=>G(i.ip),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs",children:"Analyze"})})]},a))})]})})]}),t&&t.recommendations&&t.recommendations.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Security Recommendations"}),e.jsx("div",{className:"bg-gray-700 p-4 rounded-lg",children:e.jsx("ul",{className:"list-disc list-inside space-y-2 pl-2",children:t.recommendations.map((i,a)=>e.jsx("li",{className:"text-blue-300",children:i},a))})})]})]})};export{te as T};
