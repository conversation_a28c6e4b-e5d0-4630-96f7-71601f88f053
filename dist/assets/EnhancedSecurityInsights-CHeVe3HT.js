import{D as Ce,r as f,b7 as Ae,b8 as Ie,j as e,J as Ne,N as q,aO as ge,B as z,E as A,a0 as ne,aR as De,L as pe,a3 as Ee,aQ as Me,aV as Pe,b9 as Le,as as we,ac as re,l as le,u as $,ba as Re,b6 as ke,bb as Be,bc as Te,Q as Se,U as $e,a1 as se,b4 as X,z as V,$ as B,bd as fe,c as Y,v as ee,aG as ae,aH as Fe,aI as Ue,b5 as qe,be as Oe,x as He,bf as ze}from"./index-CVvVjHWF.js";import{T as _e}from"./ThreatAnalyticsDashboard-B13JvyU5.js";const Ke=[{name:"Russia",lat:55.7558,lng:37.6173,weight:25},{name:"China",lat:39.9042,lng:116.4074,weight:25},{name:"North Korea",lat:39.0392,lng:125.7625,weight:15},{name:"Iran",lat:35.6892,lng:51.389,weight:15},{name:"United States",lat:37.0902,lng:-95.7129,weight:10},{name:"Brazil",lat:-14.235,lng:-51.9253,weight:5},{name:"Ukraine",lat:48.3794,lng:31.1656,weight:5}],We=[{name:"United States",lat:37.0902,lng:-95.7129,weight:30},{name:"United Kingdom",lat:51.5074,lng:-.1278,weight:10},{name:"Germany",lat:51.1657,lng:10.4515,weight:10},{name:"France",lat:46.2276,lng:2.2137,weight:8},{name:"Japan",lat:36.2048,lng:138.2529,weight:8},{name:"South Korea",lat:35.9078,lng:127.7669,weight:7},{name:"Australia",lat:-25.2744,lng:133.7751,weight:7},{name:"India",lat:20.5937,lng:78.9629,weight:7},{name:"Canada",lat:56.1304,lng:-106.3468,weight:5},{name:"Italy",lat:41.8719,lng:12.5674,weight:4},{name:"Spain",lat:40.4637,lng:-3.7492,weight:4}],Ge=[{name:"Ransomware",weight:25,color:"#ff0000"},{name:"DDoS",weight:20,color:"#ff3300"},{name:"Phishing",weight:20,color:"#ff6600"},{name:"Malware",weight:15,color:"#ff9900"},{name:"SQL Injection",weight:10,color:"#ffcc00"},{name:"XSS",weight:5,color:"#ffff00"},{name:"Zero-day Exploit",weight:5,color:"#ff00ff"}],Ve=[{level:"Critical",weight:15,color:"#ff0000"},{level:"High",weight:25,color:"#ff3300"},{level:"Medium",weight:35,color:"#ffaa00"},{level:"Low",weight:25,color:"#ffff00"}],Ye=[{name:"Financial Services",weight:25},{name:"Healthcare",weight:20},{name:"Government",weight:15},{name:"Energy",weight:10},{name:"Technology",weight:10},{name:"Manufacturing",weight:8},{name:"Retail",weight:7},{name:"Education",weight:5}],O=x=>{const s=x.reduce((a,t)=>a+t.weight,0);let n=Math.random()*s;for(const a of x)if(n-=a.weight,n<=0)return a;return x[0]},K=()=>(Math.random()-.5)*2;class U{constructor(){this.attacks=[],this.statistics={totalAttacks:0,attacksByType:{},attacksBySeverity:{},attacksBySource:{},attacksByTarget:{},attacksByIndustry:{}},this.listeners=[]}initialize(){return this.generateAttacks(30),this.updateStatistics(),this}generateAttacks(s=10){const n=[];for(let a=0;a<s;a++){const t=O(Ke),l=O(We);if(t.name===l.name)continue;const r=O(Ge),i=O(Ve),c=O(Ye),d={id:`attack-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,startLat:t.lat+K()*.5,startLng:t.lng+K()*.5,endLat:l.lat+K()*.5,endLng:l.lng+K()*.5,color:i.color,type:r.name,typeColor:r.color,source:t.name,target:l.name,severity:i.level,industry:c.name,timestamp:new Date().toISOString(),active:!0};n.push(d)}return this.attacks=[...this.attacks,...n],this.statistics.totalAttacks+=n.length,this.notifyListeners(),n}updateStatistics(){return this.statistics.attacksByType={},this.statistics.attacksBySeverity={},this.statistics.attacksBySource={},this.statistics.attacksByTarget={},this.statistics.attacksByIndustry={},this.attacks.forEach(s=>{this.statistics.attacksByType[s.type]=(this.statistics.attacksByType[s.type]||0)+1,this.statistics.attacksBySeverity[s.severity]=(this.statistics.attacksBySeverity[s.severity]||0)+1,this.statistics.attacksBySource[s.source]=(this.statistics.attacksBySource[s.source]||0)+1,this.statistics.attacksByTarget[s.target]=(this.statistics.attacksByTarget[s.target]||0)+1,this.statistics.attacksByIndustry[s.industry]=(this.statistics.attacksByIndustry[s.industry]||0)+1}),this.statistics}getActiveAttacks(){return this.attacks.filter(s=>s.active)}getStatistics(){return this.statistics}startRealTimeUpdates(s=5e3){return this.updateInterval&&clearInterval(this.updateInterval),this.updateInterval=setInterval(()=>{this.attacks=this.attacks.filter((n,a)=>a>=Math.floor(Math.random()*5)||a>=this.attacks.length-30),this.generateAttacks(Math.floor(Math.random()*5)+3),this.updateStatistics(),this.notifyListeners()},s),this}stopRealTimeUpdates(){return this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null),this}addListener(s){return typeof s=="function"&&this.listeners.push(s),this}removeListener(s){return this.listeners=this.listeners.filter(n=>n!==s),this}notifyListeners(){this.listeners.forEach(s=>{try{s({attacks:this.getActiveAttacks(),statistics:this.getStatistics()})}catch(n){console.error("Error in threat intelligence listener:",n)}})}static getInstance(){return U.instance||(U.instance=new U),U.instance}}const Xe=()=>{const{isPremium:x}=Ce(),[s,n]=f.useState([]),[a,t]=f.useState(!0),[l,r]=f.useState("live"),[i,c]=f.useState(!0),[d,N]=f.useState(null),m=5,p="********************************************************************************",T="****************************************************************",h=o=>{switch(o.toLowerCase()){case"ransomware":return e.jsx(le,{className:"text-red-500"});case"ddos":return e.jsx(re,{className:"text-orange-500"});case"phishing":return e.jsx(we,{className:"text-yellow-500"});case"malware":return e.jsx(Le,{className:"text-purple-500"});case"data breach":return e.jsx(Pe,{className:"text-blue-500"});case"apt":case"advanced persistent threat":return e.jsx(Me,{className:"text-pink-500"});case"network attack":return e.jsx(Ee,{className:"text-green-500"});case"unknown":return e.jsx(A,{className:"text-gray-500"});default:return e.jsx(q,{className:"text-blue-500"})}},b=o=>{switch(o.toLowerCase()){case"critical":return"bg-red-900 text-red-200";case"high":return"bg-orange-900 text-orange-200";case"medium":return"bg-yellow-900 text-yellow-200";case"low":return"bg-green-900 text-green-200";default:return"bg-blue-900 text-blue-200"}},S=o=>new Date(o).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"});f.useEffect(()=>{const o=Ae(p),k=Ie(T),I=async()=>{try{c(!0),N(null);const[F,Z]=await Promise.allSettled([o.getBlacklist(80,15).catch(w=>(console.error("AbuseIPDB API error:",w),[])),k.getPulses(5).catch(w=>(console.error("OTX API error:",w),[]))]),oe=F.status==="fulfilled"?F.value:[],de=Z.status==="fulfilled"?Z.value:[];if(oe.length===0&&de.length===0&&F.status==="rejected"&&Z.status==="rejected")throw new Error("Unable to connect to threat intelligence APIs. Using sample data instead.");const me=oe.map(w=>({id:`ip-${w.ipAddress}`,type:"Malicious IP",severity:g(w.abuseConfidenceScore),source:w.countryCode||"Unknown",target:"Multiple",industry:"Various",timestamp:new Date().toISOString(),color:y(g(w.abuseConfidenceScore)),details:{ipAddress:w.ipAddress,confidenceScore:w.abuseConfidenceScore,totalReports:w.totalReports,countryCode:w.countryCode}})),he=de.map(w=>{var ue,xe;let P="Unknown";w.tags&&Array.isArray(w.tags)&&(w.tags.some(L=>L.toLowerCase().includes("ransomware"))?P="Ransomware":w.tags.some(L=>L.toLowerCase().includes("malware"))?P="Malware":w.tags.some(L=>L.toLowerCase().includes("phishing"))?P="Phishing":w.tags.some(L=>L.toLowerCase().includes("ddos"))&&(P="DDoS"));const _=w.adversary?"High":"Medium";return{id:`pulse-${w.id||Math.random().toString(36).substring(2,15)}`,type:P,severity:_,source:w.author_name||"Threat Actor",target:((ue=w.targeted_countries)==null?void 0:ue.join(", "))||"Multiple",industry:((xe=w.industries)==null?void 0:xe.join(", "))||"Various",timestamp:w.created||new Date().toISOString(),color:y(_),details:{name:w.name||"Unknown Threat",description:w.description||"No description available",tags:w.tags||[],references:w.references||[]}}});if(me.length===0&&he.length===0)throw new Error("No threat data available. Please try again later.");{const w=[...me,...he].sort((P,_)=>new Date(_.timestamp)-new Date(P.timestamp));n(w),r("live")}c(!1)}catch(F){console.error("Error fetching threat data:",F),N(F.message||"Failed to fetch live threat data. Please try again."),c(!1),setTimeout(()=>{a&&(N(null),c(!0),I())},1e4)}};I();let J;return a&&(J=setInterval(I,3e4)),()=>{J&&clearInterval(J)}},[a,p,T]);const g=o=>o>=90?"Critical":o>=80?"High":o>=60?"Medium":"Low",y=o=>{switch(o.toLowerCase()){case"critical":return"#ef4444";case"high":return"#f97316";case"medium":return"#eab308";case"low":return"#22c55e";default:return"#3b82f6"}},[j,M]=f.useState(null),[u,v]=f.useState(!1),C=o=>({ransomware:{tactics:["Impact","Execution"],techniques:["T1486: Data Encrypted for Impact","T1490: Inhibit System Recovery"],description:"Ransomware encrypts files and demands payment for decryption keys. It often enters systems through phishing emails or exploiting vulnerabilities.",mitigation:"Maintain offline backups, implement application allowlisting, keep systems patched, and use anti-ransomware solutions.",url:"https://attack.mitre.org/techniques/T1486/"},phishing:{tactics:["Initial Access","Credential Access"],techniques:["T1566: Phishing","T1534: Internal Spearphishing"],description:"Phishing attacks use deceptive emails, messages, or websites to steal credentials or deliver malware by tricking users into taking harmful actions.",mitigation:"Implement email filtering, user awareness training, multi-factor authentication, and disable macros in documents from the internet.",url:"https://attack.mitre.org/techniques/T1566/"},ddos:{tactics:["Impact"],techniques:["T1498: Network Denial of Service","T1499: Endpoint Denial of Service"],description:"DDoS attacks overwhelm services with excessive traffic or requests, making them unavailable to legitimate users.",mitigation:"Use DDoS protection services, implement rate limiting, and configure network infrastructure to handle traffic surges.",url:"https://attack.mitre.org/techniques/T1498/"},malware:{tactics:["Execution","Defense Evasion","Discovery"],techniques:["T1204: User Execution","T1027: Obfuscated Files or Information"],description:"Malware is malicious software designed to damage systems, steal data, or gain unauthorized access to networks.",mitigation:"Use anti-malware solutions, keep systems updated, implement application allowlisting, and practice the principle of least privilege.",url:"https://attack.mitre.org/tactics/TA0002/"},"data breach":{tactics:["Exfiltration","Collection"],techniques:["T1048: Exfiltration Over Alternative Protocol","T1114: Email Collection"],description:"Data breaches involve unauthorized access to sensitive data, often followed by data theft or exposure.",mitigation:"Encrypt sensitive data, implement data loss prevention tools, monitor for unusual data access patterns, and use network segmentation.",url:"https://attack.mitre.org/tactics/TA0010/"},apt:{tactics:["Persistence","Privilege Escalation","Defense Evasion"],techniques:["T1078: Valid Accounts","T1053: Scheduled Task/Job","T1055: Process Injection"],description:"Advanced Persistent Threats are prolonged, targeted attacks by sophisticated threat actors, often nation-states, aiming to maintain long-term access to networks.",mitigation:"Implement defense-in-depth strategies, conduct threat hunting, monitor for unusual behavior, and use advanced endpoint protection.",url:"https://attack.mitre.org/groups/"},"network attack":{tactics:["Lateral Movement","Discovery"],techniques:["T1046: Network Service Scanning","T1021: Remote Services"],description:"Network attacks target network infrastructure and services to gain access, disrupt operations, or move laterally within environments.",mitigation:"Implement network segmentation, use intrusion detection systems, monitor network traffic, and secure remote access services.",url:"https://attack.mitre.org/tactics/TA0008/"}})[o.toLowerCase()]||{tactics:["Multiple"],techniques:["Various techniques may be employed"],description:"This threat type encompasses various attack vectors and methods that may target different aspects of systems and networks.",mitigation:"Implement defense-in-depth strategies, keep systems updated, and follow security best practices appropriate to your environment.",url:"https://attack.mitre.org/"};return e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${a?"bg-green-500 animate-pulse":"bg-gray-500"} mr-2`}),e.jsxs("span",{className:"text-sm text-gray-400",children:[a?"Live Feed":"Paused"," • Real-time Threat Data"]}),e.jsx("button",{className:"ml-2 text-gray-400 hover:text-gray-300",onClick:()=>v(!u),title:"Information about the threat feed",children:e.jsx(Ne,{size:14})})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:`px-2 py-1 text-xs rounded ${a?"bg-red-600 hover:bg-red-700":"bg-green-600 hover:bg-green-700"}`,onClick:()=>t(!a),disabled:i,children:a?"Pause":"Resume"}),e.jsx("button",{className:"px-2 py-1 text-xs rounded bg-blue-600 hover:bg-blue-700",onClick:()=>{a||t(!0)},disabled:i||a,children:"Refresh Now"})]})]}),u&&e.jsxs("div",{className:"mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700 text-sm",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[e.jsx(q,{className:"mr-1 text-blue-400"})," Live Threat Intelligence Feed"]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>v(!1),children:"×"})]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"This feed displays real-time cyber threats detected across the globe. Data is collected from multiple threat intelligence sources including AbuseIPDB and AlienVault OTX. Click on any threat to see detailed information including MITRE ATT&CK framework references and mitigation strategies."}),e.jsx("div",{className:"flex items-center text-xs",children:e.jsxs("a",{href:"https://attack.mitre.org/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 flex items-center",children:["Learn more about MITRE ATT&CK ",e.jsx(ge,{className:"ml-1",size:10})]})})]}),i&&e.jsxs("div",{className:"flex items-center justify-center py-4",children:[e.jsx(z,{className:"animate-spin text-blue-500 mr-2"}),e.jsx("span",{className:"text-gray-300",children:"Fetching live threat data..."})]}),d&&e.jsxs("div",{className:"bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-3 mb-4 text-red-400",children:[e.jsx(A,{className:"inline-block mr-2"}),d]}),e.jsxs("div",{className:"flex-1 flex flex-col md:flex-row gap-4 overflow-hidden",children:[e.jsx("div",{className:`overflow-y-auto space-y-2 pr-1 ${j?"md:w-3/5":"w-full"}`,children:!i&&s.length===0?e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:"No threat data available"}):(x?s:s.slice(0,m)).map(o=>e.jsxs("div",{className:`bg-gray-700 rounded-lg p-3 border-l-4 animate-fadeIn cursor-pointer transition-colors ${j===o?"bg-gray-600":"hover:bg-gray-650"}`,style:{borderLeftColor:o.color},onClick:()=>M(j===o?null:o),children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"mt-0.5 mr-2",children:h(o.type)}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:o.type}),e.jsxs("div",{className:"text-xs text-gray-400 flex items-center",children:[e.jsx(ne,{className:"mr-1",size:10}),e.jsx("span",{className:"mr-1",children:o.source})," → ",e.jsx("span",{className:"ml-1",children:o.target})]})]})]}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsx("span",{className:`text-xs px-1.5 py-0.5 rounded-full ${b(o.severity)}`,children:o.severity}),e.jsx("span",{className:"text-xs text-gray-400",children:S(o.timestamp)})]})]}),e.jsxs("div",{className:"mt-1 text-xs grid grid-cols-1 gap-0.5",children:[o.id.startsWith("ip-")&&e.jsxs("div",{className:"flex flex-wrap",children:[e.jsx("span",{className:"text-gray-400 mr-1",children:"IP:"})," ",o.details.ipAddress," •",e.jsx("span",{className:"text-gray-400 mx-1",children:"Conf:"})," ",o.details.confidenceScore,"% •",e.jsx("span",{className:"text-gray-400 mx-1",children:"Reports:"})," ",o.details.totalReports||0]}),o.id.startsWith("pulse-")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-400 mr-1",children:"Name:"})," ",o.details.name]}),o.details.tags&&o.details.tags.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-0.5",children:[o.details.tags.slice(0,3).map((k,I)=>e.jsx("span",{className:"bg-gray-800 px-1 py-0.5 rounded text-xs",children:k},I)),o.details.tags.length>3&&e.jsxs("span",{className:"bg-gray-800 px-1 py-0.5 rounded text-xs",children:["+",o.details.tags.length-3]})]})]})]})]},o.id))}),j&&e.jsxs("div",{className:"md:w-2/5 bg-gray-800 rounded-lg p-4 overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[h(j.type),e.jsxs("span",{className:"ml-2",children:[j.type," Details"]})]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-300",onClick:()=>M(null),children:"×"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Source"}),e.jsx("div",{className:"font-medium",children:j.source})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Target"}),e.jsx("div",{className:"font-medium",children:j.target})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Severity"}),e.jsx("div",{className:"font-medium",children:j.severity})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Timestamp"}),e.jsx("div",{className:"font-medium",children:new Date(j.timestamp).toLocaleString()})]})]}),j.id.startsWith("ip-")&&e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"IP Details"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"IP Address"}),e.jsx("div",{children:j.details.ipAddress})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Country"}),e.jsx("div",{children:j.details.countryCode||"Unknown"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Confidence Score"}),e.jsxs("div",{children:[j.details.confidenceScore,"%"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Total Reports"}),e.jsx("div",{children:j.details.totalReports||0})]})]})]}),j.id.startsWith("pulse-")&&e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Threat Intelligence"}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Name"}),e.jsx("div",{className:"text-sm",children:j.details.name})]}),j.details.description&&e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Description"}),e.jsx("div",{className:"text-sm",children:j.details.description})]}),j.details.tags&&j.details.tags.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:j.details.tags.map((o,k)=>e.jsx("span",{className:"bg-gray-600 px-2 py-0.5 rounded text-xs",children:o},k))})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"font-medium mb-2 flex items-center",children:[e.jsx(q,{className:"mr-1 text-blue-400"})," MITRE ATT&CK Framework"]}),x?(()=>{const o=C(j.type);return e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg text-sm",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Tactics"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:o.tactics.map((k,I)=>e.jsx("span",{className:"bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full text-xs",children:k},I))})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Techniques"}),e.jsx("div",{className:"flex flex-col gap-1 mt-1",children:o.techniques.map((k,I)=>e.jsx("span",{className:"text-xs",children:k},I))})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Description"}),e.jsx("p",{className:"text-xs mt-1",children:o.description})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Mitigation Strategies"}),e.jsx("p",{className:"text-xs mt-1",children:o.mitigation})]}),e.jsx("div",{className:"text-right",children:e.jsxs("a",{href:o.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 text-xs flex items-center justify-end",children:["Learn more ",e.jsx(ge,{className:"ml-1",size:10})]})})]})})():(()=>{const o=C(j.type);return e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg text-sm",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Tactics"}),e.jsxs("div",{className:"flex flex-wrap gap-1 mt-1",children:[o.tactics.slice(0,1).map((k,I)=>e.jsx("span",{className:"bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full text-xs",children:k},I)),o.tactics.length>1&&e.jsxs("span",{className:"bg-gray-800 text-gray-400 px-2 py-0.5 rounded-full text-xs",children:["+",o.tactics.length-1," more in Premium"]})]})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Description"}),e.jsxs("p",{className:"text-xs mt-1",children:[o.description.substring(0,100),"...",e.jsx("span",{className:"text-gray-500",children:" (Full details in Premium)"})]})]}),e.jsxs("div",{className:"mt-4 bg-amber-900/20 border border-amber-800/30 rounded p-3 text-center",children:[e.jsx(De,{className:"text-amber-400 text-lg mx-auto mb-2"}),e.jsx("p",{className:"text-amber-200 text-xs mb-2",children:"Upgrade to Premium for full MITRE ATT&CK details, mitigation strategies, and advanced threat intelligence"}),e.jsx(pe,{to:"/premium-login",className:"inline-block bg-amber-500 hover:bg-amber-600 text-white text-xs px-3 py-1 rounded transition-colors",children:"Upgrade Now"})]})]})})()]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Recommended Actions"}),x?e.jsxs("ul",{className:"list-disc list-inside text-sm space-y-1 text-gray-300",children:[e.jsxs("li",{children:["Monitor for similar ",j.type.toLowerCase()," activity in your environment"]}),e.jsxs("li",{children:["Review logs for connections to ",j.source]}),e.jsx("li",{children:"Update security controls based on the MITRE ATT&CK techniques"}),e.jsx("li",{children:"Share this intelligence with your security team"}),e.jsx("li",{children:"Implement defense-in-depth strategies to mitigate similar threats"}),e.jsx("li",{children:"Consider threat hunting for related indicators of compromise"})]}):e.jsxs("div",{children:[e.jsxs("ul",{className:"list-disc list-inside text-sm space-y-1 text-gray-300 mb-4",children:[e.jsxs("li",{children:["Monitor for similar ",j.type.toLowerCase()," activity"]}),e.jsx("li",{children:"Review logs for suspicious connections"}),e.jsx("li",{className:"text-gray-500",children:"3 more recommendations in Premium..."})]}),e.jsxs("div",{className:"bg-amber-900/20 border border-amber-800/30 rounded p-3 text-center",children:[e.jsx("p",{className:"text-amber-200 text-xs mb-2",children:"Upgrade to Premium for detailed mitigation strategies and expert recommendations"}),e.jsx(pe,{to:"/premium-login",className:"inline-block bg-amber-500 hover:bg-amber-600 text-white text-xs px-3 py-1 rounded transition-colors",children:"Try Premium"})]})]})]})]})]}),e.jsxs("div",{className:"mt-4 text-xs text-gray-400",children:[e.jsx("strong",{children:"Data Sources:"})," AbuseIPDB API and AlienVault OTX API •",e.jsx("strong",{children:"Update Frequency:"})," ",a?"Every 30 seconds":"Paused"," •",e.jsx("strong",{children:"Last Updated:"})," ",new Date().toLocaleTimeString()]})]})},H={Ransomware:{color:"#ef4444",icon:A},DDoS:{color:"#f97316",icon:re},Malware:{color:"#8b5cf6",icon:le},Phishing:{color:"#3b82f6",icon:we},Infrastructure:{color:"#10b981",icon:ne}},R=[{name:"United States",code:"US",region:"North America",risk:"High"},{name:"Russia",code:"RU",region:"Europe",risk:"Critical"},{name:"China",code:"CN",region:"Asia",risk:"Critical"},{name:"North Korea",code:"KP",region:"Asia",risk:"Critical"},{name:"Iran",code:"IR",region:"Middle East",risk:"High"},{name:"United Kingdom",code:"GB",region:"Europe",risk:"Medium"},{name:"Germany",code:"DE",region:"Europe",risk:"Medium"},{name:"Ukraine",code:"UA",region:"Europe",risk:"High"},{name:"Japan",code:"JP",region:"Asia",risk:"Medium"},{name:"India",code:"IN",region:"Asia",risk:"Medium"},{name:"Brazil",code:"BR",region:"South America",risk:"Medium"},{name:"Australia",code:"AU",region:"Oceania",risk:"Low"},{name:"Canada",code:"CA",region:"North America",risk:"Medium"},{name:"France",code:"FR",region:"Europe",risk:"Medium"},{name:"Italy",code:"IT",region:"Europe",risk:"Medium"},{name:"Spain",code:"ES",region:"Europe",risk:"Low"},{name:"South Korea",code:"KR",region:"Asia",risk:"Medium"},{name:"Mexico",code:"MX",region:"North America",risk:"Medium"},{name:"Indonesia",code:"ID",region:"Asia",risk:"Low"},{name:"Turkey",code:"TR",region:"Middle East",risk:"Medium"},{name:"Saudi Arabia",code:"SA",region:"Middle East",risk:"Medium"},{name:"South Africa",code:"ZA",region:"Africa",risk:"Low"},{name:"Egypt",code:"EG",region:"Africa",risk:"Medium"},{name:"Pakistan",code:"PK",region:"Asia",risk:"High"},{name:"Bangladesh",code:"BD",region:"Asia",risk:"Low"}],W={"North America":"#0077b6","South America":"#00b4d8",Europe:"#90e0ef",Asia:"#0096c7",Africa:"#48cae4","Middle East":"#ade8f4",Oceania:"#caf0f8"},E={Critical:"#ef4444",High:"#f97316",Medium:"#facc15",Low:"#4ade80"},te=(x,s=2e3,n=0)=>{const[a,t]=f.useState(n);return f.useEffect(()=>{let l=null;const r=i=>{l||(l=i);const c=Math.min((i-l)/s,1);t(Math.floor(c*(x-n)+n)),c<1&&window.requestAnimationFrame(r)};window.requestAnimationFrame(r)},[x,s,n]),a},Qe=({data:x})=>{const{darkMode:s}=$(),n=Object.values(x).reduce((t,l)=>t+l,0),a=Object.entries(x).sort(([,t],[,l])=>l-t);return e.jsxs("div",{className:"space-y-4",children:[a.map(([t,l])=>{const r=Math.round(l/n*100);return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:W[t]||"#cbd5e1"}}),e.jsx("span",{className:"font-medium",children:t})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"text-lg font-bold mr-1",children:[r,"%"]}),e.jsxs("span",{className:"text-sm text-gray-400",children:["(",l," attacks)"]})]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2.5 overflow-hidden shadow-inner",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500 ease-out",style:{width:`${r}%`,backgroundColor:W[t]||"#cbd5e1",boxShadow:`0 0 8px ${W[t]||"#cbd5e1"}`}})})]},t)}),e.jsx("div",{className:"mt-6 grid grid-cols-2 gap-3",children:a.slice(0,2).map(([t,l])=>{const r=Math.round(l/n*100);return e.jsxs("div",{className:"bg-gray-700 p-3 rounded-lg text-center",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Highest Threat Region"}),e.jsx("div",{className:"text-xl font-bold",children:t}),e.jsxs("div",{className:"text-sm font-medium",style:{color:W[t]||"#cbd5e1"},children:[r,"% of all attacks"]})]},`stat-${t}`)})})]})},Je=({data:x})=>{const{darkMode:s}=$(),n=f.useRef(null);return f.useEffect(()=>{if(!n.current||!x)return;const a=n.current,t=a.getContext("2d"),l=500,r=500;a.width=l,a.height=r,t.clearRect(0,0,l,r);const i=Math.min(l,r)/2.5,c=l/2,d=r/2,N=Object.values(x).reduce((p,T)=>p+T,0);let m=-Math.PI/2;Object.entries(x).forEach(([p,T])=>{var v;const h=T/N*2*Math.PI,b=m+h,S=((v=H[p])==null?void 0:v.color)||"#cbd5e1";t.fillStyle=S,t.beginPath(),t.moveTo(c,d),t.arc(c,d,i,m,b),t.closePath(),t.fill(),t.strokeStyle=s?"#1f2937":"#ffffff",t.lineWidth=1,t.beginPath(),t.moveTo(c,d),t.arc(c,d,i,m,b),t.closePath(),t.stroke();const g=Math.round(T/N*100),y=m+h/2,j=i*.7,M=c+Math.cos(y)*j,u=d+Math.sin(y)*j;if(h>.1){const C=`${g}%`;t.fillStyle="rgba(0, 0, 0, 0.7)",t.beginPath(),t.arc(M,u,15,0,2*Math.PI),t.fill(),t.fillStyle="#ffffff",t.font="bold 14px Arial",t.textAlign="center",t.textBaseline="middle",t.fillText(C,M,u)}m=b}),t.fillStyle=s?"#1f2937":"#f1f5f9",t.beginPath(),t.arc(c,d,i*.3,0,2*Math.PI),t.fill(),t.strokeStyle=s?"#374151":"#e2e8f0",t.lineWidth=2,t.beginPath(),t.arc(c,d,i*.3,0,2*Math.PI),t.stroke()},[x,s]),e.jsxs("div",{className:"relative w-full h-full",children:[e.jsx("canvas",{ref:n,width:"500",height:"500",className:"w-full h-full",style:{display:"block"}}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none",children:e.jsxs("div",{className:"text-center bg-gray-900 bg-opacity-90 rounded-full p-6 shadow-xl border-2 border-gray-700",children:[e.jsx("div",{className:"text-sm text-gray-300 uppercase tracking-wider font-semibold",children:"TOTAL"}),e.jsx("div",{className:"text-4xl font-bold",children:Object.values(x).reduce((a,t)=>a+t,0)})]})})]})},Ze=({threats:x})=>e.jsx("div",{className:"space-y-2 max-h-[300px] overflow-y-auto pr-2",children:x.map((s,n)=>{var t,l,r;const a=((t=H[s.type])==null?void 0:t.icon)||A;return e.jsxs("div",{className:"bg-gray-800 bg-opacity-50 p-2 rounded border border-gray-700 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3",style:{backgroundColor:`${(l=H[s.type])==null?void 0:l.color}20`},children:e.jsx(a,{style:{color:(r=H[s.type])==null?void 0:r.color}})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("div",{className:"font-medium",children:s.type}),e.jsx("div",{className:"text-xs text-gray-400",children:s.time})]}),e.jsxs("div",{className:"text-sm text-gray-400 flex items-center",children:[e.jsx("span",{children:s.source}),e.jsx("span",{className:"mx-2",children:"→"}),e.jsx("span",{children:s.target})]})]})]},n)})}),et=({countries:x})=>{const s=x.reduce((t,l)=>(t[l.risk]||(t[l.risk]=[]),t[l.risk].push(l),t),{}),n={Critical:0,High:1,Medium:2,Low:3},a={Critical:"Immediate action required",High:"Significant threat activity",Medium:"Moderate threat activity",Low:"Minimal threat activity"};return e.jsx("div",{className:"space-y-6",children:Object.entries(s).sort(([t],[l])=>n[t]-n[l]).map(([t,l])=>e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-5 h-5 rounded-full mr-3 flex items-center justify-center",style:{backgroundColor:E[t]},children:e.jsx("div",{className:"w-2 h-2 rounded-full bg-white"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-bold text-lg",children:[t," Risk"]}),e.jsx("p",{className:"text-sm text-gray-400",children:a[t]})]}),e.jsxs("div",{className:"ml-auto bg-gray-700 px-3 py-1 rounded-full text-sm font-medium",children:[l.length," countries"]})]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:l.map(r=>e.jsxs("div",{className:"px-3 py-2 rounded-lg flex items-center",style:{backgroundColor:`${E[t]}15`,border:`1px solid ${E[t]}40`},children:[e.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:E[t]}}),e.jsx("span",{className:"font-medium",style:{color:E[t]},children:r.name})]},r.code))}),t==="Critical"&&e.jsxs("div",{className:"p-3 rounded-lg text-sm",style:{backgroundColor:`${E[t]}15`,border:`1px solid ${E[t]}40`},children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(A,{className:"mr-2",style:{color:E[t]}}),e.jsx("span",{className:"font-bold",style:{color:E[t]},children:"Critical Alert"})]}),e.jsx("p",{className:"text-gray-300",children:"These countries are currently experiencing significant cyber attack activity and require immediate attention."})]})]},t))})},tt=()=>{const{darkMode:x}=$(),[s,n]=f.useState({totalAttacks:0,blockedAttacks:0,criticalVulnerabilities:0,securityScore:0,attacksByType:{},attacksBySource:{},attacksBySeverity:{},attacksByRegion:{},recentThreats:[]}),a=te(s.totalAttacks),t=te(s.blockedAttacks),l=te(s.securityScore);return f.useEffect(()=>{const r=U.getInstance().initialize(),i=r.getStatistics(),c={};Object.entries(i.attacksBySource||{}).forEach(([m,p])=>{const T=R.find(h=>h.name===m);if(T){const h=T.region;c[h]||(c[h]=0),c[h]+=p}});const d=[];for(let m=0;m<10;m++){const p=Math.floor(Math.random()*R.length);let T=Math.floor(Math.random()*R.length);for(;T===p;)T=Math.floor(Math.random()*R.length);const h=["Ransomware","DDoS","Malware","Phishing","Infrastructure"],b=Math.floor(Math.random()*h.length),S=Math.floor(Math.random()*24),g=Math.floor(Math.random()*60),y=`${S.toString().padStart(2,"0")}:${g.toString().padStart(2,"0")}`;d.push({source:R[p].name,target:R[T].name,type:h[b],time:y})}const N={"00:00":Math.floor(Math.random()*50)+20,"04:00":Math.floor(Math.random()*50)+20,"08:00":Math.floor(Math.random()*50)+20,"12:00":Math.floor(Math.random()*50)+20,"16:00":Math.floor(Math.random()*50)+20,"20:00":Math.floor(Math.random()*50)+20,"24:00":Math.floor(Math.random()*50)+20};return n({totalAttacks:i.totalAttacks||32320,blockedAttacks:Math.floor((i.totalAttacks||32320)*.76),criticalVulnerabilities:Math.floor(Math.random()*5)+1,securityScore:78,attacksByType:i.attacksByType||{Ransomware:35,DDoS:25,Malware:20,Phishing:15,Infrastructure:5},attacksBySource:i.attacksBySource||{},attacksBySeverity:i.attacksBySeverity||{},attacksByRegion:c||{"North America":25,Europe:30,Asia:35,"Middle East":5,"South America":3,Africa:1,Oceania:1},recentThreats:d,threatActivity:N}),r.addListener(m=>{n(p=>({...p,totalAttacks:m.statistics.totalAttacks||32320,blockedAttacks:Math.floor((m.statistics.totalAttacks||32320)*.76),attacksByType:m.statistics.attacksByType||p.attacksByType,attacksBySource:m.statistics.attacksBySource||p.attacksBySource,attacksBySeverity:m.statistics.attacksBySeverity||p.attacksBySeverity}))}),r.startRealTimeUpdates(),()=>{r.removeListener(n),r.stopRealTimeUpdates()}},[]),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-blue-500/20 flex items-center justify-center mr-5",children:e.jsx(q,{className:"text-blue-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Total Attacks"}),e.jsx("div",{className:"text-3xl font-bold",children:a.toLocaleString()})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center mr-5",children:e.jsx(Re,{className:"text-green-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Blocked Attacks"}),e.jsx("div",{className:"text-3xl font-bold",children:t.toLocaleString()})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mr-5",children:e.jsx(A,{className:"text-red-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Critical Vulnerabilities"}),e.jsx("div",{className:"text-3xl font-bold",children:s.criticalVulnerabilities})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 flex items-center shadow-lg border border-gray-700",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-yellow-500/20 flex items-center justify-center mr-5",children:e.jsx(ke,{className:"text-yellow-500 text-2xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-400 uppercase tracking-wider",children:"Security Score"}),e.jsxs("div",{className:"text-3xl font-bold",children:[l,"%"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-3",children:e.jsx(Be,{className:"text-blue-500"})}),"Live Threat Feed"]}),e.jsx("div",{className:"h-[350px]",children:e.jsx(Xe,{})})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-3",children:e.jsx(A,{className:"text-purple-500"})}),"Attack Type Distribution"]}),e.jsx("div",{className:"h-[550px] w-full flex items-center justify-center",children:e.jsx("div",{className:"w-[500px] h-[500px]",children:e.jsx(Je,{data:s.attacksByType})})}),e.jsx("div",{className:"mt-6 grid grid-cols-2 md:grid-cols-3 gap-3",children:Object.entries(s.attacksByType||{}).map(([r,i])=>{var c;return e.jsxs("div",{className:"flex items-center bg-gray-700 p-2 rounded-lg",children:[e.jsx("div",{className:"w-4 h-4 rounded-full mr-2",style:{backgroundColor:((c=H[r])==null?void 0:c.color)||"#cbd5e1"}}),e.jsx("span",{className:"text-sm font-medium",children:r})]},r)})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3",children:e.jsx(ne,{className:"text-green-500"})}),"Regional Threat Distribution"]}),e.jsx(Qe,{data:s.attacksByRegion})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center mr-3",children:e.jsx(A,{className:"text-red-500"})}),"Country Risk Matrix"]}),e.jsx(et,{countries:R})]})]}),e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700",children:[e.jsxs("h3",{className:"text-xl font-bold mb-4 flex items-center",children:[e.jsx("span",{className:"w-8 h-8 rounded-full bg-yellow-500/20 flex items-center justify-center mr-3",children:e.jsx(re,{className:"text-yellow-500"})}),"Live Threat Feed"]}),e.jsx(Ze,{threats:s.recentThreats})]})]})},Q={abuseIPDB:{baseUrl:"https://api.abuseipdb.com/api/v2",apiKey:"********************************************************************************"},otx:{baseUrl:"https://otx.alienvault.com/api/v1",apiKey:"****************************************************************"}},be=Te.create({baseURL:Q.abuseIPDB.baseUrl,headers:{Key:Q.abuseIPDB.apiKey,Accept:"application/json"}}),G=Te.create({baseURL:Q.otx.baseUrl,headers:{"X-OTX-API-KEY":Q.otx.apiKey,"Content-Type":"application/json"}}),D={data:{},timestamp:{},maxAge:15*60*1e3,get(x){const s=Date.now();return this.data[x]&&s-this.timestamp[x]<this.maxAge?this.data[x]:null},set(x,s){this.data[x]=s,this.timestamp[x]=Date.now()}};class st{async getBlacklistedIPs(s=90,n=25){const a=`blacklist_${s}_${n}`,t=D.get(a);if(t)return t;try{const r=(await be.get("/blacklist",{params:{confidenceMinimum:s,limit:n}})).data.data;return D.set(a,r),r}catch(l){throw console.error("Error fetching blacklisted IPs:",l),l}}async checkIP(s){const n=`ip_${s}`,a=D.get(n);if(a)return a;try{const l=(await be.get("/check",{params:{ipAddress:s,maxAgeInDays:90,verbose:!0}})).data.data;return D.set(n,l),l}catch(t){throw console.error(`Error checking IP ${s}:`,t),t}}async getPulses(s=10){const n=`pulses_${s}`,a=D.get(n);if(a)return a;try{const l=(await G.get(`/pulses/subscribed?limit=${s}`)).data.results;return D.set(n,l),l}catch(t){throw console.error("Error fetching OTX pulses:",t),t}}async getPulseIndicators(s){const n=`pulse_indicators_${s}`,a=D.get(n);if(a)return a;try{const l=(await G.get(`/pulses/${s}/indicators`)).data;return D.set(n,l),l}catch(t){throw console.error(`Error fetching indicators for pulse ${s}:`,t),t}}async getDomainInfo(s){const n=`domain_${s}`,a=D.get(n);if(a)return a;try{const l=(await G.get(`/indicators/domain/${s}/general`)).data;return D.set(n,l),l}catch(t){throw console.error(`Error fetching domain info for ${s}:`,t),t}}async searchIndicators(s){const n=`search_${s}`,a=D.get(n);if(a)return a;try{const l=(await G.get(`/search/pulses?q=${encodeURIComponent(s)}`)).data.results;return D.set(n,l),l}catch(t){throw console.error(`Error searching indicators for ${s}:`,t),t}}async getThreatOfTheWeek(){try{const s=await this.getPulses(20);if(!s||s.length===0)throw new Error("No pulses found");const n=s.filter(m=>m.indicator_count>3).sort((m,p)=>{var b,S;const T=(m.indicator_count||0)+(((b=m.references)==null?void 0:b.length)||0)*2;return(p.indicator_count||0)+(((S=p.references)==null?void 0:S.length)||0)*2-T});if(n.length===0)throw new Error("No significant pulses found");const a=n[0],l=(await this.getPulseIndicators(a.id)).reduce((m,p)=>(m[p.type]||(m[p.type]=[]),m[p.type].push(p),m),{}),r=a.tags.filter(m=>m.toLowerCase().includes("malware")||m.toLowerCase().includes("ransomware")||m.toLowerCase().includes("exploit")||m.toLowerCase().includes("phishing")||m.toLowerCase().includes("backdoor")||m.toLowerCase().includes("trojan")),i=[{date:new Date(a.created).toLocaleDateString(),event:"Initial discovery"}];a.modified!==a.created&&i.push({date:new Date(a.modified).toLocaleDateString(),event:"Latest update with new indicators"}),a.references&&a.references.length>0&&i.push({date:new Date(a.created).toLocaleDateString(),event:`Referenced in ${a.references.length} external sources`});const c=[];(l.IPv4||l.IPv6)&&c.push("Monitor for connections to malicious IP addresses"),(l.domain||l.hostname)&&c.push("Implement DNS monitoring for suspicious domains"),(l["FileHash-MD5"]||l["FileHash-SHA1"]||l["FileHash-SHA256"])&&c.push("Deploy hash-based detection for malicious files"),l.URL&&c.push("Filter web traffic for malicious URLs");const d=[];return r.some(m=>m.toLowerCase().includes("ransomware"))&&(d.push("Implement regular backup procedures and test recovery processes"),d.push("Deploy endpoint protection with anti-ransomware capabilities")),r.some(m=>m.toLowerCase().includes("phishing"))&&(d.push("Conduct regular phishing awareness training"),d.push("Implement email filtering solutions")),r.some(m=>m.toLowerCase().includes("exploit"))&&(d.push("Establish a robust patch management process"),d.push("Conduct regular vulnerability scanning")),{title:a.name,summary:a.description,technicalDetails:{attackVectors:r.length>0?r:["Unknown attack vector"],indicators:Object.entries(l).flatMap(([m,p])=>p.slice(0,5).map(T=>({type:m,value:T.indicator}))).slice(0,10),techniques:a.tags.filter(m=>m.match(/^T\d{4}(\.\d{3})?$/)).map(m=>{const p={T1566:{name:"Phishing",description:"Adversaries may send phishing messages to gain access to victim systems."},"T1566.001":{name:"Phishing: Spearphishing Attachment",description:"Adversaries may send spearphishing emails with a malicious attachment in an attempt to gain access to victim systems."},"T1566.002":{name:"Phishing: Spearphishing Link",description:"Adversaries may send spearphishing emails with a malicious link in an attempt to gain access to victim systems."},T1027:{name:"Obfuscated Files or Information",description:"Adversaries may attempt to make an executable or file difficult to discover or analyze by encrypting, encoding, or otherwise obfuscating its contents on the system."},"T1027.002":{name:"Software Packing",description:"Adversaries may use software packing to conceal their code."},"T1027.005":{name:"Indicator Removal from Tools",description:"Adversaries may remove indicators from tools if they believe their malicious tool was detected."},T1059:{name:"Command and Scripting Interpreter",description:"Adversaries may abuse command and script interpreters to execute commands, scripts, or binaries."},"T1059.001":{name:"PowerShell",description:"Adversaries may abuse PowerShell commands and scripts for execution."},"T1059.003":{name:"Windows Command Shell",description:"Adversaries may abuse the Windows command shell for execution."},T1204:{name:"User Execution",description:"Adversaries may rely upon specific actions by a user in order to gain execution."},"T1204.001":{name:"Malicious Link",description:"Adversaries may rely upon a user clicking a malicious link in order to gain execution."},"T1204.002":{name:"Malicious File",description:"Adversaries may rely upon a user opening a malicious file in order to gain execution."}},T=m.split(".")[0],h=p[m]||p[T]||{name:`Unknown Technique (${m})`,description:"No description available for this technique."};return{id:m,name:h.name,description:h.description}})},timeline:i,detectionMethods:c.length>0?c:["No specific detection methods available"],mitigationSteps:d.length>0?d:["No specific mitigation steps available"],learningObjectives:[`Understand ${a.name} attack techniques`,"Identify indicators of compromise associated with this threat","Learn effective detection strategies","Practice incident response for this type of attack"],challengeScenario:{description:`You are a security analyst who has detected potential indicators related to ${a.name}. Your task is to analyze the provided evidence, confirm the infection, and recommend containment steps.`,evidenceFiles:["network_capture.pcap","suspicious_process_logs.txt","email_headers.txt"],questions:["Identify the initial infection vector based on the provided evidence","Determine which systems have been compromised","Recommend immediate containment actions","Create a timeline of the attack based on log evidence"]}}}catch(s){throw console.error("Error generating threat of the week:",s),s}}async getHuntingChallenges(){try{const s=await this.getPulses(30);if(!s||s.length===0)throw new Error("No pulses found");const n=await this.getBlacklistedIPs(80,20),a=[],t=s.filter(i=>i.tags.some(c=>c.toLowerCase().includes("ransomware"))),l=s.filter(i=>i.tags.some(c=>c.toLowerCase().includes("apt"))),r=s.filter(i=>i.tags.some(c=>c.toLowerCase().includes("malware"))&&!i.tags.some(c=>c.toLowerCase().includes("ransomware")));if(t.length>0){const i=t[0];a.push({id:`hunt-ransomware-${Date.now()}`,title:`${i.name} Ransomware Detection`,difficulty:"Beginner",category:"Endpoint",description:`Learn to identify ${i.name} ransomware activity by analyzing endpoint logs and system behavior.`,objectives:["Identify suspicious process execution patterns","Detect file encryption activities","Recognize ransomware persistence mechanisms"],points:100,estimatedTime:"30 minutes",prerequisites:[],unlocked:!0,realData:{pulseId:i.id,tags:i.tags,references:i.references}})}if(n.length>0&&a.push({id:`hunt-c2-${Date.now()}`,title:"C2 Communication Hunt",difficulty:"Intermediate",category:"Network",description:"Hunt for command and control (C2) communications by analyzing network traffic patterns.",objectives:["Identify beaconing patterns in network traffic","Detect domain generation algorithms (DGA)","Recognize encrypted C2 channels"],points:200,estimatedTime:"45 minutes",prerequisites:a.length>0?[a[0].id]:[],unlocked:!0,realData:{ipAddresses:n.slice(0,5).map(i=>i.ipAddress),countries:[...new Set(n.slice(0,5).map(i=>i.countryCode))],confidenceScores:n.slice(0,5).map(i=>i.abuseConfidenceScore)}}),l.length>0){const i=l[0];a.push({id:`hunt-apt-${Date.now()}`,title:`${i.name} APT Hunt`,difficulty:"Advanced",category:"Multi-source",description:`Learn to track sophisticated ${i.name} APT activities across multiple data sources.`,objectives:["Correlate indicators across different log sources","Identify lateral movement techniques","Detect data exfiltration attempts"],points:350,estimatedTime:"60 minutes",prerequisites:a.length>0?[a[0].id]:[],unlocked:!1,realData:{pulseId:i.id,tags:i.tags,references:i.references}})}if(r.length>0){const i=r[0];a.push({id:`hunt-malware-${Date.now()}`,title:`${i.name} Analysis`,difficulty:"Intermediate",category:"Malware",description:`Analyze ${i.name} malware behavior and identify its capabilities.`,objectives:["Identify malware infection vectors","Analyze malware behavior and capabilities","Develop detection strategies"],points:250,estimatedTime:"50 minutes",prerequisites:a.length>0?[a[0].id]:[],unlocked:!1,realData:{pulseId:i.id,tags:i.tags,references:i.references}})}return a}catch(s){throw console.error("Error generating hunting challenges:",s),s}}async getAIAssistantData(){try{const s=await this.getPulses(10);if(!s||s.length===0)throw new Error("No pulses found");const n=[];if(s.length>0){const i=s[0];n.push(`What is ${i.name}?`)}const a=s.flatMap(i=>i.tags.filter(c=>c.match(/^T\d{4}(\.\d{3})?$/)));if(a.length>0){const i=[...new Set(a)];i.length>0&&n.push(`Explain the ${i[0]} technique`)}n.push("What is a command and control (C2) server?","Explain the MITRE ATT&CK framework","How do I detect lateral movement in a network?","What are common indicators of a ransomware attack?","Explain the difference between signature and behavior-based detection");const t=[],l=s.flatMap(i=>i.tags),r=[...new Set(l)];return r.some(i=>i.toLowerCase().includes("ransomware"))&&t.push({title:"Ransomware Defense",description:"Strategies for preventing and responding to ransomware attacks",module:"ransomware-defense"}),r.some(i=>i.toLowerCase().includes("apt"))&&t.push({title:"APT Detection Techniques",description:"Methods for identifying advanced persistent threats",module:"apt-detection"}),r.some(i=>i.toLowerCase().includes("phishing"))&&t.push({title:"Phishing Analysis",description:"Techniques for analyzing and detecting phishing attempts",module:"phishing-analysis"}),t.push({title:"MITRE ATT&CK Framework",description:"Comprehensive threat model and knowledge base",module:"mitre-attack"},{title:"Threat Hunting Fundamentals",description:"Learn proactive threat detection techniques",module:"threat-hunting"}),{suggestedQuestions:n.slice(0,5),relatedLearning:t.slice(0,3),recentThreats:s.slice(0,3).map(i=>({name:i.name,description:i.description,created:i.created,tags:i.tags}))}}catch(s){throw console.error("Error generating AI assistant data:",s),s}}}const ce=new st,at={title:"Emotet Banking Trojan Resurgence",summary:"Emotet has reemerged with enhanced evasion techniques and is targeting financial institutions worldwide. This modular banking trojan now uses advanced polymorphic code to evade detection and has added new modules for credential theft and lateral movement.",technicalDetails:{attackVectors:["Phishing emails with malicious Office documents","Malicious JavaScript downloaders","Exploitation of SMB vulnerabilities for lateral movement"],indicators:[{type:"File Hash (SHA-256)",value:"a2d31d36f5d68a85a7a7d35f6c8964c9a6061c1f46e9a1fcc6586edc49f28920"},{type:"Domain",value:"cdn-storage-service-secure.com"},{type:"IP Address",value:"**************"},{type:"User-Agent",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.55"}],techniques:[{id:"T1566.001",name:"Phishing: Spearphishing Attachment",description:"Emotet is primarily distributed through phishing emails containing malicious Word or Excel documents with macros."},{id:"T1027",name:"Obfuscated Files or Information",description:"Uses heavily obfuscated code and encrypted payloads to evade detection."},{id:"T1210",name:"Exploitation of Remote Services",description:"Leverages EternalBlue and other SMB exploits for lateral movement within networks."}]},timeline:[{date:"2014",event:"Initial discovery as a banking trojan"},{date:"2020 January",event:"Takedown by international law enforcement"},{date:"2021 November",event:"Reemergence with new infrastructure"},{date:"2023 March",event:"Addition of new evasion techniques"},{date:"2023 October",event:"Current campaign targeting financial sector"}],detectionMethods:["Monitor for suspicious PowerShell commands with encoded parameters","Implement email filtering for documents with macros","Deploy network monitoring for unusual SMB traffic","Use memory-based detection for Emotet's in-memory operations"],mitigationSteps:["Disable Office macros in documents from external sources","Implement application allowlisting to prevent unauthorized code execution","Segment networks to limit lateral movement capabilities","Deploy advanced endpoint protection with behavioral detection"],learningObjectives:["Understand polymorphic malware evasion techniques","Identify Emotet's infection chain and persistence mechanisms","Learn effective detection strategies for banking trojans","Practice incident response for Emotet infections"],challengeScenario:{description:"You are a security analyst at a financial institution that has detected potential Emotet indicators. Your task is to analyze the provided evidence, confirm the infection, and recommend containment steps.",evidenceFiles:["network_capture.pcap","suspicious_process_logs.txt","email_headers.txt"],questions:["Identify the initial infection vector based on the provided evidence","Determine which systems have been compromised","Recommend immediate containment actions","Create a timeline of the attack based on log evidence"]}},it=()=>{const{darkMode:x}=$(),[s,n]=f.useState("overview"),[a,t]=f.useState([]),[l,r]=f.useState(!1),[i,c]=f.useState(!0),[d,N]=f.useState(null),[m,p]=f.useState(null);f.useEffect(()=>{(async()=>{try{c(!0),p(null);const S=await ce.getThreatOfTheWeek();N(S)}catch(S){console.error("Error fetching threat of the week:",S),p("Failed to load threat data. Using fallback data."),N(at)}finally{c(!1)}})()},[]);const T=b=>{if(!a.includes(b)){const S=[...a,b];t(S),["overview","technical","detection","challenge"].every(y=>S.includes(y))&&r(!0)}},h=()=>{var b,S;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center mr-3",children:e.jsx(A,{className:"text-red-500 text-xl"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-bold",children:["Threat of the Week: ",d.title]}),e.jsxs("p",{className:"text-gray-400",children:["Updated ",new Date().toLocaleDateString()]})]})]}),e.jsx("p",{className:"text-gray-300 mt-3",children:d.summary})]}),e.jsxs("div",{className:"mb-6 bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Your Learning Progress"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:["overview","technical","detection","challenge"].map(g=>e.jsxs("div",{className:`px-3 py-1 rounded-full text-sm flex items-center ${a.includes(g)?"bg-green-900 text-green-200":"bg-gray-600 text-gray-300"}`,children:[a.includes(g)&&e.jsx(Se,{className:"mr-1"}),g.charAt(0).toUpperCase()+g.slice(1)]},g))}),e.jsxs("div",{className:"mt-3",children:[e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:`${a.length/4*100}%`}})}),e.jsxs("div",{className:"text-right text-sm text-gray-400 mt-1",children:[a.length,"/4 completed"]})]})]}),e.jsxs("div",{className:"flex border-b border-gray-700 mb-6",children:[e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="overview"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>n("overview"),children:[e.jsx($e,{className:"mr-2"}),"Overview"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="technical"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>n("technical"),children:[e.jsx(se,{className:"mr-2"}),"Technical Details"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="detection"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>n("detection"),children:[e.jsx(X,{className:"mr-2"}),"Detection & Mitigation"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${s==="challenge"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>n("challenge"),children:[e.jsx(A,{className:"mr-2"}),"Challenge"]})]}),e.jsxs("div",{className:"mb-6",children:[s==="overview"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Threat Overview"}),e.jsx("p",{className:"text-gray-300 mb-4",children:d.summary}),e.jsx("h4",{className:"font-semibold mb-2 mt-6",children:"Historical Timeline"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute left-3 top-0 bottom-0 w-0.5 bg-gray-600"}),e.jsx("div",{className:"space-y-4 ml-10",children:d.timeline.map((g,y)=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -left-10 mt-1 w-6 h-6 rounded-full bg-blue-900 border-2 border-blue-500 flex items-center justify-center",children:e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-300"})}),e.jsxs("div",{className:"bg-gray-700 p-3 rounded",children:[e.jsx("div",{className:"font-medium",children:g.date}),e.jsx("div",{className:"text-sm text-gray-300",children:g.event})]})]},y))})]}),e.jsx("h4",{className:"font-semibold mb-2 mt-6",children:"Learning Objectives"}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-300",children:d.learningObjectives.map((g,y)=>e.jsx("li",{children:g},y))}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center",onClick:()=>{T("overview"),n("technical")},children:["Continue to Technical Details ",e.jsx(V,{className:"ml-2"})]})})]}),s==="technical"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Technical Analysis"}),e.jsx("h4",{className:"font-semibold mb-2",children:"Attack Vectors"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-300 mb-4",children:d.technicalDetails.attackVectors.map((g,y)=>e.jsx("li",{children:g},y))}),e.jsx("h4",{className:"font-semibold mb-2 mt-6",children:"MITRE ATT&CK Techniques"}),e.jsx("div",{className:"space-y-4 mb-6",children:d.technicalDetails.techniques.map((g,y)=>e.jsxs("div",{className:"bg-gray-700 p-3 rounded",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("div",{className:"font-medium",children:g.name}),e.jsx("div",{className:"text-sm bg-blue-900 text-blue-200 px-2 py-0.5 rounded",children:g.id})]}),e.jsx("div",{className:"text-sm text-gray-300 mt-1",children:g.description})]},y))}),e.jsx("h4",{className:"font-semibold mb-2",children:"Indicators of Compromise (IoCs)"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Type"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Value"})]})}),e.jsx("tbody",{children:d.technicalDetails.indicators.map((g,y)=>e.jsxs("tr",{className:y%2===0?"bg-gray-700 bg-opacity-50":"",children:[e.jsx("td",{className:"px-4 py-2",children:g.type}),e.jsx("td",{className:"px-4 py-2 font-mono",children:g.value})]},y))})]})}),e.jsxs("div",{className:"mt-6 flex justify-between",children:[e.jsx("button",{className:"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center",onClick:()=>n("overview"),children:"Back to Overview"}),e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center",onClick:()=>{T("technical"),n("detection")},children:["Continue to Detection & Mitigation ",e.jsx(V,{className:"ml-2"})]})]})]}),s==="detection"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Detection & Mitigation Strategies"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h4",{className:"font-semibold mb-3 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-900 flex items-center justify-center mr-2",children:e.jsx(B,{className:"text-blue-300"})}),"Detection Methods"]}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-300",children:d.detectionMethods.map((g,y)=>e.jsx("li",{children:g},y))})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsxs("h4",{className:"font-semibold mb-3 flex items-center",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-green-900 flex items-center justify-center mr-2",children:e.jsx(q,{className:"text-green-300"})}),"Mitigation Steps"]}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-300",children:d.mitigationSteps.map((g,y)=>e.jsx("li",{children:g},y))})]})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg mb-6",children:[e.jsx("h4",{className:"font-semibold mb-3",children:"YARA Rule Example"}),e.jsx("pre",{className:"bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto",children:`rule ${d.title.replace(/\s+/g,"_")} {
    meta:
        description = "Detects ${d.title}"
        author = "XCerberus Threat Intelligence"
        date = "${new Date().toISOString().split("T")[0]}"
        hash = "${((b=d.technicalDetails.indicators.find(g=>g.type.includes("Hash")))==null?void 0:b.value)||"unknown"}"
    
    strings:
        $s1 = "${((S=d.technicalDetails.indicators.find(g=>g.type.includes("domain")))==null?void 0:S.value)||d.title}" ascii wide
        
        $code1 = { 83 EC 20 53 55 56 57 8B 7C 24 34 }
        $code2 = { 68 ?? ?? ?? ?? 68 ?? ?? ?? ?? E8 ?? ?? ?? ?? }
        
    condition:
        uint16(0) == 0x5A4D and
        filesize < 2MB and
        (all of ($s*) or all of ($code*))
}`})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg mb-6",children:[e.jsx("h4",{className:"font-semibold mb-3",children:"Sigma Rule Example"}),e.jsx("pre",{className:"bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto",children:`title: ${d.title} Detection
id: ${Math.random().toString(36).substring(2,15)}
status: experimental
description: Detects ${d.title} activity
references:
    - https://xcerberus.com/threat-intelligence/${d.title.toLowerCase().replace(/\s+/g,"-")}
author: XCerberus Threat Intelligence
date: ${new Date().toISOString().split("T")[0]}
tags:
    - attack.execution
    - attack.t1204
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine|contains:
            - 'regsvr32.exe /s /u /i:'
            - 'cmd.exe /c powershell -w hidden -ep bypass -enc'
            - 'wscript.exe //E:'
    condition: selection
falsepositives:
    - Legitimate administrative scripts
level: high`})]}),e.jsxs("div",{className:"mt-6 flex justify-between",children:[e.jsx("button",{className:"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center",onClick:()=>n("technical"),children:"Back to Technical Details"}),e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center",onClick:()=>{T("detection"),n("challenge")},children:["Continue to Challenge ",e.jsx(V,{className:"ml-2"})]})]})]}),s==="challenge"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Hands-on Challenge"}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg mb-6",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Scenario"}),e.jsx("p",{className:"text-gray-300 mb-4",children:d.challengeScenario.description}),e.jsx("h4",{className:"font-semibold mb-2",children:"Available Evidence"}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:d.challengeScenario.evidenceFiles.map((g,y)=>e.jsxs("div",{className:"bg-gray-800 px-3 py-2 rounded flex items-center",children:[e.jsx(fe,{className:"mr-2 text-blue-400"}),e.jsx("span",{className:"font-mono text-sm",children:g})]},y))}),e.jsx("h4",{className:"font-semibold mb-2",children:"Challenge Questions"}),e.jsx("div",{className:"space-y-4",children:d.challengeScenario.questions.map((g,y)=>e.jsxs("div",{className:"bg-gray-800 p-3 rounded",children:[e.jsx("p",{className:"mb-2",children:g}),e.jsx("textarea",{className:"w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm",rows:"2",placeholder:"Enter your answer here..."})]},y))}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{className:"bg-green-600 hover:bg-green-700 px-4 py-2 rounded",onClick:()=>T("challenge"),children:"Submit Answers"})})]}),e.jsx("div",{className:"mt-6 flex justify-between",children:e.jsx("button",{className:"bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded flex items-center",onClick:()=>n("detection"),children:"Back to Detection & Mitigation"})})]})]}),l&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white text-black max-w-2xl w-full p-8 rounded-lg",children:[e.jsxs("div",{className:"border-8 border-double border-gray-300 p-6 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Certificate of Completion"}),e.jsx("p",{className:"text-xl mb-6",children:"This certifies that"}),e.jsx("p",{className:"text-2xl font-bold mb-6",children:"Cybersecurity Student"}),e.jsx("p",{className:"text-xl mb-6",children:"has successfully completed the threat analysis module"}),e.jsx("p",{className:"text-2xl font-bold mb-8",children:d.title}),e.jsxs("div",{className:"flex justify-between items-center mt-12",children:[e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"font-bold",children:"XCerberus Academy"}),e.jsx("p",{children:"Threat Intelligence Division"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{children:new Date().toLocaleDateString()}),e.jsxs("p",{children:["Certificate ID: TOW-",new Date().toISOString().split("T")[0]]})]})]})]}),e.jsxs("div",{className:"mt-6 flex justify-center",children:[e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mr-4",onClick:()=>r(!1),children:"Close"}),e.jsxs("button",{className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center",children:[e.jsx(fe,{className:"mr-2"})," Download Certificate"]})]})]})})]})};return i?e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx(z,{className:"animate-spin text-blue-500 text-4xl mb-4"}),e.jsx("p",{className:"text-gray-300",children:"Fetching real-time threat intelligence data..."})]})}):m?e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsxs("div",{className:"bg-red-900 bg-opacity-20 border border-red-800 rounded-lg p-4 text-red-400 mb-6",children:[e.jsx(A,{className:"inline-block mr-2"}),m]}),d&&h()]}):e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:h()})},ye=[{id:"hunt-1",title:"Ransomware Detection Challenge",difficulty:"Beginner",category:"Endpoint",description:"Learn to identify ransomware activity by analyzing endpoint logs and system behavior.",objectives:["Identify suspicious process execution patterns","Detect file encryption activities","Recognize ransomware persistence mechanisms"],points:100,estimatedTime:"30 minutes",prerequisites:[],unlocked:!0},{id:"hunt-2",title:"C2 Communication Hunt",difficulty:"Intermediate",category:"Network",description:"Hunt for command and control (C2) communications by analyzing network traffic patterns.",objectives:["Identify beaconing patterns in network traffic","Detect domain generation algorithms (DGA)","Recognize encrypted C2 channels"],points:200,estimatedTime:"45 minutes",prerequisites:["hunt-1"],unlocked:!0},{id:"hunt-3",title:"Advanced Persistent Threat (APT) Hunt",difficulty:"Advanced",category:"Multi-source",description:"Learn to track sophisticated APT activities across multiple data sources.",objectives:["Correlate indicators across different log sources","Identify lateral movement techniques","Detect data exfiltration attempts"],points:350,estimatedTime:"60 minutes",prerequisites:["hunt-1","hunt-2"],unlocked:!1},{id:"hunt-4",title:"Supply Chain Compromise Detection",difficulty:"Advanced",category:"Multi-source",description:"Hunt for indicators of supply chain compromises in your environment.",objectives:["Identify suspicious software updates","Detect unusual certificate usage","Recognize compromised vendor access"],points:400,estimatedTime:"75 minutes",prerequisites:["hunt-2","hunt-3"],unlocked:!1}],ve={completedChallenges:["hunt-1"],currentChallenge:"hunt-2",totalPoints:100,badges:[{id:"beginner-hunter",name:"Beginner Threat Hunter",icon:"FaSearch"}],skills:{"Log Analysis":75,"Network Traffic Analysis":40,"MITRE ATT&CK Knowledge":60,"Threat Intelligence":30}},nt=({userLevel:x="beginner"})=>{const{darkMode:s}=$(),[n,a]=f.useState([]),[t,l]=f.useState(null),[r,i]=f.useState(null),[c,d]=f.useState(!0),[N,m]=f.useState("challenges");f.useEffect(()=>{(async()=>{try{d(!0);const b=await ce.getHuntingChallenges();b&&b.length>0?a(b):(console.log("Using fallback challenge data"),a(ye)),l(ve)}catch(b){console.error("Error fetching hunting challenges:",b),a(ye),l(ve)}finally{d(!1)}})()},[]);const p=h=>{const b=n.find(S=>S.id===h);i(b)},T=h=>{var g;const b={...t,completedChallenges:[...t.completedChallenges,h],totalPoints:t.totalPoints+(((g=n.find(y=>y.id===h))==null?void 0:g.points)||0)};l(b),i(null);const S=n.map(y=>!y.unlocked&&y.prerequisites.every(j=>b.completedChallenges.includes(j))?{...y,unlocked:!0}:y);a(S)};return c?e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx(z,{className:"animate-spin text-blue-500 text-4xl mb-4"}),e.jsx("p",{className:"text-gray-300",children:"Loading threat hunting challenges based on real-time intelligence..."})]})}):r?e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("div",{className:"mb-6",children:e.jsx("button",{className:"bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded flex items-center text-sm",onClick:()=>i(null),children:"Back to Challenges"})}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center mr-3 ${r.difficulty==="Beginner"?"bg-blue-500/20":r.difficulty==="Intermediate"?"bg-yellow-500/20":"bg-red-500/20"}`,children:e.jsx(B,{className:r.difficulty==="Beginner"?"text-blue-500":r.difficulty==="Intermediate"?"text-yellow-500":"text-red-500"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:r.title}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx("span",{className:`px-2 py-0.5 rounded mr-2 ${r.difficulty==="Beginner"?"bg-blue-900 text-blue-200":r.difficulty==="Intermediate"?"bg-yellow-900 text-yellow-200":"bg-red-900 text-red-200"}`,children:r.difficulty}),e.jsx("span",{className:"text-gray-400 mr-2",children:r.category}),e.jsx("span",{className:"text-gray-400",children:r.estimatedTime})]})]})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Challenge Description"}),e.jsx("p",{className:"text-gray-300 mb-4",children:r.description}),e.jsx("h3",{className:"font-semibold mb-2",children:"Objectives"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-300 mb-4",children:r.objectives.map((h,b)=>e.jsx("li",{children:h},b))}),r.realData&&e.jsxs("div",{className:"bg-blue-900 bg-opacity-30 border border-blue-800 rounded p-3 mb-4",children:[e.jsx("h4",{className:"font-semibold text-blue-300 mb-2",children:"Based on Real Threat Intelligence"}),r.realData.pulseId&&e.jsxs("div",{className:"text-sm text-gray-300 mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Intelligence Source:"})," AlienVault OTX"]}),r.realData.ipAddresses&&e.jsxs("div",{className:"text-sm text-gray-300 mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Sample Malicious IPs:"})," ",r.realData.ipAddresses.slice(0,2).join(", ")]}),r.realData.tags&&r.realData.tags.length>0&&e.jsxs("div",{className:"text-sm text-gray-300 mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Associated Tags:"})," ",e.jsx("div",{className:"inline-flex flex-wrap gap-1 mt-1",children:r.realData.tags.slice(0,5).map((h,b)=>e.jsx("span",{className:"bg-gray-700 px-2 py-0.5 rounded text-xs",children:h},b))})]}),r.realData.references&&r.realData.references.length>0&&e.jsxs("div",{className:"text-sm text-gray-300",children:[e.jsx("span",{className:"text-gray-400",children:"References:"})," ",r.realData.references.length," available"]})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400",children:[e.jsxs("div",{children:["Points: ",r.points]}),e.jsxs("div",{children:["Estimated Time: ",r.estimatedTime]})]})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Challenge Environment"}),e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg mb-4 font-mono text-sm",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("div",{children:"Threat Hunting Console"}),e.jsx("div",{className:"text-green-400",children:"● Connected"})]}),e.jsxs("div",{className:"border-t border-gray-700 pt-2",children:[e.jsx("p",{className:"text-gray-400 mb-2",children:"// This is a simulated environment. In a real implementation, this would be an interactive console."}),e.jsxs("p",{className:"mb-1",children:["$ ",e.jsx("span",{className:"text-green-400",children:"hunt"})," --init"]}),e.jsx("p",{className:"text-gray-300 mb-1",children:"Initializing hunting environment..."}),e.jsx("p",{className:"text-gray-300 mb-1",children:"Loading data sources..."}),e.jsx("p",{className:"text-green-400 mb-1",children:"Environment ready!"}),e.jsxs("p",{className:"mb-1",children:["$ ",e.jsx("span",{className:"text-green-400",children:"hunt"})," --list-data-sources"]}),e.jsx("p",{className:"text-gray-300 mb-1",children:"Available data sources:"}),e.jsx("p",{className:"text-gray-300 mb-1",children:"- Windows Event Logs (last 7 days)"}),e.jsx("p",{className:"text-gray-300 mb-1",children:"- Network Traffic Captures (last 24 hours)"}),e.jsx("p",{className:"text-gray-300 mb-1",children:"- Endpoint Telemetry (last 7 days)"}),e.jsx("p",{className:"mb-1",children:"$ _"})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{className:"bg-green-600 hover:bg-green-700 px-4 py-2 rounded",onClick:()=>T(r.id),children:"Complete Challenge"})})]})]}):e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Threat Hunting Academy"}),e.jsx("p",{className:"text-gray-400",children:"Learn and practice threat hunting skills with real-world scenarios based on actual threat intelligence."})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-2 flex items-center",children:[e.jsx(Y,{className:"text-yellow-400 mr-2"}),e.jsxs("span",{className:"font-bold",children:[t.totalPoints," XP"]})]})]}),e.jsxs("div",{className:"flex border-b border-gray-700 mb-6",children:[e.jsxs("button",{className:`flex items-center px-4 py-2 ${N==="challenges"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>m("challenges"),children:[e.jsx(X,{className:"mr-2"}),"Challenges"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${N==="skills"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>m("skills"),children:[e.jsx(B,{className:"mr-2"}),"Skills"]}),e.jsxs("button",{className:`flex items-center px-4 py-2 ${N==="badges"?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>m("badges"),children:[e.jsx(Y,{className:"mr-2"}),"Badges"]})]}),N==="challenges"&&e.jsx("div",{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:n.map(h=>e.jsxs("div",{className:`bg-gray-700 p-4 rounded-lg border-l-4 ${t.completedChallenges.includes(h.id)?"border-green-500":h.unlocked?"border-blue-500":"border-gray-600"}`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h3",{className:"font-semibold",children:h.title}),t.completedChallenges.includes(h.id)&&e.jsx(Se,{className:"text-green-500"})]}),e.jsxs("div",{className:"flex items-center text-xs mb-2",children:[e.jsx("span",{className:`px-2 py-0.5 rounded mr-2 ${h.difficulty==="Beginner"?"bg-blue-900 text-blue-200":h.difficulty==="Intermediate"?"bg-yellow-900 text-yellow-200":"bg-red-900 text-red-200"}`,children:h.difficulty}),e.jsx("span",{className:"text-gray-400",children:h.category})]}),e.jsx("p",{className:"text-sm text-gray-300 mb-3 line-clamp-2",children:h.description}),e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsxs("div",{className:"text-gray-400",children:[h.points," XP"]}),h.unlocked?e.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded flex items-center text-xs",onClick:()=>p(h.id),children:[t.completedChallenges.includes(h.id)?"Replay":"Start"," ",e.jsx(V,{className:"ml-1"})]}):e.jsxs("div",{className:"flex items-center text-gray-500",children:[e.jsx(le,{className:"mr-1"})," Locked"]})]})]},h.id))})}),N==="skills"&&e.jsxs("div",{children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Your Hunting Skills"}),e.jsx("div",{className:"space-y-4",children:Object.entries(t.skills).map(([h,b])=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsx("div",{children:h}),e.jsxs("div",{className:"text-sm text-gray-400",children:[b,"/100"]})]}),e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:`h-2.5 rounded-full ${b>=75?"bg-green-600":b>=50?"bg-blue-600":b>=25?"bg-yellow-600":"bg-red-600"}`,style:{width:`${b}%`}})})]},h))})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Recommended Skill Development"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Network Traffic Analysis"}),e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Current Level: 40/100"}),e.jsx("p",{className:"text-sm text-gray-300",children:"Focus on learning to identify C2 traffic patterns and unusual protocol behaviors."}),e.jsx("button",{className:"mt-2 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs",children:"Start Learning Path"})]}),e.jsxs("div",{className:"border-l-4 border-red-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Threat Intelligence"}),e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Current Level: 30/100"}),e.jsx("p",{className:"text-sm text-gray-300",children:"Improve your ability to leverage threat intelligence in hunting activities."}),e.jsx("button",{className:"mt-2 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs",children:"Start Learning Path"})]})]})]})]}),N==="badges"&&e.jsxs("div",{children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Earned Badges"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-blue-900 flex items-center justify-center mx-auto mb-2",children:e.jsx(B,{className:"text-blue-300 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"Beginner Threat Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Completed first hunt"})]})})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Available Badges"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center opacity-50",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2",children:e.jsx(X,{className:"text-gray-500 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"Network Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Complete C2 hunt"})]}),e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center opacity-50",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2",children:e.jsx(A,{className:"text-gray-500 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"APT Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Complete APT hunt"})]}),e.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg text-center opacity-50",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2",children:e.jsx(Y,{className:"text-gray-500 text-2xl"})}),e.jsx("div",{className:"font-medium",children:"Master Hunter"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Complete all hunts"})]})]})]})]})]})},rt="your-gemini-api-key-here",lt="https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",ct=50,ot=60*1e3;let ie=0,je=Date.now();const dt=24*60*60*1e3,mt=new Map,ht=()=>{const x=Date.now();return x-je>ot&&(ie=0,je=x),ie<ct},ut=x=>{const s=mt.get(x);return s&&Date.now()-s.timestamp<dt?s.response:null},xt=async x=>{var s,n,a,t,l;try{const r=ut(x);if(r)return r;if(!ht())throw new Error("Rate limit exceeded. Please try again later.");try{const{data:i,error:c}=await ee.from("ai_responses").select("*").eq("keyword",x.toLowerCase()).limit(1).single();if(!c&&i)return i}catch{console.log("No exact match found, trying fuzzy search")}try{const{data:i,error:c}=await ee.from("ai_responses").select("*").ilike("keyword",`%${x.toLowerCase()}%`).limit(5);if(!c&&i&&i.length>0)return i[0]}catch{console.log("No fuzzy matches found, generating new response")}ie++;try{const i=await fetch(`${lt}?key=${rt}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`You are a cybersecurity expert assistant. Provide a detailed, educational response to the following question about cybersecurity: "${x}"`}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!i.ok)throw new Error(`Gemini API error: ${i.status} ${i.statusText}`);return{content:((l=(t=(a=(n=(s=(await i.json()).candidates)==null?void 0:s[0])==null?void 0:n.content)==null?void 0:a.parts)==null?void 0:t[0])==null?void 0:l.text)||"I apologize, but I was unable to generate a response. Please try again with a different question.",category:"cybersecurity"}}catch(i){console.error("Error calling Gemini API:",i);try{const{data:c,error:d}=await ee.from("ai_responses").select("*").ilike("keyword",`%${x.toLowerCase()}%`).limit(1);if(!d&&c&&c.length>0)return c[0]}catch(c){console.error("Error searching database for fallback response:",c)}throw new Error("Unable to connect to AI service. Please try again later.")}}catch(r){return console.error("Error getting AI response:",r),{content:`I understand you're asking about "${x}". Could you please provide more details or rephrase your question? I want to give you the most accurate and helpful response.`,category:"general"}}},gt=[{role:"assistant",content:"Hello! I'm your AI Threat Intelligence Assistant. I can help you understand cybersecurity concepts, analyze threat data, and learn about attack techniques. What would you like to know about?",timestamp:new Date().toISOString()}],pt=["What is a command and control (C2) server?","Explain the MITRE ATT&CK framework","How do I detect lateral movement in a network?","What are common indicators of a ransomware attack?","Explain the difference between signature and behavior-based detection"],ft={"What is a command and control (C2) server?":`A Command and Control (C2) server is a computer controlled by an attacker or cybercriminal which is used to send commands to systems compromised by malware and receive stolen data from a target network.

Key characteristics of C2 servers include:

1. **Central Control Point**: They serve as the centralized management point for compromised systems (often called a botnet).

2. **Communication Channels**: They use various protocols for communication, including HTTP/HTTPS, DNS, and social media platforms to blend in with normal traffic.

3. **Functions**:
   - Send commands to compromised systems
   - Receive stolen data
   - Distribute malware updates
   - Control lateral movement within networks

4. **Detection Challenges**: Modern C2 infrastructure often uses encryption, legitimate services, and domain fronting to evade detection.

Identifying C2 communication is a critical part of threat hunting and incident response. Common detection methods include looking for unusual outbound connections, beaconing patterns, and anomalous DNS queries.`,"Explain the MITRE ATT&CK framework":`The MITRE ATT&CK (Adversarial Tactics, Techniques, and Common Knowledge) framework is a globally-accessible knowledge base of adversary tactics and techniques based on real-world observations of cyber attacks.

**Key Components:**

1. **Tactics**: The tactical goals that attackers try to achieve during an operation (the "why" of an attack technique).
   - Examples: Initial Access, Execution, Persistence, Privilege Escalation, Defense Evasion

2. **Techniques**: The specific methods used by attackers to achieve tactical goals (the "how").
   - Examples: Phishing (for Initial Access), PowerShell (for Execution)

3. **Sub-techniques**: More specific methods under a technique.
   - Example: Spearphishing Attachment (sub-technique of Phishing)

4. **Procedures**: Specific implementations of techniques used by threat actors.

**Practical Applications:**

- **Threat Intelligence**: Mapping observed attacker behavior to known patterns
- **Detection & Analytics**: Building detection rules based on technique behaviors
- **Red Teaming**: Simulating realistic attack scenarios
- **Gap Analysis**: Assessing security coverage against known attack methods

The framework is organized into different matrices for Enterprise (Windows, macOS, Linux), Mobile, and ICS (Industrial Control Systems) environments.

ATT&CK has become the de facto standard for describing and categorizing adversary behavior and is widely used by security teams for improving defenses based on real attack patterns.`,"How do I detect lateral movement in a network?":`Detecting lateral movement in a network is crucial for identifying attackers who have already gained initial access and are attempting to expand their foothold. Here are effective detection strategies:

**1. Network Traffic Analysis:**
- Monitor for unusual SMB, RDP, WMI, or PowerShell Remoting traffic between endpoints that don't typically communicate
- Look for authentication attempts from unexpected source systems
- Detect unusual port/protocol usage between internal systems

**2. Authentication Monitoring:**
- Track authentication events across systems (Windows Event IDs 4624, 4625, 4648, 4672)
- Look for accounts authenticating to multiple systems in short timeframes
- Monitor for credential dumping activities (Event ID 10 for Mimikatz)

**3. Endpoint Detection:**
- Monitor for new service creation on remote systems
- Track process creation with command-line arguments indicating remote execution
- Look for unusual scheduled task creation

**4. Behavioral Analytics:**
- Establish baselines of normal user and system behavior
- Alert on deviations from typical access patterns
- Use machine learning to identify anomalous authentication sequences

**5. Key Tools and Techniques:**
- Deploy honeypots/honeytoken accounts to detect unauthorized access attempts
- Implement network segmentation to limit lateral movement opportunities
- Use EDR solutions with specific lateral movement detection capabilities

**Common Lateral Movement Techniques to Monitor:**
- Pass-the-Hash/Pass-the-Ticket attacks
- Use of PsExec, WMI, PowerShell remoting
- Remote service creation and exploitation
- Internal port scanning and discovery activities

Effective lateral movement detection requires visibility across multiple data sources and correlation between network and endpoint telemetry.`,"What are common indicators of a ransomware attack?":`**Common Indicators of a Ransomware Attack**

Ransomware attacks typically exhibit several observable indicators across different phases of the attack lifecycle:

**Pre-encryption Indicators:**

1. **Unusual Authentication Activities:**
   - Multiple failed login attempts
   - Authentication from unusual locations/times
   - Sudden privileged account creation

2. **Suspicious Network Traffic:**
   - Communication with known C2 servers
   - Unusual SMB traffic (used for file discovery)
   - Unexpected data transfer patterns

3. **System/Admin Tool Misuse:**
   - Unexpected use of legitimate tools like PsExec, WMI, PowerShell
   - Volume Shadow Copy deletion commands
   - Suspicious registry modifications
   - Disabling of security tools

**Active Encryption Indicators:**

1. **File System Activities:**
   - High CPU/disk usage across multiple systems
   - Rapid file modifications (extensions changing)
   - Access to unusually large numbers of files
   - Creation of ransom notes in multiple directories

2. **Process Behavior:**
   - Unusual process lineage (e.g., Office app spawning cmd.exe)
   - Processes accessing large numbers of files
   - Known ransomware process names or patterns

**Post-encryption Indicators:**

1. **System Evidence:**
   - Changed file extensions (e.g., .encrypted, .locked, .crypted)
   - Ransom notes (often as text files or desktop wallpaper)
   - Inability to open common files
   - Applications failing to start

2. **Communication Evidence:**
   - Tor installation for dark web payment sites
   - Cryptocurrency wallet addresses in ransom notes
   - Communication attempts to payment verification servers

**Detection Best Practices:**
- Deploy file integrity monitoring
- Monitor for mass file type changes
- Implement behavioral analytics for process activity
- Create alerts for known ransomware file operations
- Monitor for encryption command-line parameters

Early detection of these indicators can help organizations contain ransomware before widespread encryption occurs.`,"Explain the difference between signature and behavior-based detection":`**Signature-Based vs. Behavior-Based Detection**

**Signature-Based Detection:**

Signature-based detection identifies threats by matching patterns against known malicious code, file hashes, or specific byte sequences.

**Characteristics:**
- Relies on a database of known threat signatures
- Very effective against known, previously analyzed threats
- Low false positive rate for identified threats
- Fast processing and minimal system impact

**Limitations:**
- Cannot detect zero-day or previously unseen threats
- Easily evaded by polymorphic or obfuscated malware
- Requires constant signature updates
- Ineffective against fileless malware

**Behavior-Based Detection:**

Behavior-based detection identifies threats by monitoring and analyzing the actions and behaviors of programs, processes, or users to identify suspicious or malicious activity patterns.

**Characteristics:**
- Analyzes actions rather than code patterns
- Can detect novel and zero-day threats
- Effective against polymorphic and obfuscated malware
- Provides protection against fileless attacks

**Limitations:**
- Higher false positive potential
- More resource-intensive
- Requires baseline establishment and tuning
- More complex to implement and manage

**Key Differences:**

| Aspect | Signature-Based | Behavior-Based |
|--------|----------------|----------------|
| Detection Method | Pattern matching | Activity analysis |
| Zero-day Detection | Poor | Good |
| Resource Usage | Lower | Higher |
| False Positives | Lower | Higher |
| Evasion Difficulty | Easier to evade | Harder to evade |
| Implementation | Simpler | More complex |

**Modern Approach:**
Today's most effective security solutions combine both approaches:
- Signature-based detection for efficient identification of known threats
- Behavior-based detection for identifying novel attacks and evasive malware
- Machine learning to improve both methods over time

This hybrid approach provides comprehensive protection against both known and emerging threats while balancing performance and accuracy.`},bt=()=>{const{darkMode:x}=$(),[s,n]=f.useState(gt),[a,t]=f.useState(""),[l,r]=f.useState(!1),i=f.useRef(null),[c,d]=f.useState(!1),[N,m]=f.useState(pt),[p,T]=f.useState(null),[h,b]=f.useState(!0),[S,g]=f.useState(null);f.useEffect(()=>{(async()=>{try{b(!0),g(null);const v=await ce.getAIAssistantData();v&&(v.suggestedQuestions&&v.suggestedQuestions.length>0&&m(v.suggestedQuestions),v.relatedLearning&&v.relatedLearning.length>0&&T({relatedLearning:v.relatedLearning,recentThreats:v.recentThreats||[]}))}catch(v){console.error("Error fetching AI assistant data:",v),g("Failed to load threat intelligence data.")}finally{b(!1)}})()},[]),f.useEffect(()=>{var u;(u=i.current)==null||u.scrollIntoView({behavior:"smooth"})},[s]);const y=async(u=a)=>{if(!u.trim())return;const v={role:"user",content:u,timestamp:new Date().toISOString()};n(C=>[...C,v]),t(""),r(!0),d(!0);try{let C=null;for(const[k,I]of Object.entries(ft))if(u.toLowerCase().includes(k.toLowerCase())||k.toLowerCase().includes(u.toLowerCase())){C=I;break}if(!C)try{C=(await xt(u)).content}catch(k){throw console.error("Error getting AI response:",k),new Error("I'm having trouble connecting to my knowledge base right now. Please try again later.")}const o={role:"assistant",content:C,timestamp:new Date().toISOString()};n(k=>[...k,o])}catch(C){console.error("Error processing message:",C);const o={role:"assistant",content:C.message||"I'm sorry, I encountered an error while processing your request. Please try again later.",timestamp:new Date().toISOString(),isError:!0};n(k=>[...k,o]),setTimeout(()=>{const k={role:"system",content:"Would you like to try again?",timestamp:new Date().toISOString(),isRetryPrompt:!0,originalMessage:u};n(I=>[...I,k])},1e3)}finally{r(!1),d(!1)}},j=u=>{y(u)},M=u=>u.split(`
`).map((v,C)=>{if(v=v.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),v.match(/^\d+\.\s/))return e.jsx("li",{className:"ml-5 list-decimal",children:v.replace(/^\d+\.\s/,"")},C);if(v.match(/^-\s/))return e.jsx("li",{className:"ml-5 list-disc",children:v.replace(/^-\s/,"")},C);if(v.match(/^\s*$/))return e.jsx("br",{},C);if(v.match(/^#{1,6}\s/)){const o=v.match(/^(#{1,6})\s/)[1].length,k=v.replace(/^#{1,6}\s/,"");switch(o){case 1:return e.jsx("h1",{className:"text-xl font-bold my-2",children:k},C);case 2:return e.jsx("h2",{className:"text-lg font-bold my-2",children:k},C);case 3:return e.jsx("h3",{className:"text-md font-bold my-2",children:k},C);default:return e.jsx("h4",{className:"font-bold my-1",children:k},C)}}else return e.jsx("p",{dangerouslySetInnerHTML:{__html:v}},C)});return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3",children:e.jsx(ae,{className:"text-blue-500"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"AI Threat Intelligence Assistant"}),e.jsx("p",{className:"text-gray-400",children:"Ask questions about cybersecurity concepts and threats"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 h-[500px] flex flex-col",children:[e.jsxs("div",{className:"flex-1 overflow-y-auto mb-4 space-y-4",children:[s.map((u,v)=>e.jsx("div",{className:`flex ${u.role==="user"?"justify-end":"justify-start"}`,children:u.isRetryPrompt?e.jsxs("div",{className:"w-full bg-gray-900/50 rounded-lg p-3 text-center",children:[e.jsx("p",{className:"text-gray-400 mb-2",children:u.content}),e.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded text-sm",onClick:()=>y(u.originalMessage),children:"Retry"})]}):e.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${u.role==="user"?"bg-blue-600 text-white":u.isError?"bg-red-900/30 border border-red-800 text-gray-200":"bg-gray-800 text-gray-200"}`,children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx("div",{className:`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${u.role==="user"?"bg-blue-700":u.isError?"bg-red-800":"bg-gray-700"}`,children:u.role==="user"?e.jsx(Fe,{className:"text-xs"}):u.isError?e.jsx(A,{className:"text-xs"}):e.jsx(ae,{className:"text-xs"})}),e.jsxs("div",{className:"text-xs opacity-75",children:[u.role==="user"?"You":"AI Assistant"," • ",new Date(u.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),e.jsx("div",{className:"text-sm space-y-1",children:M(u.content)})]})},v)),l&&e.jsx("div",{className:"flex justify-start",children:e.jsx("div",{className:"bg-gray-800 text-gray-200 rounded-lg p-3 max-w-[80%]",children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-400 animate-bounce",style:{animationDelay:"0ms"}}),e.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-400 animate-bounce",style:{animationDelay:"150ms"}}),e.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-400 animate-bounce",style:{animationDelay:"300ms"}})]})})}),e.jsx("div",{ref:i})]}),e.jsx("div",{className:"mt-auto",children:e.jsxs("form",{onSubmit:u=>{u.preventDefault(),y()},className:"flex items-center",children:[e.jsx("input",{type:"text",value:a,onChange:u=>t(u.target.value),placeholder:"Ask about cybersecurity threats, techniques, or concepts...",className:"flex-1 bg-gray-800 border border-gray-600 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:c}),e.jsx("button",{type:"submit",className:`bg-blue-600 hover:bg-blue-700 rounded-r px-4 py-2 flex items-center ${c?"opacity-50 cursor-not-allowed":""}`,disabled:c,children:e.jsx(Ue,{})})]})})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(Ne,{className:"mr-2 text-blue-400"}),"Suggested Questions"]}),h?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(z,{className:"animate-spin text-blue-500"})}):e.jsx("div",{className:"space-y-2",children:N.map((u,v)=>e.jsx("button",{className:"w-full text-left bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm transition-colors",onClick:()=>j(u),disabled:c,children:u},v))}),S&&e.jsxs("div",{className:"text-xs text-red-400 mt-2",children:[e.jsx(A,{className:"inline-block mr-1"}),S]})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(B,{className:"mr-2 text-green-400"}),"Related Learning"]}),h?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(z,{className:"animate-spin text-green-500"})}):p&&p.relatedLearning?e.jsx("div",{className:"space-y-2",children:p.relatedLearning.map((u,v)=>e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:u.title}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:u.description}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",children:"View Module"})]},v))}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:"MITRE ATT&CK Framework"}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Comprehensive threat model and knowledge base"}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",children:"View Module"})]}),e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:"Threat Hunting Fundamentals"}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Learn proactive threat detection techniques"}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",children:"View Module"})]})]})]}),p&&p.recentThreats&&p.recentThreats.length>0&&e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(A,{className:"mr-2 text-red-400"}),"Recent Threats"]}),e.jsx("div",{className:"space-y-3",children:p.recentThreats.map((u,v)=>e.jsxs("div",{className:"bg-gray-800 rounded p-3",children:[e.jsx("h4",{className:"font-medium text-sm",children:u.name}),e.jsx("p",{className:"text-xs text-gray-400 mb-1",children:new Date(u.created).toLocaleDateString()}),e.jsxs("div",{className:"flex flex-wrap gap-1 mb-2",children:[u.tags.slice(0,3).map((C,o)=>e.jsx("span",{className:"bg-gray-700 px-2 py-0.5 rounded text-xs",children:C},o)),u.tags.length>3&&e.jsxs("span",{className:"bg-gray-700 px-2 py-0.5 rounded text-xs",children:["+",u.tags.length-3," more"]})]}),e.jsx("button",{className:"text-xs bg-blue-600 hover:bg-blue-700 rounded px-2 py-1",onClick:()=>j(`What is ${u.name}?`),children:"Ask About This Threat"})]},v))})]}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold mb-3 flex items-center",children:[e.jsx(se,{className:"mr-2 text-yellow-400"}),"Threat Analysis Tools"]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("button",{className:"w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between",children:[e.jsx("span",{children:"IOC Analyzer"}),e.jsx(A,{className:"text-yellow-400"})]}),e.jsxs("button",{className:"w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between",children:[e.jsx("span",{children:"YARA Rule Generator"}),e.jsx(se,{className:"text-blue-400"})]}),e.jsxs("button",{className:"w-full bg-gray-800 hover:bg-gray-600 rounded p-2 text-sm flex items-center justify-between",children:[e.jsx("span",{children:"Threat Intelligence Search"}),e.jsx(B,{className:"text-green-400"})]})]})]})]})]})]})},jt=()=>{const{darkMode:x}=$(),[s,n]=f.useState("dashboard"),[a,t]=f.useState("beginner"),[l,r]=f.useState(!0);f.useEffect(()=>{const N=setTimeout(()=>{r(!1)},1500);return()=>clearTimeout(N)},[]);const c=[{id:"dashboard",label:"Threat Dashboard",icon:qe,level:"all"},{id:"threat-of-week",label:"Threat of the Week",icon:Oe,level:"all"},{id:"hunting-academy",label:"Hunting Academy",icon:Y,level:"all"},{id:"ai-assistant",label:"AI Threat Assistant",icon:ae,level:"all"},{id:"analytics",label:"Advanced Analytics",icon:ke,level:"intermediate"},{id:"learning-path",label:"Learning Path",icon:He,level:"all"},{id:"practice-lab",label:"Practice Lab",icon:X,level:"intermediate"}].filter(N=>N.level==="all"||N.level==="intermediate"&&(a==="intermediate"||a==="advanced")||N.level==="advanced"&&a==="advanced"),d=()=>{if(l)return e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})});switch(s){case"dashboard":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Cyber Threat Intelligence Dashboard"}),e.jsxs("p",{className:"text-gray-400",children:["Comprehensive visualization of global cyber threat landscape and attack patterns.",e.jsx("span",{className:"ml-2 text-blue-400",children:"Learn as you explore real-world threat data."})]})]})}),e.jsx(tt,{})]});case"threat-of-week":return e.jsx(it,{});case"hunting-academy":return e.jsx(nt,{userLevel:a});case"ai-assistant":return e.jsx(bt,{});case"analytics":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Advanced Threat Analytics"}),e.jsxs("p",{className:"text-gray-400",children:["In-depth analysis of threat data from multiple intelligence sources.",e.jsx("span",{className:"ml-2 text-blue-400",children:"For intermediate and advanced cybersecurity students."})]})]})}),e.jsx(_e,{})]});case"learning-path":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Personalized Learning Path"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Customized cybersecurity learning recommendations based on current threat landscape and your progress."}),e.jsxs("div",{className:"bg-gray-700 rounded-lg p-4 mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold mb-3",children:["Your Current Level: ",a.charAt(0).toUpperCase()+a.slice(1)]}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:a==="beginner"?"25%":a==="intermediate"?"65%":"90%"}})}),e.jsx("span",{className:"ml-2 text-sm text-gray-400",children:a==="beginner"?"25%":a==="intermediate"?"65%":"90%"})]}),e.jsxs("div",{className:"flex space-x-2 mb-6",children:[e.jsx("button",{className:`px-3 py-1 rounded ${a==="beginner"?"bg-blue-600":"bg-gray-600"}`,onClick:()=>t("beginner"),children:"Beginner"}),e.jsx("button",{className:`px-3 py-1 rounded ${a==="intermediate"?"bg-blue-600":"bg-gray-600"}`,onClick:()=>t("intermediate"),children:"Intermediate"}),e.jsx("button",{className:`px-3 py-1 rounded ${a==="advanced"?"bg-blue-600":"bg-gray-600"}`,onClick:()=>t("advanced"),children:"Advanced"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:"Based on current threat trends and your progress, we recommend focusing on these skills:"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"border-l-4 border-blue-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Malware Analysis Fundamentals"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Related to 43% increase in polymorphic malware attacks"})]}),e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Network Traffic Analysis"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Critical for detecting C2 communications in recent attacks"})]}),e.jsxs("div",{className:"border-l-4 border-green-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"OSINT Techniques"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Valuable for early threat detection and reconnaissance"})]})]})]})]});case"practice-lab":return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Hands-on Practice Lab"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Apply your knowledge in realistic scenarios based on actual threat intelligence."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Available Scenarios"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"border-l-4 border-blue-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Ransomware Incident Response"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Difficulty: Intermediate"}),e.jsx("button",{className:"mt-2 px-3 py-1 bg-blue-600 rounded text-sm",children:"Start Scenario"})]}),e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"APT Detection Challenge"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Difficulty: Advanced"}),e.jsx("button",{className:"mt-2 px-3 py-1 bg-blue-600 rounded text-sm",children:"Start Scenario"})]}),e.jsxs("div",{className:"border-l-4 border-red-500 pl-3 py-1",children:[e.jsx("div",{className:"font-medium",children:"Supply Chain Attack Analysis"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Difficulty: Advanced"}),e.jsx("button",{className:"mt-2 px-3 py-1 bg-blue-600 rounded text-sm",children:"Start Scenario"})]})]})]}),e.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Your Progress"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{children:"Completed Scenarios"}),e.jsx("div",{className:"font-semibold",children:"3/12"})]}),e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:"25%"}})}),e.jsxs("div",{className:"flex justify-between items-center mt-4",children:[e.jsx("div",{children:"Earned Badges"}),e.jsx("div",{className:"font-semibold",children:"2"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center",children:e.jsx(q,{className:"text-blue-500"})}),e.jsx("div",{className:"w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(ze,{className:"text-green-500"})})]})]})]})]})]});default:return e.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Coming Soon"}),e.jsx("p",{className:"text-gray-400",children:"This feature is currently under development. Check back soon!"})]})}};return e.jsxs("div",{className:`p-6 ${x?"text-white":"text-gray-900"}`,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Security Insights & Learning Hub"}),e.jsx("p",{className:"text-gray-400",children:"Explore real-world threat intelligence and enhance your cybersecurity skills through interactive learning experiences."})]}),e.jsxs("div",{className:"mb-6 bg-gray-800 rounded-lg p-4 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-400 mr-2",children:"Experience Level:"}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${a==="beginner"?"bg-blue-900 text-blue-200":a==="intermediate"?"bg-yellow-900 text-yellow-200":"bg-green-900 text-green-200"}`,children:a.charAt(0).toUpperCase()+a.slice(1)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm text-gray-400 mr-2",children:"Quick Search:"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search threats, techniques...",className:"bg-gray-700 border border-gray-600 rounded pl-8 pr-4 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.jsx(B,{className:"absolute left-2 top-2 text-gray-400"})]})]})]}),e.jsx("div",{className:"flex flex-wrap border-b border-gray-700 mb-6",children:c.map(N=>e.jsxs("button",{className:`flex items-center px-4 py-2 ${s===N.id?"border-b-2 border-blue-500 text-blue-500":"text-gray-400 hover:text-gray-200"}`,onClick:()=>n(N.id),children:[e.jsx(N.icon,{className:"mr-2"}),N.label]},N.id))}),d()]})};export{jt as default};
