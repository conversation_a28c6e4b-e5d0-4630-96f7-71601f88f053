import{w as _,r as c,v as f,j as e,br as P,N as x,x as F,J as M,aO as T,aJ as u}from"./index-CVvVjHWF.js";const q=()=>{const{user:l}=_(),[b,h]=c.useState(!0),[n,y]=c.useState(null),[d,v]=c.useState("nist"),[m,j]=c.useState("security+"),N=[{id:"nist",name:"NIST Cybersecurity Framework",icon:e.jsx(x,{})},{id:"mitre",name:"MITRE ATT&CK",icon:e.jsx(x,{})},{id:"cis",name:"CIS Controls",icon:e.jsx(x,{})}],C=[{id:"security+",name:"CompTIA Security+",icon:e.jsx(u,{})},{id:"ceh",name:"Certified Ethical Hacker (CEH)",icon:e.jsx(u,{})},{id:"cissp",name:"<PERSON>IS<PERSON>",icon:e.jsx(u,{})},{id:"oscp",name:"Offensive Security OSCP",icon:e.jsx(u,{})}];c.useEffect(()=>{l&&S()},[l]),c.useEffect(()=>{l&&n&&k()},[d,m]);const S=async()=>{try{h(!0);const{data:t,error:i}=await f.from("user_module_progress").select(`
          module_id,
          is_completed,
          modules:learning_modules(
            id,
            title,
            skills(id, name, category)
          )
        `).eq("user_id",l.id).eq("is_completed",!0);if(i)throw i;const s={};t.forEach(a=>{a.is_completed&&a.modules.skills&&a.modules.skills.forEach(r=>{s[r.category]||(s[r.category]=[]),s[r.category].find(o=>o.id===r.id)||s[r.category].push(r)})}),y({completedSkills:s}),await k(),h(!1)}catch(t){console.error("Error fetching skill data:",t),h(!1)}},k=async()=>{try{const{data:t,error:i}=await f.from("framework_skill_mappings").select("*").eq("framework_id",d);if(i)throw i;const{data:s,error:a}=await f.from("certification_skill_mappings").select("*").eq("certification_id",m);if(a)throw a;y(r=>({...r,frameworkData:t,certificationData:s}))}catch(t){console.error("Error fetching framework data:",t)}},D=t=>{if(!n||!n.frameworkData)return 0;const i=n.frameworkData.filter(a=>a.category_id===t);return i.length?i.filter(a=>{var p;const r=a.skill_category,o=a.skill_id;return(p=n.completedSkills[r])==null?void 0:p.some(A=>A.id===o)}).length/i.length*100:0},E=()=>{if(!n||!n.certificationData)return 0;const t=n.certificationData.length;return t?n.certificationData.filter(s=>{var o;const a=s.skill_category,r=s.skill_id;return(o=n.completedSkills[a])==null?void 0:o.some(p=>p.id===r)}).length/t*100:0};if(b)return e.jsx(P,{text:"Loading skill data..."});const I={nist:[{id:"identify",name:"Identify",description:"Develop organizational understanding to manage cybersecurity risk"},{id:"protect",name:"Protect",description:"Develop and implement appropriate safeguards"},{id:"detect",name:"Detect",description:"Develop and implement activities to identify cybersecurity events"},{id:"respond",name:"Respond",description:"Develop and implement activities to take action on detected events"},{id:"recover",name:"Recover",description:"Develop and implement activities to maintain resilience"}],mitre:[{id:"reconnaissance",name:"Reconnaissance",description:"Gathering information to plan future adversary operations"},{id:"resource-development",name:"Resource Development",description:"Establishing resources to support operations"},{id:"initial-access",name:"Initial Access",description:"Getting into your network"},{id:"execution",name:"Execution",description:"Running malicious code"},{id:"persistence",name:"Persistence",description:"Maintaining access"},{id:"privilege-escalation",name:"Privilege Escalation",description:"Gaining higher-level permissions"},{id:"defense-evasion",name:"Defense Evasion",description:"Avoiding detection"},{id:"credential-access",name:"Credential Access",description:"Stealing account names and passwords"},{id:"discovery",name:"Discovery",description:"Understanding the environment"},{id:"lateral-movement",name:"Lateral Movement",description:"Moving through the environment"},{id:"collection",name:"Collection",description:"Gathering data of interest"},{id:"exfiltration",name:"Exfiltration",description:"Stealing data"},{id:"impact",name:"Impact",description:"Manipulate, interrupt, or destroy systems and data"}],cis:[{id:"basic",name:"Basic CIS Controls",description:"Fundamental cybersecurity actions"},{id:"foundational",name:"Foundational CIS Controls",description:"Technical best practices"},{id:"organizational",name:"Organizational CIS Controls",description:"Management, policy and training"}]}[d]||[],g=E(),w={"security+":{domains:[{name:"Attacks, Threats, and Vulnerabilities",weight:"24%"},{name:"Architecture and Design",weight:"21%"},{name:"Implementation",weight:"25%"},{name:"Operations and Incident Response",weight:"16%"},{name:"Governance, Risk, and Compliance",weight:"14%"}],description:"CompTIA Security+ is a global certification that validates the baseline skills necessary to perform core security functions and pursue an IT security career."},ceh:{domains:[{name:"Information Security and Ethical Hacking",weight:"6%"},{name:"Reconnaissance Techniques",weight:"22%"},{name:"System Hacking",weight:"14%"},{name:"Network & Perimeter Hacking",weight:"17%"},{name:"Web Application Hacking",weight:"16%"},{name:"Wireless Network Hacking",weight:"6%"},{name:"Mobile Platform, IoT & OT Hacking",weight:"8%"},{name:"Cloud Computing",weight:"6%"},{name:"Cryptography",weight:"5%"}],description:"Certified Ethical Hacker (CEH) is a qualification obtained by demonstrating knowledge of assessing the security of computer systems by looking for weaknesses and vulnerabilities."},cissp:{domains:[{name:"Security and Risk Management",weight:"15%"},{name:"Asset Security",weight:"10%"},{name:"Security Architecture and Engineering",weight:"13%"},{name:"Communication and Network Security",weight:"13%"},{name:"Identity and Access Management",weight:"13%"},{name:"Security Assessment and Testing",weight:"12%"},{name:"Security Operations",weight:"13%"},{name:"Software Development Security",weight:"11%"}],description:"CISSP covers critical topics in security today, including cloud security, mobile security, and application development security."},oscp:{domains:[{name:"Information Gathering & Vulnerability Identification",weight:"20%"},{name:"Client-side Attacks",weight:"10%"},{name:"Web Application Attacks",weight:"20%"},{name:"Password Attacks",weight:"10%"},{name:"Windows & Linux Privilege Escalation",weight:"15%"},{name:"Post-exploitation & Maintaining Access",weight:"15%"},{name:"Reporting & Documentation",weight:"10%"}],description:"Offensive Security Certified Professional (OSCP) is a hands-on penetration testing certification that requires candidates to pass a 24-hour practical exam."}}[m]||{domains:[],description:""};return e.jsxs("div",{className:"max-w-6xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-white mb-2",children:"Skills & Certification Mapping"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Track your skills against industry frameworks and certification requirements"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 p-6",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center",children:[e.jsx(x,{className:"mr-2 text-blue-500"}),"Industry Framework Alignment"]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Select Framework"}),e.jsx("div",{className:"flex space-x-3 mb-4 overflow-x-auto pb-2",children:N.map(t=>e.jsxs("button",{onClick:()=>v(t.id),className:`flex items-center px-4 py-2 rounded-lg whitespace-nowrap ${d===t.id?"bg-blue-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"mr-2",children:t.icon}),e.jsx("span",{children:t.name})]},t.id))})]}),e.jsx("div",{className:"space-y-4",children:I.map(t=>{const i=D(t.id);return e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 dark:text-white",children:t.name}),e.jsxs("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${i>=75?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":i>=50?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}`,children:[Math.round(i),"% Coverage"]})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:t.description}),e.jsx("div",{className:"h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{className:`h-full rounded-full ${i>=75?"bg-green-500":i>=50?"bg-yellow-500":"bg-red-500"}`,style:{width:`${i}%`}})})]},t.id)})})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center",children:[e.jsx(F,{className:"mr-2 text-purple-500"}),"Certification Progress"]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Select Certification"}),e.jsx("div",{className:"flex space-x-3 mb-4 overflow-x-auto pb-2",children:C.map(t=>e.jsxs("button",{onClick:()=>j(t.id),className:`flex items-center px-4 py-2 rounded-lg whitespace-nowrap ${m===t.id?"bg-purple-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"mr-2",children:t.icon}),e.jsx("span",{children:t.name})]},t.id))})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-md font-medium text-gray-800 dark:text-white",children:"Overall Progress"}),e.jsxs("span",{className:"text-sm font-semibold text-gray-800 dark:text-white",children:[Math.round(g),"%"]})]}),e.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mb-2",children:e.jsx("div",{className:`h-full rounded-full ${g>=80?"bg-green-500":g>=50?"bg-yellow-500":"bg-red-500"}`,style:{width:`${g}%`}})}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-4",children:w.description}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsxs("a",{href:"#",className:"text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 flex items-center",onClick:t=>{t.preventDefault()},children:["View recommended courses ",e.jsx(FaArrowRight,{className:"ml-1"})]})})]}),e.jsxs("div",{children:[e.jsxs("h4",{className:"text-md font-medium text-gray-800 dark:text-white mb-3 flex items-center",children:[e.jsx(M,{className:"mr-2 text-blue-500"}),"Exam Domains"]}),e.jsx("div",{className:"space-y-3",children:w.domains.map((t,i)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:t.name}),e.jsx("span",{className:"text-xs font-medium text-gray-500 dark:text-gray-400",children:t.weight})]},i))}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300",children:["Learn more about this certification ",e.jsx(T,{className:"ml-1"})]})})]})]})]})]})};export{q as default};
