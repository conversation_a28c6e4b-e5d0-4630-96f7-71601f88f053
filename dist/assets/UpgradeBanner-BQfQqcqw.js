import{j as e,m as x,L as p,D as m,aR as b,z as g}from"./index-CVvVjHWF.js";const f={primary:"bg-[#88cc14] hover:bg-[#7ab811] text-black",secondary:"bg-[#1A1F35] hover:bg-[#252D4A] text-white",outline:"bg-transparent border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10",ghost:"bg-transparent hover:bg-gray-800/50 text-[#88cc14]",danger:"bg-red-500 hover:bg-red-600 text-white",dark:"bg-[#0B1120] hover:bg-gray-900 text-white border border-gray-700"},j={sm:"py-1 px-3 text-sm",md:"py-2 px-4",lg:"py-3 px-6 text-lg"};function y({children:t,variant:s="primary",size:i="md",to:a,className:o="",icon:r,disabled:n=!1,onClick:u,type:h="button",...l}){const c=`font-bold rounded-lg transition-all duration-300 flex items-center justify-center gap-2 relative overflow-hidden ${f[s]} ${j[i]} ${o}`,d=e.jsxs(e.Fragment,{children:[r&&e.jsx("span",{className:"text-lg",children:r}),e.jsx("span",{className:"relative z-10",children:t}),s==="primary"||s==="secondary"?e.jsx(x.span,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-500",initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.8,ease:"easeInOut"}}):null]});return a?e.jsx(p,{to:a,className:`${c} group`,...l,children:d}):e.jsx("button",{type:h,className:`${c} group ${n?"opacity-50 cursor-not-allowed":""}`,onClick:u,disabled:n,...l,children:d})}const w=({targetTier:t="premium",message:s,showClose:i=!1,onClose:a})=>{const{tierFeatures:o}=m(),r=o[t];if(!r)return null;const n=`Upgrade to ${t.charAt(0).toUpperCase()+t.slice(1)} for full access to all features`;return e.jsxs(x.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-[#1A1F35] border border-[#88cc14]/30 rounded-lg p-4 mb-6 relative overflow-hidden",children:[e.jsx("div",{className:"absolute -top-10 -right-10 w-40 h-40 bg-[#88cc14]/10 rounded-full blur-xl"}),e.jsx("div",{className:"absolute -bottom-10 -left-10 w-40 h-40 bg-[#88cc14]/5 rounded-full blur-xl"}),e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4 relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:e.jsx(b,{className:"text-[#88cc14] text-xl"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-bold text-white",children:[t.charAt(0).toUpperCase()+t.slice(1)," Subscription"]}),e.jsx("p",{className:"text-gray-300 text-sm",children:s||n})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("span",{className:"text-white font-bold",children:["$",r.price]}),e.jsx(p,{to:"/pricing",children:e.jsx(y,{variant:"primary",size:"sm",icon:e.jsx(g,{}),iconPosition:"right",children:"Upgrade Now"})})]})]}),i&&e.jsx("button",{onClick:a,className:"absolute top-2 right-2 text-gray-400 hover:text-white","aria-label":"Close",children:"×"})]})};export{w as U};
