const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CVvVjHWF.js","assets/index-Ct6XYr8p.css"])))=>i.map(i=>d[i]);
import{r as y,u as R,bI as B,j as e,E as F,m as P,C as D,b5 as _,b6 as O,D as I,J,$ as V,am as E,_ as L,N as U}from"./index-CVvVjHWF.js";const g=({type:r="line",data:w=[],labels:u=[],title:m="",height:v=200,colors:A=["#3B82F6","#10B981","#8B5CF6","#F59E0B","#EF4444"],showLegend:M=!0,showGrid:f=!0,animate:i=!0,loading:k=!1,error:b=null,options:p={}})=>{var l,c;const C=y.useRef(null),t=y.useRef(null),{darkMode:x}=R(),a=x?["rgba(59, 130, 246, 0.8)","rgba(16, 185, 129, 0.8)","rgba(249, 115, 22, 0.8)","rgba(139, 92, 246, 0.8)"]:["rgba(37, 99, 235, 0.8)","rgba(5, 150, 105, 0.8)","rgba(234, 88, 12, 0.8)","rgba(124, 58, 237, 0.8)"],j=x?"rgba(229, 231, 235, 0.8)":"rgba(31, 41, 55, 0.8)",s=x?"rgba(75, 85, 99, 0.2)":"rgba(209, 213, 219, 0.5)";y.useEffect(()=>{if(k||b||!w.length)return;t.current&&t.current.destroy();const n=C.current;if(!n)return;const o=n.getContext("2d"),d=w.map(($,T)=>{const N=a[T%a.length];return{label:`Dataset ${T+1}`,data:$,backgroundColor:r==="line"?"transparent":N,borderColor:N,borderWidth:2,tension:.4,pointBackgroundColor:N,pointRadius:3,pointHoverRadius:5,fill:r==="line"?{target:"origin",above:N.replace("0.8","0.1")}:void 0}}),S={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!!m,text:m,color:j,font:{size:16,weight:"bold"},padding:{top:10,bottom:20}},legend:{display:M&&w.length>1,position:"top",labels:{color:j,usePointStyle:!0,padding:15}},tooltip:{backgroundColor:x?"rgba(17, 24, 39, 0.9)":"rgba(255, 255, 255, 0.9)",titleColor:x?"rgba(229, 231, 235, 1)":"rgba(31, 41, 55, 1)",bodyColor:x?"rgba(209, 213, 219, 1)":"rgba(55, 65, 81, 1)",borderColor:x?"rgba(75, 85, 99, 0.2)":"rgba(209, 213, 219, 0.5)",borderWidth:1,padding:10,cornerRadius:6,displayColors:!0,usePointStyle:!0,boxPadding:4}},scales:r==="pie"||r==="doughnut"?void 0:{x:{grid:{color:s,drawBorder:!1,drawTicks:!1,display:f},ticks:{color:j,padding:8}},y:{grid:{color:s,drawBorder:!1,drawTicks:!1,display:f},ticks:{color:j,padding:8},beginAtZero:!0}},animation:{duration:i?1e3:0,easing:"easeOutQuart"}};return t.current=new B(o,{type:r,data:{labels:u,datasets:d},options:{...S,...p}}),()=>{t.current&&t.current.destroy()}},[r,w,u,m,x,M,f,i,k,b,p,a,j,s]);const h=()=>{switch(r){case"line":return e.jsx(D,{className:"text-blue-500"});case"bar":return e.jsx(O,{className:"text-green-500"});case"pie":return e.jsx(_,{className:"text-purple-500"});default:return e.jsx(D,{className:"text-blue-500"})}};return k?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${v}px`},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):b?e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${v}px`},children:e.jsxs("div",{className:"text-center text-red-500 dark:text-red-400",children:[e.jsx(F,{className:"mx-auto text-2xl mb-2"}),e.jsx("p",{className:"text-sm",children:b})]})}):w.length?e.jsxs(P.div,{initial:i?{opacity:0,y:20}:!1,animate:i?{opacity:1,y:0}:!1,transition:{duration:.3},className:"w-full h-full",children:[m&&!((c=(l=p==null?void 0:p.plugins)==null?void 0:l.title)!=null&&c.display)&&e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"mr-2",children:h()}),e.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:m})]}),e.jsx("div",{style:{height:`${v}px`,width:"100%"},children:e.jsx("canvas",{ref:C})})]}):e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",style:{height:`${v}px`},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-gray-400 dark:text-gray-500 mb-2",children:h()}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No data available"})]})})},W=()=>e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"})}),z=()=>{const{darkMode:r}=R(),{isPremium:w}=I(),[u,m]=y.useState("overview"),[v,A]=y.useState("month"),[M,f]=y.useState(!0),[i,k]=y.useState(null);y.useEffect(()=>{(async()=>{try{f(!0);const x=(await L(async()=>{const{default:s}=await import("./index-CVvVjHWF.js").then(h=>h.c3);return{default:s}},__vite__mapDeps([0,1]))).default;x.getLastUpdated()||await x.initialize();const a=await x.fetchThreatData(),j={threatsByRegion:{labels:Object.keys(a.statistics.byCountry||{}).slice(0,10),data:[Object.values(a.statistics.byCountry||{}).slice(0,10)]},threatTypes:{labels:Object.keys(a.statistics.byType||{}),data:Object.values(a.statistics.byType||{})},threatSeverity:{labels:Object.keys(a.statistics.bySeverity||{}),data:Object.values(a.statistics.bySeverity||{})},threatTrends:{labels:["Jan","Feb","Mar","Apr","May","Jun"],data:[[Math.round(a.attacks.length*.6),Math.round(a.attacks.length*.7),Math.round(a.attacks.length*.8),Math.round(a.attacks.length*.9),Math.round(a.attacks.length*.95),a.attacks.length],[Math.round(a.attacks.filter(s=>s.severity==="critical").length*.6),Math.round(a.attacks.filter(s=>s.severity==="critical").length*.7),Math.round(a.attacks.filter(s=>s.severity==="critical").length*.8),Math.round(a.attacks.filter(s=>s.severity==="critical").length*.9),Math.round(a.attacks.filter(s=>s.severity==="critical").length*.95),a.attacks.filter(s=>s.severity==="critical").length]]},attackVectors:{labels:["Email","Web","Network","Social","Physical","Cloud"],data:[Math.round(a.attacks.length*.45),Math.round(a.attacks.length*.3),Math.round(a.attacks.length*.15),Math.round(a.attacks.length*.05),Math.round(a.attacks.length*.02),Math.round(a.attacks.length*.03)]},vulnerabilityTrends:{labels:["Jan","Feb","Mar","Apr","May","Jun"],data:[[Math.round(a.vulnerabilities.length*.6),Math.round(a.vulnerabilities.length*.7),Math.round(a.vulnerabilities.length*.8),Math.round(a.vulnerabilities.length*.9),Math.round(a.vulnerabilities.length*.95),a.vulnerabilities.length],[Math.round(a.vulnerabilities.filter(s=>{var l,c,n,o,d;return((d=(o=(n=(c=(l=s.cve)==null?void 0:l.metrics)==null?void 0:c.cvssMetricV31)==null?void 0:n[0])==null?void 0:o.cvssData)==null?void 0:d.baseScore)>=9}).length*.6),Math.round(a.vulnerabilities.filter(s=>{var l,c,n,o,d;return((d=(o=(n=(c=(l=s.cve)==null?void 0:l.metrics)==null?void 0:c.cvssMetricV31)==null?void 0:n[0])==null?void 0:o.cvssData)==null?void 0:d.baseScore)>=9}).length*.7),Math.round(a.vulnerabilities.filter(s=>{var l,c,n,o,d;return((d=(o=(n=(c=(l=s.cve)==null?void 0:l.metrics)==null?void 0:c.cvssMetricV31)==null?void 0:n[0])==null?void 0:o.cvssData)==null?void 0:d.baseScore)>=9}).length*.8),Math.round(a.vulnerabilities.filter(s=>{var l,c,n,o,d;return((d=(o=(n=(c=(l=s.cve)==null?void 0:l.metrics)==null?void 0:c.cvssMetricV31)==null?void 0:n[0])==null?void 0:o.cvssData)==null?void 0:d.baseScore)>=9}).length*.9),Math.round(a.vulnerabilities.filter(s=>{var l,c,n,o,d;return((d=(o=(n=(c=(l=s.cve)==null?void 0:l.metrics)==null?void 0:c.cvssMetricV31)==null?void 0:n[0])==null?void 0:o.cvssData)==null?void 0:d.baseScore)>=9}).length*.95),a.vulnerabilities.filter(s=>{var l,c,n,o,d;return((d=(o=(n=(c=(l=s.cve)==null?void 0:l.metrics)==null?void 0:c.cvssMetricV31)==null?void 0:n[0])==null?void 0:o.cvssData)==null?void 0:d.baseScore)>=9}).length]]},activeThreats:a.attacks.slice(0,5).map((s,h)=>({id:s.id||`threat-${h+1}`,name:s.details.name||s.type.replace("_"," ").charAt(0).toUpperCase()+s.type.replace("_"," ").slice(1),severity:s.severity,region:s.details.countryCode||"Global",trend:["increasing","stable","decreasing"][Math.floor(Math.random()*3)],count:Math.floor(Math.random()*1e3)+500})),recentVulnerabilities:a.vulnerabilities.slice(0,5).map((s,h)=>{var c,n,o;const l=s.cve;return{id:l.id||`vuln-${h+1}`,name:l.id||`CVE-${new Date().getFullYear()}-${Math.floor(Math.random()*1e4)}`,severity:(()=>{var S,$,T,N;const d=(N=(T=($=(S=l.metrics)==null?void 0:S.cvssMetricV31)==null?void 0:$[0])==null?void 0:T.cvssData)==null?void 0:N.baseScore;return d?d>=9?"critical":d>=7?"high":d>=4?"medium":"low":"medium"})(),affected:((o=(n=(c=l.descriptions)==null?void 0:c[0])==null?void 0:n.value)==null?void 0:o.split(" ")[0])+" Systems"||"Multiple Systems",status:Math.random()>.5?"active":"patched",published:new Date(l.published||Date.now()).toISOString().split("T")[0]}})};k(j),f(!1)}catch(x){console.error("Error fetching real threat data:",x),console.warn("Falling back to mock threat data"),k({threatsByRegion:{labels:["North America","Europe","Asia","South America","Africa","Oceania"],data:[[35,28,42,18,15,12]]},threatTypes:{labels:["Ransomware","Phishing","DDoS","Data Breach","Supply Chain","Zero-day"],data:[30,25,15,12,10,8]},threatSeverity:{labels:["Critical","High","Medium","Low"],data:[15,35,40,10]},threatTrends:{labels:["Jan","Feb","Mar","Apr","May","Jun"],data:[[120,150,180,220,250,280],[40,45,60,80,95,110]]},attackVectors:{labels:["Email","Web","Network","Social","Physical","Cloud"],data:[45,30,15,5,2,3]},vulnerabilityTrends:{labels:["Jan","Feb","Mar","Apr","May","Jun"],data:[[80,95,110,125,140,160],[25,30,35,45,50,60]]},activeThreats:[{id:"threat-1",name:"Ransomware Campaign",severity:"high",region:"Global",trend:"increasing",count:1250},{id:"threat-2",name:"DDoS Attacks",severity:"medium",region:"Asia",trend:"stable",count:850},{id:"threat-3",name:"Phishing Campaign",severity:"high",region:"North America",trend:"increasing",count:1800},{id:"threat-4",name:"Supply Chain Attacks",severity:"critical",region:"Global",trend:"increasing",count:450},{id:"threat-5",name:"Zero-day Exploits",severity:"critical",region:"Europe",trend:"stable",count:320}],recentVulnerabilities:[{id:"vuln-1",name:"CVE-2023-1234",severity:"critical",affected:"Windows Systems",status:"active",published:"2023-05-15"},{id:"vuln-2",name:"CVE-2023-5678",severity:"high",affected:"Apache Servers",status:"patched",published:"2023-04-28"},{id:"vuln-3",name:"CVE-2023-9012",severity:"medium",affected:"Chrome Browser",status:"active",published:"2023-06-02"},{id:"vuln-4",name:"CVE-2023-3456",severity:"critical",affected:"Linux Kernel",status:"active",published:"2023-05-30"},{id:"vuln-5",name:"CVE-2023-7890",severity:"high",affected:"iOS Devices",status:"patched",published:"2023-05-10"}]}),f(!1)}})()},[v]);const b=t=>{switch(t){case"critical":return"text-red-500 dark:text-red-400";case"high":return"text-orange-500 dark:text-orange-400";case"medium":return"text-yellow-500 dark:text-yellow-400";case"low":return"text-green-500 dark:text-green-400";default:return"text-gray-500 dark:text-gray-400"}},p=t=>{switch(t){case"increasing":return e.jsx("span",{className:"text-red-500 dark:text-red-400",children:"↑"});case"decreasing":return e.jsx("span",{className:"text-green-500 dark:text-green-400",children:"↓"});case"stable":return e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"→"});default:return null}},C=[{id:"overview",label:"Overview",icon:e.jsx(_,{})},{id:"threats",label:"Active Threats",icon:e.jsx(F,{})},{id:"vulnerabilities",label:"Vulnerabilities",icon:e.jsx(U,{})},{id:"trends",label:"Trends & Analytics",icon:e.jsx(O,{})}];return M?e.jsx(W,{}):e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto",children:[C.map(t=>e.jsxs("button",{className:`flex items-center px-4 py-2 text-sm font-medium ${u===t.id?r?"border-b-2 border-blue-500 text-blue-500":"border-b-2 border-blue-600 text-blue-600":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}`,onClick:()=>m(t.id),children:[e.jsx("span",{className:"mr-2",children:t.icon}),t.label]},t.id)),e.jsxs("div",{className:"ml-auto flex items-center px-4",children:[e.jsx(J,{className:"text-gray-500 mr-2"}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Data refreshes automatically"})]})]}),e.jsx("div",{className:"flex justify-end mb-4",children:e.jsx("div",{className:`inline-flex rounded-md shadow-sm ${r?"bg-gray-700":"bg-gray-100"}`,children:["week","month","quarter","year"].map(t=>e.jsx("button",{onClick:()=>A(t),className:`px-4 py-2 text-sm font-medium ${v===t?r?"bg-blue-600 text-white":"bg-blue-100 text-blue-700":r?"text-gray-300 hover:bg-gray-600":"text-gray-700 hover:bg-gray-200"} ${t==="week"?"rounded-l-md":""} ${t==="year"?"rounded-r-md":""}`,children:t.charAt(0).toUpperCase()+t.slice(1)},t))})}),e.jsxs("div",{className:"flex-1 overflow-auto",children:[u==="overview"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Global Threat Distribution"}),e.jsx(g,{type:"bar",data:i.threatsByRegion.data,labels:i.threatsByRegion.labels,height:250})]}),e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Threat Types"}),e.jsx(g,{type:"pie",data:[i.threatTypes.data],labels:i.threatTypes.labels,height:250})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Threat Severity"}),e.jsx(g,{type:"doughnut",data:[i.threatSeverity.data],labels:i.threatSeverity.labels,height:250})]}),e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Threat Trends"}),e.jsx(g,{type:"line",data:i.threatTrends.data,labels:i.threatTrends.labels,height:250})]})]}),e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Active Threats"}),e.jsx("button",{onClick:()=>m("threats"),className:"text-blue-600 dark:text-blue-400 text-sm hover:underline",children:"View All"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Threat"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Severity"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Region"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Trend"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:i.activeThreats.slice(0,3).map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm font-medium",children:t.name}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${b(t.severity)}`,children:t.severity.charAt(0).toUpperCase()+t.severity.slice(1)})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:t.region}),e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:[p(t.trend)," ",t.trend.charAt(0).toUpperCase()+t.trend.slice(1)]})]},t.id))})]})})]})]}),u==="threats"&&e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Active Threats"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search threats...",className:`pl-9 pr-4 py-2 rounded-lg ${r?"bg-gray-700 text-white border-gray-600 focus:border-blue-500":"bg-gray-100 text-gray-900 border-gray-300 focus:border-blue-500"} border focus:outline-none focus:ring-1 focus:ring-blue-500`}),e.jsx(V,{className:"absolute left-3 top-3 text-gray-400"})]}),e.jsxs("button",{className:`px-4 py-2 rounded-lg flex items-center gap-2 ${r?"bg-gray-700 hover:bg-gray-600 text-white":"bg-gray-100 hover:bg-gray-200 text-gray-800"}`,children:[e.jsx(E,{}),e.jsx("span",{children:"Filter"})]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Threat"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Severity"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Region"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Trend"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Count"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:i.activeThreats.map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm font-medium",children:t.name}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${b(t.severity)}`,children:t.severity.charAt(0).toUpperCase()+t.severity.slice(1)})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:t.region}),e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:[p(t.trend)," ",t.trend.charAt(0).toUpperCase()+t.trend.slice(1)]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm font-medium",children:t.count.toLocaleString()})]},t.id))})]})})]}),u==="vulnerabilities"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Recent Vulnerabilities"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search vulnerabilities...",className:`pl-9 pr-4 py-2 rounded-lg ${r?"bg-gray-700 text-white border-gray-600 focus:border-blue-500":"bg-gray-100 text-gray-900 border-gray-300 focus:border-blue-500"} border focus:outline-none focus:ring-1 focus:ring-blue-500`}),e.jsx(V,{className:"absolute left-3 top-3 text-gray-400"})]}),e.jsxs("button",{className:`px-4 py-2 rounded-lg flex items-center gap-2 ${r?"bg-gray-700 hover:bg-gray-600 text-white":"bg-gray-100 hover:bg-gray-200 text-gray-800"}`,children:[e.jsx(E,{}),e.jsx("span",{children:"Filter"})]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"CVE ID"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Severity"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Affected Systems"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Published"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:i.recentVulnerabilities.map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm font-medium",children:t.name}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${b(t.severity)}`,children:t.severity.charAt(0).toUpperCase()+t.severity.slice(1)})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:t.affected}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${t.status==="active"?"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400":"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"}`,children:t.status.charAt(0).toUpperCase()+t.status.slice(1)})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:t.published})]},t.id))})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Vulnerability Trends"}),e.jsx(g,{type:"line",data:i.vulnerabilityTrends.data,labels:i.vulnerabilityTrends.labels,height:250})]}),e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Attack Vectors"}),e.jsx(g,{type:"pie",data:[i.attackVectors.data],labels:i.attackVectors.labels,height:250})]})]})]}),u==="trends"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Threat Trends"}),e.jsx(g,{type:"line",data:i.threatTrends.data,labels:i.threatTrends.labels,height:300})]}),e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Vulnerability Trends"}),e.jsx(g,{type:"line",data:i.vulnerabilityTrends.data,labels:i.vulnerabilityTrends.labels,height:300})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Threat Types"}),e.jsx(g,{type:"pie",data:[i.threatTypes.data],labels:i.threatTypes.labels,height:300})]}),e.jsxs("div",{className:`${r?"bg-gray-800":"bg-white"} rounded-lg p-4`,children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Attack Vectors"}),e.jsx(g,{type:"bar",data:[i.attackVectors.data],labels:i.attackVectors.labels,height:300})]})]})]})]})]})},Z=Object.freeze(Object.defineProperty({__proto__:null,default:z},Symbol.toStringTag,{value:"Module"}));export{g as A,z as T,Z as a};
