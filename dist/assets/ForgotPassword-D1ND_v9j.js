import{u as g,r as t,j as e,L as b,ae as h,aY as p,v as y}from"./index-CVvVjHWF.js";const w=()=>{const{darkMode:s}=g(),[o,x]=t.useState(""),[a,n]=t.useState(!1),[i,d]=t.useState(null),[c,m]=t.useState(null),u=async l=>{l.preventDefault(),n(!0),d(null),m(null);try{const{error:r}=await y.auth.resetPasswordForEmail(o,{redirectTo:`${window.location.origin}/reset-password`});if(r)throw r;m("Password reset instructions have been sent to your email.")}catch(r){console.error("Password reset error:",r),d(r.message||"Failed to send password reset email. Please try again.")}finally{n(!1)}};return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120]":"bg-gray-50"} flex items-center justify-center p-4`,children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx("div",{className:"mb-8",children:e.jsxs(b,{to:"/login",className:`${s?"text-gray-400 hover:text-white":"text-gray-600 hover:text-gray-900"} flex items-center gap-2`,children:[e.jsx(h,{}),e.jsx("span",{children:"Back to Login"})]})}),e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-xl border overflow-hidden`,children:e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Reset your password"})}),i&&e.jsx("div",{className:`${s?"bg-red-500/20 text-red-400":"bg-red-100 text-red-800"} px-4 py-3 rounded mb-6`,children:i}),c&&e.jsx("div",{className:`${s?"bg-green-500/20 text-green-400":"bg-green-100 text-green-800"} px-4 py-3 rounded mb-6`,children:c}),e.jsxs("form",{onSubmit:u,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"email",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(p,{className:"text-gray-500"})}),e.jsx("input",{type:"email",id:"email",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"<EMAIL>",value:o,onChange:l=>x(l.target.value),required:!0})]})]}),e.jsx("button",{type:"submit",className:`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${a?"opacity-70 cursor-not-allowed":""}`,disabled:a,children:a?"Sending Instructions...":"Send Reset Instructions"})]})]})})]})})};export{w as default};
