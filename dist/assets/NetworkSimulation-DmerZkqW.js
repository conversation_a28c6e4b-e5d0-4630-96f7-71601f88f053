import{r as l,j as e,E,ai as H,a4 as A,a2 as T,as as M,ac as q,a3 as z}from"./index-CVvVjHWF.js";const Q=({exerciseType:x="basic",onComplete:f})=>{const[u,p]=l.useState([{id:1,type:"router",name:"Router",ip:"***********",status:"online",x:400,y:100},{id:2,type:"switch",name:"Switch",ip:"***********",status:"online",x:400,y:200},{id:3,type:"computer",name:"PC-1",ip:"***********0",status:"online",x:200,y:300},{id:4,type:"computer",name:"PC-2",ip:"***********1",status:"online",x:400,y:300},{id:5,type:"computer",name:"PC-3",ip:"************",status:"online",x:600,y:300}]),[v,G]=l.useState([{from:1,to:2},{from:2,to:3},{from:2,to:4},{from:2,to:5}]),[n,w]=l.useState(null),[i,k]=l.useState(null),[r,C]=l.useState(null),[g,b]=l.useState(null),[h,S]=l.useState(!1),[c,j]=l.useState("explore"),[y,N]=l.useState({deviceConfiguration:!1,pingTest:!1,troubleshooting:!1}),[D,P]=l.useState("Explore the network"),[J,K]=l.useState(!1);l.useEffect(()=>{x==="troubleshooting"?(p(s=>s.map(t=>t.id===3?{...t,status:"offline"}:t)),P("Find and fix the network issue")):x==="configuration"&&(p(s=>s.map(t=>t.id===5?{...t,ip:"0.0.0.0"}:t)),P("Configure PC-3 with the correct IP address"))},[x]),l.useEffect(()=>{Object.values(y).every(s=>s)&&f&&f()},[y,f]);const B=s=>{const t=u.find(a=>a.id===s);w(t),c==="ping"&&!i?k(t):c==="ping"&&i&&!r&&C(t)},F=s=>{p(t=>t.map(a=>a.id===n.id?{...a,...s}:a)),x==="configuration"&&s.ip==="************"&&N(t=>({...t,deviceConfiguration:!0})),x==="troubleshooting"&&s.status==="online"&&N(t=>({...t,troubleshooting:!0}))},R=()=>{!i||!r||(S(!0),b(null),setTimeout(()=>{const s=O(i.id,r.id),t=u.find(a=>a.id===r.id).status==="online";b({success:s&&t,message:s&&t?`Ping successful: ${i.ip} → ${r.ip}`:`Ping failed: ${i.ip} → ${r.ip}`}),S(!1),s&&t&&N(a=>({...a,pingTest:!0}))},1500))},O=(s,t)=>{const a=new Set,m=o=>{if(o===t)return!0;if(a.has(o))return!1;a.add(o);const W=v.filter(d=>d.from===o||d.to===o).map(d=>d.from===o?d.to:d.from);for(const d of W)if(m(d))return!0;return!1};return m(s)},$=()=>{k(null),C(null),b(null)},I=s=>{switch(s){case"router":return e.jsx(z,{className:"text-blue-400 text-xl"});case"switch":return e.jsx(q,{className:"text-green-400 text-xl"});case"computer":return e.jsx(T,{className:"text-amber-400 text-xl"});case"wireless":return e.jsx(M,{className:"text-purple-400 text-xl"});default:return e.jsx(T,{className:"text-gray-400 text-xl"})}};return e.jsxs("div",{className:"bg-[#0F172A] rounded-lg p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Network Simulation"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>j("explore"),className:`px-3 py-1 rounded text-sm ${c==="explore"?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:"Explore"}),e.jsx("button",{onClick:()=>{j("ping"),$()},className:`px-3 py-1 rounded text-sm ${c==="ping"?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:"Ping Test"}),e.jsx("button",{onClick:()=>j("configure"),className:`px-3 py-1 rounded text-sm ${c==="configure"?"bg-primary text-black":"bg-[#1E293B] text-white"}`,children:"Configure"})]})]}),e.jsxs("div",{className:"mb-2 text-sm text-gray-400",children:["Current Task: ",D]}),e.jsxs("div",{className:"relative h-80 bg-[#1E293B] rounded-lg mb-4 overflow-hidden border border-gray-700",children:[e.jsxs("svg",{className:"absolute inset-0 w-full h-full",children:[v.map((s,t)=>{const a=u.find(o=>o.id===s.from),m=u.find(o=>o.id===s.to);return e.jsx("line",{x1:a.x,y1:a.y,x2:m.x,y2:m.y,stroke:"#4B5563",strokeWidth:"2"},t)}),h&&i&&r&&e.jsxs("circle",{cx:i.x,cy:i.y,r:"5",fill:"#38BDF8",children:[e.jsx("animate",{attributeName:"cx",from:i.x,to:r.x,dur:"1.5s",repeatCount:"1"}),e.jsx("animate",{attributeName:"cy",from:i.y,to:r.y,dur:"1.5s",repeatCount:"1"})]})]}),u.map(s=>e.jsxs("div",{className:`absolute w-16 h-16 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center cursor-pointer transition-all ${(n==null?void 0:n.id)===s.id?"ring-2 ring-primary":""} ${(i==null?void 0:i.id)===s.id?"ring-2 ring-blue-500":""} ${(r==null?void 0:r.id)===s.id?"ring-2 ring-green-500":""}`,style:{left:s.x,top:s.y},onClick:()=>B(s.id),children:[e.jsxs("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${s.status==="online"?"bg-[#0F172A]":"bg-red-900/30"}`,children:[I(s.type),s.status==="offline"&&e.jsx("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center",children:e.jsx(E,{className:"text-white text-xs"})})]}),e.jsxs("div",{className:"text-xs mt-1 text-center",children:[e.jsx("div",{className:"font-bold",children:s.name}),e.jsx("div",{className:"text-gray-400",children:s.ip})]})]},s.id))]}),e.jsxs("div",{className:"bg-[#1E293B] p-4 rounded-lg",children:[c==="explore"&&n&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:"Device Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsx("div",{className:"text-gray-400",children:"Name:"}),e.jsx("div",{children:n.name}),e.jsx("div",{className:"text-gray-400",children:"Type:"}),e.jsx("div",{children:n.type}),e.jsx("div",{className:"text-gray-400",children:"IP Address:"}),e.jsx("div",{children:n.ip}),e.jsx("div",{className:"text-gray-400",children:"Status:"}),e.jsx("div",{className:n.status==="online"?"text-green-400":"text-red-400",children:n.status})]})]}),c==="ping"&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:"Ping Test"}),e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Source"}),e.jsx("div",{className:"bg-[#0F172A] px-3 py-2 rounded border border-gray-700 min-w-[120px]",children:i?i.name:"Select a device"})]}),e.jsx("div",{className:"text-gray-400",children:"→"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Destination"}),e.jsx("div",{className:"bg-[#0F172A] px-3 py-2 rounded border border-gray-700 min-w-[120px]",children:r?r.name:"Select a device"})]}),e.jsxs("div",{className:"ml-auto",children:[e.jsx("button",{onClick:R,disabled:!i||!r||h,className:`px-4 py-2 rounded ${!i||!r||h?"bg-gray-700 text-gray-400 cursor-not-allowed":"bg-primary text-black"}`,children:h?"Pinging...":"Ping"}),e.jsx("button",{onClick:$,className:"ml-2 px-4 py-2 rounded bg-[#0F172A] text-gray-400 hover:bg-gray-800",children:e.jsx(H,{})})]})]}),g&&e.jsxs("div",{className:`p-3 rounded ${g.success?"bg-green-900/30 text-green-400":"bg-red-900/30 text-red-400"}`,children:[g.success?e.jsx(A,{className:"inline mr-2"}):e.jsx(E,{className:"inline mr-2"}),g.message]})]}),c==="configure"&&n&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold mb-2",children:"Configure Device"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-gray-400 block mb-1",children:"IP Address"}),e.jsx("input",{type:"text",value:n.ip,onChange:s=>F({ip:s.target.value}),className:"w-full bg-[#0F172A] px-3 py-2 rounded border border-gray-700"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-xs text-gray-400 block mb-1",children:"Status"}),e.jsxs("select",{value:n.status,onChange:s=>F({status:s.target.value}),className:"w-full bg-[#0F172A] px-3 py-2 rounded border border-gray-700",children:[e.jsx("option",{value:"online",children:"Online"}),e.jsx("option",{value:"offline",children:"Offline"})]})]})]}),e.jsx("button",{onClick:()=>w(null),className:"px-4 py-2 rounded bg-primary text-black",children:"Apply Changes"})]})]}),e.jsxs("div",{className:"mt-4 bg-[#0F172A] p-3 rounded-lg border border-gray-700",children:[e.jsx("h4",{className:"font-bold mb-2 text-sm",children:"Task Progress"}),e.jsx("div",{className:"space-y-2",children:Object.entries(y).map(([s,t])=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${t?"bg-green-500 text-white":"bg-gray-700 text-gray-400"}`,children:t?e.jsx(A,{className:"text-xs"}):null}),e.jsxs("span",{className:"text-sm",children:[s==="deviceConfiguration"&&"Configure device",s==="pingTest"&&"Test connectivity",s==="troubleshooting"&&"Fix network issue"]})]},s))})]})]})};export{Q as default};
