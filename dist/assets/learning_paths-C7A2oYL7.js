import{l as i}from"./learning-paths-structure-YI8Fv9cD.js";const s=(e,t)=>({id:e.id,title:e.title,description:e.description,short_description:e.description.substring(0,100)+"...",icon:`/icons/${e.category}.svg`,cover_image:`/images/covers/${e.id}.jpg`,category:e.category,difficulty:e.difficulty,estimated_hours:e.estimatedHours,order_index:t+1,is_active:!0,is_featured:e.category==="fundamentals",modules_count:e.modules?e.modules.length:0}),r=[{id:"fundamentals-track",title:"Fundamentals Track",description:"Master the core concepts of cybersecurity with our comprehensive fundamentals track. This track covers networking, operating systems, and essential cybersecurity principles to build a solid foundation for your cybersecurity journey.",short_description:"Build a solid foundation in cybersecurity fundamentals",icon:"/icons/fundamentals.svg",cover_image:"/images/covers/fundamentals-track.jpg",category:"fundamentals",difficulty:"beginner",estimated_hours:40,order_index:1,is_active:!0,is_featured:!0,modules_count:3},{id:"bug-bounty-hunter",title:"Bug Bounty Hunter",description:"Learn how to find and report security vulnerabilities in web applications. This track covers web application security, vulnerability assessment, and responsible disclosure practices.",short_description:"Learn how to find and report security vulnerabilities in web applications",icon:"/icons/bug-bounty.svg",cover_image:"/images/covers/bug-bounty-hunter.jpg",category:"offensive",difficulty:"intermediate",estimated_hours:24,order_index:2,is_active:!0,is_featured:!0,modules_count:12},{id:"network-defender",title:"Network Defender",description:"Master the skills needed to protect networks from cyber attacks. This track covers network security architecture, firewall configuration, IDS/IPS implementation, and network monitoring.",short_description:"Master the skills needed to protect networks from cyber attacks",icon:"/icons/network-defender.svg",cover_image:"/images/covers/network-defender.jpg",category:"defensive",difficulty:"intermediate",estimated_hours:30,order_index:3,is_active:!0,is_featured:!0,modules_count:15}],o=[...r,...i.map((e,t)=>s(e,t+r.length))],a=[{id:"cybersecurity-foundations",learning_path_id:"fundamentals-track",title:"Cybersecurity Foundations",description:"Essential cybersecurity concepts, terminology, and principles that form the foundation for all cybersecurity learning paths.",short_description:"Master core cybersecurity concepts and terminology",icon:"/icons/security.svg",difficulty:"beginner",estimated_hours:10,order_index:1,prerequisites:{required_modules:[]},learning_objectives:["Understand fundamental cybersecurity concepts and terminology","Identify different types of cyber threats and attack vectors","Learn about security frameworks and compliance requirements","Understand risk management and security governance basics","Explore career paths in cybersecurity"],is_active:!0},{id:"security-tools-introduction",learning_path_id:"fundamentals-track",title:"Security Tools Introduction",description:"Introduction to essential cybersecurity tools and technologies used across different security domains.",short_description:"Get familiar with essential cybersecurity tools",icon:"/icons/tools.svg",difficulty:"beginner",estimated_hours:8,order_index:2,prerequisites:{required_modules:["cybersecurity-foundations"]},learning_objectives:["Understand different categories of security tools","Learn basic usage of common security tools","Explore vulnerability scanners and assessment tools","Introduction to SIEM and monitoring tools","Basic penetration testing tool overview"],is_active:!0}],c={user_id:"sample-user-id",topic_progress:[{topic_id:"intro-to-networking",status:"completed",score:100,max_score:100,completion_date:"2023-05-15T14:30:00Z",time_spent_seconds:1800}],module_progress:[{module_id:"network-fundamentals-101",status:"in_progress",progress_percentage:25,started_at:"2023-05-15T10:00:00Z",completed_at:null}],path_progress:[{learning_path_id:"fundamentals-track",status:"in_progress",progress_percentage:8,started_at:"2023-05-15T10:00:00Z",completed_at:null}]};export{a as f,o as l,c as s};
