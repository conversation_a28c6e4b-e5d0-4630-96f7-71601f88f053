import{w as f,u as p,bt as N,r as o,j as e,L as s,x as $,bu as m,U as g,c as y,C as b,aF as v,t as z,bv as M,A as k,m as j}from"./index-CVvVjHWF.js";import{P as x}from"./index-Cc2BstNK.js";const C=()=>{const{user:c}=f(),{darkMode:t,toggleDarkMode:l}=p(),r=N(),[n,d]=o.useState(!1),[u,h]=o.useState(!1);o.useEffect(()=>{const i=()=>{window.scrollY>20?h(!0):h(!1)};return window.addEventListener("scroll",i),()=>{window.removeEventListener("scroll",i)}},[]),o.useEffect(()=>{d(!1)},[r.pathname]);const w=()=>{d(!n)},a=i=>r.pathname.startsWith(i);return e.jsxs("nav",{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${u?t?"bg-gray-900 shadow-lg":"bg-white shadow-lg":t?"bg-gray-900/90 backdrop-blur-sm":"bg-white/90 backdrop-blur-sm"}`,children:[e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex items-center justify-between h-16",children:[e.jsx(s,{to:"/",className:"flex items-center",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-blue-500",children:e.jsx($,{className:"text-2xl mr-2"})}),e.jsxs("span",{className:`font-bold text-xl ${t?"text-white":"text-gray-800"}`,children:["Cyber",e.jsx("span",{className:"text-yellow-500",children:"Force"})]})]})}),e.jsxs("div",{className:"hidden md:flex items-center space-x-4",children:[e.jsx(s,{to:"/",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a("/")&&!a("/learning-paths")&&!a("/modules")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(m,{className:"mr-1"}),e.jsx("span",{children:"Home"})]})}),e.jsx(s,{to:"/learning-paths",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a("/learning-paths")||a("/modules")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(g,{className:"mr-1"}),e.jsx("span",{children:"Learning Paths"})]})}),e.jsx(s,{to:"/leaderboard",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a("/leaderboard")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(y,{className:"mr-1"}),e.jsx("span",{children:"Leaderboard"})]})}),e.jsx(s,{to:"/challenges",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a("/challenges")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(b,{className:"mr-1"}),e.jsx("span",{children:"Challenges"})]})}),e.jsx(s,{to:"/profile",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a("/profile")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(v,{className:"mr-1"}),e.jsx("span",{children:"Profile"})]})}),e.jsx("button",{onClick:l,className:`p-2 rounded-full transition-colors ${t?"bg-gray-700 text-yellow-300 hover:bg-gray-600":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,"aria-label":"Toggle dark mode",children:t?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z",clipRule:"evenodd"})}):e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{d:"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"})})})]}),e.jsx("div",{className:"md:hidden",children:e.jsx("button",{onClick:w,className:`p-2 rounded-md transition-colors ${t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,"aria-label":"Toggle menu",children:n?e.jsx(z,{className:"h-6 w-6"}):e.jsx(M,{className:"h-6 w-6"})})})]})}),e.jsx(k,{children:n&&e.jsx(j.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:`md:hidden ${t?"bg-gray-800":"bg-white"} border-t ${t?"border-gray-700":"border-gray-200"}`,children:e.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:[e.jsx(s,{to:"/",className:`block px-3 py-2 rounded-md text-base font-medium transition-colors ${a("/")&&!a("/learning-paths")&&!a("/modules")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(m,{className:"mr-2"}),e.jsx("span",{children:"Home"})]})}),e.jsx(s,{to:"/learning-paths",className:`block px-3 py-2 rounded-md text-base font-medium transition-colors ${a("/learning-paths")||a("/modules")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(g,{className:"mr-2"}),e.jsx("span",{children:"Learning Paths"})]})}),e.jsx(s,{to:"/leaderboard",className:`block px-3 py-2 rounded-md text-base font-medium transition-colors ${a("/leaderboard")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(y,{className:"mr-2"}),e.jsx("span",{children:"Leaderboard"})]})}),e.jsx(s,{to:"/challenges",className:`block px-3 py-2 rounded-md text-base font-medium transition-colors ${a("/challenges")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(b,{className:"mr-2"}),e.jsx("span",{children:"Challenges"})]})}),e.jsx(s,{to:"/profile",className:`block px-3 py-2 rounded-md text-base font-medium transition-colors ${a("/profile")?"bg-blue-500 text-white":t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(v,{className:"mr-2"}),e.jsx("span",{children:"Profile"})]})}),e.jsx("button",{onClick:l,className:`flex items-center w-full px-3 py-2 rounded-md text-base font-medium transition-colors ${t?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,children:t?e.jsxs(e.Fragment,{children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z",clipRule:"evenodd"})}),e.jsx("span",{children:"Light Mode"})]}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{d:"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"})}),e.jsx("span",{children:"Dark Mode"})]})})]})})})]})},F=({children:c,fullWidth:t=!1,className:l=""})=>{const{darkMode:r}=p();return e.jsxs("div",{className:`min-h-screen flex flex-col ${r?"bg-gray-900 text-white":"bg-gray-50 text-gray-900"}`,children:[e.jsx(C,{}),e.jsx("main",{className:`flex-grow pt-16 ${l}`,children:e.jsx(j.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:t?"w-full":"container mx-auto px-4 py-6",children:c})}),e.jsx("footer",{className:`py-6 ${r?"bg-gray-800 text-gray-300":"bg-white text-gray-600"} border-t ${r?"border-gray-700":"border-gray-200"}`,children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[e.jsxs("div",{className:"mb-4 md:mb-0",children:[e.jsx("div",{className:"flex items-center",children:e.jsxs("span",{className:"font-bold text-lg",children:["Cyber",e.jsx("span",{className:"text-yellow-500",children:"Force"})]})}),e.jsxs("p",{className:"text-sm mt-1",children:["© ",new Date().getFullYear()," CyberForce. All rights reserved."]})]}),e.jsxs("div",{className:"flex flex-wrap justify-center md:justify-end gap-4",children:[e.jsx("a",{href:"/about",className:`text-sm hover:${r?"text-white":"text-gray-900"}`,children:"About"}),e.jsx("a",{href:"/services",className:`text-sm hover:${r?"text-white":"text-gray-900"}`,children:"Services"}),e.jsx("a",{href:"/pricing",className:`text-sm hover:${r?"text-white":"text-gray-900"}`,children:"Pricing"}),e.jsx("a",{href:"mailto:<EMAIL>",className:`text-sm hover:${r?"text-white":"text-gray-900"}`,children:"Contact"})]})]})})})]})};F.propTypes={children:x.node.isRequired,fullWidth:x.bool,className:x.string};export{F as L};
