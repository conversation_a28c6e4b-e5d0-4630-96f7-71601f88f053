import{b as y,u as j,D as f,r,j as e,L as w,ae as N,aR as v,aY as $,l as k,bF as F}from"./index-CVvVjHWF.js";const L=()=>{const g=y(),{darkMode:s}=j(),{upgradeSubscription:u}=f(),[i,b]=r.useState(""),[n,h]=r.useState(""),[l,c]=r.useState(!1),[o,d]=r.useState(null),[m,x]=r.useState(null),p=async t=>{t.preventDefault(),c(!0),d(null),x(null);try{const{session:a}=await F(i,n);a&&(x("Login successful! Upgrading to premium..."),await u("premium"),setTimeout(()=>{g("/security-insights")},1500))}catch(a){console.error("Login error:",a),d(a.message||"Login failed. Please check your credentials.")}finally{c(!1)}};return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120]":"bg-gray-50"} flex items-center justify-center p-4`,children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx("div",{className:"mb-8",children:e.jsxs(w,{to:"/",className:`${s?"text-gray-400 hover:text-white":"text-gray-600 hover:text-gray-900"} flex items-center gap-2`,children:[e.jsx(N,{}),e.jsx("span",{children:"Back to Home"})]})}),e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-xl border overflow-hidden`,children:e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(v,{className:"text-amber-500 text-4xl mx-auto mb-3"}),e.jsx("h1",{className:`text-3xl font-bold ${s?"text-white":"text-gray-900"} mb-2`,children:"Premium Access"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Sign in to access premium features"})]}),o&&e.jsx("div",{className:`${s?"bg-red-500/20 text-red-400":"bg-red-100 text-red-800"} px-4 py-3 rounded mb-6`,children:o}),m&&e.jsx("div",{className:`${s?"bg-green-500/20 text-green-400":"bg-green-100 text-green-800"} px-4 py-3 rounded mb-6`,children:m}),e.jsxs("form",{onSubmit:p,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"email",className:`block mb-2 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Email Address"}),e.jsxs("div",{className:`flex items-center ${s?"bg-[#0B1120] border-gray-700":"bg-gray-50 border-gray-300"} border rounded-lg px-3 py-2`,children:[e.jsx($,{className:`${s?"text-gray-500":"text-gray-400"} mr-2`}),e.jsx("input",{type:"email",id:"email",value:i,onChange:t=>b(t.target.value),className:`block w-full bg-transparent outline-none ${s?"text-white":"text-gray-900"}`,placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"password",className:`block mb-2 text-sm font-medium ${s?"text-gray-300":"text-gray-700"}`,children:"Password"}),e.jsxs("div",{className:`flex items-center ${s?"bg-[#0B1120] border-gray-700":"bg-gray-50 border-gray-300"} border rounded-lg px-3 py-2`,children:[e.jsx(k,{className:`${s?"text-gray-500":"text-gray-400"} mr-2`}),e.jsx("input",{type:"password",id:"password",value:n,onChange:t=>h(t.target.value),className:`block w-full bg-transparent outline-none ${s?"text-white":"text-gray-900"}`,placeholder:"Enter your password",required:!0})]})]}),e.jsx("button",{type:"submit",disabled:l,className:`w-full bg-amber-500 hover:bg-amber-600 text-white font-medium rounded-lg px-4 py-3 flex items-center justify-center transition-colors ${l?"opacity-70 cursor-not-allowed":""}`,children:l?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):"Access Premium Features"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("p",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"For testing purposes, use the pre-filled credentials"})})]})})]})})};export{L as default};
