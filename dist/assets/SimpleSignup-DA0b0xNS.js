import{b as f,u as v,r as a,j as e,L as t,ae as N,aH as w,aY as $,l as x,bD as k,bE as F}from"./index-CVvVjHWF.js";const P=()=>{const m=f(),{darkMode:s}=v(),[g,u]=a.useState(""),[b,h]=a.useState(""),[n,y]=a.useState(""),[i,p]=a.useState(""),[l,c]=a.useState(!1),[o,d]=a.useState(null),j=r=>{if(r.preventDefault(),c(!0),d(null),n!==i){d("Passwords don't match"),c(!1);return}setTimeout(()=>{c(!1),m("/simplified-dashboard")},1e3)};return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120]":"bg-gray-50"} flex items-center justify-center p-4`,children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx("div",{className:"mb-8",children:e.jsxs(t,{to:"/",className:`${s?"text-gray-400 hover:text-white":"text-gray-600 hover:text-gray-900"} flex items-center gap-2`,children:[e.jsx(N,{}),e.jsx("span",{children:"Back to Home"})]})}),e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-xl border overflow-hidden`,children:e.jsx("div",{className:"p-8",children:e.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md",children:[e.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Create your account"})}),o&&e.jsx("div",{className:`${s?"bg-red-500/20 text-red-400":"bg-red-100 text-red-800"} px-4 py-3 rounded mb-6`,children:o}),e.jsxs("form",{onSubmit:j,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"username",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Username"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(w,{className:"text-gray-500"})}),e.jsx("input",{type:"text",id:"username",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"cyberhacker",value:g,onChange:r=>u(r.target.value),required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"email",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx($,{className:"text-gray-500"})}),e.jsx("input",{type:"email",id:"email",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"<EMAIL>",value:b,onChange:r=>h(r.target.value),required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"password",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(x,{className:"text-gray-500"})}),e.jsx("input",{type:"password",id:"password",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"••••••••",value:n,onChange:r=>y(r.target.value),required:!0,minLength:8})]}),e.jsx("p",{className:`mt-1 text-sm ${s?"text-gray-500":"text-gray-600"}`,children:"Password must be at least 8 characters"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"confirmPassword",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Confirm Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(x,{className:"text-gray-500"})}),e.jsx("input",{type:"password",id:"confirmPassword",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"••••••••",value:i,onChange:r=>p(r.target.value),required:!0})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex items-center h-5",children:e.jsx("input",{id:"terms",type:"checkbox",className:"w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-[#88cc14]",required:!0})}),e.jsxs("label",{htmlFor:"terms",className:`ml-2 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:["I agree to the ",e.jsx(t,{to:"/terms",className:"text-[#88cc14] hover:underline",children:"Terms of Service"})," and ",e.jsx(t,{to:"/privacy",className:"text-[#88cc14] hover:underline",children:"Privacy Policy"})]})]})}),e.jsx("button",{type:"submit",className:`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${l?"opacity-70 cursor-not-allowed":""}`,disabled:l,children:l?"Creating Account...":"Create Account"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:["Already have an account?"," ",e.jsx(t,{to:"/login",className:"text-[#88cc14] hover:underline",children:"Sign In"})]})}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:`w-full border-t ${s?"border-gray-800":"border-gray-300"}`})}),e.jsx("div",{className:"relative flex justify-center",children:e.jsx("span",{className:`${s?"bg-[#1A1F35]":"bg-white"} px-4 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Or continue with"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 ${s?"bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800":"bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300"} py-3 px-4 rounded-lg border transition-colors`,children:[e.jsx(k,{}),e.jsx("span",{children:"Google"})]}),e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2 ${s?"bg-[#0B1120] hover:bg-[#151F38] text-white border-gray-800":"bg-gray-50 hover:bg-gray-100 text-gray-900 border-gray-300"} py-3 px-4 rounded-lg border transition-colors`,children:[e.jsx(F,{}),e.jsx("span",{children:"GitHub"})]})]})]})]})})})})]})})};export{P as default};
