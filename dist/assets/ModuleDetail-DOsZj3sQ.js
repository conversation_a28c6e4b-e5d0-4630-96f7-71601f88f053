import{a as T,b as k,w as S,u as L,r as i,j as e,br as M,bs as u,ae as g,U as p}from"./index-CVvVjHWF.js";import{f as E,h as I}from"./learningApiOverride-effR4Get.js";import{L as o}from"./LearningLayout-nZy_7HLr.js";import"./learning-paths-structure-YI8Fv9cD.js";import"./index-Cc2BstNK.js";const B=()=>{const{moduleId:c}=T(),b=k(),{user:D}=S(),{darkMode:t}=L(),[a,j]=i.useState(null),[n,y]=i.useState([]),[f,d]=i.useState(!0),[x,N]=i.useState(null),[m,v]=i.useState(0);i.useEffect(()=>{(async()=>{try{d(!0);const s=await E(c);if(!s)throw new Error("Module not found");const $=await I(c);j(s),y($)}catch(s){console.error("Error fetching module data:",s),N(s.message||"Failed to load module. Please try again later.")}finally{d(!1)}})()},[c]);const h=()=>{b(-1)},w=l=>{v(l)};if(f)return e.jsx(o,{children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(M,{size:"lg"})})});if(x||!a)return e.jsx(o,{children:e.jsx(u,{icon:e.jsx(p,{className:"text-5xl"}),title:"Module Not Found",message:x||"The module you're looking for doesn't exist or has been removed.",action:e.jsxs("button",{className:`px-4 py-2 rounded-lg transition-colors ${t?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-500 hover:bg-blue-600 text-white"}`,onClick:h,children:[e.jsx(g,{className:"inline-block mr-2"}),"Back"]})})});const r=n[m]||null;return e.jsxs(o,{children:[e.jsxs("button",{onClick:h,className:`mb-4 flex items-center ${t?"text-gray-300 hover:text-white":"text-gray-600 hover:text-gray-800"}`,children:[e.jsx(g,{className:"mr-2"}),"Back to Learning Path"]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:`text-3xl font-bold mb-2 ${t?"text-white":"text-gray-800"}`,children:a.title}),e.jsx("p",{className:`text-lg ${t?"text-gray-300":"text-gray-600"}`,children:a.description}),e.jsxs("div",{className:"flex flex-wrap gap-4 mt-4",children:[e.jsx("div",{className:`px-3 py-1 rounded-full text-sm ${t?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:a.difficulty}),e.jsxs("div",{className:`px-3 py-1 rounded-full text-sm ${t?"bg-purple-900/30 text-purple-300":"bg-purple-100 text-purple-800"}`,children:[a.estimatedTime," minutes"]})]}),a.objectives&&a.objectives.length>0&&e.jsxs("div",{className:"mt-6",children:[e.jsx("h2",{className:`text-xl font-semibold mb-2 ${t?"text-white":"text-gray-800"}`,children:"Learning Objectives"}),e.jsx("ul",{className:`list-disc pl-5 ${t?"text-gray-300":"text-gray-900"}`,children:a.objectives.map((l,s)=>e.jsx("li",{className:"mb-1",children:l},s))})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsx("div",{className:"md:col-span-1",children:e.jsxs("div",{className:`rounded-lg p-4 ${t?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("h2",{className:`text-lg font-semibold mb-4 ${t?"text-white":"text-gray-800"}`,children:"Topics"}),e.jsx("ul",{className:"space-y-2",children:n.map((l,s)=>e.jsx("li",{children:e.jsx("button",{onClick:()=>w(s),className:`w-full text-left px-3 py-2 rounded-lg transition-colors ${s===m?t?"bg-blue-600 text-white":"bg-blue-500 text-white":t?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-200 text-gray-700"}`,children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs("span",{className:"mr-2",children:[s+1,"."]}),e.jsx("span",{className:"flex-1",children:l.title})]})})},l.id))})]})}),e.jsx("div",{className:"md:col-span-3",children:r?e.jsxs("div",{className:`rounded-lg p-6 ${t?"bg-gray-800":"bg-white border border-gray-200"}`,children:[e.jsx("h2",{className:`text-2xl font-bold mb-4 ${t?"text-white":"text-gray-800"}`,children:r.title}),r.type==="text"?e.jsx("div",{className:`prose max-w-none ${t?"prose-invert":""}`,dangerouslySetInnerHTML:{__html:r.content}}):r.type==="quiz"?e.jsxs("div",{className:"quiz-container",children:[e.jsx("h3",{className:`text-xl font-semibold mb-4 ${t?"text-white":"text-gray-800"}`,children:"Knowledge Check"}),e.jsx("p",{className:t?"text-gray-300":"text-gray-600",children:"This is a placeholder for the quiz content."})]}):r.type==="interactive"?e.jsxs("div",{className:"interactive-container",children:[e.jsx("h3",{className:`text-xl font-semibold mb-4 ${t?"text-white":"text-gray-800"}`,children:"Interactive Exercise"}),e.jsx("p",{className:t?"text-gray-300":"text-gray-600",children:"This is a placeholder for the interactive content."})]}):e.jsx("p",{className:t?"text-gray-300":"text-gray-600",children:"Content type not supported."})]}):e.jsx(u,{icon:e.jsx(p,{className:"text-5xl"}),title:"No Topic Selected",message:"Please select a topic from the sidebar to view its content."})})]})]})},A=()=>e.jsx(B,{});export{A as default};
