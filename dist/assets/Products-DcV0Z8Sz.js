import{j as e,m as w,f as M,g as L,h as T,r as o,i as $,k as E,l as I,n as D,o as O,p as B,q as R,A as S,s as U,t as V}from"./index-CVvVjHWF.js";function X({items:d,onUpdateQuantity:u,onRemoveItem:g,onCheckout:n}){const l=d.reduce((t,x)=>t+x.price*x.quantity,0),c=d.some(t=>t.is_physical)?99:0,i=l+c;return e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Shopping Cart"}),d.length===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Your cart is empty"})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4 mb-6",children:d.map(t=>e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"flex items-center gap-4 p-4 bg-gray-50 rounded-lg",children:[e.jsx("img",{src:t.image_url,alt:t.name,className:"w-16 h-16 object-cover rounded"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-bold text-gray-900",children:t.name}),e.jsx("p",{className:"text-sm text-gray-500",children:t.category}),t.size&&e.jsxs("p",{className:"text-sm text-gray-500",children:["Size: ",t.size]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>u(t.id,t.quantity-1),disabled:t.quantity<=1,className:"p-1 rounded hover:bg-gray-200 disabled:opacity-50",children:e.jsx(M,{className:"text-gray-600"})}),e.jsx("span",{className:"w-8 text-center font-medium",children:t.quantity}),e.jsx("button",{onClick:()=>u(t.id,t.quantity+1),className:"p-1 rounded hover:bg-gray-200",children:e.jsx(L,{className:"text-gray-600"})})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"font-bold text-gray-900",children:["₹",(t.price*t.quantity).toFixed(2)]}),e.jsx("button",{onClick:()=>g(t.id),className:"text-red-500 hover:text-red-600",children:e.jsx(T,{})})]})]},t.id))}),e.jsxs("div",{className:"border-t border-gray-200 pt-4 space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-gray-600",children:[e.jsx("span",{children:"Subtotal"}),e.jsxs("span",{children:["₹",l.toFixed(2)]})]}),e.jsxs("div",{className:"flex justify-between text-gray-600",children:[e.jsx("span",{children:"Shipping"}),e.jsx("span",{children:c>0?`₹${c.toFixed(2)}`:"Free"})]}),e.jsxs("div",{className:"flex justify-between text-xl font-bold text-gray-900 pt-2",children:[e.jsx("span",{children:"Total"}),e.jsxs("span",{children:["₹",i.toFixed(2)]})]})]}),e.jsx("button",{onClick:n,className:"w-full mt-6 bg-[#88cc14] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#7ab811] transition-colors",children:"Proceed to Checkout"})]})]})}function Y({onSubmit:d,initialData:u={},buttonText:g="Save Address"}){const[n,l]=o.useState({full_name:"",address_line1:"",address_line2:"",city:"",state:"",postal_code:"",country:"",phone:"",is_default:!1,...u}),c=t=>{const{name:x,value:h,type:j,checked:p}=t.target;l(f=>({...f,[x]:j==="checkbox"?p:h}))},i=t=>{t.preventDefault(),d(n)};return e.jsxs("form",{onSubmit:i,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),e.jsx("input",{type:"text",name:"full_name",value:n.full_name,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),e.jsx("input",{type:"tel",name:"phone",value:n.phone,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address Line 1"}),e.jsx("input",{type:"text",name:"address_line1",value:n.address_line1,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address Line 2 (Optional)"}),e.jsx("input",{type:"text",name:"address_line2",value:n.address_line2,onChange:c,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City"}),e.jsx("input",{type:"text",name:"city",value:n.city,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State"}),e.jsx("input",{type:"text",name:"state",value:n.state,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code"}),e.jsx("input",{type:"text",name:"postal_code",value:n.postal_code,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country"}),e.jsxs("select",{name:"country",value:n.country,onChange:c,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]",children:[e.jsx("option",{value:"",children:"Select Country"}),e.jsx("option",{value:"IN",children:"India"}),e.jsx("option",{value:"US",children:"United States"}),e.jsx("option",{value:"GB",children:"United Kingdom"})]})]}),e.jsx("div",{className:"md:col-span-2",children:e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",name:"is_default",checked:n.is_default,onChange:c,className:"rounded text-[#88cc14] focus:ring-[#88cc14]"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Set as default address"})]})})]}),e.jsx("button",{type:"submit",className:"w-full bg-[#88cc14] text-black font-bold py-3 px-4 rounded-lg hover:bg-[#7ab811] transition-colors",children:g})]})}function G({cart:d,addresses:u,onAddAddress:g,onPlaceOrder:n}){const[l,c]=o.useState(1),[i,t]=o.useState(null),[x,h]=o.useState(null),j=d.reduce((s,y)=>s+y.price*y.quantity,0),p=d.some(s=>s.is_physical)?99:0,f=j+p,C=async s=>{const y=await g(s);t(y.id),c(2)},v=async()=>{await n({addressId:i,paymentMethod:x,total:f})};return e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"flex items-center justify-between mb-8",children:[{number:1,title:"Shipping"},{number:2,title:"Payment"},{number:3,title:"Review"}].map(s=>e.jsxs("div",{className:`flex items-center ${s.number<l?"text-[#88cc14]":s.number===l?"text-gray-900":"text-gray-400"}`,children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${s.number<l?"bg-[#88cc14] text-white":s.number===l?"bg-gray-900 text-white":"bg-gray-200 text-gray-600"}`,children:s.number}),e.jsx("span",{className:"ml-2 font-medium",children:s.title}),s.number<3&&e.jsx("div",{className:"mx-4 h-px w-16 bg-gray-200"})]},s.number))}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[l===1&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Shipping Address"}),u.length>0&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Saved Addresses"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map(s=>e.jsxs("div",{onClick:()=>t(s.id),className:`p-4 rounded-lg border-2 cursor-pointer transition-colors ${i===s.id?"border-[#88cc14] bg-[#88cc14]/5":"border-gray-200 hover:border-gray-300"}`,children:[e.jsx("div",{className:"font-medium text-gray-900",children:s.full_name}),e.jsxs("div",{className:"text-gray-600 text-sm",children:[s.address_line1,s.address_line2&&e.jsxs(e.Fragment,{children:[", ",s.address_line2]})]}),e.jsxs("div",{className:"text-gray-600 text-sm",children:[s.city,", ",s.state," ",s.postal_code]}),e.jsx("div",{className:"text-gray-600 text-sm",children:s.country}),e.jsx("div",{className:"text-gray-600 text-sm",children:s.phone})]},s.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:u.length>0?"Add New Address":"Enter Shipping Address"}),e.jsx(Y,{onSubmit:C})]})]}),l===2&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Payment Method"}),e.jsx("div",{className:"space-y-4",children:[{id:"card",icon:$,title:"Credit/Debit Card"},{id:"paypal",icon:E,title:"PayPal"}].map(s=>e.jsx("div",{onClick:()=>h(s.id),className:`p-4 rounded-lg border-2 cursor-pointer transition-colors ${x===s.id?"border-[#88cc14] bg-[#88cc14]/5":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(s.icon,{className:"text-2xl"}),e.jsx("span",{className:"font-medium",children:s.title})]})},s.id))}),x==="card"&&e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Card Number"}),e.jsx("input",{type:"text",placeholder:"1234 5678 9012 3456",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Expiry Date"}),e.jsx("input",{type:"text",placeholder:"MM/YY",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CVV"}),e.jsx("input",{type:"text",placeholder:"123",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-[#88cc14] focus:border-[#88cc14]"})]})]})]})]}),l===3&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Order Summary"}),e.jsx("div",{className:"space-y-4 mb-6",children:d.map(s=>e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:s.image_url,alt:s.name,className:"w-16 h-16 object-cover rounded"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Quantity: ",s.quantity]})]}),e.jsxs("div",{className:"font-medium text-gray-900",children:["₹",(s.price*s.quantity).toFixed(2)]})]},s.id))}),e.jsxs("div",{className:"border-t border-gray-200 pt-4 space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-gray-600",children:[e.jsx("span",{children:"Subtotal"}),e.jsxs("span",{children:["₹",j.toFixed(2)]})]}),e.jsxs("div",{className:"flex justify-between text-gray-600",children:[e.jsx("span",{children:"Shipping"}),e.jsx("span",{children:p>0?`₹${p.toFixed(2)}`:"Free"})]}),e.jsxs("div",{className:"flex justify-between text-xl font-bold text-gray-900 pt-2",children:[e.jsx("span",{children:"Total"}),e.jsxs("span",{children:["₹",f.toFixed(2)]})]})]})]}),e.jsxs("div",{className:"mt-8 flex justify-between",children:[l>1&&e.jsx("button",{onClick:()=>c(l-1),className:"px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Back"}),l<3?e.jsx("button",{onClick:()=>c(l+1),disabled:l===1&&!i||l===2&&!x,className:"ml-auto bg-[#88cc14] text-black font-bold px-6 py-2 rounded-lg hover:bg-[#7ab811] transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Continue"}):e.jsxs("button",{onClick:v,className:"ml-auto bg-[#88cc14] text-black font-bold px-6 py-2 rounded-lg hover:bg-[#7ab811] transition-colors flex items-center gap-2",children:[e.jsx(I,{}),"Place Order"]})]})]})]})}function Q(){const[d,u]=o.useState([]),[g,n]=o.useState(!0),[l,c]=o.useState("all"),[i,t]=o.useState([]),[x,h]=o.useState(!1),[j,p]=o.useState(!1),[f,C]=o.useState([]);o.useEffect(()=>{(async()=>{try{const r=await U();u(r)}catch(r){console.error("Error fetching products:",r)}finally{n(!1)}})()},[]);const v=[{id:"all",name:"All Products",icon:O},{id:"apparel",name:"Apparel",icon:B},{id:"coins",name:"Coin Packs",icon:R}],s=l==="all"?d:d.filter(a=>a.category.toLowerCase()===l),y=(a,r=null)=>{t(m=>m.find(b=>b.id===a.id&&(!r||b.size===r))?m.map(b=>b.id===a.id&&(!r||b.size===r)?{...b,quantity:b.quantity+1}:b):[...m,{...a,quantity:1,size:r}]),h(!0)},F=(a,r)=>{r<1||t(m=>m.map(N=>N.id===a?{...N,quantity:r}:N))},_=a=>{t(r=>r.filter(m=>m.id!==a))},q=()=>{h(!1),p(!0)},A=async a=>{console.log("Adding address:",a)},P=async a=>{console.log("Placing order:",a)},k=({children:a,onClose:r})=>e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs(w.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto relative",children:[e.jsx("button",{onClick:r,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:e.jsx(V,{className:"text-xl"})}),e.jsx("div",{className:"p-6",children:a})]})});return e.jsxs("div",{className:"min-h-screen bg-gray-50 pt-20",children:[e.jsx("div",{className:"bg-black text-white py-12 md:py-16",children:e.jsx("div",{className:"container mx-auto px-4 md:px-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"XCerberus Store"}),e.jsx("p",{className:"text-gray-400 text-base md:text-lg mb-8",children:"Get official XCerberus merchandise and coin packs to unlock challenges."}),e.jsxs("button",{onClick:()=>h(!0),className:"inline-flex items-center gap-3 bg-primary text-black px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors",children:[e.jsx(D,{className:"text-lg"}),e.jsxs("span",{className:"font-bold",children:[i.reduce((a,r)=>a+r.quantity,0)," items"]}),e.jsx("span",{className:"mx-2 text-black/60",children:"|"}),e.jsxs("span",{className:"font-bold",children:["₹",i.reduce((a,r)=>a+r.price*r.quantity,0).toFixed(2)]})]})]})})}),e.jsxs("div",{className:"container mx-auto px-4 md:px-6 py-8",children:[e.jsx("div",{className:"flex flex-wrap gap-3 mb-8 justify-center md:justify-start",children:v.map(a=>e.jsxs("button",{onClick:()=>c(a.id),className:`flex items-center gap-2 px-4 py-2.5 rounded-full transition-all ${l===a.id?"bg-primary text-white":"bg-white text-gray-600 hover:bg-gray-100"}`,children:[e.jsx(a.icon,{className:"text-lg"}),e.jsx("span",{className:"font-medium",children:a.name})]},a.id))}),g?e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"})}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8",children:s.map((a,r)=>e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:r*.1},className:"bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6",children:[e.jsxs("div",{className:"relative mb-6 aspect-square rounded-lg overflow-hidden bg-gray-100",children:[e.jsx("img",{src:a.image_url,alt:a.name,className:"w-full h-full object-cover"}),a.category==="Coins"&&e.jsx("div",{className:"absolute top-3 right-3 bg-primary text-black text-sm font-bold px-3 py-1 rounded-full",children:"Best Value"})]}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:a.name}),e.jsx("p",{className:"text-gray-600 mb-6 line-clamp-2 text-sm",children:a.description}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-2xl font-bold text-primary",children:["₹",a.price.toFixed(2)]}),e.jsx("button",{onClick:()=>y(a),className:"bg-black text-white px-5 py-2.5 rounded-lg hover:bg-gray-800 transition-colors font-medium",children:"Add to Cart"})]}),a.is_physical&&e.jsx("div",{className:"mt-6 pt-4 border-t border-gray-100",children:e.jsx("div",{className:"flex flex-wrap gap-2",children:["S","M","L","XL"].map(m=>e.jsx("button",{onClick:()=>y(a,m),className:"min-w-[3rem] px-3 py-2 rounded-lg border border-gray-200 text-sm font-medium hover:border-primary hover:text-primary transition-colors",children:m},m))})})]},a.id))})]}),e.jsx(S,{children:x&&e.jsx(k,{onClose:()=>h(!1),children:e.jsx(X,{items:i,onUpdateQuantity:F,onRemoveItem:_,onCheckout:q})})}),e.jsx(S,{children:j&&e.jsx(k,{onClose:()=>p(!1),children:e.jsx(G,{cart:i,addresses:f,onAddAddress:A,onPlaceOrder:P})})})]})}export{Q as default};
