import{u as p,a as j,r as c,b as N,j as e,F as d,c as g,d as h,e as x,m}from"./index-CVvVjHWF.js";import{S as v}from"./StartHackHub-pG7sxzHy.js";import{C as f}from"./ChallengerHub-DvrHIatL.js";const $=()=>{const{darkMode:a}=p(),{simulationId:b,challengeId:y}=j(),[s,l]=c.useState("start-hack"),[t,u]=c.useState(!1),n=N();c.useEffect(()=>{window.location.pathname.includes("/challenger")?l("challenger"):l("start-hack")},[window.location.pathname]);const r=i=>{l(i),i==="start-hack"?n("/dashboard/start-hack"):i==="challenger"&&n("/dashboard/challenger")},o=()=>{u(!t)};return e.jsx("div",{className:`${a?"text-white":"text-gray-900"}`,children:e.jsxs("div",{className:"px-4 py-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:e.jsxs("div",{className:"flex space-x-4 mb-4 md:mb-0",children:[e.jsxs("button",{onClick:()=>r("start-hack"),className:`px-4 py-2 rounded-lg flex items-center ${s==="start-hack"?a?"bg-blue-600 text-white":"bg-blue-500 text-white":a?"bg-gray-800 text-gray-300 hover:bg-gray-700":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:[e.jsx(d,{className:"mr-2"}),"Start Hack"]}),e.jsxs("button",{onClick:()=>r("challenger"),className:`px-4 py-2 rounded-lg flex items-center ${s==="challenger"?a?"bg-blue-600 text-white":"bg-blue-500 text-white":a?"bg-gray-800 text-gray-300 hover:bg-gray-700":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:[e.jsx(g,{className:"mr-2"}),"Challenger"]})]})}),e.jsx("div",{className:"mb-8",children:s==="start-hack"?e.jsx("div",{children:e.jsx(v,{simulationId:b})}):e.jsx("div",{children:e.jsx(f,{challengeId:y})})}),s==="start-hack"&&e.jsxs("div",{className:`mt-12 p-6 rounded-lg ${a?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border`,children:[e.jsxs("div",{className:"flex justify-between items-center cursor-pointer",onClick:o,children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold flex items-center",children:[e.jsx(g,{className:"text-yellow-500 mr-2"}),"Ready for a Challenge?"]}),e.jsx("p",{className:`mt-2 ${a?"text-gray-400":"text-gray-600"}`,children:"Test your skills in competitive cybersecurity challenges"})]}),e.jsx("div",{children:t?e.jsx(h,{className:`text-xl ${a?"text-gray-400":"text-gray-600"}`}):e.jsx(x,{className:`text-xl ${a?"text-gray-400":"text-gray-600"}`})})]}),t&&e.jsxs(m.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},className:"mt-6",children:[e.jsx("p",{className:"mb-4",children:"The Challenger section offers competitive cybersecurity challenges where you can test your skills against other users. Complete challenges, earn points, and climb the leaderboards to prove your expertise."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Competitive Challenges"}),e.jsx("p",{className:`text-sm ${a?"text-gray-400":"text-gray-600"}`,children:"Solve real-world cybersecurity challenges with time tracking and scoring"})]}),e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Global Leaderboards"}),e.jsx("p",{className:`text-sm ${a?"text-gray-400":"text-gray-600"}`,children:"Compete against other users and see how you rank globally"})]})]}),e.jsx("button",{onClick:()=>r("challenger"),className:`px-4 py-2 rounded-lg ${a?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-500 hover:bg-blue-600 text-white"}`,children:"Go to Challenger"})]})]}),s==="challenger"&&e.jsxs("div",{className:`mt-12 p-6 rounded-lg ${a?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border`,children:[e.jsxs("div",{className:"flex justify-between items-center cursor-pointer",onClick:o,children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold flex items-center",children:[e.jsx(d,{className:"text-blue-500 mr-2"}),"Need More Practice?"]}),e.jsx("p",{className:`mt-2 ${a?"text-gray-400":"text-gray-600"}`,children:"Practice cybersecurity techniques in guided simulations"})]}),e.jsx("div",{children:t?e.jsx(h,{className:`text-xl ${a?"text-gray-400":"text-gray-600"}`}):e.jsx(x,{className:`text-xl ${a?"text-gray-400":"text-gray-600"}`})})]}),t&&e.jsxs(m.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},className:"mt-6",children:[e.jsx("p",{className:"mb-4",children:"The Start Hack section offers guided simulations where you can practice cybersecurity techniques in a safe environment. Learn at your own pace with step-by-step guidance and immediate feedback."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Guided Simulations"}),e.jsx("p",{className:`text-sm ${a?"text-gray-400":"text-gray-600"}`,children:"Practice techniques with step-by-step guidance and immediate feedback"})]}),e.jsxs("div",{className:`p-4 rounded-lg ${a?"bg-gray-800":"bg-gray-100"}`,children:[e.jsx("h3",{className:"font-bold mb-2",children:"Real-world Scenarios"}),e.jsx("p",{className:`text-sm ${a?"text-gray-400":"text-gray-600"}`,children:"Apply your skills in realistic cybersecurity scenarios"})]})]}),e.jsx("button",{onClick:()=>r("start-hack"),className:`px-4 py-2 rounded-lg ${a?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-500 hover:bg-blue-600 text-white"}`,children:"Go to Start Hack"})]})]})]})})};export{$ as default};
