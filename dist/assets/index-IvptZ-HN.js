const l={id:"ais-1",pathId:"ai-security",title:"AI Security Fundamentals",description:"Master the fundamentals of artificial intelligence security, including AI/ML concepts, security challenges, and the unique threat landscape facing AI systems.",objectives:["Understand AI and machine learning fundamentals","Learn AI security challenges and threat models","Master AI security principles and frameworks","Develop skills in AI risk assessment","Learn AI security governance basics","Implement foundational AI security practices"],difficulty:"Beginner",estimatedTime:120,sections:[{title:"AI and Machine Learning Fundamentals",content:`
        <h2>Artificial Intelligence and Machine Learning Overview</h2>
        <p>Understanding AI and ML fundamentals is essential for implementing effective security measures and identifying potential vulnerabilities in AI systems.</p>
        
        <h3>AI and ML Concepts</h3>
        <ul>
          <li><strong>Artificial Intelligence (AI):</strong>
            <ul>
              <li>Computer systems performing tasks requiring human intelligence</li>
              <li>Problem-solving, learning, and decision-making capabilities</li>
              <li>Narrow AI vs. General AI concepts</li>
              <li>AI applications across industries</li>
            </ul>
          </li>
          <li><strong>Machine Learning (ML):</strong>
            <ul>
              <li>Subset of AI focused on learning from data</li>
              <li>Supervised, unsupervised, and reinforcement learning</li>
              <li>Training, validation, and testing phases</li>
              <li>Model performance and evaluation metrics</li>
            </ul>
          </li>
          <li><strong>Deep Learning:</strong>
            <ul>
              <li>Neural networks with multiple layers</li>
              <li>Feature extraction and representation learning</li>
              <li>Convolutional and recurrent neural networks</li>
              <li>Transfer learning and pre-trained models</li>
            </ul>
          </li>
        </ul>
        
        <h3>AI/ML Lifecycle and Components</h3>
        <ul>
          <li><strong>Data Pipeline:</strong>
            <ul>
              <li>Data collection and acquisition</li>
              <li>Data preprocessing and cleaning</li>
              <li>Feature engineering and selection</li>
              <li>Data validation and quality assurance</li>
            </ul>
          </li>
          <li><strong>Model Development:</strong>
            <ul>
              <li>Algorithm selection and design</li>
              <li>Model training and optimization</li>
              <li>Hyperparameter tuning</li>
              <li>Model validation and testing</li>
            </ul>
          </li>
          <li><strong>Deployment and Operations:</strong>
            <ul>
              <li>Model deployment strategies</li>
              <li>Inference and prediction serving</li>
              <li>Model monitoring and maintenance</li>
              <li>Continuous integration and deployment</li>
            </ul>
          </li>
        </ul>
        
        <h3>AI/ML Infrastructure and Platforms</h3>
        <ul>
          <li><strong>Development Frameworks:</strong>
            <ul>
              <li>TensorFlow, PyTorch, Scikit-learn</li>
              <li>Keras, JAX, and other libraries</li>
              <li>Cloud-based ML platforms</li>
              <li>AutoML and low-code solutions</li>
            </ul>
          </li>
          <li><strong>Compute Infrastructure:</strong>
            <ul>
              <li>GPUs and specialized AI hardware</li>
              <li>Cloud computing resources</li>
              <li>Edge computing and mobile deployment</li>
              <li>Distributed training systems</li>
            </ul>
          </li>
          <li><strong>Data Storage and Management:</strong>
            <ul>
              <li>Data lakes and warehouses</li>
              <li>Feature stores and data versioning</li>
              <li>Real-time data streaming</li>
              <li>Data governance and lineage</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"AI Security Challenges and Threat Landscape",content:`
        <h2>AI Security Challenges and Unique Threats</h2>
        <p>AI systems face unique security challenges that differ from traditional software security, requiring specialized approaches and understanding.</p>
        
        <h3>Unique AI Security Challenges</h3>
        <ul>
          <li><strong>Data-Driven Vulnerabilities:</strong>
            <ul>
              <li>Training data poisoning and manipulation</li>
              <li>Data privacy and confidentiality issues</li>
              <li>Bias and fairness in datasets</li>
              <li>Data provenance and integrity</li>
            </ul>
          </li>
          <li><strong>Model-Specific Attacks:</strong>
            <ul>
              <li>Adversarial examples and inputs</li>
              <li>Model inversion and extraction</li>
              <li>Membership inference attacks</li>
              <li>Backdoor and trojan attacks</li>
            </ul>
          </li>
          <li><strong>Operational Security Issues:</strong>
            <ul>
              <li>Model drift and degradation</li>
              <li>Inference-time attacks</li>
              <li>Supply chain vulnerabilities</li>
              <li>Deployment and scaling challenges</li>
            </ul>
          </li>
        </ul>
        
        <h3>AI Threat Actors and Motivations</h3>
        <ul>
          <li><strong>Malicious Actors:</strong>
            <ul>
              <li>Nation-state attackers targeting AI systems</li>
              <li>Cybercriminals exploiting AI vulnerabilities</li>
              <li>Competitors seeking intellectual property</li>
              <li>Insider threats and malicious employees</li>
            </ul>
          </li>
          <li><strong>Attack Motivations:</strong>
            <ul>
              <li>Data theft and privacy violations</li>
              <li>Model theft and intellectual property</li>
              <li>Service disruption and availability attacks</li>
              <li>Manipulation of AI decision-making</li>
            </ul>
          </li>
          <li><strong>Attack Vectors:</strong>
            <ul>
              <li>Input manipulation and adversarial examples</li>
              <li>Training data contamination</li>
              <li>Model parameter extraction</li>
              <li>Infrastructure and platform attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>AI Security vs. Traditional Security</h3>
        <ul>
          <li><strong>Differences from Traditional Security:</strong>
            <ul>
              <li>Probabilistic vs. deterministic behavior</li>
              <li>Data-dependent security properties</li>
              <li>Black-box nature of many AI models</li>
              <li>Continuous learning and adaptation</li>
            </ul>
          </li>
          <li><strong>Shared Security Concerns:</strong>
            <ul>
              <li>Infrastructure and platform security</li>
              <li>Access control and authentication</li>
              <li>Network security and encryption</li>
              <li>Compliance and regulatory requirements</li>
            </ul>
          </li>
          <li><strong>AI-Specific Security Requirements:</strong>
            <ul>
              <li>Model integrity and authenticity</li>
              <li>Training data protection</li>
              <li>Inference privacy and confidentiality</li>
              <li>Explainability and transparency</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"AI Security Principles and Frameworks",content:`
        <h2>AI Security Principles and Governance Frameworks</h2>
        <p>Establishing security principles and frameworks provides the foundation for building secure, trustworthy, and responsible AI systems.</p>
        
        <h3>Core AI Security Principles</h3>
        <ul>
          <li><strong>Confidentiality:</strong>
            <ul>
              <li>Protecting training data and model parameters</li>
              <li>Inference privacy and data protection</li>
              <li>Secure multi-party computation</li>
              <li>Differential privacy techniques</li>
            </ul>
          </li>
          <li><strong>Integrity:</strong>
            <ul>
              <li>Model authenticity and tamper detection</li>
              <li>Training data integrity verification</li>
              <li>Secure model updates and versioning</li>
              <li>Output validation and verification</li>
            </ul>
          </li>
          <li><strong>Availability:</strong>
            <ul>
              <li>Resilient AI service delivery</li>
              <li>DDoS protection for AI endpoints</li>
              <li>Fault tolerance and recovery</li>
              <li>Performance monitoring and optimization</li>
            </ul>
          </li>
        </ul>
        
        <h3>AI Security Frameworks</h3>
        <ul>
          <li><strong>NIST AI Risk Management Framework:</strong>
            <ul>
              <li>AI risk identification and assessment</li>
              <li>Risk mitigation strategies</li>
              <li>Governance and oversight</li>
              <li>Continuous monitoring and improvement</li>
            </ul>
          </li>
          <li><strong>ISO/IEC 23053 (AI Security):</strong>
            <ul>
              <li>AI system security requirements</li>
              <li>Security controls and measures</li>
              <li>Risk assessment methodologies</li>
              <li>Implementation guidance</li>
            </ul>
          </li>
          <li><strong>OWASP Machine Learning Security:</strong>
            <ul>
              <li>Top 10 ML security risks</li>
              <li>Security testing methodologies</li>
              <li>Secure development practices</li>
              <li>Community-driven best practices</li>
            </ul>
          </li>
        </ul>
        
        <h3>Responsible AI and Ethics</h3>
        <ul>
          <li><strong>Ethical AI Principles:</strong>
            <ul>
              <li>Fairness and non-discrimination</li>
              <li>Transparency and explainability</li>
              <li>Accountability and responsibility</li>
              <li>Human oversight and control</li>
            </ul>
          </li>
          <li><strong>Trustworthy AI:</strong>
            <ul>
              <li>Reliability and robustness</li>
              <li>Safety and harm prevention</li>
              <li>Privacy and data protection</li>
              <li>Human-centric design</li>
            </ul>
          </li>
          <li><strong>AI Governance:</strong>
            <ul>
              <li>AI ethics committees and boards</li>
              <li>Policy development and enforcement</li>
              <li>Risk assessment and management</li>
              <li>Stakeholder engagement</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary difference between AI security and traditional cybersecurity?",options:["AI security only focuses on network protection","AI security deals with data-dependent and probabilistic security properties","AI security is less important than traditional security","AI security only applies to cloud environments"],correctAnswer:1,explanation:"AI security differs from traditional cybersecurity because it deals with data-dependent and probabilistic security properties, where the security of the system depends on the training data and the model's behavior is probabilistic rather than deterministic."},{question:"Which of the following is a unique AI-specific security threat?",options:["SQL injection attacks","Adversarial examples","Cross-site scripting (XSS)","Buffer overflow attacks"],correctAnswer:1,explanation:"Adversarial examples are a unique AI-specific security threat where carefully crafted inputs can cause AI models to make incorrect predictions or classifications, which doesn't exist in traditional software systems."},{question:"What is the primary goal of the NIST AI Risk Management Framework?",options:["To replace all existing security frameworks","To provide guidance for identifying, assessing, and mitigating AI risks","To eliminate all AI security threats","To standardize AI development tools"],correctAnswer:1,explanation:"The NIST AI Risk Management Framework provides guidance for identifying, assessing, and mitigating AI risks through structured governance, risk management processes, and continuous monitoring approaches."}]},type:"quiz"}]},a={id:"ais-2",pathId:"ai-security",title:"Machine Learning Security Basics",description:"Learn the fundamental security concepts specific to machine learning systems, including training security, model protection, and inference safety.",objectives:["Understand ML pipeline security requirements","Learn training phase security challenges","Master model protection techniques","Develop skills in inference security","Learn ML security testing basics","Implement basic ML security controls"],difficulty:"Beginner",estimatedTime:130,sections:[{title:"ML Pipeline Security",content:`
        <h2>Machine Learning Pipeline Security Overview</h2>
        <p>The ML pipeline consists of multiple stages, each with unique security requirements and potential vulnerabilities that must be addressed comprehensively.</p>
        
        <h3>ML Pipeline Stages and Security</h3>
        <ul>
          <li><strong>Data Collection and Preparation:</strong>
            <ul>
              <li>Data source validation and authentication</li>
              <li>Data integrity verification</li>
              <li>Privacy-preserving data collection</li>
              <li>Data sanitization and cleaning</li>
            </ul>
          </li>
          <li><strong>Feature Engineering:</strong>
            <ul>
              <li>Feature extraction security</li>
              <li>Feature selection validation</li>
              <li>Dimensionality reduction safety</li>
              <li>Feature store security</li>
            </ul>
          </li>
          <li><strong>Model Training:</strong>
            <ul>
              <li>Training environment security</li>
              <li>Hyperparameter protection</li>
              <li>Training data access controls</li>
              <li>Model checkpoint security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Security in ML</h3>
        <ul>
          <li><strong>Training Data Protection:</strong>
            <ul>
              <li>Data encryption at rest and in transit</li>
              <li>Access control and authentication</li>
              <li>Data anonymization and pseudonymization</li>
              <li>Secure data sharing protocols</li>
            </ul>
          </li>
          <li><strong>Data Quality and Integrity:</strong>
            <ul>
              <li>Data validation and verification</li>
              <li>Outlier detection and handling</li>
              <li>Data lineage and provenance</li>
              <li>Version control for datasets</li>
            </ul>
          </li>
          <li><strong>Privacy-Preserving Techniques:</strong>
            <ul>
              <li>Differential privacy mechanisms</li>
              <li>Federated learning approaches</li>
              <li>Homomorphic encryption</li>
              <li>Secure multi-party computation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Model Development Security</h3>
        <ul>
          <li><strong>Secure Development Environment:</strong>
            <ul>
              <li>Isolated development environments</li>
              <li>Code review and validation</li>
              <li>Dependency management</li>
              <li>Version control security</li>
            </ul>
          </li>
          <li><strong>Algorithm Security:</strong>
            <ul>
              <li>Algorithm selection criteria</li>
              <li>Implementation validation</li>
              <li>Robustness testing</li>
              <li>Performance monitoring</li>
            </ul>
          </li>
          <li><strong>Training Process Security:</strong>
            <ul>
              <li>Secure training protocols</li>
              <li>Resource access controls</li>
              <li>Training monitoring and logging</li>
              <li>Checkpoint validation</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Model Protection and Integrity",content:`
        <h2>ML Model Protection and Integrity Assurance</h2>
        <p>Protecting ML models from tampering, theft, and unauthorized access is crucial for maintaining system security and intellectual property.</p>
        
        <h3>Model Integrity Protection</h3>
        <ul>
          <li><strong>Model Authentication:</strong>
            <ul>
              <li>Digital signatures for models</li>
              <li>Cryptographic hashing</li>
              <li>Certificate-based validation</li>
              <li>Tamper detection mechanisms</li>
            </ul>
          </li>
          <li><strong>Model Versioning and Control:</strong>
            <ul>
              <li>Secure model repositories</li>
              <li>Version control systems</li>
              <li>Change tracking and auditing</li>
              <li>Rollback capabilities</li>
            </ul>
          </li>
          <li><strong>Model Encryption:</strong>
            <ul>
              <li>Model parameter encryption</li>
              <li>Secure model storage</li>
              <li>Encrypted model transmission</li>
              <li>Key management for models</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intellectual Property Protection</h3>
        <ul>
          <li><strong>Model Theft Prevention:</strong>
            <ul>
              <li>Model extraction attack mitigation</li>
              <li>Query limiting and monitoring</li>
              <li>Watermarking techniques</li>
              <li>Fingerprinting methods</li>
            </ul>
          </li>
          <li><strong>Access Control:</strong>
            <ul>
              <li>Role-based access control (RBAC)</li>
              <li>Attribute-based access control (ABAC)</li>
              <li>API authentication and authorization</li>
              <li>Multi-factor authentication</li>
            </ul>
          </li>
          <li><strong>Secure Model Sharing:</strong>
            <ul>
              <li>Federated learning protocols</li>
              <li>Secure aggregation methods</li>
              <li>Privacy-preserving collaboration</li>
              <li>Trusted execution environments</li>
            </ul>
          </li>
        </ul>
        
        <h3>Model Validation and Testing</h3>
        <ul>
          <li><strong>Security Testing:</strong>
            <ul>
              <li>Adversarial testing frameworks</li>
              <li>Robustness evaluation</li>
              <li>Stress testing protocols</li>
              <li>Security benchmarking</li>
            </ul>
          </li>
          <li><strong>Validation Techniques:</strong>
            <ul>
              <li>Cross-validation security</li>
              <li>Hold-out testing</li>
              <li>Statistical validation</li>
              <li>Performance monitoring</li>
            </ul>
          </li>
          <li><strong>Continuous Monitoring:</strong>
            <ul>
              <li>Model drift detection</li>
              <li>Performance degradation alerts</li>
              <li>Anomaly detection</li>
              <li>Security incident monitoring</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Inference Security and Deployment",content:`
        <h2>ML Inference Security and Secure Deployment</h2>
        <p>Securing the inference phase and deployment of ML models is critical for protecting against runtime attacks and ensuring reliable service delivery.</p>
        
        <h3>Inference-Time Security</h3>
        <ul>
          <li><strong>Input Validation and Sanitization:</strong>
            <ul>
              <li>Input format validation</li>
              <li>Range and boundary checking</li>
              <li>Malicious input detection</li>
              <li>Data preprocessing security</li>
            </ul>
          </li>
          <li><strong>Adversarial Input Protection:</strong>
            <ul>
              <li>Adversarial detection methods</li>
              <li>Input transformation techniques</li>
              <li>Ensemble defense strategies</li>
              <li>Certified defense mechanisms</li>
            </ul>
          </li>
          <li><strong>Output Protection:</strong>
            <ul>
              <li>Output validation and filtering</li>
              <li>Confidence threshold enforcement</li>
              <li>Result sanitization</li>
              <li>Information leakage prevention</li>
            </ul>
          </li>
        </ul>
        
        <h3>Secure Deployment Practices</h3>
        <ul>
          <li><strong>Infrastructure Security:</strong>
            <ul>
              <li>Secure containerization</li>
              <li>Network segmentation</li>
              <li>Load balancer security</li>
              <li>API gateway protection</li>
            </ul>
          </li>
          <li><strong>Runtime Protection:</strong>
            <ul>
              <li>Runtime application self-protection (RASP)</li>
              <li>Container security monitoring</li>
              <li>Process isolation</li>
              <li>Resource limitation</li>
            </ul>
          </li>
          <li><strong>Scalability and Availability:</strong>
            <ul>
              <li>Auto-scaling security</li>
              <li>DDoS protection</li>
              <li>Failover mechanisms</li>
              <li>Performance monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Privacy-Preserving Inference</h3>
        <ul>
          <li><strong>Differential Privacy:</strong>
            <ul>
              <li>Privacy budget management</li>
              <li>Noise injection techniques</li>
              <li>Privacy accounting</li>
              <li>Utility-privacy trade-offs</li>
            </ul>
          </li>
          <li><strong>Secure Computation:</strong>
            <ul>
              <li>Homomorphic encryption inference</li>
              <li>Secure multi-party computation</li>
              <li>Trusted execution environments</li>
              <li>Zero-knowledge proofs</li>
            </ul>
          </li>
          <li><strong>Data Minimization:</strong>
            <ul>
              <li>Feature selection optimization</li>
              <li>Data reduction techniques</li>
              <li>Purpose limitation</li>
              <li>Retention policies</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which stage of the ML pipeline is most vulnerable to data poisoning attacks?",options:["Model deployment","Feature engineering","Data collection and preparation","Model inference"],correctAnswer:2,explanation:"Data collection and preparation is most vulnerable to data poisoning attacks because this is where malicious data can be introduced into the training dataset, potentially compromising the entire model's behavior."},{question:"What is the primary purpose of model watermarking in ML security?",options:["To improve model performance","To detect model theft and unauthorized use","To reduce model size","To speed up inference"],correctAnswer:1,explanation:"Model watermarking is primarily used to detect model theft and unauthorized use by embedding unique identifiers that can prove ownership and detect when a model has been copied or stolen."},{question:"Which technique is most effective for protecting against adversarial examples during inference?",options:["Increasing model complexity","Input validation and adversarial detection","Using more training data","Reducing model parameters"],correctAnswer:1,explanation:"Input validation and adversarial detection are most effective for protecting against adversarial examples during inference, as they can identify and filter malicious inputs before they reach the model."}]},type:"quiz"}]},r={id:"ais-3",pathId:"ai-security",title:"AI Threat Landscape",description:"Explore the comprehensive threat landscape facing AI systems, including attack vectors, threat actors, and emerging AI-specific security risks.",objectives:["Understand AI-specific attack vectors and techniques","Learn about threat actors targeting AI systems","Master AI threat modeling methodologies","Develop skills in AI risk assessment","Learn emerging AI security threats","Implement AI threat intelligence practices"],difficulty:"Intermediate",estimatedTime:140,sections:[{title:"AI Attack Vectors and Techniques",content:`
        <h2>AI-Specific Attack Vectors and Techniques</h2>
        <p>AI systems face unique attack vectors that exploit the probabilistic nature of machine learning and the dependency on training data.</p>
        
        <h3>Data-Based Attacks</h3>
        <ul>
          <li><strong>Data Poisoning Attacks:</strong>
            <ul>
              <li>Training data contamination</li>
              <li>Label flipping and manipulation</li>
              <li>Backdoor injection through data</li>
              <li>Availability attacks on data quality</li>
            </ul>
          </li>
          <li><strong>Data Extraction Attacks:</strong>
            <ul>
              <li>Training data reconstruction</li>
              <li>Membership inference attacks</li>
              <li>Property inference attacks</li>
              <li>Model inversion techniques</li>
            </ul>
          </li>
          <li><strong>Data Privacy Attacks:</strong>
            <ul>
              <li>Differential privacy attacks</li>
              <li>Linkage attacks on anonymized data</li>
              <li>Attribute inference attacks</li>
              <li>Re-identification techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Model-Based Attacks</h3>
        <ul>
          <li><strong>Adversarial Attacks:</strong>
            <ul>
              <li>Evasion attacks with adversarial examples</li>
              <li>Fast Gradient Sign Method (FGSM)</li>
              <li>Projected Gradient Descent (PGD)</li>
              <li>Carlini & Wagner (C&W) attacks</li>
            </ul>
          </li>
          <li><strong>Model Extraction Attacks:</strong>
            <ul>
              <li>Functionality stealing attacks</li>
              <li>Fidelity extraction techniques</li>
              <li>Query-based model stealing</li>
              <li>Surrogate model creation</li>
            </ul>
          </li>
          <li><strong>Model Manipulation:</strong>
            <ul>
              <li>Trojan and backdoor attacks</li>
              <li>Neural network trojans</li>
              <li>Trigger-based attacks</li>
              <li>Model parameter manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Infrastructure and Platform Attacks</h3>
        <ul>
          <li><strong>ML Pipeline Attacks:</strong>
            <ul>
              <li>Supply chain attacks on ML frameworks</li>
              <li>Dependency confusion attacks</li>
              <li>Container and orchestration attacks</li>
              <li>CI/CD pipeline compromise</li>
            </ul>
          </li>
          <li><strong>Cloud ML Platform Attacks:</strong>
            <ul>
              <li>Multi-tenancy exploitation</li>
              <li>Shared resource attacks</li>
              <li>API security vulnerabilities</li>
              <li>Privilege escalation attacks</li>
            </ul>
          </li>
          <li><strong>Hardware-Based Attacks:</strong>
            <ul>
              <li>GPU memory attacks</li>
              <li>Side-channel attacks on AI accelerators</li>
              <li>Fault injection attacks</li>
              <li>Hardware trojan insertion</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Threat Actors and Motivations",content:`
        <h2>AI Threat Actors and Their Motivations</h2>
        <p>Understanding the various threat actors targeting AI systems and their motivations is crucial for developing appropriate defense strategies.</p>
        
        <h3>Nation-State Actors</h3>
        <ul>
          <li><strong>Strategic Objectives:</strong>
            <ul>
              <li>AI technology theft and espionage</li>
              <li>Critical infrastructure disruption</li>
              <li>Economic and military advantage</li>
              <li>Surveillance and social control</li>
            </ul>
          </li>
          <li><strong>Capabilities and Resources:</strong>
            <ul>
              <li>Advanced persistent threat (APT) groups</li>
              <li>Sophisticated attack techniques</li>
              <li>Long-term campaign planning</li>
              <li>Significant financial resources</li>
            </ul>
          </li>
          <li><strong>Target Selection:</strong>
            <ul>
              <li>AI research institutions</li>
              <li>Technology companies</li>
              <li>Government AI systems</li>
              <li>Critical infrastructure AI</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cybercriminal Organizations</h3>
        <ul>
          <li><strong>Financial Motivations:</strong>
            <ul>
              <li>AI model theft for resale</li>
              <li>Ransomware targeting AI systems</li>
              <li>Fraud using AI manipulation</li>
              <li>Cryptocurrency mining on AI infrastructure</li>
            </ul>
          </li>
          <li><strong>Attack Methods:</strong>
            <ul>
              <li>Automated attack tools</li>
              <li>AI-as-a-Service exploitation</li>
              <li>Social engineering campaigns</li>
              <li>Dark web AI services</li>
            </ul>
          </li>
          <li><strong>Emerging Trends:</strong>
            <ul>
              <li>AI-powered cybercrime tools</li>
              <li>Deepfake-based fraud</li>
              <li>Automated vulnerability discovery</li>
              <li>AI-generated phishing content</li>
            </ul>
          </li>
        </ul>
        
        <h3>Insider Threats</h3>
        <ul>
          <li><strong>Malicious Insiders:</strong>
            <ul>
              <li>Data scientists and ML engineers</li>
              <li>System administrators</li>
              <li>Business users with AI access</li>
              <li>Third-party contractors</li>
            </ul>
          </li>
          <li><strong>Threat Scenarios:</strong>
            <ul>
              <li>Training data manipulation</li>
              <li>Model parameter theft</li>
              <li>Backdoor insertion</li>
              <li>Intellectual property theft</li>
            </ul>
          </li>
          <li><strong>Detection Challenges:</strong>
            <ul>
              <li>Legitimate access privileges</li>
              <li>Subtle manipulation techniques</li>
              <li>Long-term dormant threats</li>
              <li>Difficult attribution</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Emerging AI Security Threats",content:`
        <h2>Emerging AI Security Threats and Future Risks</h2>
        <p>The AI threat landscape continues to evolve rapidly, with new attack techniques and threat vectors emerging as AI technology advances.</p>
        
        <h3>Advanced AI Attacks</h3>
        <ul>
          <li><strong>AI-Powered Attacks:</strong>
            <ul>
              <li>Automated vulnerability discovery</li>
              <li>AI-generated malware</li>
              <li>Intelligent social engineering</li>
              <li>Adaptive attack strategies</li>
            </ul>
          </li>
          <li><strong>Generative AI Threats:</strong>
            <ul>
              <li>Deepfake attacks and manipulation</li>
              <li>Synthetic media generation</li>
              <li>AI-generated disinformation</li>
              <li>Voice and video impersonation</li>
            </ul>
          </li>
          <li><strong>Large Language Model (LLM) Attacks:</strong>
            <ul>
              <li>Prompt injection attacks</li>
              <li>Jailbreaking techniques</li>
              <li>Training data extraction</li>
              <li>Model alignment attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cross-Domain AI Threats</h3>
        <ul>
          <li><strong>IoT and Edge AI Threats:</strong>
            <ul>
              <li>Resource-constrained attack vectors</li>
              <li>Distributed AI system attacks</li>
              <li>Edge device compromise</li>
              <li>Federated learning attacks</li>
            </ul>
          </li>
          <li><strong>Autonomous System Threats:</strong>
            <ul>
              <li>Autonomous vehicle attacks</li>
              <li>Drone and robotics manipulation</li>
              <li>Industrial automation threats</li>
              <li>Smart city infrastructure attacks</li>
            </ul>
          </li>
          <li><strong>Healthcare AI Threats:</strong>
            <ul>
              <li>Medical AI system manipulation</li>
              <li>Patient data privacy attacks</li>
              <li>Diagnostic system compromise</li>
              <li>Treatment recommendation attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Future Threat Landscape</h3>
        <ul>
          <li><strong>Quantum-AI Intersection:</strong>
            <ul>
              <li>Quantum machine learning attacks</li>
              <li>Post-quantum AI security</li>
              <li>Quantum advantage in attacks</li>
              <li>Quantum-resistant AI systems</li>
            </ul>
          </li>
          <li><strong>AI Arms Race:</strong>
            <ul>
              <li>Adversarial AI development</li>
              <li>Defense-offense co-evolution</li>
              <li>AI security research gaps</li>
              <li>International AI security cooperation</li>
            </ul>
          </li>
          <li><strong>Regulatory and Compliance Threats:</strong>
            <ul>
              <li>AI governance failures</li>
              <li>Compliance violation risks</li>
              <li>Regulatory arbitrage</li>
              <li>Cross-border AI security challenges</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary characteristic of data poisoning attacks in AI systems?",options:["They target the inference phase only","They manipulate training data to compromise model behavior","They only affect model performance, not security","They require physical access to AI hardware"],correctAnswer:1,explanation:"Data poisoning attacks manipulate training data to compromise model behavior, causing the AI system to make incorrect predictions or classifications when encountering specific inputs or conditions."},{question:"Which type of attack is most concerning for large language models (LLMs)?",options:["Buffer overflow attacks","SQL injection attacks","Prompt injection attacks","Cross-site scripting attacks"],correctAnswer:2,explanation:"Prompt injection attacks are most concerning for LLMs because they can manipulate the model's behavior by crafting specific prompts that bypass safety measures or extract sensitive information from the training data."},{question:"What makes insider threats particularly dangerous in AI systems?",options:["They always have physical access to hardware","They have legitimate access privileges and deep system knowledge","They only target small AI systems","They are easily detected by traditional security tools"],correctAnswer:1,explanation:"Insider threats are particularly dangerous in AI systems because they have legitimate access privileges and deep knowledge of the system architecture, making their malicious activities harder to detect and potentially more damaging."}]},type:"quiz"}]},o={id:"ais-4",pathId:"ai-security",title:"Data Security in AI",description:"Master data security principles for AI systems, including training data protection, privacy-preserving techniques, and secure data handling throughout the AI lifecycle.",objectives:["Understand AI data security requirements","Learn training data protection techniques","Master privacy-preserving AI methods","Develop skills in secure data handling","Learn data governance for AI systems","Implement comprehensive data security controls"],difficulty:"Intermediate",estimatedTime:135,sections:[{title:"AI Data Security Fundamentals",content:`
        <h2>Data Security Requirements for AI Systems</h2>
        <p>AI systems have unique data security requirements due to their dependence on large datasets and the sensitive nature of training and inference data.</p>
        
        <h3>AI Data Types and Sensitivity</h3>
        <ul>
          <li><strong>Training Data:</strong>
            <ul>
              <li>Historical datasets for model training</li>
              <li>Labeled and unlabeled data</li>
              <li>Synthetic and augmented data</li>
              <li>Validation and test datasets</li>
            </ul>
          </li>
          <li><strong>Inference Data:</strong>
            <ul>
              <li>Real-time input data</li>
              <li>User-generated content</li>
              <li>Sensor and IoT data</li>
              <li>Streaming data feeds</li>
            </ul>
          </li>
          <li><strong>Model Data:</strong>
            <ul>
              <li>Model parameters and weights</li>
              <li>Hyperparameters and configurations</li>
              <li>Model metadata and lineage</li>
              <li>Performance metrics and logs</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Security Threats in AI</h3>
        <ul>
          <li><strong>Data Poisoning:</strong>
            <ul>
              <li>Malicious data injection</li>
              <li>Label manipulation attacks</li>
              <li>Backdoor data insertion</li>
              <li>Availability attacks on data quality</li>
            </ul>
          </li>
          <li><strong>Data Extraction:</strong>
            <ul>
              <li>Training data reconstruction</li>
              <li>Membership inference attacks</li>
              <li>Model inversion techniques</li>
              <li>Property inference attacks</li>
            </ul>
          </li>
          <li><strong>Privacy Violations:</strong>
            <ul>
              <li>Personal information exposure</li>
              <li>Sensitive attribute inference</li>
              <li>Re-identification attacks</li>
              <li>Linkage attacks on anonymized data</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Lifecycle Security</h3>
        <ul>
          <li><strong>Data Collection Security:</strong>
            <ul>
              <li>Source authentication and validation</li>
              <li>Data integrity verification</li>
              <li>Consent and authorization management</li>
              <li>Quality assurance and filtering</li>
            </ul>
          </li>
          <li><strong>Data Storage Security:</strong>
            <ul>
              <li>Encryption at rest</li>
              <li>Access control and authorization</li>
              <li>Data classification and labeling</li>
              <li>Backup and recovery security</li>
            </ul>
          </li>
          <li><strong>Data Processing Security:</strong>
            <ul>
              <li>Secure computation environments</li>
              <li>Data transformation security</li>
              <li>Pipeline integrity protection</li>
              <li>Audit logging and monitoring</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Privacy-Preserving AI Techniques",content:`
        <h2>Privacy-Preserving Techniques for AI Systems</h2>
        <p>Privacy-preserving AI techniques enable the development of AI systems while protecting individual privacy and sensitive information.</p>
        
        <h3>Differential Privacy</h3>
        <ul>
          <li><strong>Differential Privacy Fundamentals:</strong>
            <ul>
              <li>Privacy budget and epsilon values</li>
              <li>Noise injection mechanisms</li>
              <li>Laplace and Gaussian mechanisms</li>
              <li>Composition and privacy accounting</li>
            </ul>
          </li>
          <li><strong>DP in Machine Learning:</strong>
            <ul>
              <li>DP-SGD (Differentially Private Stochastic Gradient Descent)</li>
              <li>Private aggregation techniques</li>
              <li>Gradient clipping and noise addition</li>
              <li>Privacy-utility trade-offs</li>
            </ul>
          </li>
          <li><strong>Implementation Considerations:</strong>
            <ul>
              <li>Privacy budget allocation</li>
              <li>Hyperparameter tuning with privacy</li>
              <li>Model selection under privacy constraints</li>
              <li>Privacy auditing and verification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Federated Learning</h3>
        <ul>
          <li><strong>Federated Learning Architecture:</strong>
            <ul>
              <li>Centralized and decentralized approaches</li>
              <li>Client-server communication protocols</li>
              <li>Model aggregation techniques</li>
              <li>Secure aggregation methods</li>
            </ul>
          </li>
          <li><strong>Privacy Protection in FL:</strong>
            <ul>
              <li>Local differential privacy</li>
              <li>Secure multi-party computation</li>
              <li>Homomorphic encryption</li>
              <li>Gradient compression and quantization</li>
            </ul>
          </li>
          <li><strong>Security Challenges:</strong>
            <ul>
              <li>Byzantine and adversarial clients</li>
              <li>Model poisoning attacks</li>
              <li>Inference attacks on updates</li>
              <li>Communication security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Secure Multi-Party Computation</h3>
        <ul>
          <li><strong>SMPC Fundamentals:</strong>
            <ul>
              <li>Secret sharing schemes</li>
              <li>Garbled circuits</li>
              <li>Oblivious transfer protocols</li>
              <li>Zero-knowledge proofs</li>
            </ul>
          </li>
          <li><strong>SMPC for Machine Learning:</strong>
            <ul>
              <li>Private set intersection</li>
              <li>Secure aggregation protocols</li>
              <li>Private inference systems</li>
              <li>Collaborative learning without data sharing</li>
            </ul>
          </li>
          <li><strong>Performance and Scalability:</strong>
            <ul>
              <li>Computational overhead considerations</li>
              <li>Communication complexity</li>
              <li>Optimization techniques</li>
              <li>Practical deployment challenges</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Data Governance and Compliance",content:`
        <h2>Data Governance and Compliance for AI Systems</h2>
        <p>Effective data governance ensures that AI systems comply with regulations, maintain data quality, and protect stakeholder interests.</p>
        
        <h3>AI Data Governance Framework</h3>
        <ul>
          <li><strong>Data Governance Principles:</strong>
            <ul>
              <li>Data ownership and stewardship</li>
              <li>Data quality and integrity</li>
              <li>Data lineage and provenance</li>
              <li>Data lifecycle management</li>
            </ul>
          </li>
          <li><strong>Governance Processes:</strong>
            <ul>
              <li>Data classification and cataloging</li>
              <li>Access control and authorization</li>
              <li>Data usage policies and procedures</li>
              <li>Audit and compliance monitoring</li>
            </ul>
          </li>
          <li><strong>Organizational Structure:</strong>
            <ul>
              <li>Data governance committees</li>
              <li>Data protection officers</li>
              <li>AI ethics boards</li>
              <li>Cross-functional teams</li>
            </ul>
          </li>
        </ul>
        
        <h3>Regulatory Compliance</h3>
        <ul>
          <li><strong>Privacy Regulations:</strong>
            <ul>
              <li>GDPR (General Data Protection Regulation)</li>
              <li>CCPA (California Consumer Privacy Act)</li>
              <li>PIPEDA (Personal Information Protection)</li>
              <li>Sector-specific privacy laws</li>
            </ul>
          </li>
          <li><strong>AI-Specific Regulations:</strong>
            <ul>
              <li>EU AI Act requirements</li>
              <li>Algorithmic accountability laws</li>
              <li>Bias and fairness regulations</li>
              <li>Transparency and explainability requirements</li>
            </ul>
          </li>
          <li><strong>Industry Standards:</strong>
            <ul>
              <li>ISO/IEC 23053 (AI Security)</li>
              <li>ISO/IEC 23894 (AI Risk Management)</li>
              <li>IEEE standards for AI systems</li>
              <li>NIST AI Risk Management Framework</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Security Controls</h3>
        <ul>
          <li><strong>Technical Controls:</strong>
            <ul>
              <li>Encryption and key management</li>
              <li>Access control systems</li>
              <li>Data loss prevention (DLP)</li>
              <li>Monitoring and alerting systems</li>
            </ul>
          </li>
          <li><strong>Administrative Controls:</strong>
            <ul>
              <li>Data handling policies</li>
              <li>Training and awareness programs</li>
              <li>Incident response procedures</li>
              <li>Vendor management processes</li>
            </ul>
          </li>
          <li><strong>Physical Controls:</strong>
            <ul>
              <li>Secure data centers</li>
              <li>Environmental controls</li>
              <li>Physical access restrictions</li>
              <li>Media handling procedures</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary goal of differential privacy in AI systems?",options:["To improve model accuracy","To protect individual privacy while enabling data analysis","To reduce computational costs","To speed up training processes"],correctAnswer:1,explanation:"Differential privacy aims to protect individual privacy while enabling data analysis by adding carefully calibrated noise to data or query results, ensuring that the presence or absence of any individual's data doesn't significantly affect the output."},{question:"Which attack specifically targets the reconstruction of training data from AI models?",options:["Adversarial examples","Model inversion attacks","Evasion attacks","Backdoor attacks"],correctAnswer:1,explanation:"Model inversion attacks specifically target the reconstruction of training data by exploiting the model's learned patterns to infer or reconstruct sensitive information from the original training dataset."},{question:"What is the main advantage of federated learning for data privacy?",options:["Faster model training","Better model accuracy","Data remains on local devices and is not centrally collected","Reduced computational requirements"],correctAnswer:2,explanation:"The main advantage of federated learning for data privacy is that data remains on local devices and is not centrally collected, allowing collaborative model training while keeping sensitive data distributed and private."}]},type:"quiz"}]},s={id:"ais-5",pathId:"ai-security",title:"Model Security",description:"Master AI model security including model protection, integrity verification, secure deployment, and defense against model-specific attacks and vulnerabilities.",objectives:["Understand AI model security requirements","Learn model protection and integrity techniques","Master secure model deployment practices","Develop skills in model attack defense","Learn model monitoring and validation","Implement comprehensive model security controls"],difficulty:"Intermediate",estimatedTime:140,sections:[{title:"Model Protection and Integrity",content:`
        <h2>AI Model Protection and Integrity Assurance</h2>
        <p>Protecting AI models from tampering, theft, and unauthorized access is crucial for maintaining system security and intellectual property.</p>
        
        <h3>Model Integrity Protection</h3>
        <ul>
          <li><strong>Model Authentication:</strong>
            <ul>
              <li>Digital signatures for model files</li>
              <li>Cryptographic hashing and checksums</li>
              <li>Certificate-based model validation</li>
              <li>Tamper detection mechanisms</li>
            </ul>
          </li>
          <li><strong>Model Versioning and Control:</strong>
            <ul>
              <li>Secure model repositories</li>
              <li>Version control systems for models</li>
              <li>Change tracking and audit trails</li>
              <li>Rollback and recovery capabilities</li>
            </ul>
          </li>
          <li><strong>Model Encryption:</strong>
            <ul>
              <li>Model parameter encryption at rest</li>
              <li>Secure model storage systems</li>
              <li>Encrypted model transmission</li>
              <li>Key management for model encryption</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intellectual Property Protection</h3>
        <ul>
          <li><strong>Model Theft Prevention:</strong>
            <ul>
              <li>Model extraction attack mitigation</li>
              <li>Query limiting and rate limiting</li>
              <li>API access control and monitoring</li>
              <li>Behavioral analysis for suspicious queries</li>
            </ul>
          </li>
          <li><strong>Model Watermarking:</strong>
            <ul>
              <li>Embedding unique identifiers in models</li>
              <li>Watermark detection techniques</li>
              <li>Robust watermarking against attacks</li>
              <li>Ownership verification methods</li>
            </ul>
          </li>
          <li><strong>Model Fingerprinting:</strong>
            <ul>
              <li>Unique model behavior signatures</li>
              <li>Statistical fingerprinting methods</li>
              <li>Model provenance tracking</li>
              <li>Unauthorized use detection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Access Control and Authorization</h3>
        <ul>
          <li><strong>Model Access Control:</strong>
            <ul>
              <li>Role-based access control (RBAC)</li>
              <li>Attribute-based access control (ABAC)</li>
              <li>Fine-grained permission systems</li>
              <li>Dynamic access control policies</li>
            </ul>
          </li>
          <li><strong>API Security:</strong>
            <ul>
              <li>Authentication and authorization</li>
              <li>API key management</li>
              <li>OAuth and token-based security</li>
              <li>Rate limiting and throttling</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication:</strong>
            <ul>
              <li>Strong authentication mechanisms</li>
              <li>Hardware security keys</li>
              <li>Biometric authentication</li>
              <li>Risk-based authentication</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Model Attack Defense",content:`
        <h2>Defense Against Model-Specific Attacks</h2>
        <p>AI models face unique attack vectors that require specialized defense mechanisms and security controls to maintain model integrity and performance.</p>
        
        <h3>Adversarial Attack Defense</h3>
        <ul>
          <li><strong>Adversarial Detection:</strong>
            <ul>
              <li>Statistical detection methods</li>
              <li>Neural network-based detectors</li>
              <li>Input preprocessing techniques</li>
              <li>Ensemble detection approaches</li>
            </ul>
          </li>
          <li><strong>Adversarial Training:</strong>
            <ul>
              <li>Training with adversarial examples</li>
              <li>Robust optimization techniques</li>
              <li>Certified defense methods</li>
              <li>Adversarial regularization</li>
            </ul>
          </li>
          <li><strong>Input Transformation:</strong>
            <ul>
              <li>Input sanitization and filtering</li>
              <li>Randomized transformations</li>
              <li>Feature squeezing techniques</li>
              <li>Defensive distillation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Model Poisoning Defense</h3>
        <ul>
          <li><strong>Data Validation:</strong>
            <ul>
              <li>Training data integrity checks</li>
              <li>Outlier detection and removal</li>
              <li>Statistical data validation</li>
              <li>Data provenance verification</li>
            </ul>
          </li>
          <li><strong>Robust Training:</strong>
            <ul>
              <li>Byzantine-robust aggregation</li>
              <li>Robust loss functions</li>
              <li>Influence function analysis</li>
              <li>Gradient clipping and filtering</li>
            </ul>
          </li>
          <li><strong>Model Validation:</strong>
            <ul>
              <li>Performance monitoring</li>
              <li>Behavior analysis</li>
              <li>Backdoor detection techniques</li>
              <li>Model comparison and validation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Model Extraction Defense</h3>
        <ul>
          <li><strong>Query Monitoring:</strong>
            <ul>
              <li>Query pattern analysis</li>
              <li>Suspicious behavior detection</li>
              <li>Rate limiting and throttling</li>
              <li>User behavior analytics</li>
            </ul>
          </li>
          <li><strong>Output Perturbation:</strong>
            <ul>
              <li>Noise injection in outputs</li>
              <li>Differential privacy techniques</li>
              <li>Output rounding and quantization</li>
              <li>Confidence score manipulation</li>
            </ul>
          </li>
          <li><strong>Model Obfuscation:</strong>
            <ul>
              <li>Model architecture hiding</li>
              <li>Parameter obfuscation</li>
              <li>Ensemble-based protection</li>
              <li>Knowledge distillation defense</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Secure Model Deployment and Monitoring",content:`
        <h2>Secure Model Deployment and Continuous Monitoring</h2>
        <p>Secure deployment and continuous monitoring ensure that AI models maintain security and performance throughout their operational lifecycle.</p>
        
        <h3>Secure Deployment Practices</h3>
        <ul>
          <li><strong>Infrastructure Security:</strong>
            <ul>
              <li>Secure containerization</li>
              <li>Kubernetes security configurations</li>
              <li>Network segmentation and isolation</li>
              <li>Load balancer and API gateway security</li>
            </ul>
          </li>
          <li><strong>Runtime Protection:</strong>
            <ul>
              <li>Runtime application self-protection (RASP)</li>
              <li>Container security monitoring</li>
              <li>Process isolation and sandboxing</li>
              <li>Resource limitation and quotas</li>
            </ul>
          </li>
          <li><strong>Deployment Automation:</strong>
            <ul>
              <li>Secure CI/CD pipelines</li>
              <li>Automated security testing</li>
              <li>Infrastructure as Code security</li>
              <li>Deployment validation and verification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Model Performance Monitoring</h3>
        <ul>
          <li><strong>Performance Metrics:</strong>
            <ul>
              <li>Accuracy and precision monitoring</li>
              <li>Latency and throughput tracking</li>
              <li>Resource utilization monitoring</li>
              <li>Error rate and failure analysis</li>
            </ul>
          </li>
          <li><strong>Model Drift Detection:</strong>
            <ul>
              <li>Data drift monitoring</li>
              <li>Concept drift detection</li>
              <li>Performance degradation alerts</li>
              <li>Statistical drift analysis</li>
            </ul>
          </li>
          <li><strong>Behavioral Monitoring:</strong>
            <ul>
              <li>Prediction pattern analysis</li>
              <li>Anomaly detection in outputs</li>
              <li>Bias and fairness monitoring</li>
              <li>Explainability tracking</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Monitoring and Incident Response</h3>
        <ul>
          <li><strong>Security Event Monitoring:</strong>
            <ul>
              <li>Attack detection and alerting</li>
              <li>Suspicious query identification</li>
              <li>Access pattern analysis</li>
              <li>Security log aggregation</li>
            </ul>
          </li>
          <li><strong>Incident Response:</strong>
            <ul>
              <li>Security incident classification</li>
              <li>Automated response procedures</li>
              <li>Model isolation and quarantine</li>
              <li>Forensic analysis capabilities</li>
            </ul>
          </li>
          <li><strong>Recovery and Remediation:</strong>
            <ul>
              <li>Model rollback procedures</li>
              <li>Clean model restoration</li>
              <li>Retraining and validation</li>
              <li>Lessons learned integration</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary purpose of model watermarking in AI security?",options:["To improve model performance","To detect unauthorized use and prove ownership","To reduce model size","To speed up inference"],correctAnswer:1,explanation:"Model watermarking embeds unique identifiers in AI models to detect unauthorized use and prove ownership, helping protect intellectual property and identify model theft."},{question:"Which defense technique is most effective against adversarial examples?",options:["Increasing model complexity","Adversarial training with robust optimization","Using more training data","Reducing model parameters"],correctAnswer:1,explanation:"Adversarial training with robust optimization is most effective against adversarial examples as it trains the model to be robust against adversarial perturbations by including adversarial examples in the training process."},{question:"What is the main indicator of a potential model extraction attack?",options:["High model accuracy","Unusual query patterns and high-frequency API calls","Low inference latency","Normal user behavior"],correctAnswer:1,explanation:"Unusual query patterns and high-frequency API calls are main indicators of model extraction attacks, as attackers typically need to make many queries to reconstruct or steal the model's functionality."}]},type:"quiz"}]},i=(n,e,t)=>({id:n,pathId:"ai-security",title:e,description:t,objectives:[`Learn ${e.toLowerCase()} concepts`,`Understand ${e.toLowerCase()} applications`,`Master ${e.toLowerCase()} best practices`,`Implement ${e.toLowerCase()} solutions`],difficulty:"Intermediate",estimatedTime:120,sections:[{title:"Overview",content:`<h2>${e}</h2><p>${t}</p><p>This module will be developed with comprehensive content covering all aspects of ${e.toLowerCase()}.</p>`,type:"text"}]}),c=i("ais-6","Adversarial Attacks","Learn about adversarial attacks against AI systems"),u=i("ais-7","Model Poisoning","Understand data and model poisoning techniques"),d=i("ais-8","Privacy-Preserving AI","Master privacy techniques in AI systems"),g=i("ais-9","AI Governance and Ethics","Learn AI governance and ethical considerations"),m=i("ais-10","AI Risk Management","Understand risk management for AI systems"),p=i("ais-11","Secure AI Development","Learn secure AI development practices"),y=i("ais-12","AI Security Testing","Master AI security testing methodologies"),h=i("ais-13","MLOps Security","Secure machine learning operations"),f=i("ais-14","AI Infrastructure Security","Secure AI infrastructure and platforms"),v=i("ais-15","Federated Learning Security","Security in federated learning systems"),A=i("ais-16","AI Explainability and Security","Explainable AI and security implications"),I=i("ais-17","AI Compliance","Regulatory compliance for AI systems"),k=i("ais-18","AI Incident Response","Incident response for AI security breaches"),S=i("ais-19","AI Forensics","Digital forensics for AI systems"),b=i("ais-20","AI Security Monitoring","Continuous monitoring of AI systems"),M=i("ais-21","Deep Learning Security","Security challenges in deep learning"),C=i("ais-22","NLP Security","Natural language processing security"),D=i("ais-23","Computer Vision Security","Security in computer vision systems"),P=i("ais-24","AI Hardware Security","Security of AI hardware and accelerators"),w=i("ais-25","Quantum AI Security","Quantum computing and AI security"),T=i("ais-26","AI in Cybersecurity","Using AI for cybersecurity applications"),q=i("ais-27","AI Threat Intelligence","AI-powered threat intelligence"),L=i("ais-28","AI Security Automation","Automating security with AI"),x=i("ais-29","Emerging AI Threats","Future and emerging AI security threats"),R=i("ais-30","AI Security Capstone","Comprehensive AI security project"),z=[l,a,r,o,s,c,u,d,g,m,p,y,h,f,v,A,I,k,S,b,M,C,D,P,w,T,q,L,x,R];export{z as aiSecurityModules};
