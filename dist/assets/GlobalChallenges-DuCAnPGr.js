import{b as F,w as A,D as T,r as a,j as e,am as Q,d as X,e as Y,$ as z,bX as l,l as C,Y as V,ax as q,aR as G,b2 as S,m as H}from"./index-CVvVjHWF.js";import{U as E}from"./UpgradeBanner-BQfQqcqw.js";const R=[{id:"web-1",title:"SQL Injection Basics",description:"Learn the fundamentals of SQL injection attacks and how to prevent them.",difficulty:"beginner",category:"web",points:100,completionRate:78,estimatedTime:"30 min",tier:"free"},{id:"web-2",title:"Cross-Site Scripting (XSS)",description:"Understand how XSS attacks work and implement proper defenses.",difficulty:"beginner",category:"web",points:150,completionRate:65,estimatedTime:"45 min",tier:"free"},{id:"network-1",title:"Network Traffic Analysis",description:"Analyze network packets to identify suspicious activities.",difficulty:"intermediate",category:"network",points:200,completionRate:52,estimatedTime:"1 hour",tier:"free"},{id:"crypto-1",title:"Cryptography Basics",description:"Learn about encryption, hashing, and cryptographic attacks.",difficulty:"beginner",category:"crypto",points:120,completionRate:70,estimatedTime:"40 min",tier:"premium"},{id:"forensics-1",title:"Digital Forensics Investigation",description:"Recover and analyze digital evidence from compromised systems.",difficulty:"intermediate",category:"forensics",points:250,completionRate:45,estimatedTime:"1.5 hours",tier:"premium"},{id:"binary-1",title:"Buffer Overflow Exploitation",description:"Exploit buffer overflow vulnerabilities in C programs.",difficulty:"advanced",category:"binary",points:300,completionRate:30,estimatedTime:"2 hours",tier:"premium"},{id:"web-3",title:"Advanced Web Application Attacks",description:"Master complex web attacks including CSRF, SSRF, and more.",difficulty:"advanced",category:"web",points:350,completionRate:25,estimatedTime:"2.5 hours",tier:"business"},{id:"network-2",title:"Enterprise Network Penetration",description:"Conduct a full penetration test on an enterprise network.",difficulty:"advanced",category:"network",points:400,completionRate:20,estimatedTime:"3 hours",tier:"business"}],O=()=>{const n=F(),{user:i}=A(),{subscriptionLevel:m,hasAccess:o,getRemainingContent:c,trackContentUsage:u,requiresCoins:b,hasEnoughCoins:y,purchaseWithCoins:f}=T(),[d,k]=a.useState([]),[j,h]=a.useState([]),[x,w]=a.useState("all"),[g,N]=a.useState("all"),[p,I]=a.useState(""),[U,B]=a.useState(0),[v,L]=a.useState(!1);a.useEffect(()=>{if(k(R),h(R),i){const{remaining:t}=c("challenges");B(t)}},[c,i]),a.useEffect(()=>{let t=d;if(x!=="all"&&(t=t.filter(s=>s.category===x)),g!=="all"&&(t=t.filter(s=>s.difficulty===g)),p){const s=p.toLowerCase();t=t.filter(r=>r.title.toLowerCase().includes(s)||r.description.toLowerCase().includes(s))}h(t)},[d,x,g,p]);const D=t=>{if(!i){n("/login");return}if(o("challenges",t.tier)&&!(m===l.FREE&&!u("challenges",t.id))){if(m===l.PREMIUM&&b("challenge",t.difficulty)){if(!y(t.points))return;f(t.points)}n(`/challenges/${t.id}`)}},M=t=>{switch(t){case l.PREMIUM:return e.jsx(G,{className:"text-yellow-400"});case l.BUSINESS:return e.jsx(q,{className:"text-blue-400"});default:return null}},P=t=>{if(!t||t===l.FREE)return null;const s=M(t),r=o("challenges",t);return e.jsxs("span",{className:`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${r?t===l.PREMIUM?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}`,children:[s,e.jsx("span",{className:"ml-1",children:t.charAt(0).toUpperCase()+t.slice(1)}),!r&&e.jsx(C,{className:"ml-1 text-xs"})]})},$=t=>{let s,r;switch(t){case"beginner":s="bg-green-100 dark:bg-green-900",r="text-green-800 dark:text-green-300";break;case"intermediate":s="bg-yellow-100 dark:bg-yellow-900",r="text-yellow-800 dark:text-yellow-300";break;case"advanced":s="bg-red-100 dark:bg-red-900",r="text-red-800 dark:text-red-300";break;default:s="bg-gray-100 dark:bg-gray-900",r="text-gray-800 dark:text-gray-300"}return e.jsx("span",{className:`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${s} ${r}`,children:t.charAt(0).toUpperCase()+t.slice(1)})},W=t=>{let s,r;switch(t){case"web":s="bg-purple-100 dark:bg-purple-900",r="text-purple-800 dark:text-purple-300";break;case"network":s="bg-blue-100 dark:bg-blue-900",r="text-blue-800 dark:text-blue-300";break;case"crypto":s="bg-green-100 dark:bg-green-900",r="text-green-800 dark:text-green-300";break;case"forensics":s="bg-yellow-100 dark:bg-yellow-900",r="text-yellow-800 dark:text-yellow-300";break;case"binary":s="bg-red-100 dark:bg-red-900",r="text-red-800 dark:text-red-300";break;default:s="bg-gray-100 dark:bg-gray-900",r="text-gray-800 dark:text-gray-300"}return e.jsx("span",{className:`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${s} ${r}`,children:t.charAt(0).toUpperCase()+t.slice(1)})},_=t=>{const s=i?o("challenges",t.tier):t.tier===l.FREE;return e.jsx("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all duration-200 ${s?"hover:shadow-lg cursor-pointer transform hover:-translate-y-1":"opacity-75"}`,onClick:()=>D(t),children:e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:[t.title,P(t.tier)]}),!s&&e.jsx(C,{className:"text-gray-400 dark:text-gray-500"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300",children:t.description}),e.jsxs("div",{className:"mt-4 flex flex-wrap gap-2",children:[$(t.difficulty),W(t.category)]}),e.jsxs("div",{className:"mt-4 flex justify-between items-center text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"text-yellow-400 mr-1"}),e.jsxs("span",{children:[t.points," points"]})]}),e.jsx("span",{children:t.estimatedTime})]}),!s&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsx("button",{className:"w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",onClick:r=>{r.stopPropagation(),n(i?"/pricing":"/login")},children:i?"Upgrade to Access":"Sign In to Access"})})]})},t.id)};return e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 min-h-screen",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow-sm",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Challenges"}),e.jsxs("button",{className:"ml-4 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 md:hidden",onClick:()=>L(!v),children:[e.jsx(Q,{className:"mr-2"}),v?e.jsx(X,{}):e.jsx(Y,{})]})]}),e.jsx("div",{className:"mt-4 md:mt-0 relative",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:"relative rounded-md shadow-sm",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(z,{className:"text-gray-400"})}),e.jsx("input",{type:"text",className:"focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Search challenges",value:p,onChange:t=>I(t.target.value)})]})})})]}),e.jsx("div",{className:`md:hidden mt-4 ${v?"block":"hidden"}`,children:e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Category"}),e.jsxs("select",{className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",value:x,onChange:t=>w(t.target.value),children:[e.jsx("option",{value:"all",children:"All Categories"}),e.jsx("option",{value:"web",children:"Web"}),e.jsx("option",{value:"network",children:"Network"}),e.jsx("option",{value:"crypto",children:"Crypto"}),e.jsx("option",{value:"forensics",children:"Forensics"}),e.jsx("option",{value:"binary",children:"Binary"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Difficulty"}),e.jsxs("select",{className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",value:g,onChange:t=>N(t.target.value),children:[e.jsx("option",{value:"all",children:"All Difficulties"}),e.jsx("option",{value:"beginner",children:"Beginner"}),e.jsx("option",{value:"intermediate",children:"Intermediate"}),e.jsx("option",{value:"advanced",children:"Advanced"})]})]})]})}),e.jsxs("div",{className:"hidden md:flex md:items-center md:space-x-4 mt-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mr-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Category:"}),e.jsxs("select",{className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",value:x,onChange:t=>w(t.target.value),children:[e.jsx("option",{value:"all",children:"All Categories"}),e.jsx("option",{value:"web",children:"Web"}),e.jsx("option",{value:"network",children:"Network"}),e.jsx("option",{value:"crypto",children:"Crypto"}),e.jsx("option",{value:"forensics",children:"Forensics"}),e.jsx("option",{value:"binary",children:"Binary"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mr-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Difficulty:"}),e.jsxs("select",{className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",value:g,onChange:t=>N(t.target.value),children:[e.jsx("option",{value:"all",children:"All Difficulties"}),e.jsx("option",{value:"beginner",children:"Beginner"}),e.jsx("option",{value:"intermediate",children:"Intermediate"}),e.jsx("option",{value:"advanced",children:"Advanced"})]})]})]})]})}),!i&&e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:e.jsx(E,{title:"Sign In to Track Your Progress",description:"Create an account or sign in to save your progress and access more challenges.",buttonText:"Sign In",onButtonClick:()=>n("/login"),variant:"prominent"})}),i&&m===l.FREE&&e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:e.jsx(E,{title:"Unlock All Challenges",description:`You have access to ${U} more challenges with your free account. Upgrade to unlock all content.`,buttonText:"View Plans",onButtonClick:()=>n("/pricing")})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:j.map(t=>_(t))}),j.length===0&&e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"No challenges match your filters. Try adjusting your search criteria."})})]})]})},Z=()=>{const n=F(),{user:i,loading:m}=A(),{subscriptionLevel:o,loading:c,getRemainingContent:u}=T(),[b,y]=a.useState(0),[f,d]=a.useState(!1);a.useEffect(()=>{if(localStorage.getItem("has_visited_challenges")||(d(!0),localStorage.setItem("has_visited_challenges","true")),!c&&i){const{remaining:h}=u("challenges");y(h)}},[c,u,i]);const k=()=>f?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs(H.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6",children:[e.jsx("div",{className:"flex items-center justify-center mb-4",children:e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 p-3 rounded-full",children:e.jsx(S,{className:"text-blue-600 dark:text-blue-300 text-2xl"})})}),e.jsx("h2",{className:"text-2xl font-bold text-center mb-4 text-gray-800 dark:text-white",children:"Welcome to XCerberus Challenges"}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-300 mb-6 text-center",children:["Test your cybersecurity skills with our hands-on challenges across various categories and difficulty levels.",o===l.FREE&&e.jsxs("span",{className:"block mt-2 text-sm",children:["You have access to ",e.jsx("span",{className:"font-bold",children:b})," challenges with your free account."]})]}),e.jsxs("div",{className:"flex flex-col space-y-3",children:[e.jsx("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors",onClick:()=>d(!1),children:"Start Challenges"}),o===l.FREE&&e.jsx("button",{className:"w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-lg transition-colors",onClick:()=>n("/pricing"),children:"Explore Premium"})]})]})}):null;return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[e.jsx(S,{className:"mr-2 text-blue-600 dark:text-blue-400"}),"Cybersecurity Challenges"]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Put your skills to the test with real-world security scenarios"})]}),o===l.FREE&&e.jsx("div",{className:"hidden sm:block",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>n("/pricing"),children:"Upgrade for Full Access"})})]})})}),e.jsx("div",{children:e.jsx(O,{})}),k()]})};export{Z as default};
