import{u as R,j as e,m as X,a4 as C,l as D,x as U,G as z,bq as B,ak as F,a as Y,w as Z,b as ee,r as y,br as te,bs as T,ae as A,U as q,_ as S,C as se,N as re,a1 as ae}from"./index-CVvVjHWF.js";import{P as l}from"./index-Cc2BstNK.js";import{L as I}from"./LearningLayout-nZy_7HLr.js";import{a as ie,b as ne,c as le,e as oe}from"./learningApiOverride-effR4Get.js";import{l as ce,f as de,s as H}from"./learning_paths-C7A2oYL7.js";import"./learning-paths-structure-YI8Fv9cD.js";const O=({module:i,userProgress:o,isLocked:r=!1,onClick:b,className:a=""})=>{const{darkMode:c}=R(),{id:f,title:P,description:_,short_description:L,difficulty:N,estimated_hours:M,icon:k}=i,m=(o==null?void 0:o.progress_percentage)||0,h=m===100,$=()=>{switch(N){case"beginner":return"text-green-500";case"intermediate":return"text-yellow-500";case"advanced":return"text-orange-500";case"expert":return"text-red-500";default:return"text-gray-500"}};return e.jsx(X.div,{className:`rounded-lg overflow-hidden shadow-md ${r?"cursor-not-allowed opacity-75":"cursor-pointer"} ${c?"bg-gray-800 hover:bg-gray-700":"bg-white hover:bg-gray-50"} transition-colors ${a}`,whileHover:r?{}:{y:-5,transition:{duration:.2}},onClick:()=>!r&&b&&b(f),children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${h?"bg-green-100 dark:bg-green-900/30":r?"bg-gray-100 dark:bg-gray-700":"bg-blue-100 dark:bg-blue-900/30"}`,children:k?e.jsx("img",{src:k,alt:P,className:"w-6 h-6"}):h?e.jsx(C,{className:"text-green-500 text-xl"}):r?e.jsx(D,{className:`${c?"text-gray-500":"text-gray-400"} text-xl`}):e.jsx(U,{className:"text-blue-500 text-xl"})}),e.jsxs("div",{children:[e.jsx("h3",{className:`text-lg font-semibold ${c?"text-white":"text-gray-800"}`,children:P}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx("span",{className:`capitalize ${$()}`,children:N}),e.jsx("span",{className:`mx-2 ${c?"text-gray-400":"text-gray-500"}`,children:"•"}),e.jsxs("span",{className:`flex items-center ${c?"text-gray-400":"text-gray-500"}`,children:[e.jsx(z,{className:"mr-1"}),M," ",M===1?"hour":"hours"]})]})]})]}),e.jsx("p",{className:`text-sm mb-4 line-clamp-2 ${c?"text-gray-300":"text-gray-600"}`,children:L||_}),!r&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:c?"text-gray-300":"text-gray-600",children:"Progress"}),e.jsxs("span",{className:h?"text-green-500":"text-blue-500",children:[m,"%"]})]}),e.jsx(B,{value:m,size:"md",color:h?"success":"primary",showLabel:!1,animate:!1})]}),e.jsx("button",{className:`mt-4 w-full py-2 px-4 rounded-md transition-colors flex items-center justify-center ${r?c?"bg-gray-700 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-500 cursor-not-allowed":h?"bg-green-500 hover:bg-green-600 text-white":m>0?"bg-blue-500 hover:bg-blue-600 text-white":c?"bg-gray-700 hover:bg-gray-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"}`,disabled:r,children:r?e.jsxs(e.Fragment,{children:[e.jsx(D,{className:"mr-2"}),"Locked"]}):h?e.jsxs(e.Fragment,{children:[e.jsx(C,{className:"mr-2"}),"Completed"]}):m>0?e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"mr-2"}),"Continue"]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"mr-2"}),"Start Module"]})})]})})};O.propTypes={module:l.shape({id:l.string.isRequired,title:l.string.isRequired,description:l.string.isRequired,short_description:l.string,difficulty:l.string.isRequired,estimated_hours:l.number,icon:l.string}).isRequired,userProgress:l.shape({progress_percentage:l.number}),isLocked:l.bool,onClick:l.func,className:l.string};const pe=()=>{const{pathId:i}=Y(),{user:o}=Z(),{darkMode:r}=R(),b=ee(),[a,c]=y.useState(null),[f,P]=y.useState([]),[_,L]=y.useState(null),[N,M]=y.useState({}),[k,m]=y.useState(!0),[h,$]=y.useState(null);y.useEffect(()=>{(async()=>{try{console.log("🔍 LearningPathDetail: Starting to fetch data for pathId:",i),m(!0);let n=null,g=[],w=null,j={};try{if(console.log("🔍 LearningPathDetail: Attempting to fetch path and modules for:",i),i==="ethical-hacking"){console.log("🎯 LearningPathDetail: Loading ethical hacking content directly...");try{const{getAllEthicalHackingModules:s}=await S(async()=>{const{getAllEthicalHackingModules:u}=await import("./index-B_44CtXO.js");return{getAllEthicalHackingModules:u}},[]),t=s();console.log(`✅ LearningPathDetail: Successfully loaded ${t.length} ethical hacking modules`),n={id:i,title:"Ethical Hacking Fundamentals",description:"Master ethical hacking and penetration testing from fundamental concepts to advanced attack techniques, designed for cybersecurity professionals pursuing CEH, OSCP, and professional penetration testing careers.",category:"offensive",difficulty:"intermediate",estimated_hours:120,modules_count:t.length,is_active:!0,is_featured:!0,order_index:1,tags:["offensive","intermediate",`${t.length} modules`],rating:4.8,modules:t.length,estimatedHours:120,aiMatch:95,enrolled:!1,progress:0},g=t.map((u,p)=>{var x,v;return{...u,learning_path_id:i,order_index:p+1,is_active:!0,topics:((x=u.sections)==null?void 0:x.map(W=>W.title))||[],short_description:((v=u.description)==null?void 0:v.substring(0,100))+"..."||"",estimated_hours:Math.ceil((u.estimatedTime||60)/60)}}),console.log(`✅ LearningPathDetail: Formatted ${g.length} modules for UI`)}catch(s){throw console.error("❌ LearningPathDetail: Error loading ethical hacking modules:",s),s}}else if(i==="ai-security"){console.log("🎯 LearningPathDetail: Loading AI Security content directly...");try{const{aiSecurityModules:s}=await S(async()=>{const{aiSecurityModules:t}=await import("./index-IvptZ-HN.js");return{aiSecurityModules:t}},[]);console.log(`✅ LearningPathDetail: Successfully loaded ${s.length} AI Security modules`),n={id:i,title:"AI Security",description:"Master artificial intelligence and machine learning security, including threat mitigation, secure development, privacy-preserving techniques, and comprehensive AI governance frameworks.",category:"emerging-tech",difficulty:"advanced",estimated_hours:120,modules_count:s.length,is_active:!0,is_featured:!0,order_index:1,tags:["emerging-tech","advanced",`${s.length} modules`],rating:4.7,modules:s.length,estimatedHours:120,aiMatch:95,enrolled:!1,progress:0},g=s.map((t,u)=>{var p,x;return{...t,learning_path_id:i,order_index:u+1,is_active:!0,topics:((p=t.sections)==null?void 0:p.map(v=>v.title))||[],short_description:((x=t.description)==null?void 0:x.substring(0,100))+"..."||"",estimated_hours:Math.ceil((t.estimatedTime||60)/60)}}),console.log(`✅ LearningPathDetail: Formatted ${g.length} AI Security modules for UI`)}catch(s){throw console.error("❌ LearningPathDetail: Error loading AI Security modules:",s),s}}else if(i==="iot-security"){console.log("🎯 LearningPathDetail: Loading IoT Security content directly...");try{const{iotSecurityModules:s}=await S(async()=>{const{iotSecurityModules:t}=await import("./index-CwEd6sFS.js");return{iotSecurityModules:t}},[]);console.log(`✅ LearningPathDetail: Successfully loaded ${s.length} IoT Security modules`),n={id:i,title:"IoT Security",description:"Master Internet of Things security across device, network, and cloud layers. Learn IoT threat modeling, secure architecture design, device security, and comprehensive IoT ecosystem protection.",category:"emerging-tech",difficulty:"intermediate",estimated_hours:100,modules_count:s.length,is_active:!0,is_featured:!0,order_index:1,tags:["emerging-tech","intermediate",`${s.length} modules`],rating:4.6,modules:s.length,estimatedHours:100,aiMatch:90,enrolled:!1,progress:0},g=s.map((t,u)=>{var p,x;return{...t,learning_path_id:i,order_index:u+1,is_active:!0,topics:((p=t.sections)==null?void 0:p.map(v=>v.title))||[],short_description:((x=t.description)==null?void 0:x.substring(0,100))+"..."||"",estimated_hours:Math.ceil((t.estimatedTime||60)/60)}}),console.log(`✅ LearningPathDetail: Formatted ${g.length} IoT Security modules for UI`)}catch(s){throw console.error("❌ LearningPathDetail: Error loading IoT Security modules:",s),s}}else{if(n=await ie(i),!n)throw new Error("Learning path not found");g=await ne(i)}o&&(w=await le(o.id,i),j=await oe(o.id))}catch(s){if(console.warn("API fetch failed, using seed data:",s),n=ce.find(t=>t.id===i),!n)throw new Error("Learning path not found");n.id==="fundamentals-track"&&(g=de),o&&(w=H.path_progress.find(t=>t.learning_path_id===i),H.module_progress.forEach(t=>{j[t.module_id]=t}))}c(n),P(g),L(w),M(j)}catch(n){console.error("Error fetching learning path:",n),$(n.message||"Failed to load learning path. Please try again later.")}finally{m(!1)}})()},[i,o]);const G=d=>{b(`/modules/${d}`)},V=()=>{if(!a)return null;switch(a.category){case"fundamentals":return e.jsx(U,{className:"text-blue-500"});case"offensive":return e.jsx(ae,{className:"text-red-500"});case"defensive":return e.jsx(re,{className:"text-green-500"});default:return e.jsx(se,{className:"text-purple-500"})}},J=()=>{if(!a)return"";switch(a.category){case"fundamentals":return"text-blue-500";case"offensive":return"text-red-500";case"defensive":return"text-green-500";default:return"text-purple-500"}},K=()=>{if(!a)return"";switch(a.difficulty){case"beginner":return"text-green-500";case"intermediate":return"text-yellow-500";case"advanced":return"text-orange-500";case"expert":return"text-red-500";default:return"text-gray-500"}},Q=(d,n)=>{if(n===0)return!1;if(d.prerequisites&&d.prerequisites.required_modules){const g=d.prerequisites.required_modules;for(const w of g){const j=N[w];if(!j||j.status!=="completed")return!0}}return!1};if(k)return e.jsx(I,{children:e.jsx("div",{className:"flex justify-center items-center min-h-[60vh]",children:e.jsx(te,{size:"lg"})})});if(h||!a)return e.jsx(I,{children:e.jsx(T,{icon:e.jsx(q,{className:"text-5xl"}),title:"Learning Path Not Found",message:h||"The learning path you're looking for doesn't exist or has been removed.",action:e.jsxs("button",{className:`px-4 py-2 rounded-lg transition-colors ${r?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-500 hover:bg-blue-600 text-white"}`,onClick:()=>b("/learning-paths"),children:[e.jsx(A,{className:"inline-block mr-2"}),"Back to Learning Paths"]})})});const E=(_==null?void 0:_.progress_percentage)||0;return e.jsxs(I,{children:[e.jsxs("button",{className:`mb-6 flex items-center ${r?"text-gray-300 hover:text-white":"text-gray-600 hover:text-gray-800"} transition-colors`,onClick:()=>b("/learning-paths"),children:[e.jsx(A,{className:"mr-2"}),"Back to Learning Paths"]}),e.jsx("div",{className:`p-6 rounded-lg mb-8 ${r?"bg-gray-800":"bg-white"} shadow-md`,children:e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center",children:[e.jsx("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mb-4 md:mb-0 md:mr-6 ${a.category==="fundamentals"?"bg-blue-100 dark:bg-blue-900/30":a.category==="offensive"?"bg-red-100 dark:bg-red-900/30":a.category==="defensive"?"bg-green-100 dark:bg-green-900/30":"bg-purple-100 dark:bg-purple-900/30"}`,children:a.icon?e.jsx("img",{src:a.icon,alt:a.title,className:"w-8 h-8"}):V()}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:`text-3xl font-bold mb-2 ${r?"text-white":"text-gray-800"}`,children:a.title}),e.jsxs("div",{className:"flex flex-wrap items-center text-sm mb-4",children:[e.jsx("span",{className:`capitalize ${J()}`,children:a.category}),e.jsx("span",{className:`mx-2 ${r?"text-gray-400":"text-gray-500"}`,children:"•"}),e.jsx("span",{className:`capitalize ${K()}`,children:a.difficulty}),e.jsx("span",{className:`mx-2 ${r?"text-gray-400":"text-gray-500"}`,children:"•"}),e.jsxs("span",{className:`flex items-center ${r?"text-gray-400":"text-gray-500"}`,children:[e.jsx(z,{className:"mr-1"}),a.estimated_hours," ",a.estimated_hours===1?"hour":"hours"]}),f.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`mx-2 ${r?"text-gray-400":"text-gray-500"}`,children:"•"}),e.jsxs("span",{className:r?"text-gray-400":"text-gray-500",children:[f.length," ",f.length===1?"Module":"Modules"]})]})]}),e.jsx("p",{className:`mb-6 ${r?"text-gray-300":"text-gray-600"}`,children:a.description}),o&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:r?"text-gray-300":"text-gray-600",children:"Overall Progress"}),e.jsxs("span",{className:"text-blue-500",children:[E,"%"]})]}),e.jsx(B,{value:E,size:"md",color:"primary",showLabel:!1})]})]})]})}),e.jsx("h2",{className:`text-2xl font-bold mb-6 ${r?"text-white":"text-gray-800"}`,children:"Modules"}),f.length===0?e.jsx(T,{icon:e.jsx(q,{className:"text-5xl"}),title:"No Modules Available",message:"This learning path doesn't have any modules yet. Check back later for updates."}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map((d,n)=>e.jsx(O,{module:d,userProgress:N[d.id],isLocked:o?Q(d,n):!1,onClick:G},d.id))})]})};export{pe as default};
