import{u as g,r as n,j as e,bP as f,y as b,$ as N,a as j,b as v,ae as h,c as u}from"./index-CVvVjHWF.js";import{S as w,P as S,N as k,B as C}from"./PasswordCrackingSimulator-Bg5vPKqJ.js";const $=({onComplete:s})=>{const{darkMode:t}=g(),[r,a]=n.useState(!1),[c,o]=n.useState(0),l=()=>{o(i=>i+1),a(!0)};return e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(f,{className:"text-green-500 mr-2"}),"Steganography Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:l,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(b,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:`p-8 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"Steganography Simulator"}),e.jsx("p",{className:"mb-6",children:"This challenge is coming soon! Check back later for a fully interactive steganography simulation."}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>{s&&s({success:!0,flag:"flag{hidden_message_found}",attempts:1,timeSpent:"12:45"})},className:"px-6 py-3 theme-button-primary rounded-lg",children:"Simulate Completion"})})]})})]})},A=({onComplete:s})=>{const{darkMode:t}=g(),[r,a]=n.useState(!1),[c,o]=n.useState(0),l=()=>{o(i=>i+1),a(!0)};return e.jsxs("div",{className:`${t?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden`,children:[e.jsxs("div",{className:`p-4 border-b ${t?"border-gray-800":"border-gray-200"} flex justify-between items-center`,children:[e.jsxs("h2",{className:"text-xl font-bold flex items-center",children:[e.jsx(N,{className:"text-purple-500 mr-2"}),"OSINT Investigation Challenge"]}),e.jsx("div",{children:e.jsxs("button",{onClick:l,className:`px-3 py-1 rounded-lg flex items-center ${t?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,children:[e.jsx(b,{className:"mr-1 text-yellow-500"})," Get Hint"]})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:`p-8 rounded-lg ${t?"bg-[#252D4A] border-gray-700":"bg-gray-50 border-gray-200"} border text-center`,children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"OSINT Investigation Simulator"}),e.jsx("p",{className:"mb-6",children:"This challenge is coming soon! Check back later for a fully interactive OSINT investigation simulation."}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>{s&&s({success:!0,flag:"flag{osint_investigation_complete}",attempts:1,timeSpent:"15:30"})},className:"px-6 py-3 theme-button-primary rounded-lg",children:"Simulate Completion"})})]})})]})},T=[{id:"c1",slug:"sql-injection-basics",title:"SQL Injection Basics",description:"Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.",category:{name:"Web Security"},difficulty:{name:"Beginner"},type:{name:"Exploitation"},points:100,coin_reward:10,estimated_time:20,simulator:w},{id:"c2",slug:"password-cracking-basics",title:"Password Cracking Basics",description:"Learn the fundamentals of password cracking by breaking a series of increasingly complex password hashes. This challenge introduces you to common password cracking tools and techniques.",category:{name:"Cryptography"},difficulty:{name:"Beginner"},type:{name:"Analysis"},points:100,coin_reward:10,estimated_time:25,simulator:S},{id:"c3",slug:"network-traffic-analysis",title:"Network Traffic Analysis",description:"Analyze captured network traffic to identify suspicious activities and extract hidden information. This challenge introduces you to packet analysis and network forensics.",category:{name:"Network Security"},difficulty:{name:"Easy"},type:{name:"Analysis"},points:150,coin_reward:15,estimated_time:30,simulator:k},{id:"c4",slug:"osint-investigation",title:"OSINT Investigation",description:"Use Open Source Intelligence techniques to gather information about a fictional target. This challenge introduces you to the power of publicly available information.",category:{name:"OSINT"},difficulty:{name:"Easy"},type:{name:"Reconnaissance"},points:150,coin_reward:15,estimated_time:35,simulator:A},{id:"c5",slug:"basic-steganography",title:"Basic Steganography",description:"Discover hidden messages concealed within digital images. This challenge introduces you to steganography techniques and tools.",category:{name:"Forensics"},difficulty:{name:"Easy"},type:{name:"Analysis"},points:150,coin_reward:15,estimated_time:25,simulator:$},{id:"c6",slug:"simple-binary-analysis",title:"Simple Binary Analysis",description:"Analyze a simple executable file to understand its behavior and find hidden functionality. This challenge introduces you to basic reverse engineering concepts.",category:{name:"Reverse Engineering"},difficulty:{name:"Medium"},type:{name:"Analysis"},points:200,coin_reward:20,estimated_time:40,simulator:C}],B=()=>{const{darkMode:s}=g(),{challengeId:t}=j(),r=v(),[a,c]=n.useState(null),[o,l]=n.useState(!1),[i,m]=n.useState(null);n.useEffect(()=>{const d=T.find(x=>x.slug===t||x.id===t);c(d)},[t]);const y=d=>{l(!0),m(d)};if(!a)return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("button",{onClick:()=>r("/challenges"),className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(h,{className:"mr-2"})," Back to Challenges"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Challenge Not Found"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"The challenge you're looking for doesn't exist or has been removed."}),e.jsx("button",{onClick:()=>r("/challenges"),className:"mt-4 px-6 py-2 theme-button-primary rounded-lg",children:"Browse Challenges"})]})]})});const p=a.simulator;return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("button",{onClick:()=>r("/challenges"),className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(h,{className:"mr-2"})," Back to Challenges"]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-3xl font-bold",children:a.title}),e.jsxs("div",{className:"flex flex-wrap gap-2 mt-2",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:a.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:a.difficulty.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-green-900/20 text-green-300":"bg-green-100 text-green-800"}`,children:a.type.name}),e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(u,{className:"mr-1 text-yellow-500"})," ",a.points," points"]})]})]}),e.jsx(p,{onComplete:y}),o&&i&&e.jsxs("div",{className:`mt-8 ${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("h2",{className:"text-2xl font-bold mb-4 flex items-center",children:[e.jsx(u,{className:"text-yellow-500 mr-2"})," Challenge Completed!"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-[#252D4A]":"bg-gray-100"}`,children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Points Earned"}),e.jsx("div",{className:"text-2xl font-bold",children:a.points})]}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-[#252D4A]":"bg-gray-100"}`,children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Coins Rewarded"}),e.jsx("div",{className:"text-2xl font-bold",children:a.coin_reward})]}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-[#252D4A]":"bg-gray-100"}`,children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Time Spent"}),e.jsx("div",{className:"text-2xl font-bold",children:i.timeSpent||"15:32"})]})]}),e.jsxs("div",{className:`p-4 rounded-lg ${s?"bg-[#252D4A]":"bg-gray-100"} mb-6`,children:[e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Your Flag"}),e.jsx("div",{className:`p-3 rounded ${s?"bg-black":"bg-gray-900"} font-mono text-green-400`,children:i.flag})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("button",{onClick:()=>r("/challenges"),className:`px-6 py-2 rounded-lg ${s?"bg-gray-800 hover:bg-gray-700 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-800"}`,children:"Back to Challenges"}),e.jsx("button",{onClick:()=>{l(!1),m(null)},className:"px-6 py-2 theme-button-primary rounded-lg",children:"Next Challenge"})]})]})]})})};export{B as default};
