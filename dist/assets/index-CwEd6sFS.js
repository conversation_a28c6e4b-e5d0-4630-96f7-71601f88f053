const l={id:"iot-1",pathId:"iot-security",title:"IoT Security Fundamentals",description:"Master the fundamentals of Internet of Things security, including IoT ecosystem components, security challenges, and foundational security principles for connected devices.",objectives:["Understand IoT ecosystem and architecture","Learn IoT security challenges and threat models","Master IoT security principles and frameworks","Develop skills in IoT risk assessment","Learn IoT security standards and regulations","Implement foundational IoT security practices"],difficulty:"Beginner",estimatedTime:120,sections:[{title:"IoT Ecosystem and Architecture",content:`
        <h2>Internet of Things Ecosystem Overview</h2>
        <p>Understanding the IoT ecosystem is essential for implementing comprehensive security measures across all components and layers of IoT systems.</p>
        
        <h3>IoT Components and Layers</h3>
        <ul>
          <li><strong>Device Layer:</strong>
            <ul>
              <li>Sensors and actuators</li>
              <li>Microcontrollers and processors</li>
              <li>Embedded operating systems</li>
              <li>Device firmware and software</li>
            </ul>
          </li>
          <li><strong>Connectivity Layer:</strong>
            <ul>
              <li>Communication protocols (WiFi, Bluetooth, Zigbee, LoRaWAN)</li>
              <li>Network infrastructure and gateways</li>
              <li>Edge computing nodes</li>
              <li>Cellular and satellite connections</li>
            </ul>
          </li>
          <li><strong>Data Processing Layer:</strong>
            <ul>
              <li>Edge analytics and processing</li>
              <li>Cloud computing platforms</li>
              <li>Data storage and management</li>
              <li>Real-time processing systems</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Architecture Models</h3>
        <ul>
          <li><strong>Three-Layer Architecture:</strong>
            <ul>
              <li>Perception layer (sensors and devices)</li>
              <li>Network layer (communication and connectivity)</li>
              <li>Application layer (services and interfaces)</li>
            </ul>
          </li>
          <li><strong>Four-Layer Architecture:</strong>
            <ul>
              <li>Sensing layer (data collection)</li>
              <li>Network layer (data transmission)</li>
              <li>Data processing layer (analytics and storage)</li>
              <li>Application layer (user interfaces and services)</li>
            </ul>
          </li>
          <li><strong>Five-Layer Architecture:</strong>
            <ul>
              <li>Perception layer</li>
              <li>Transport layer</li>
              <li>Processing layer</li>
              <li>Application layer</li>
              <li>Business layer</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Deployment Models</h3>
        <ul>
          <li><strong>Consumer IoT:</strong>
            <ul>
              <li>Smart home devices and appliances</li>
              <li>Wearable technology</li>
              <li>Personal health monitors</li>
              <li>Entertainment and media devices</li>
            </ul>
          </li>
          <li><strong>Industrial IoT (IIoT):</strong>
            <ul>
              <li>Manufacturing automation</li>
              <li>Supply chain management</li>
              <li>Predictive maintenance</li>
              <li>Quality control systems</li>
            </ul>
          </li>
          <li><strong>Smart City IoT:</strong>
            <ul>
              <li>Traffic management systems</li>
              <li>Environmental monitoring</li>
              <li>Public safety infrastructure</li>
              <li>Utility management</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"IoT Security Challenges",content:`
        <h2>Unique IoT Security Challenges</h2>
        <p>IoT systems face distinct security challenges due to their distributed nature, resource constraints, and diverse deployment environments.</p>
        
        <h3>Device-Level Challenges</h3>
        <ul>
          <li><strong>Resource Constraints:</strong>
            <ul>
              <li>Limited processing power and memory</li>
              <li>Battery life and power consumption</li>
              <li>Storage capacity limitations</li>
              <li>Cost constraints affecting security features</li>
            </ul>
          </li>
          <li><strong>Physical Security:</strong>
            <ul>
              <li>Unattended device deployment</li>
              <li>Physical tampering and theft</li>
              <li>Environmental exposure</li>
              <li>Hardware extraction attacks</li>
            </ul>
          </li>
          <li><strong>Lifecycle Management:</strong>
            <ul>
              <li>Firmware update challenges</li>
              <li>Long deployment lifecycles</li>
              <li>End-of-life device management</li>
              <li>Legacy system integration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network and Communication Challenges</h3>
        <ul>
          <li><strong>Protocol Diversity:</strong>
            <ul>
              <li>Multiple communication protocols</li>
              <li>Protocol security variations</li>
              <li>Interoperability issues</li>
              <li>Legacy protocol support</li>
            </ul>
          </li>
          <li><strong>Network Security:</strong>
            <ul>
              <li>Wireless communication vulnerabilities</li>
              <li>Man-in-the-middle attacks</li>
              <li>Network segmentation challenges</li>
              <li>Bandwidth and latency constraints</li>
            </ul>
          </li>
          <li><strong>Scale and Complexity:</strong>
            <ul>
              <li>Massive device deployments</li>
              <li>Heterogeneous device types</li>
              <li>Dynamic network topologies</li>
              <li>Cross-domain communications</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data and Privacy Challenges</h3>
        <ul>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>Sensitive data collection</li>
              <li>Data transmission security</li>
              <li>Data storage and retention</li>
              <li>Data sharing and analytics</li>
            </ul>
          </li>
          <li><strong>Privacy Concerns:</strong>
            <ul>
              <li>Personal information collection</li>
              <li>Location tracking and profiling</li>
              <li>Behavioral monitoring</li>
              <li>Consent and transparency</li>
            </ul>
          </li>
          <li><strong>Regulatory Compliance:</strong>
            <ul>
              <li>GDPR and privacy regulations</li>
              <li>Industry-specific requirements</li>
              <li>Cross-border data transfers</li>
              <li>Audit and compliance reporting</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"IoT Security Principles and Standards",content:`
        <h2>IoT Security Principles and Industry Standards</h2>
        <p>Establishing security principles and following industry standards provides the foundation for building secure and trustworthy IoT systems.</p>
        
        <h3>Core IoT Security Principles</h3>
        <ul>
          <li><strong>Security by Design:</strong>
            <ul>
              <li>Built-in security from conception</li>
              <li>Threat modeling and risk assessment</li>
              <li>Secure development lifecycle</li>
              <li>Defense in depth strategy</li>
            </ul>
          </li>
          <li><strong>Identity and Authentication:</strong>
            <ul>
              <li>Unique device identification</li>
              <li>Strong authentication mechanisms</li>
              <li>Certificate-based authentication</li>
              <li>Multi-factor authentication where possible</li>
            </ul>
          </li>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>End-to-end encryption</li>
              <li>Data integrity verification</li>
              <li>Secure key management</li>
              <li>Privacy-preserving techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Security Standards and Frameworks</h3>
        <ul>
          <li><strong>NIST Cybersecurity Framework for IoT:</strong>
            <ul>
              <li>Device identification and management</li>
              <li>Device configuration and management</li>
              <li>Data protection and privacy</li>
              <li>Logical access to interfaces</li>
            </ul>
          </li>
          <li><strong>ISO/IEC 27001 for IoT:</strong>
            <ul>
              <li>Information security management</li>
              <li>Risk assessment and treatment</li>
              <li>Security controls implementation</li>
              <li>Continuous improvement</li>
            </ul>
          </li>
          <li><strong>OWASP IoT Top 10:</strong>
            <ul>
              <li>Weak, guessable, or hardcoded passwords</li>
              <li>Insecure network services</li>
              <li>Insecure ecosystem interfaces</li>
              <li>Lack of secure update mechanism</li>
            </ul>
          </li>
        </ul>
        
        <h3>Regulatory and Compliance Requirements</h3>
        <ul>
          <li><strong>Privacy Regulations:</strong>
            <ul>
              <li>GDPR (General Data Protection Regulation)</li>
              <li>CCPA (California Consumer Privacy Act)</li>
              <li>PIPEDA (Personal Information Protection)</li>
              <li>Regional privacy laws</li>
            </ul>
          </li>
          <li><strong>Industry Standards:</strong>
            <ul>
              <li>IEC 62443 (Industrial Automation)</li>
              <li>ISO 26262 (Automotive Functional Safety)</li>
              <li>FDA guidelines (Medical Devices)</li>
              <li>FCC regulations (Wireless Communications)</li>
            </ul>
          </li>
          <li><strong>Certification Programs:</strong>
            <ul>
              <li>Common Criteria evaluations</li>
              <li>FIPS 140-2 compliance</li>
              <li>UL cybersecurity certification</li>
              <li>Industry-specific certifications</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary challenge that distinguishes IoT security from traditional IT security?",options:["IoT devices are always connected to the internet","IoT devices have resource constraints and diverse deployment environments","IoT devices only use wireless communication","IoT devices don't store any data"],correctAnswer:1,explanation:"The primary challenge is that IoT devices have resource constraints (limited processing power, memory, battery) and are deployed in diverse, often uncontrolled environments, making traditional security approaches difficult to implement."},{question:"Which layer of IoT architecture is most vulnerable to physical attacks?",options:["Application layer","Network layer","Perception/Device layer","Business layer"],correctAnswer:2,explanation:"The perception/device layer is most vulnerable to physical attacks because IoT devices are often deployed in unattended, accessible locations where attackers can physically tamper with or steal the devices."},{question:"What is the first principle of secure IoT development according to security by design?",options:["Add security features after development","Build security into the system from conception","Focus only on network security","Use the cheapest security solutions"],correctAnswer:1,explanation:"Security by design requires building security into the system from conception, including threat modeling, risk assessment, and implementing security controls throughout the development lifecycle."}]},type:"quiz"}]},o={id:"iot-2",pathId:"iot-security",title:"IoT Architecture Security",description:"Master secure IoT architecture design principles, including layered security models, secure communication patterns, and comprehensive security architecture frameworks for IoT systems.",objectives:["Understand secure IoT architecture principles","Learn layered security models for IoT","Master secure communication architectures","Develop skills in IoT security design patterns","Learn edge and cloud security integration","Implement comprehensive IoT security architectures"],difficulty:"Intermediate",estimatedTime:130,sections:[{title:"Secure IoT Architecture Principles",content:`
        <h2>Secure IoT Architecture Design Principles</h2>
        <p>Secure IoT architecture requires comprehensive security design principles that address the unique challenges of distributed, resource-constrained, and heterogeneous IoT environments.</p>
        
        <h3>Security-by-Design Principles</h3>
        <ul>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple layers of security controls</li>
              <li>Redundant security mechanisms</li>
              <li>Fail-safe and fail-secure designs</li>
              <li>Comprehensive threat coverage</li>
            </ul>
          </li>
          <li><strong>Least Privilege:</strong>
            <ul>
              <li>Minimal access rights for devices</li>
              <li>Role-based access control</li>
              <li>Dynamic privilege adjustment</li>
              <li>Regular privilege review and audit</li>
            </ul>
          </li>
          <li><strong>Zero Trust Architecture:</strong>
            <ul>
              <li>Never trust, always verify</li>
              <li>Continuous authentication and authorization</li>
              <li>Micro-segmentation of IoT networks</li>
              <li>Encrypted communications by default</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Security Architecture Layers</h3>
        <ul>
          <li><strong>Device Security Layer:</strong>
            <ul>
              <li>Hardware security modules (HSM)</li>
              <li>Secure boot and trusted execution</li>
              <li>Device identity and authentication</li>
              <li>Firmware integrity protection</li>
            </ul>
          </li>
          <li><strong>Communication Security Layer:</strong>
            <ul>
              <li>Protocol-level security (TLS/DTLS)</li>
              <li>Network segmentation and isolation</li>
              <li>VPN and secure tunneling</li>
              <li>Wireless security protocols</li>
            </ul>
          </li>
          <li><strong>Data Security Layer:</strong>
            <ul>
              <li>End-to-end encryption</li>
              <li>Data integrity verification</li>
              <li>Secure key management</li>
              <li>Privacy-preserving techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Architecture Patterns</h3>
        <ul>
          <li><strong>Gateway-Based Security:</strong>
            <ul>
              <li>Security gateway as control point</li>
              <li>Protocol translation and security</li>
              <li>Centralized policy enforcement</li>
              <li>Traffic inspection and filtering</li>
            </ul>
          </li>
          <li><strong>Distributed Security:</strong>
            <ul>
              <li>Device-level security capabilities</li>
              <li>Peer-to-peer security protocols</li>
              <li>Distributed trust management</li>
              <li>Resilient security architectures</li>
            </ul>
          </li>
          <li><strong>Hybrid Security Models:</strong>
            <ul>
              <li>Combination of centralized and distributed</li>
              <li>Adaptive security based on context</li>
              <li>Scalable security architectures</li>
              <li>Multi-tier security approaches</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Secure Communication Architectures",content:`
        <h2>Secure IoT Communication Architectures</h2>
        <p>Secure communication is critical for IoT systems, requiring robust protocols, encryption, and network security measures to protect data in transit.</p>
        
        <h3>IoT Communication Security Protocols</h3>
        <ul>
          <li><strong>Application Layer Security:</strong>
            <ul>
              <li>HTTPS and HTTP/2 security</li>
              <li>CoAP (Constrained Application Protocol) security</li>
              <li>MQTT security with TLS</li>
              <li>WebSocket security protocols</li>
            </ul>
          </li>
          <li><strong>Transport Layer Security:</strong>
            <ul>
              <li>TLS/SSL for TCP connections</li>
              <li>DTLS (Datagram TLS) for UDP</li>
              <li>Certificate management and validation</li>
              <li>Perfect forward secrecy</li>
            </ul>
          </li>
          <li><strong>Network Layer Security:</strong>
            <ul>
              <li>IPSec for IP-based communications</li>
              <li>6LoWPAN security considerations</li>
              <li>Network access control</li>
              <li>Routing protocol security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Wireless Communication Security</h3>
        <ul>
          <li><strong>WiFi Security:</strong>
            <ul>
              <li>WPA3 and enterprise security</li>
              <li>WiFi Protected Setup (WPS) risks</li>
              <li>Guest network isolation</li>
              <li>WiFi monitoring and intrusion detection</li>
            </ul>
          </li>
          <li><strong>Bluetooth Security:</strong>
            <ul>
              <li>Bluetooth Low Energy (BLE) security</li>
              <li>Pairing and bonding security</li>
              <li>Bluetooth mesh security</li>
              <li>Legacy Bluetooth vulnerabilities</li>
            </ul>
          </li>
          <li><strong>LPWAN Security:</strong>
            <ul>
              <li>LoRaWAN security architecture</li>
              <li>Sigfox security considerations</li>
              <li>NB-IoT and LTE-M security</li>
              <li>Satellite IoT security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Segmentation and Isolation</h3>
        <ul>
          <li><strong>Network Segmentation Strategies:</strong>
            <ul>
              <li>VLAN-based segmentation</li>
              <li>Software-defined networking (SDN)</li>
              <li>Micro-segmentation techniques</li>
              <li>Dynamic network isolation</li>
            </ul>
          </li>
          <li><strong>IoT Network Zones:</strong>
            <ul>
              <li>Device management zone</li>
              <li>Data collection zone</li>
              <li>Processing and analytics zone</li>
              <li>External connectivity zone</li>
            </ul>
          </li>
          <li><strong>Traffic Control and Monitoring:</strong>
            <ul>
              <li>Firewall rules and policies</li>
              <li>Intrusion detection systems (IDS)</li>
              <li>Network traffic analysis</li>
              <li>Anomaly detection systems</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Edge and Cloud Security Integration",content:`
        <h2>Edge and Cloud Security Integration for IoT</h2>
        <p>Modern IoT architectures span edge and cloud environments, requiring integrated security approaches that protect data and services across distributed infrastructure.</p>
        
        <h3>Edge Computing Security</h3>
        <ul>
          <li><strong>Edge Security Architecture:</strong>
            <ul>
              <li>Edge gateway security</li>
              <li>Distributed security management</li>
              <li>Local data processing security</li>
              <li>Edge-to-cloud secure connectivity</li>
            </ul>
          </li>
          <li><strong>Edge Device Protection:</strong>
            <ul>
              <li>Hardware security modules</li>
              <li>Secure enclaves and trusted zones</li>
              <li>Container security for edge</li>
              <li>Edge device lifecycle management</li>
            </ul>
          </li>
          <li><strong>Edge Data Security:</strong>
            <ul>
              <li>Local data encryption</li>
              <li>Data residency and sovereignty</li>
              <li>Edge analytics security</li>
              <li>Secure data synchronization</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud IoT Security</h3>
        <ul>
          <li><strong>Cloud IoT Platforms:</strong>
            <ul>
              <li>AWS IoT Core security</li>
              <li>Azure IoT Hub security</li>
              <li>Google Cloud IoT security</li>
              <li>Multi-cloud IoT strategies</li>
            </ul>
          </li>
          <li><strong>Cloud Security Services:</strong>
            <ul>
              <li>Identity and access management</li>
              <li>Device registry and management</li>
              <li>Security monitoring and analytics</li>
              <li>Threat detection and response</li>
            </ul>
          </li>
          <li><strong>Data Pipeline Security:</strong>
            <ul>
              <li>Ingestion security controls</li>
              <li>Stream processing security</li>
              <li>Data lake and warehouse security</li>
              <li>Analytics and ML security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Hybrid Architecture Security</h3>
        <ul>
          <li><strong>Edge-Cloud Integration:</strong>
            <ul>
              <li>Secure hybrid connectivity</li>
              <li>Workload distribution security</li>
              <li>Data synchronization security</li>
              <li>Failover and redundancy</li>
            </ul>
          </li>
          <li><strong>Security Orchestration:</strong>
            <ul>
              <li>Centralized security management</li>
              <li>Policy distribution and enforcement</li>
              <li>Incident response coordination</li>
              <li>Security analytics aggregation</li>
            </ul>
          </li>
          <li><strong>Compliance and Governance:</strong>
            <ul>
              <li>Cross-environment compliance</li>
              <li>Data governance frameworks</li>
              <li>Audit and reporting systems</li>
              <li>Risk management integration</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary benefit of implementing Zero Trust architecture in IoT systems?",options:["Reduced network latency","Continuous verification and never trusting by default","Lower implementation costs","Simplified device management"],correctAnswer:1,explanation:"Zero Trust architecture's primary benefit is continuous verification and never trusting by default, which is crucial for IoT systems where devices may be compromised or operate in untrusted environments."},{question:"Which security protocol is most appropriate for constrained IoT devices using UDP communication?",options:["TLS (Transport Layer Security)","DTLS (Datagram Transport Layer Security)","SSH (Secure Shell)","HTTPS"],correctAnswer:1,explanation:"DTLS (Datagram Transport Layer Security) is most appropriate for constrained IoT devices using UDP communication as it provides TLS-like security for datagram protocols without requiring reliable transport."},{question:"What is the main advantage of gateway-based security architecture in IoT deployments?",options:["Eliminates the need for device-level security","Provides centralized security control and policy enforcement","Reduces network bandwidth usage","Increases device battery life"],correctAnswer:1,explanation:"Gateway-based security architecture provides centralized security control and policy enforcement, allowing for consistent security policies across diverse IoT devices and simplified security management."}]},type:"quiz"}]},r={id:"iot-3",pathId:"iot-security",title:"IoT Device Security",description:"Master comprehensive IoT device security including hardware security, firmware protection, secure boot processes, and device lifecycle security management.",objectives:["Understand IoT device security fundamentals","Learn hardware security mechanisms","Master firmware security and secure boot","Develop skills in device identity management","Learn device lifecycle security","Implement comprehensive device security controls"],difficulty:"Intermediate",estimatedTime:135,sections:[{title:"Hardware Security Fundamentals",content:`
        <h2>IoT Hardware Security Fundamentals</h2>
        <p>Hardware security forms the foundation of IoT device security, providing the root of trust and secure execution environment for all device operations.</p>
        
        <h3>Hardware Security Modules (HSM)</h3>
        <ul>
          <li><strong>Dedicated Security Chips:</strong>
            <ul>
              <li>Trusted Platform Module (TPM)</li>
              <li>Secure Element (SE) integration</li>
              <li>Hardware Security Module (HSM)</li>
              <li>Cryptographic co-processors</li>
            </ul>
          </li>
          <li><strong>Secure Key Storage:</strong>
            <ul>
              <li>Hardware-based key generation</li>
              <li>Tamper-resistant key storage</li>
              <li>Key derivation functions</li>
              <li>Secure key lifecycle management</li>
            </ul>
          </li>
          <li><strong>Cryptographic Operations:</strong>
            <ul>
              <li>Hardware-accelerated encryption</li>
              <li>Digital signature generation</li>
              <li>Random number generation</li>
              <li>Hash function acceleration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Trusted Execution Environments</h3>
        <ul>
          <li><strong>ARM TrustZone:</strong>
            <ul>
              <li>Secure and non-secure worlds</li>
              <li>Secure monitor mode</li>
              <li>Trusted applications (TAs)</li>
              <li>Secure storage and communication</li>
            </ul>
          </li>
          <li><strong>Intel SGX (Software Guard Extensions):</strong>
            <ul>
              <li>Secure enclaves for sensitive code</li>
              <li>Memory encryption and isolation</li>
              <li>Attestation mechanisms</li>
              <li>Sealed storage capabilities</li>
            </ul>
          </li>
          <li><strong>RISC-V Security Extensions:</strong>
            <ul>
              <li>Physical Memory Protection (PMP)</li>
              <li>Supervisor Binary Interface (SBI)</li>
              <li>Cryptographic extensions</li>
              <li>Secure boot implementations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Physical Security Measures</h3>
        <ul>
          <li><strong>Tamper Detection and Response:</strong>
            <ul>
              <li>Physical tamper sensors</li>
              <li>Voltage and frequency monitoring</li>
              <li>Temperature anomaly detection</li>
              <li>Automatic key erasure on tamper</li>
            </ul>
          </li>
          <li><strong>Side-Channel Attack Protection:</strong>
            <ul>
              <li>Power analysis countermeasures</li>
              <li>Electromagnetic emission shielding</li>
              <li>Timing attack mitigation</li>
              <li>Fault injection protection</li>
            </ul>
          </li>
          <li><strong>Secure Packaging:</strong>
            <ul>
              <li>Tamper-evident enclosures</li>
              <li>Secure chip packaging</li>
              <li>Anti-probing measures</li>
              <li>Environmental protection</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Firmware Security and Secure Boot",content:`
        <h2>Firmware Security and Secure Boot Processes</h2>
        <p>Firmware security ensures the integrity and authenticity of device software from boot to runtime, protecting against malicious code execution and unauthorized modifications.</p>
        
        <h3>Secure Boot Implementation</h3>
        <ul>
          <li><strong>Root of Trust:</strong>
            <ul>
              <li>Hardware-based root of trust</li>
              <li>Immutable boot ROM</li>
              <li>Public key infrastructure (PKI)</li>
              <li>Certificate chain validation</li>
            </ul>
          </li>
          <li><strong>Boot Chain Verification:</strong>
            <ul>
              <li>Bootloader signature verification</li>
              <li>Kernel image authentication</li>
              <li>Device tree verification</li>
              <li>Application signature checking</li>
            </ul>
          </li>
          <li><strong>Measured Boot:</strong>
            <ul>
              <li>Boot component measurement</li>
              <li>Platform Configuration Registers (PCR)</li>
              <li>Attestation capabilities</li>
              <li>Remote verification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Firmware Protection Mechanisms</h3>
        <ul>
          <li><strong>Code Integrity:</strong>
            <ul>
              <li>Firmware signing and verification</li>
              <li>Code authentication mechanisms</li>
              <li>Runtime integrity checking</li>
              <li>Control flow integrity (CFI)</li>
            </ul>
          </li>
          <li><strong>Memory Protection:</strong>
            <ul>
              <li>Execute-only memory (XOM)</li>
              <li>Stack protection mechanisms</li>
              <li>Heap overflow protection</li>
              <li>Address space layout randomization (ASLR)</li>
            </ul>
          </li>
          <li><strong>Secure Storage:</strong>
            <ul>
              <li>Encrypted firmware storage</li>
              <li>Secure configuration storage</li>
              <li>Key and certificate storage</li>
              <li>Secure logging and audit trails</li>
            </ul>
          </li>
        </ul>
        
        <h3>Firmware Update Security</h3>
        <ul>
          <li><strong>Secure Update Mechanisms:</strong>
            <ul>
              <li>Authenticated firmware updates</li>
              <li>Encrypted update channels</li>
              <li>Rollback protection</li>
              <li>Update verification and validation</li>
            </ul>
          </li>
          <li><strong>Over-the-Air (OTA) Security:</strong>
            <ul>
              <li>Secure communication protocols</li>
              <li>Update package integrity</li>
              <li>Partial update mechanisms</li>
              <li>Failure recovery procedures</li>
            </ul>
          </li>
          <li><strong>Update Management:</strong>
            <ul>
              <li>Version control and tracking</li>
              <li>Update scheduling and policies</li>
              <li>Device compatibility checking</li>
              <li>Update status monitoring</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Device Identity and Lifecycle Security",content:`
        <h2>IoT Device Identity and Lifecycle Security Management</h2>
        <p>Device identity and lifecycle security ensure that IoT devices maintain secure identities and security postures throughout their operational lifetime.</p>
        
        <h3>Device Identity Management</h3>
        <ul>
          <li><strong>Unique Device Identity:</strong>
            <ul>
              <li>Hardware-based device identifiers</li>
              <li>Cryptographic device certificates</li>
              <li>Device fingerprinting techniques</li>
              <li>Identity verification mechanisms</li>
            </ul>
          </li>
          <li><strong>Certificate Management:</strong>
            <ul>
              <li>X.509 certificate provisioning</li>
              <li>Certificate lifecycle management</li>
              <li>Certificate revocation handling</li>
              <li>Certificate renewal automation</li>
            </ul>
          </li>
          <li><strong>Authentication Mechanisms:</strong>
            <ul>
              <li>Mutual authentication protocols</li>
              <li>Challenge-response authentication</li>
              <li>Biometric authentication (where applicable)</li>
              <li>Multi-factor authentication</li>
            </ul>
          </li>
        </ul>
        
        <h3>Device Provisioning Security</h3>
        <ul>
          <li><strong>Secure Provisioning:</strong>
            <ul>
              <li>Zero-touch provisioning</li>
              <li>Secure credential injection</li>
              <li>Manufacturing security controls</li>
              <li>Supply chain security</li>
            </ul>
          </li>
          <li><strong>Initial Configuration:</strong>
            <ul>
              <li>Secure default configurations</li>
              <li>Configuration validation</li>
              <li>Security policy enforcement</li>
              <li>Initial key establishment</li>
            </ul>
          </li>
          <li><strong>Onboarding Security:</strong>
            <ul>
              <li>Secure device registration</li>
              <li>Network access control</li>
              <li>Device authorization</li>
              <li>Initial trust establishment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Device Lifecycle Management</h3>
        <ul>
          <li><strong>Operational Security:</strong>
            <ul>
              <li>Continuous security monitoring</li>
              <li>Security policy enforcement</li>
              <li>Vulnerability management</li>
              <li>Incident response capabilities</li>
            </ul>
          </li>
          <li><strong>Maintenance and Updates:</strong>
            <ul>
              <li>Security patch management</li>
              <li>Configuration updates</li>
              <li>Key rotation and renewal</li>
              <li>Performance monitoring</li>
            </ul>
          </li>
          <li><strong>End-of-Life Security:</strong>
            <ul>
              <li>Secure device decommissioning</li>
              <li>Data sanitization procedures</li>
              <li>Key and certificate revocation</li>
              <li>Asset disposal security</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary purpose of a Hardware Security Module (HSM) in IoT devices?",options:["To improve device performance","To provide tamper-resistant key storage and cryptographic operations","To reduce power consumption","To enable wireless communication"],correctAnswer:1,explanation:"The primary purpose of an HSM in IoT devices is to provide tamper-resistant key storage and cryptographic operations, establishing a hardware root of trust for device security."},{question:"Which component is most critical for establishing trust in the secure boot process?",options:["Operating system kernel","Application software","Hardware-based root of trust","Network connectivity"],correctAnswer:2,explanation:"The hardware-based root of trust is most critical for establishing trust in the secure boot process, as it provides the immutable foundation for verifying all subsequent boot components."},{question:"What is the main security benefit of implementing measured boot in IoT devices?",options:["Faster boot times","Lower power consumption","Ability to verify device integrity and provide attestation","Improved network performance"],correctAnswer:2,explanation:"The main security benefit of measured boot is the ability to verify device integrity and provide attestation by measuring and recording the state of boot components, enabling remote verification of device trustworthiness."}]},type:"quiz"}]},a={id:"iot-4",pathId:"iot-security",title:"IoT Network Security",description:"Master IoT network security including wireless protocols, network segmentation, secure communication, and comprehensive network protection strategies for IoT environments.",objectives:["Understand IoT network security challenges","Learn wireless protocol security","Master network segmentation for IoT","Develop skills in secure IoT communications","Learn network monitoring and detection","Implement comprehensive IoT network security"],difficulty:"Intermediate",estimatedTime:125,sections:[{title:"IoT Wireless Protocol Security",content:`
        <h2>IoT Wireless Protocol Security</h2>
        <p>IoT devices rely heavily on wireless communications, making protocol security critical for protecting data transmission and preventing unauthorized access.</p>
        
        <h3>WiFi Security for IoT</h3>
        <ul>
          <li><strong>WiFi Security Standards:</strong>
            <ul>
              <li>WPA3 and WPA2 security protocols</li>
              <li>Enterprise vs. personal security modes</li>
              <li>WiFi Protected Setup (WPS) vulnerabilities</li>
              <li>WiFi 6 security enhancements</li>
            </ul>
          </li>
          <li><strong>WiFi Network Configuration:</strong>
            <ul>
              <li>Secure SSID and password policies</li>
              <li>Guest network isolation</li>
              <li>MAC address filtering limitations</li>
              <li>Hidden SSID security considerations</li>
            </ul>
          </li>
          <li><strong>Enterprise WiFi Security:</strong>
            <ul>
              <li>802.1X authentication</li>
              <li>RADIUS server integration</li>
              <li>Certificate-based authentication</li>
              <li>Dynamic VLAN assignment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Bluetooth and BLE Security</h3>
        <ul>
          <li><strong>Bluetooth Security Mechanisms:</strong>
            <ul>
              <li>Bluetooth Low Energy (BLE) security</li>
              <li>Pairing and bonding procedures</li>
              <li>Link layer encryption</li>
              <li>Application layer security</li>
            </ul>
          </li>
          <li><strong>Bluetooth Vulnerabilities:</strong>
            <ul>
              <li>Bluejacking and bluesnarfing</li>
              <li>Man-in-the-middle attacks</li>
              <li>Eavesdropping vulnerabilities</li>
              <li>Device impersonation attacks</li>
            </ul>
          </li>
          <li><strong>Bluetooth Mesh Security:</strong>
            <ul>
              <li>Mesh network security architecture</li>
              <li>Network and application keys</li>
              <li>Secure provisioning procedures</li>
              <li>Message authentication and encryption</li>
            </ul>
          </li>
        </ul>
        
        <h3>LPWAN Security</h3>
        <ul>
          <li><strong>LoRaWAN Security:</strong>
            <ul>
              <li>LoRaWAN security architecture</li>
              <li>Network and application session keys</li>
              <li>Join procedures and security</li>
              <li>Frame counter and replay protection</li>
            </ul>
          </li>
          <li><strong>Sigfox Security:</strong>
            <ul>
              <li>Sigfox security mechanisms</li>
              <li>Device authentication</li>
              <li>Message integrity protection</li>
              <li>Network security considerations</li>
            </ul>
          </li>
          <li><strong>NB-IoT and LTE-M Security:</strong>
            <ul>
              <li>Cellular IoT security standards</li>
              <li>SIM-based authentication</li>
              <li>Network access security</li>
              <li>End-to-end encryption</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Network Segmentation and Isolation",content:`
        <h2>IoT Network Segmentation and Isolation Strategies</h2>
        <p>Network segmentation is crucial for IoT security, limiting the impact of compromised devices and controlling communication between different network zones.</p>
        
        <h3>Network Segmentation Approaches</h3>
        <ul>
          <li><strong>VLAN-Based Segmentation:</strong>
            <ul>
              <li>IoT device VLAN isolation</li>
              <li>Inter-VLAN routing controls</li>
              <li>Dynamic VLAN assignment</li>
              <li>VLAN security best practices</li>
            </ul>
          </li>
          <li><strong>Software-Defined Networking (SDN):</strong>
            <ul>
              <li>SDN-based IoT segmentation</li>
              <li>Centralized policy management</li>
              <li>Dynamic flow control</li>
              <li>Programmable network security</li>
            </ul>
          </li>
          <li><strong>Micro-Segmentation:</strong>
            <ul>
              <li>Device-level isolation</li>
              <li>Application-aware segmentation</li>
              <li>Zero-trust network principles</li>
              <li>Granular access controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Network Zones</h3>
        <ul>
          <li><strong>Device Management Zone:</strong>
            <ul>
              <li>Device provisioning and onboarding</li>
              <li>Configuration management</li>
              <li>Firmware update services</li>
              <li>Device monitoring and diagnostics</li>
            </ul>
          </li>
          <li><strong>Data Collection Zone:</strong>
            <ul>
              <li>Sensor data aggregation</li>
              <li>Local data processing</li>
              <li>Edge computing services</li>
              <li>Data validation and filtering</li>
            </ul>
          </li>
          <li><strong>External Connectivity Zone:</strong>
            <ul>
              <li>Internet gateway services</li>
              <li>Cloud connectivity</li>
              <li>Remote access controls</li>
              <li>External API interfaces</li>
            </ul>
          </li>
        </ul>
        
        <h3>Access Control and Firewall Rules</h3>
        <ul>
          <li><strong>Firewall Configuration:</strong>
            <ul>
              <li>IoT-specific firewall rules</li>
              <li>Protocol-based filtering</li>
              <li>Time-based access controls</li>
              <li>Geo-location restrictions</li>
            </ul>
          </li>
          <li><strong>Network Access Control (NAC):</strong>
            <ul>
              <li>Device authentication and authorization</li>
              <li>Compliance checking</li>
              <li>Dynamic policy enforcement</li>
              <li>Quarantine procedures</li>
            </ul>
          </li>
          <li><strong>Intrusion Prevention Systems (IPS):</strong>
            <ul>
              <li>IoT-aware threat detection</li>
              <li>Behavioral analysis</li>
              <li>Automated response actions</li>
              <li>Signature-based detection</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Secure IoT Communications",content:`
        <h2>Secure IoT Communication Protocols and Practices</h2>
        <p>Implementing secure communication protocols ensures data confidentiality, integrity, and authenticity in IoT systems while managing resource constraints.</p>
        
        <h3>Application Layer Security</h3>
        <ul>
          <li><strong>MQTT Security:</strong>
            <ul>
              <li>MQTT over TLS (MQTTS)</li>
              <li>Client certificate authentication</li>
              <li>Topic-based access control</li>
              <li>Message payload encryption</li>
            </ul>
          </li>
          <li><strong>CoAP Security:</strong>
            <ul>
              <li>DTLS for CoAP (CoAPS)</li>
              <li>Pre-shared key authentication</li>
              <li>Certificate-based security</li>
              <li>Object security for CoAP (OSCORE)</li>
            </ul>
          </li>
          <li><strong>HTTP/HTTPS Security:</strong>
            <ul>
              <li>TLS configuration for IoT</li>
              <li>Certificate management</li>
              <li>API security best practices</li>
              <li>RESTful API protection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Transport Layer Security</h3>
        <ul>
          <li><strong>TLS/DTLS Implementation:</strong>
            <ul>
              <li>Lightweight TLS for constrained devices</li>
              <li>DTLS for UDP-based protocols</li>
              <li>Cipher suite selection</li>
              <li>Perfect forward secrecy</li>
            </ul>
          </li>
          <li><strong>Certificate Management:</strong>
            <ul>
              <li>X.509 certificate provisioning</li>
              <li>Certificate validation procedures</li>
              <li>Certificate revocation handling</li>
              <li>Automated certificate renewal</li>
            </ul>
          </li>
          <li><strong>Key Management:</strong>
            <ul>
              <li>Pre-shared key (PSK) management</li>
              <li>Public key infrastructure (PKI)</li>
              <li>Key derivation and rotation</li>
              <li>Secure key distribution</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Monitoring and Threat Detection</h3>
        <ul>
          <li><strong>Traffic Analysis:</strong>
            <ul>
              <li>Network flow monitoring</li>
              <li>Protocol analysis</li>
              <li>Bandwidth utilization tracking</li>
              <li>Communication pattern analysis</li>
            </ul>
          </li>
          <li><strong>Anomaly Detection:</strong>
            <ul>
              <li>Behavioral baseline establishment</li>
              <li>Statistical anomaly detection</li>
              <li>Machine learning-based detection</li>
              <li>Real-time alerting systems</li>
            </ul>
          </li>
          <li><strong>Incident Response:</strong>
            <ul>
              <li>Automated threat response</li>
              <li>Device isolation procedures</li>
              <li>Forensic data collection</li>
              <li>Recovery and remediation</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"Which security protocol is most appropriate for MQTT communications in IoT systems?",options:["HTTP Basic Authentication","MQTT over TLS (MQTTS)","Unencrypted MQTT","FTP over SSL"],correctAnswer:1,explanation:"MQTT over TLS (MQTTS) is most appropriate for secure MQTT communications as it provides encryption, authentication, and integrity protection while maintaining the lightweight nature required for IoT devices."},{question:"What is the primary benefit of network micro-segmentation in IoT environments?",options:["Increased network speed","Reduced hardware costs","Device-level isolation and granular access control","Simplified network management"],correctAnswer:2,explanation:"The primary benefit of micro-segmentation is device-level isolation and granular access control, which limits the impact of compromised devices and provides fine-grained security policies for IoT networks."},{question:"Which factor is most critical when implementing TLS for constrained IoT devices?",options:["Maximum encryption strength regardless of resources","Balancing security with resource constraints and performance","Using only the latest TLS version","Implementing all available cipher suites"],correctAnswer:1,explanation:"Balancing security with resource constraints and performance is most critical for constrained IoT devices, as they have limited processing power, memory, and battery life while still requiring adequate security protection."}]},type:"quiz"}]},s={id:"iot-5",pathId:"iot-security",title:"IoT Data Security",description:"Master IoT data security including data protection, privacy preservation, secure storage, and comprehensive data lifecycle security management for IoT systems.",objectives:["Understand IoT data security requirements","Learn data encryption and protection techniques","Master IoT data privacy preservation","Develop skills in secure data storage","Learn data lifecycle security management","Implement comprehensive IoT data security controls"],difficulty:"Intermediate",estimatedTime:130,sections:[{title:"IoT Data Protection Fundamentals",content:`
        <h2>IoT Data Protection and Classification</h2>
        <p>IoT systems generate and process vast amounts of data that require comprehensive protection strategies to ensure confidentiality, integrity, and availability.</p>
        
        <h3>IoT Data Types and Classification</h3>
        <ul>
          <li><strong>Sensor Data:</strong>
            <ul>
              <li>Environmental measurements</li>
              <li>Biometric and health data</li>
              <li>Location and tracking data</li>
              <li>Operational and performance data</li>
            </ul>
          </li>
          <li><strong>Device Data:</strong>
            <ul>
              <li>Configuration and settings</li>
              <li>Firmware and software versions</li>
              <li>Device status and diagnostics</li>
              <li>Security logs and events</li>
            </ul>
          </li>
          <li><strong>User Data:</strong>
            <ul>
              <li>Personal identification information</li>
              <li>User preferences and profiles</li>
              <li>Behavioral and usage patterns</li>
              <li>Authentication credentials</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Sensitivity and Risk Assessment</h3>
        <ul>
          <li><strong>Data Classification Levels:</strong>
            <ul>
              <li>Public data (no protection required)</li>
              <li>Internal data (basic protection)</li>
              <li>Confidential data (enhanced protection)</li>
              <li>Restricted data (maximum protection)</li>
            </ul>
          </li>
          <li><strong>Privacy Impact Assessment:</strong>
            <ul>
              <li>Personal data identification</li>
              <li>Privacy risk evaluation</li>
              <li>Data minimization principles</li>
              <li>Consent and lawful basis</li>
            </ul>
          </li>
          <li><strong>Regulatory Compliance:</strong>
            <ul>
              <li>GDPR compliance requirements</li>
              <li>CCPA and state privacy laws</li>
              <li>Industry-specific regulations</li>
              <li>Cross-border data transfer rules</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Protection Strategies</h3>
        <ul>
          <li><strong>Data Minimization:</strong>
            <ul>
              <li>Purpose limitation principles</li>
              <li>Data collection optimization</li>
              <li>Storage limitation policies</li>
              <li>Retention period management</li>
            </ul>
          </li>
          <li><strong>Data Anonymization:</strong>
            <ul>
              <li>Pseudonymization techniques</li>
              <li>K-anonymity and l-diversity</li>
              <li>Differential privacy methods</li>
              <li>Data masking and obfuscation</li>
            </ul>
          </li>
          <li><strong>Access Control:</strong>
            <ul>
              <li>Role-based access control (RBAC)</li>
              <li>Attribute-based access control (ABAC)</li>
              <li>Fine-grained permissions</li>
              <li>Dynamic access policies</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Data Encryption and Secure Storage",content:`
        <h2>IoT Data Encryption and Secure Storage Solutions</h2>
        <p>Implementing robust encryption and secure storage mechanisms protects IoT data throughout its lifecycle, from collection to processing and storage.</p>
        
        <h3>Data Encryption Strategies</h3>
        <ul>
          <li><strong>End-to-End Encryption:</strong>
            <ul>
              <li>Device-to-cloud encryption</li>
              <li>Application-layer encryption</li>
              <li>Key management for E2E encryption</li>
              <li>Performance optimization techniques</li>
            </ul>
          </li>
          <li><strong>Encryption at Rest:</strong>
            <ul>
              <li>Database encryption</li>
              <li>File system encryption</li>
              <li>Cloud storage encryption</li>
              <li>Backup and archive encryption</li>
            </ul>
          </li>
          <li><strong>Encryption in Transit:</strong>
            <ul>
              <li>TLS/DTLS for communication</li>
              <li>VPN and secure tunneling</li>
              <li>Message-level encryption</li>
              <li>Protocol-specific security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cryptographic Key Management</h3>
        <ul>
          <li><strong>Key Generation and Distribution:</strong>
            <ul>
              <li>Hardware-based key generation</li>
              <li>Secure key distribution protocols</li>
              <li>Key derivation functions</li>
              <li>Multi-party key establishment</li>
            </ul>
          </li>
          <li><strong>Key Lifecycle Management:</strong>
            <ul>
              <li>Key rotation policies</li>
              <li>Key escrow and recovery</li>
              <li>Key revocation procedures</li>
              <li>Key destruction and sanitization</li>
            </ul>
          </li>
          <li><strong>Key Storage Security:</strong>
            <ul>
              <li>Hardware security modules (HSM)</li>
              <li>Trusted execution environments</li>
              <li>Secure key vaults</li>
              <li>Distributed key storage</li>
            </ul>
          </li>
        </ul>
        
        <h3>Secure Storage Architectures</h3>
        <ul>
          <li><strong>Local Storage Security:</strong>
            <ul>
              <li>Device storage encryption</li>
              <li>Secure file systems</li>
              <li>Tamper-resistant storage</li>
              <li>Data integrity verification</li>
            </ul>
          </li>
          <li><strong>Edge Storage Security:</strong>
            <ul>
              <li>Edge computing data protection</li>
              <li>Distributed storage security</li>
              <li>Data synchronization security</li>
              <li>Local processing privacy</li>
            </ul>
          </li>
          <li><strong>Cloud Storage Security:</strong>
            <ul>
              <li>Cloud encryption strategies</li>
              <li>Multi-cloud data protection</li>
              <li>Data residency compliance</li>
              <li>Cloud access security</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Data Privacy and Lifecycle Management",content:`
        <h2>IoT Data Privacy and Comprehensive Lifecycle Management</h2>
        <p>Managing IoT data privacy and implementing comprehensive lifecycle management ensures compliance with regulations and protects user privacy throughout the data journey.</p>
        
        <h3>Privacy-Preserving Techniques</h3>
        <ul>
          <li><strong>Differential Privacy:</strong>
            <ul>
              <li>Privacy budget management</li>
              <li>Noise injection mechanisms</li>
              <li>Local vs. global differential privacy</li>
              <li>Utility-privacy trade-offs</li>
            </ul>
          </li>
          <li><strong>Homomorphic Encryption:</strong>
            <ul>
              <li>Computation on encrypted data</li>
              <li>Partially vs. fully homomorphic encryption</li>
              <li>Performance considerations</li>
              <li>Practical implementation challenges</li>
            </ul>
          </li>
          <li><strong>Secure Multi-Party Computation:</strong>
            <ul>
              <li>Collaborative data analysis</li>
              <li>Secret sharing schemes</li>
              <li>Privacy-preserving aggregation</li>
              <li>Federated learning approaches</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Governance and Compliance</h3>
        <ul>
          <li><strong>Data Governance Framework:</strong>
            <ul>
              <li>Data stewardship roles</li>
              <li>Data quality management</li>
              <li>Data lineage tracking</li>
              <li>Metadata management</li>
            </ul>
          </li>
          <li><strong>Consent Management:</strong>
            <ul>
              <li>Granular consent mechanisms</li>
              <li>Consent withdrawal procedures</li>
              <li>Dynamic consent management</li>
              <li>Consent audit trails</li>
            </ul>
          </li>
          <li><strong>Data Subject Rights:</strong>
            <ul>
              <li>Right to access and portability</li>
              <li>Right to rectification</li>
              <li>Right to erasure (right to be forgotten)</li>
              <li>Right to restrict processing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Lifecycle Security</h3>
        <ul>
          <li><strong>Data Collection Security:</strong>
            <ul>
              <li>Secure data ingestion</li>
              <li>Data validation and sanitization</li>
              <li>Source authentication</li>
              <li>Quality assurance procedures</li>
            </ul>
          </li>
          <li><strong>Data Processing Security:</strong>
            <ul>
              <li>Secure data transformation</li>
              <li>Processing environment isolation</li>
              <li>Audit logging and monitoring</li>
              <li>Error handling and recovery</li>
            </ul>
          </li>
          <li><strong>Data Retention and Disposal:</strong>
            <ul>
              <li>Retention policy enforcement</li>
              <li>Automated data purging</li>
              <li>Secure data destruction</li>
              <li>Compliance verification</li>
            </ul>
          </li>
        </ul>
      `,type:"text"},{title:"Knowledge Check",content:{questions:[{question:"What is the primary purpose of data classification in IoT systems?",options:["To improve data processing speed","To determine appropriate security controls based on data sensitivity","To reduce storage costs","To simplify data management"],correctAnswer:1,explanation:"The primary purpose of data classification is to determine appropriate security controls based on data sensitivity, ensuring that more sensitive data receives stronger protection measures while optimizing resources for less sensitive data."},{question:"Which privacy-preserving technique allows computation on encrypted data without decryption?",options:["Differential privacy","Data anonymization","Homomorphic encryption","Data masking"],correctAnswer:2,explanation:"Homomorphic encryption allows computation on encrypted data without decryption, enabling privacy-preserving analytics and processing while maintaining data confidentiality throughout the computation process."},{question:"What is the most critical consideration for IoT data retention policies?",options:["Maximizing storage capacity","Balancing business needs with privacy regulations and data minimization","Keeping all data indefinitely","Minimizing storage costs only"],correctAnswer:1,explanation:"The most critical consideration is balancing business needs with privacy regulations and data minimization principles, ensuring compliance with laws like GDPR while meeting legitimate business requirements for data retention."}]},type:"quiz"}]},i=(n,e,t)=>({id:n,pathId:"iot-security",title:e,description:t,objectives:[`Learn ${e.toLowerCase()} concepts`,`Understand ${e.toLowerCase()} applications`,`Master ${e.toLowerCase()} best practices`,`Implement ${e.toLowerCase()} solutions`],difficulty:"Intermediate",estimatedTime:120,sections:[{title:"Overview",content:`<h2>${e}</h2><p>${t}</p><p>This module will be developed with comprehensive content covering all aspects of ${e.toLowerCase()}.</p>`,type:"text"}]}),c=i("iot-6","IoT Communication Security","Secure IoT communication protocols and standards"),u=i("iot-7","IoT Identity Management","Identity and access management for IoT devices"),d=i("iot-8","IoT Firmware Security","Firmware security and secure boot processes"),g=i("iot-9","IoT Cloud Security","Cloud security for IoT platforms and services"),y=i("iot-10","IoT Privacy and Compliance","Privacy protection and regulatory compliance"),m=i("iot-11","IoT Penetration Testing","Security testing methodologies for IoT systems"),p=i("iot-12","IoT Vulnerability Assessment","Vulnerability assessment techniques"),h=i("iot-13","IoT Incident Response","Incident response for IoT security breaches"),f=i("iot-14","IoT Digital Forensics","Digital forensics for IoT devices and networks"),v=i("iot-15","IoT Security Monitoring","Continuous monitoring and threat detection"),S=i("iot-16","IoT Security Automation","Automated security for IoT environments"),T=i("iot-17","IoT Edge Security","Edge computing security for IoT"),I=i("iot-18","IoT 5G Security","5G network security for IoT applications"),w=i("iot-19","IoT Blockchain Security","Blockchain applications in IoT security"),C=i("iot-20","IoT AI Security","AI and machine learning security in IoT"),b=i("iot-21","Industrial IoT Security","Security for Industrial IoT and OT systems"),D=i("iot-22","Smart City Security","Security for smart city IoT infrastructure"),P=i("iot-23","Healthcare IoT Security","Medical IoT device and system security"),k=i("iot-24","Automotive IoT Security","Connected vehicle and automotive IoT security"),A=i("iot-25","IoT Security Capstone","Comprehensive IoT security project"),L=[l,o,r,a,s,c,u,d,g,y,m,p,h,f,v,S,T,I,w,C,b,D,P,k,A];export{L as iotSecurityModules};
