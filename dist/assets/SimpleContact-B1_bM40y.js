import{r,j as e,L as i,ae as b,a4 as l,ax as o,aH as j,aY as N,aZ as f}from"./index-CVvVjHWF.js";const w=()=>{const[s,m]=r.useState({name:"",email:"",company:"",phone:"",employees:"",message:""}),[t,n]=r.useState(!1),[d,x]=r.useState(!1),a=c=>{const{name:u,value:p}=c.target;m(g=>({...g,[u]:p}))},h=c=>{c.preventDefault(),n(!0),setTimeout(()=>{n(!1),x(!0)},1500)};return e.jsx("div",{className:"min-h-screen bg-[#0B1120] py-16 px-4",children:e.jsxs("div",{className:"container mx-auto max-w-4xl",children:[e.jsx("div",{className:"mb-8",children:e.jsxs(i,{to:"/pricing",className:"text-gray-400 hover:text-white flex items-center gap-2",children:[e.jsx(b,{}),e.jsx("span",{children:"Back to Pricing"})]})}),e.jsx("div",{className:"bg-[#1A1F35] rounded-xl border border-gray-800 overflow-hidden",children:d?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-[#88cc14]/20 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx(l,{className:"text-[#88cc14] text-3xl"})}),e.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Thank You!"}),e.jsx("p",{className:"text-xl text-gray-300 mb-6",children:"Your business inquiry has been received. Our team will contact you shortly to discuss your requirements."}),e.jsx(i,{to:"/",className:"inline-block bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold px-6 py-3 rounded-lg transition-colors",children:"Return to Home"})]}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2",children:[e.jsxs("div",{className:"bg-[#0B1120] p-8",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(o,{className:"text-[#88cc14] text-2xl"}),e.jsx("h2",{className:"text-2xl font-bold text-white",children:"Business Tier"})]}),e.jsx("p",{className:"text-gray-300 mb-8",children:"Contact our sales team to learn more about our Business tier and how we can help your organization improve its cybersecurity posture."}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"Business Tier Includes:"}),e.jsxs("ul",{className:"space-y-4",children:[e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"text-[#88cc14] mt-1"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Unlimited Access"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Full access to all 150 challenges and 50 learning modules"})]})]}),e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"text-[#88cc14] mt-1"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Team Management"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Manage your team's progress and assign specific learning paths"})]})]}),e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"text-[#88cc14] mt-1"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Custom Learning Paths"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Tailored learning experiences for different roles in your organization"})]})]}),e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"text-[#88cc14] mt-1"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Dedicated Support"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Priority support with a dedicated account manager"})]})]}),e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"text-[#88cc14] mt-1"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Advanced Reporting"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Detailed analytics and progress reports for your team"})]})]})]})]})]}),e.jsxs("div",{className:"p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Contact Sales"}),e.jsxs("form",{onSubmit:h,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"name",className:"block text-gray-400 mb-2",children:"Full Name *"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(j,{className:"text-gray-500"})}),e.jsx("input",{type:"text",id:"name",name:"name",className:"bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"John Doe",value:s.name,onChange:a,required:!0})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"email",className:"block text-gray-400 mb-2",children:"Email Address *"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(N,{className:"text-gray-500"})}),e.jsx("input",{type:"email",id:"email",name:"email",className:"bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"<EMAIL>",value:s.email,onChange:a,required:!0})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"company",className:"block text-gray-400 mb-2",children:"Company Name *"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(o,{className:"text-gray-500"})}),e.jsx("input",{type:"text",id:"company",name:"company",className:"bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"Company Inc.",value:s.company,onChange:a,required:!0})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-gray-400 mb-2",children:"Phone Number"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(f,{className:"text-gray-500"})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",className:"bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"+****************",value:s.phone,onChange:a})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"employees",className:"block text-gray-400 mb-2",children:"Number of Employees"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(y,{className:"text-gray-500"})}),e.jsxs("select",{id:"employees",name:"employees",className:"bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",value:s.employees,onChange:a,children:[e.jsx("option",{value:"",className:"bg-[#0B1120]",children:"Select..."}),e.jsx("option",{value:"1-10",className:"bg-[#0B1120]",children:"1-10"}),e.jsx("option",{value:"11-50",className:"bg-[#0B1120]",children:"11-50"}),e.jsx("option",{value:"51-200",className:"bg-[#0B1120]",children:"51-200"}),e.jsx("option",{value:"201-500",className:"bg-[#0B1120]",children:"201-500"}),e.jsx("option",{value:"501+",className:"bg-[#0B1120]",children:"501+"})]})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"message",className:"block text-gray-400 mb-2",children:"Message"}),e.jsx("textarea",{id:"message",name:"message",rows:"4",className:"bg-[#0B1120] border border-gray-800 text-white rounded-lg block w-full p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]",placeholder:"Tell us about your requirements...",value:s.message,onChange:a})]}),e.jsx("button",{type:"submit",className:`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${t?"opacity-70 cursor-not-allowed":""}`,disabled:t,children:t?"Submitting...":"Submit Inquiry"})]})]})]})})]})})},y=({className:s})=>e.jsx("svg",{className:s,fill:"currentColor",viewBox:"0 0 640 512",children:e.jsx("path",{d:"M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"})});export{w as default};
