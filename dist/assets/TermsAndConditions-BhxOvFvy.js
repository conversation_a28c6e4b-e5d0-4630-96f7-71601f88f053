import{u as r,j as e,L as i,ae as l}from"./index-CVvVjHWF.js";const t=()=>{const{darkMode:s}=r();return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16 px-4`,children:e.jsxs("div",{className:"container mx-auto max-w-4xl",children:[e.jsxs(i,{to:"/",className:`inline-flex items-center mb-6 ${s?"text-gray-300 hover:text-white":"text-gray-600 hover:text-gray-900"}`,children:[e.jsx(l,{className:"mr-2"})," Back to Home"]}),e.jsx("h1",{className:"text-3xl md:text-4xl font-bold mb-8",children:"Terms and Conditions of Use"}),e.jsx("p",{className:"mb-8 text-sm",children:"Last Updated: May 2025"}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"1. Definitions"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:'"User" refers to any individual who registers on or accesses the Platform.'}),e.jsx("li",{children:'"Courses" means online educational materials related to cybersecurity, Linux, and related topics, including but not limited to videos, assessments, documents, and labs.'}),e.jsx("li",{children:'"We", "Our", or "CyberForce" refers to the operator and owner of the CyberForce Platform.'}),e.jsx("li",{children:'"Platform" refers to the CyberForce website, mobile application, training content, services, and related software.'})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"2. Eligibility"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"You must be at least 12 years of age to use the Platform."}),e.jsx("li",{children:"You represent that the registration information you provide is accurate and up to date."}),e.jsx("li",{children:"You may not use the Platform if your access has been previously revoked or banned."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"3. User Account and Responsibilities"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"You agree to keep your username and password secure and confidential."}),e.jsx("li",{children:"You are solely responsible for activities that occur under your account."}),e.jsx("li",{children:"Sharing your login credentials or providing unauthorized access to the platform is strictly prohibited."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"4. Platform Use and License"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"CyberForce grants you a limited, non-exclusive, non-transferable, revocable license to access and use the platform for personal, non-commercial learning."}),e.jsxs("li",{children:["You agree not to:",e.jsxs("ul",{className:"list-disc pl-6 mt-2 space-y-1",children:[e.jsx("li",{children:"Modify, copy, distribute, sell, or license any content."}),e.jsx("li",{children:"Use any automated means (scraping, crawling, bots) to access the platform."}),e.jsx("li",{children:"Upload or transmit malware, viruses, or harmful code."}),e.jsx("li",{children:"Disrupt the functionality of the platform or other users' learning experience."})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"5. Course Access, Duration, and Expiry"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"Upon purchasing a course or accessing a free course, you may retain access for the defined course period (e.g., 3 months, 6 months)."}),e.jsx("li",{children:"CyberForce may archive or retire outdated courses without prior notice, especially for content no longer aligned with industry standards."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"6. Fees, Payments, and Refunds"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"Course fees must be paid in full before gaining access to premium content."}),e.jsx("li",{children:"Payment methods include credit/debit cards, bank transfers, or other methods specified on the Platform."}),e.jsxs("li",{children:["Refund Policy:",e.jsxs("ul",{className:"list-disc pl-6 mt-2 space-y-1",children:[e.jsx("li",{children:"Refunds are available within 7 days of purchase if less than 20% of the course is completed."}),e.jsx("li",{children:"No refunds are issued for completed courses or certificates."}),e.jsx("li",{children:"CyberForce reserves the right to adjust pricing, promotions, or discount policies at any time."})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"7. Intellectual Property"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"All content, logos, trademarks, design elements, training material, software, and platform elements are the exclusive property of CyberForce or its licensors."}),e.jsx("li",{children:"Unauthorized use of any intellectual property may result in legal action."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"8. Code of Conduct"}),e.jsx("ul",{className:"list-disc pl-6 space-y-2",children:e.jsxs("li",{children:["You agree to:",e.jsxs("ul",{className:"list-disc pl-6 mt-2 space-y-1",children:[e.jsx("li",{children:"Use respectful and professional language in all platform communications (forums, assignments, chat)."}),e.jsx("li",{children:"Avoid harassment, hate speech, or any form of discriminatory behavior."}),e.jsx("li",{children:"Report suspicious activities or misconduct."})]})]})})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"9. Certificates and Credentials"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"Upon successful completion of courses and passing assessments, you may receive a digital certificate."}),e.jsx("li",{children:"CyberForce does not guarantee job placement, industry certification, or formal academic accreditation unless stated."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"10. Availability and Service Disruption"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"While CyberForce aims for 99.9% uptime, we do not guarantee continuous or error-free access."}),e.jsx("li",{children:"Platform maintenance, upgrades, and outages may occur, and users will be notified in advance when possible."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"11. Third-Party Services"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"The platform may integrate or link to third-party tools, labs, or learning environments."}),e.jsx("li",{children:"CyberForce is not responsible for content or services provided by third parties."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"12. Privacy and Data Protection"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"We collect and process user data in accordance with our Privacy Policy and applicable data protection laws."}),e.jsx("li",{children:"CyberForce may collect data on usage, assessments, and progress for platform improvement and reporting."}),e.jsx("li",{children:"We do not sell or share your personal data with third parties for marketing."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"13. Termination and Suspension"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsxs("li",{children:["CyberForce reserves the right to suspend or terminate your account if you:",e.jsxs("ul",{className:"list-disc pl-6 mt-2 space-y-1",children:[e.jsx("li",{children:"Violate any part of these Terms."}),e.jsx("li",{children:"Engage in fraudulent, abusive, or harmful behavior."}),e.jsx("li",{children:"Breach intellectual property rights."})]})]}),e.jsx("li",{children:"Users may also delete their account upon written request."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"14. Limitation of Liability"}),e.jsx("ul",{className:"list-disc pl-6 space-y-2",children:e.jsxs("li",{children:["To the maximum extent permitted by law:",e.jsxs("ul",{className:"list-disc pl-6 mt-2 space-y-1",children:[e.jsx("li",{children:"CyberForce is not liable for any direct, indirect, incidental, or consequential damages."}),e.jsx("li",{children:"We are not responsible for any loss of data, profits, opportunities, or career outcomes resulting from platform use."})]})]})})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"15. Changes to Terms"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"CyberForce reserves the right to amend these Terms at any time."}),e.jsx("li",{children:"Significant changes will be communicated via email or platform notifications."}),e.jsx("li",{children:"Continued use of the platform after changes means acceptance of the new terms."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"16. Governing Law and Jurisdiction"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[e.jsx("li",{children:"These Terms shall be governed by the laws of the Sultanate of Oman."}),e.jsx("li",{children:"Any disputes shall be submitted to the competent courts in Muscat."})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-8`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"17. Contact Information"}),e.jsx("p",{className:"mb-4",children:"If you have any questions about these Terms, please contact:"}),e.jsxs("div",{className:"pl-6",children:[e.jsx("p",{className:"font-bold",children:"CyberForce"}),e.jsx("p",{children:"Email: <EMAIL>"}),e.jsx("p",{children:"Phone: +968 71104475"}),e.jsx("p",{children:"Website: www.cyberforce.om"})]})]})]})})};export{t as default};
