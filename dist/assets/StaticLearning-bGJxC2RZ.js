import{j as e,N as b,a1 as y,a3 as w,u as f,a as k,w as S,r as x,L as d,ae as j,G as h,Q as A,Y as _,af as I,l as N,x as g}from"./index-CVvVjHWF.js";const T=s=>{if(s<60)return`${s} min`;const i=Math.floor(s/60),c=s%60;return c>0?`${i}h ${c}m`:`${i}h`},$=[{id:"m1",slug:"intro-to-cybersecurity",title:"Introduction to Cybersecurity",description:"Learn the fundamentals of cybersecurity, including key concepts, common threats, and basic security practices. This module provides a solid foundation for beginners.",category:{name:"Web Security"},difficulty:{name:"<PERSON><PERSON><PERSON>"},estimated_time:60,icon:e.jsx(b,{}),isFree:!0,content:{introduction:"Welcome to the Introduction to Cybersecurity module! In this module, you will learn the fundamental concepts of cybersecurity, understand common threats, and discover basic security practices that everyone should follow. By the end of this module, you will have a solid foundation in cybersecurity principles that will prepare you for more advanced topics.",sections:["cybersecurity-basics","common-threats","security-practices","career-paths"],section_details:{"cybersecurity-basics":{title:"Cybersecurity Basics",content:"Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These attacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes. The field encompasses various disciplines, including network security, application security, information security, operational security, and more.",key_points:["The CIA Triad: Confidentiality, Integrity, and Availability","Authentication vs. Authorization","Defense in Depth Strategy","Security by Design Principles"]},"common-threats":{title:"Common Cybersecurity Threats",content:"Understanding the threat landscape is crucial for effective cybersecurity. This section covers the most common types of cyber threats and attack vectors that organizations and individuals face today.",key_points:["Malware: Viruses, Worms, Trojans, Ransomware","Social Engineering: Phishing, Spear Phishing, Pretexting","Man-in-the-Middle Attacks","Denial of Service (DoS) and Distributed Denial of Service (DDoS)","SQL Injection and Cross-Site Scripting (XSS)"]},"security-practices":{title:"Basic Security Practices",content:"Implementing good security practices is essential for protecting yourself and your organization from cyber threats. This section covers fundamental security measures that everyone should follow.",key_points:["Strong Password Management","Multi-Factor Authentication","Regular Software Updates and Patching","Data Backup and Recovery","Security Awareness and Training"]},"career-paths":{title:"Cybersecurity Career Paths",content:"The field of cybersecurity offers diverse career opportunities. This section provides an overview of various cybersecurity roles and the skills required for each.",key_points:["Security Analyst","Penetration Tester","Security Engineer","Security Architect","Chief Information Security Officer (CISO)"]}},quiz:{questions:[{question:"What does the 'I' in the CIA triad stand for?",options:["Intelligence","Integrity","Internet","Interface"],correct_answer:"Integrity"},{question:"Which of the following is NOT a type of malware?",options:["Virus","Worm","Phishing","Ransomware"],correct_answer:"Phishing"},{question:"What is the primary purpose of multi-factor authentication?",options:["To make login faster","To eliminate the need for passwords","To add additional layers of security beyond just a password","To track user activity"],correct_answer:"To add additional layers of security beyond just a password"}]},resources:[{title:"NIST Cybersecurity Framework",url:"https://www.nist.gov/cyberframework"},{title:"OWASP Top Ten",url:"https://owasp.org/www-project-top-ten/"}]}},{id:"m2",slug:"web-security-fundamentals",title:"Web Security Fundamentals",description:"Learn about common web vulnerabilities, how they are exploited, and best practices for securing web applications. This module covers essential web security concepts.",category:{name:"Web Security"},difficulty:{name:"Beginner"},estimated_time:75,icon:e.jsx(y,{}),isFree:!0,content:{introduction:"Welcome to the Web Security Fundamentals module! In this module, you will learn about common web vulnerabilities, how they are exploited by attackers, and best practices for securing web applications. Web security is crucial as most modern businesses rely on web applications for their operations and customer interactions.",sections:["web-architecture","common-vulnerabilities","secure-coding","security-testing"],section_details:{"web-architecture":{title:"Web Application Architecture",content:"Understanding how web applications work is essential for securing them. This section covers the basic components of web architecture and how they interact.",key_points:["Client-Server Model","HTTP/HTTPS Protocols","Frontend vs. Backend","Databases and Data Storage","Authentication and Session Management"]},"common-vulnerabilities":{title:"Common Web Vulnerabilities",content:"Web applications are susceptible to various security vulnerabilities. This section covers the most common web vulnerabilities based on the OWASP Top Ten.",key_points:["Injection Flaws (SQL, NoSQL, OS, LDAP)","Broken Authentication and Session Management","Cross-Site Scripting (XSS)","Cross-Site Request Forgery (CSRF)","Security Misconfigurations"]},"secure-coding":{title:"Secure Coding Practices",content:"Implementing secure coding practices is essential for developing secure web applications. This section covers fundamental principles and techniques for secure coding.",key_points:["Input Validation and Sanitization","Parameterized Queries","Output Encoding","Secure Authentication Implementation","Proper Error Handling"]},"security-testing":{title:"Web Application Security Testing",content:"Regular security testing is crucial for identifying and addressing vulnerabilities in web applications. This section introduces various approaches to web application security testing.",key_points:["Static Application Security Testing (SAST)","Dynamic Application Security Testing (DAST)","Manual Penetration Testing","Security Code Reviews","Continuous Security Testing"]}},quiz:{questions:[{question:"Which of the following is an example of an injection attack?",options:["Cross-Site Scripting","SQL Injection","Session Hijacking","Brute Force Attack"],correct_answer:"SQL Injection"},{question:"What is the primary defense against Cross-Site Scripting (XSS) attacks?",options:["Input validation","Output encoding","Using HTTPS","Strong passwords"],correct_answer:"Output encoding"},{question:"Which of the following is NOT a secure coding practice?",options:["Using parameterized queries","Storing passwords in plain text","Implementing proper error handling","Input validation"],correct_answer:"Storing passwords in plain text"}]},resources:[{title:"OWASP Web Security Testing Guide",url:"https://owasp.org/www-project-web-security-testing-guide/"},{title:"Mozilla Web Security Guidelines",url:"https://infosec.mozilla.org/guidelines/web_security"}]}},{id:"m3",slug:"network-security-basics",title:"Network Security Basics",description:"Learn the fundamentals of network security, including common network attacks, defense mechanisms, and monitoring techniques. This module provides essential knowledge for securing networks.",category:{name:"Network Security"},difficulty:{name:"Beginner"},estimated_time:70,icon:e.jsx(w,{}),isFree:!1,content:{introduction:"Welcome to the Network Security Basics module! In this module, you will learn the fundamentals of network security, including network architecture, common attack vectors, defense mechanisms, and monitoring techniques. Network security is critical as it protects the underlying infrastructure that supports information systems and communications.",sections:["network-fundamentals","network-threats","defense-mechanisms","monitoring-detection"],section_details:{"network-fundamentals":{title:"Network Fundamentals",content:"Understanding how networks function is essential for securing them. This section covers basic network concepts and components.",key_points:["OSI and TCP/IP Models","Network Protocols (TCP, UDP, ICMP, etc.)","Network Devices (Routers, Switches, Firewalls)","IP Addressing and Subnetting","Network Topologies"]},"network-threats":{title:"Common Network Threats",content:"Networks face various security threats. This section covers common network attacks and vulnerabilities.",key_points:["Man-in-the-Middle Attacks","Denial of Service (DoS) and Distributed Denial of Service (DDoS)","ARP Poisoning and MAC Flooding","DNS Attacks (Cache Poisoning, Tunneling)","Wireless Network Attacks"]},"defense-mechanisms":{title:"Network Defense Mechanisms",content:"Implementing proper defense mechanisms is crucial for network security. This section covers various tools and techniques for protecting networks.",key_points:["Firewalls and Access Control Lists","Intrusion Detection and Prevention Systems","Virtual Private Networks (VPNs)","Network Segmentation","Encryption Protocols (TLS/SSL, IPsec)"]},"monitoring-detection":{title:"Network Monitoring and Detection",content:"Continuous monitoring is essential for detecting and responding to network security incidents. This section covers monitoring tools and techniques.",key_points:["Network Traffic Analysis","Log Management and Analysis","Security Information and Event Management (SIEM)","Network Behavior Analysis","Incident Response Procedures"]}},quiz:{questions:[{question:"Which layer of the OSI model is responsible for routing?",options:["Physical Layer","Data Link Layer","Network Layer","Transport Layer"],correct_answer:"Network Layer"},{question:"What type of attack aims to exhaust a system's resources, making it unavailable to legitimate users?",options:["Man-in-the-Middle Attack","SQL Injection","Denial of Service Attack","Cross-Site Scripting"],correct_answer:"Denial of Service Attack"},{question:"Which of the following is NOT a function of a firewall?",options:["Filtering network traffic","Blocking unauthorized access","Encrypting data","Monitoring network traffic"],correct_answer:"Encrypting data"}]},resources:[{title:"SANS Network Security Resources",url:"https://www.sans.org/network-security/"},{title:"Wireshark Network Protocol Analyzer",url:"https://www.wireshark.org/"}]}}],M=s=>{try{const i=localStorage.getItem("learning_progress");return i&&JSON.parse(i)[s]||0}catch(i){return console.error("Error getting module progress:",i),0}},v=(s,i)=>{try{const c=localStorage.getItem("learning_progress"),l=c?JSON.parse(c):{};l[s]=i,localStorage.setItem("learning_progress",JSON.stringify(l))}catch(c){console.error("Error saving module progress:",c)}},F=()=>{const{darkMode:s}=f(),{profile:i}=S(),[c,l]=x.useState({});return x.useEffect(()=>{try{const t=localStorage.getItem("learning_progress");t&&l(JSON.parse(t))}catch(t){console.error("Error loading progress data:",t)}},[]),e.jsx("div",{className:"learning-page",children:e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Welcome to XCerberus Learning Platform"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:"Explore our comprehensive cybersecurity learning modules designed to help you master essential skills and advance your career."}),!i&&e.jsxs("div",{className:"flex flex-wrap gap-3 mt-4",children:[e.jsx(d,{to:"/login",className:"px-4 py-2 bg-[#88cc14] hover:bg-[#7ab811] text-black font-medium rounded-lg transition-colors",children:"Sign In to Track Progress"}),e.jsx(d,{to:"/signup",className:"px-4 py-2 border border-[#88cc14] text-[#88cc14] hover:bg-[#88cc14]/10 font-medium rounded-lg transition-colors",children:"Create Free Account"})]})]}),e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Available Learning Modules"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:$.map(t=>e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg overflow-hidden transition-transform hover:transform hover:scale-[1.02] flex flex-col h-full`,children:e.jsxs("div",{className:"p-6 flex flex-col flex-grow",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsx("h3",{className:"text-xl font-bold",children:t.title}),e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:t.icon})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:t.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:t.difficulty.name}),t.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(h,{className:"mr-1"})," ",T(t.estimated_time)]}),e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${t.isFree?s?"bg-green-900/20 text-green-300":"bg-green-100 text-green-800":s?"bg-amber-900/20 text-amber-300":"bg-amber-100 text-amber-800"}`,children:[t.isFree?e.jsx(I,{className:"mr-1"}):e.jsx(N,{className:"mr-1"}),t.isFree?"Free":"Premium"]})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} mb-4`,children:t.description}),e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-3 rounded-lg mb-4 border ${s?"border-gray-700":"border-gray-200"}`,children:[e.jsxs("h4",{className:"font-medium mb-2 flex items-center",children:[e.jsx(g,{className:"mr-2 text-[#88cc14]"})," Preview Content"]}),e.jsxs("ul",{className:`list-disc list-inside space-y-1 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:[t.topics&&t.topics.slice(0,3).map((a,p)=>e.jsx("li",{children:a},p)),!t.topics&&[e.jsxs("li",{children:["Introduction to ",t.title]},"1"),e.jsx("li",{children:"Core concepts and terminology"},"2"),e.jsx("li",{children:"Hands-on practice exercises"},"3")]]})]}),c[t.id]>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between text-xs mb-1",children:[e.jsx("span",{children:c[t.id]===100?"Completed":"In Progress"}),e.jsxs("span",{children:[c[t.id],"%"]})]}),e.jsx("div",{className:`h-2 w-full rounded-full ${s?"bg-gray-800":"bg-gray-200"}`,children:e.jsx("div",{className:`h-2 rounded-full ${c[t.id]===100?"bg-green-500":"bg-[#88cc14]"}`,style:{width:`${c[t.id]}%`}})})]}),e.jsxs("div",{className:"mt-auto pt-4 flex flex-col gap-2",children:[e.jsx(d,{to:`/learn/preview/${t.slug}`,className:`block w-full text-center px-4 py-2 ${t.isFree||(i==null?void 0:i.subscription_tier)==="premium"||(i==null?void 0:i.subscription_tier)==="business"?"bg-[#88cc14] hover:bg-[#7ab811] text-black":"bg-gray-600 text-gray-300 cursor-not-allowed"} font-medium rounded-lg transition-colors`,onClick:a=>{!t.isFree&&(!i||i.subscription_tier==="free")&&(a.preventDefault(),alert("This is a premium module. Please upgrade to access this content."))},children:c[t.id]>0?c[t.id]===100?"Review Module":"Continue Learning":"Start Learning"}),!i&&e.jsxs(d,{to:"/login",className:`w-full text-center px-4 py-2 ${s?"bg-gray-800 hover:bg-gray-700 text-gray-300":"bg-gray-100 hover:bg-gray-200 text-gray-700"} font-medium rounded-lg transition-colors flex items-center justify-center`,children:[e.jsx(g,{className:"mr-2"})," Sign In to Track Progress"]}),i&&!t.isFree&&i.subscription_tier==="free"&&e.jsxs(d,{to:"/pricing",className:`w-full text-center px-4 py-2 ${s?"bg-amber-800 hover:bg-amber-700 text-white":"bg-amber-500 hover:bg-amber-600 text-white"} font-medium rounded-lg transition-colors flex items-center justify-center`,children:[e.jsx(N,{className:"mr-2"})," Upgrade to Access"]})]})]})},t.id))}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:e.jsx(g,{className:"text-[#88cc14]"})}),e.jsx("h2",{className:"text-xl font-bold",children:"Why Learn With Us?"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-2",children:[e.jsxs("div",{className:"p-4 border border-dashed rounded-lg border-gray-700",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Practical Skills"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"Hands-on exercises and real-world scenarios to build practical cybersecurity skills."})]}),e.jsxs("div",{className:"p-4 border border-dashed rounded-lg border-gray-700",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Industry Relevant"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"Content aligned with industry standards and current cybersecurity practices."})]}),e.jsxs("div",{className:"p-4 border border-dashed rounded-lg border-gray-700",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Career Growth"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm`,children:"Structured learning paths to help you advance your cybersecurity career."})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Current Learning Paths"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg`,children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center",children:e.jsx(b,{className:"text-blue-500"})}),e.jsx("h3",{className:"font-bold",children:"Defensive Security"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm mb-3`,children:"Learn to protect systems and networks from cyber threats with defensive security techniques."}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsxs("span",{className:"flex items-center mr-3",children:[e.jsx(g,{className:"mr-1"})," 8 Modules"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(h,{className:"mr-1"})," 24 Hours"]})]})]}),e.jsxs("div",{className:`${s?"bg-[#252D4A]":"bg-gray-50"} p-4 rounded-lg`,children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center",children:e.jsx(y,{className:"text-red-500"})}),e.jsx("h3",{className:"font-bold",children:"Offensive Security"})]}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"} text-sm mb-3`,children:"Master ethical hacking and penetration testing to identify and exploit vulnerabilities."}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsxs("span",{className:"flex items-center mr-3",children:[e.jsx(g,{className:"mr-1"})," 10 Modules"]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(h,{className:"mr-1"})," 30 Hours"]})]})]})]})]}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Your Learning Experience"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsx("div",{className:"flex flex-col h-full",children:e.jsxs("div",{className:"bg-gradient-to-r from-[#88cc14]/20 to-transparent p-5 rounded-lg mb-4 flex-grow",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#88cc14]/30 rounded-full flex items-center justify-center",children:e.jsx(b,{className:"text-[#88cc14]"})}),e.jsx("h3",{className:"text-lg font-bold",children:"Interactive Labs"})]}),e.jsx("p",{className:`${s?"text-gray-300":"text-gray-700"} mb-3`,children:"Practice in real-world environments with our hands-on virtual labs. Apply what you learn immediately with guided exercises that reinforce key concepts."}),e.jsxs("ul",{className:`list-disc list-inside space-y-1 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:[e.jsx("li",{children:"Secure sandbox environments"}),e.jsx("li",{children:"Real-world attack simulations"}),e.jsx("li",{children:"Guided step-by-step exercises"})]})]})}),e.jsx("div",{className:"flex flex-col h-full",children:e.jsxs("div",{className:"bg-gradient-to-r from-blue-500/20 to-transparent p-5 rounded-lg mb-4 flex-grow",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500/30 rounded-full flex items-center justify-center",children:e.jsx(g,{className:"text-blue-500"})}),e.jsx("h3",{className:"text-lg font-bold",children:"Structured Learning"})]}),e.jsx("p",{className:`${s?"text-gray-300":"text-gray-700"} mb-3`,children:"Follow a carefully designed curriculum that builds your skills progressively. Track your progress and earn certificates as you complete modules."}),e.jsxs("ul",{className:`list-disc list-inside space-y-1 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:[e.jsx("li",{children:"Progressive skill development"}),e.jsx("li",{children:"Knowledge checkpoints"}),e.jsx("li",{children:"Completion certificates"})]})]})}),e.jsx("div",{className:"flex flex-col h-full",children:e.jsxs("div",{className:"bg-gradient-to-r from-purple-500/20 to-transparent p-5 rounded-lg flex-grow",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-purple-500/30 rounded-full flex items-center justify-center",children:e.jsx(w,{className:"text-purple-500"})}),e.jsx("h3",{className:"text-lg font-bold",children:"Community Support"})]}),e.jsx("p",{className:`${s?"text-gray-300":"text-gray-700"} mb-3`,children:"Join a community of cybersecurity enthusiasts. Collaborate on challenges, share insights, and learn from peers and industry experts."}),e.jsxs("ul",{className:`list-disc list-inside space-y-1 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:[e.jsx("li",{children:"Discussion forums"}),e.jsx("li",{children:"Peer collaboration"}),e.jsx("li",{children:"Expert mentorship"})]})]})}),e.jsx("div",{className:"flex flex-col h-full",children:e.jsxs("div",{className:"bg-gradient-to-r from-red-500/20 to-transparent p-5 rounded-lg flex-grow",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-red-500/30 rounded-full flex items-center justify-center",children:e.jsx(y,{className:"text-red-500"})}),e.jsx("h3",{className:"text-lg font-bold",children:"Practical Challenges"})]}),e.jsx("p",{className:`${s?"text-gray-300":"text-gray-700"} mb-3`,children:"Test your skills with realistic cybersecurity challenges. Solve puzzles, find vulnerabilities, and build your problem-solving abilities."}),e.jsxs("ul",{className:`list-disc list-inside space-y-1 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:[e.jsx("li",{children:"CTF-style challenges"}),e.jsx("li",{children:"Vulnerability hunting"}),e.jsx("li",{children:"Defensive exercises"})]})]})})]})]})]})})},D=()=>{const{darkMode:s}=f(),{moduleId:i}=k(),{profile:c}=S(),[l,t]=x.useState(0),[a,p]=x.useState(null),r=$.find(n=>n.slug===i||n.id===i);x.useEffect(()=>{if(r){const n=M(r.id);t(n)}},[r]);const C=n=>{if(p(n),l<100){const o=r.content.sections.length,m=Math.floor(100/o),u=Math.min(l+m,100);t(u),v(r.id,u)}},P=()=>{v(r.id,100),t(100),alert("Congratulations! You have completed this module.")};return r?e.jsxs("div",{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(d,{to:"/learn",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(j,{className:"mr-2"})," Back to Learning Modules"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6`,children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:r.title}),e.jsx("div",{className:"w-12 h-12 bg-[#88cc14]/20 rounded-full flex items-center justify-center",children:r.icon})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-blue-900/20 text-blue-300":"bg-blue-100 text-blue-800"}`,children:r.category.name}),e.jsx("span",{className:`px-2 py-1 rounded text-xs ${s?"bg-purple-900/20 text-purple-300":"bg-purple-100 text-purple-800"}`,children:r.difficulty.name}),r.estimated_time&&e.jsxs("span",{className:`px-2 py-1 rounded text-xs flex items-center ${s?"bg-gray-800 text-gray-300":"bg-gray-100 text-gray-800"}`,children:[e.jsx(h,{className:"mr-1"})," ",T(r.estimated_time)]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{children:l===0?"Not Started":l===100?"Completed":"In Progress"}),e.jsxs("span",{children:[l,"%"]})]}),e.jsx("div",{className:`h-2 w-full rounded-full ${s?"bg-gray-800":"bg-gray-200"}`,children:e.jsx("div",{className:`h-2 rounded-full ${l===100?"bg-green-500":"bg-[#88cc14]"}`,style:{width:`${l}%`}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Introduction"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:r.content.introduction})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Module Content"}),r.content.sections&&r.content.sections.length>0?e.jsx("div",{children:e.jsx("ul",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg divide-y ${s?"divide-gray-700":"divide-gray-200"}`,children:r.content.sections.map((n,o)=>{const m=r.content.section_details[n];return e.jsx("li",{className:`p-4 ${a===n?`${s?"bg-gray-800":"bg-gray-100"}`:""}`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("span",{className:`w-8 h-8 flex items-center justify-center rounded-full mr-3 ${a===n?"bg-[#88cc14] text-black":s?"bg-gray-700 text-gray-300":"bg-gray-200 text-gray-700"}`,children:o+1}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-medium",children:m.title}),e.jsxs("p",{className:`mt-1 text-sm ${s?"text-gray-400":"text-gray-600"}`,children:[m.content.substring(0,100),"..."]}),e.jsx("div",{className:"mt-2",children:e.jsx("button",{onClick:()=>C(n),className:`text-sm px-3 py-1 rounded-md ${a===n?"bg-[#88cc14] text-black":s?"text-[#88cc14] hover:bg-gray-700":"text-[#88cc14] hover:bg-gray-200"}`,children:a===n?"Currently Viewing":"View Section"})})]})]})},n)})})}):e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"No sections available for this module."})]}),r.content.resources&&r.content.resources.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Additional Resources"}),e.jsx("ul",{className:`list-disc pl-5 ${s?"text-gray-400":"text-gray-600"}`,children:r.content.resources.map((n,o)=>e.jsx("li",{className:"mb-1",children:e.jsx("a",{href:n.url,target:"_blank",rel:"noopener noreferrer",className:"text-[#88cc14] hover:underline",children:n.title})},o))})]}),a&&e.jsxs("div",{className:`${s?"bg-[#252D4A] border-gray-700":"bg-gray-100 border-gray-300"} border rounded-lg p-6 mb-6`,children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:r.content.section_details[a].title}),e.jsxs("div",{className:"prose prose-lg max-w-none dark:prose-invert",children:[e.jsx("p",{children:r.content.section_details[a].content}),r.content.section_details[a].key_points&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Key Points"}),e.jsx("ul",{className:"list-disc pl-5",children:r.content.section_details[a].key_points.map((n,o)=>e.jsx("li",{className:"mb-1",children:n},o))})]}),r.content.section_details[a].quiz&&e.jsxs("div",{className:"mt-6 p-4 border border-dashed rounded-lg border-gray-500",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Knowledge Check"}),e.jsx("p",{className:"mb-4",children:"Test your understanding of this section with these questions:"}),e.jsx("div",{className:"space-y-4",children:r.content.section_details[a].quiz.map((n,o)=>e.jsxs("div",{className:"p-3 bg-opacity-50 rounded-lg bg-gray-800",children:[e.jsx("p",{className:"font-medium mb-2",children:n.question}),e.jsx("div",{className:"space-y-2",children:n.options.map((m,u)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"radio",id:`q${o}-opt${u}`,name:`question-${o}`,className:"mr-2"}),e.jsx("label",{htmlFor:`q${o}-opt${u}`,children:m})]},u))})]},o))})]}),r.content.section_details[a].exercise&&e.jsxs("div",{className:"mt-6 p-4 border border-dashed rounded-lg border-gray-500",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:r.content.section_details[a].exercise.title}),e.jsx("p",{className:"mb-3",children:r.content.section_details[a].exercise.description}),e.jsx("div",{className:"space-y-3",children:r.content.section_details[a].exercise.scenarios.map((n,o)=>e.jsxs("div",{className:"p-3 bg-opacity-50 rounded-lg bg-gray-800",children:[e.jsxs("p",{className:"mb-2",children:[e.jsxs("strong",{children:["Scenario ",o+1,":"]})," ",n]}),e.jsx("input",{type:"text",placeholder:"Your answer...",className:"w-full p-2 rounded bg-gray-700 text-white border border-gray-600"})]},o))})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-between items-center mt-8 pt-6 border-t border-gray-700",children:[e.jsxs("button",{onClick:P,className:`w-full sm:w-auto px-6 py-3 ${l===100?"bg-green-500 hover:bg-green-600":"bg-[#88cc14] hover:bg-[#7ab811]"} text-black font-medium rounded-lg transition-colors flex items-center justify-center`,children:[e.jsx(A,{className:"mr-2"})," ",l===100?"Completed!":"Mark as Completed"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"mr-2",children:"Rate:"}),e.jsx("div",{className:"flex",children:[1,2,3,4,5].map(n=>e.jsx("button",{type:"button",className:"text-2xl focus:outline-none",children:e.jsx(_,{className:`${s?"text-gray-700":"text-gray-300"}`})},n))})]})]})]})]}):e.jsxs("div",{children:[e.jsx("div",{className:"mb-6",children:e.jsxs(d,{to:"/learn",className:"flex items-center text-[#88cc14] hover:underline",children:[e.jsx(j,{className:"mr-2"})," Back to Learning Modules"]})}),e.jsxs("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} border rounded-lg p-6 text-center`,children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Module Not Found"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"The learning module you're looking for doesn't exist or has been removed."})]})]})},W=()=>{const{darkMode:s}=f(),{moduleId:i}=k();return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120] text-white":"bg-gray-50 text-gray-900"} pt-24 pb-16`,children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("h1",{className:"text-3xl font-bold mb-6",children:i?"Learning Module":"Learning Modules"}),i?e.jsx(D,{}):e.jsx(F,{})]})})};export{$ as MODULES,W as default};
