import{b as p,u as w,r as a,j as e,ae as f,l as m,v as j}from"./index-CVvVjHWF.js";const N=()=>{const d=p(),{darkMode:s}=w(),[l,b]=a.useState(""),[i,h]=a.useState(""),[o,n]=a.useState(!1),[x,c]=a.useState(null),[g,u]=a.useState(null),y=async r=>{if(r.preventDefault(),n(!0),c(null),u(null),l!==i){c("Passwords don't match"),n(!1);return}try{const{error:t}=await j.auth.updateUser({password:l});if(t)throw t;u("Password has been reset successfully!"),setTimeout(()=>{d("/login")},2e3)}catch(t){console.error("Password reset error:",t),c(t.message||"Failed to reset password. Please try again.")}finally{n(!1)}};return e.jsx("div",{className:`min-h-screen ${s?"bg-[#0B1120]":"bg-gray-50"} flex items-center justify-center p-4`,children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("button",{onClick:()=>d("/login"),className:`${s?"text-gray-400 hover:text-white":"text-gray-600 hover:text-gray-900"} flex items-center gap-2`,children:[e.jsx(f,{}),e.jsx("span",{children:"Back to Login"})]})}),e.jsx("div",{className:`${s?"bg-[#1A1F35] border-gray-800":"bg-white border-gray-200"} rounded-xl border overflow-hidden`,children:e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:`text-3xl font-bold ${s?"text-white":"text-gray-900"} mb-2`,children:"Set New Password"}),e.jsx("p",{className:`${s?"text-gray-400":"text-gray-600"}`,children:"Enter your new password below"})]}),x&&e.jsx("div",{className:`${s?"bg-red-500/20 text-red-400":"bg-red-100 text-red-800"} px-4 py-3 rounded mb-6`,children:x}),g&&e.jsx("div",{className:`${s?"bg-green-500/20 text-green-400":"bg-green-100 text-green-800"} px-4 py-3 rounded mb-6`,children:g}),e.jsxs("form",{onSubmit:y,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"password",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(m,{className:"text-gray-500"})}),e.jsx("input",{type:"password",id:"password",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"••••••••",value:l,onChange:r=>b(r.target.value),required:!0,minLength:8})]}),e.jsx("p",{className:`mt-1 text-sm ${s?"text-gray-500":"text-gray-600"}`,children:"Password must be at least 8 characters"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"confirmPassword",className:`block ${s?"text-gray-400":"text-gray-700"} mb-2`,children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(m,{className:"text-gray-500"})}),e.jsx("input",{type:"password",id:"confirmPassword",className:`${s?"bg-[#0B1120] border-gray-800 text-white":"bg-gray-50 border-gray-300 text-gray-900"} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`,placeholder:"••••••••",value:i,onChange:r=>h(r.target.value),required:!0})]})]}),e.jsx("button",{type:"submit",className:`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${o?"opacity-70 cursor-not-allowed":""}`,disabled:o,children:o?"Resetting Password...":"Reset Password"})]})]})})]})})};export{N as default};
